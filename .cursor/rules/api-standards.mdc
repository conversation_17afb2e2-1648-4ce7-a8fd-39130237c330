---
description: 
globs: 
alwaysApply: false
---
---
description: API Standards and Best Practices
globs: ["app/api/**/*.ts", "app/api/**/*.js", "api/**/*.ts", "api/**/*.js", "src/api/**/*.ts", "src/api/**/*.js"]
alwaysApply: true
---
# API Standards

- All API endpoints should follow the project naming conventions:
  - Use camelCase for API paths
  - Use plural nouns for resource collections
  - Use nested resources for related data
  - Example: `/api/affiliate/serviceAreas`

- Response bodies should:
  - Use camelCase for all property names
  - Include appropriate metadata for lists (pagination, total count)
  - Follow consistent error formats

- HTTP methods should be used correctly:
  - GET: Retrieve resources
  - POST: Create new resources
  - PUT: Update resources (full replacement)
  - PATCH: Partial update of resources
  - DELETE: Remove resources

- Status codes should be used appropriately:
  - 200 OK: Successful request
  - 201 Created: Resource successfully created
  - 204 No Content: Successful request with no response body
  - 400 Bad Request: Invalid request parameters
  - 401 Unauthorized: Authentication required
  - 403 Forbidden: Authenticated but not authorized
  - 404 Not Found: Resource not found
  - 500 Internal Server Error: Server error

- Error responses should follow the standard format:
```json
{
  "error": "Error message",
  "details": {
    "field": "Specific error for this field"
  }
}
```

- All API handlers should include:
  - Input validation using Zod schemas
  - Authentication checks
  - Appropriate error handling
  - Fallback mechanisms for critical operations
  - Comprehensive logging

- When updating existing endpoints, maintain backward compatibility:
  - Create new endpoints with updated conventions
  - Update old endpoints to forward requests to new endpoints
  - Add deprecation notices to old endpoints

- API endpoints should implement proper access control:
  - Customer can only access their own data
  - Affiliate can only access offers sent to them
  - Admin has full access but must respect role boundaries