---
description: UI Design System Standards
globs: ["app/components/**/*.tsx", "app/components/**/*.jsx", "src/components/**/*.tsx", "src/components/**/*.jsx"]
alwaysApply: true
---
# UI Design System

- Component usage:
  - Always use design system components instead of custom styles
  - Use shadcn/ui components consistently throughout the application
  - Extend design system components rather than creating new ones
  - Follow component composition patterns

- Styling guidelines:
  - Use Tailwind CSS for all styling
  - Follow the project's color palette defined in tailwind.config.js
  - Use class-variance-authority (cva) for component variants
  - Use the cn utility for combining class names
  - Maintain a consistent spacing system

- Accessibility:
  - Use semantic HTML elements
  - Ensure proper ARIA attributes
  - Maintain sufficient color contrast
  - Support keyboard navigation
  - Implement proper focus management

- Responsive design:
  - Design mobile-first
  - Use Tailwind breakpoints consistently
  - Test on multiple screen sizes
  - Ensure touch targets are appropriate for mobile
  - Provide appropriate content adjustments for small screens

- Form elements:
  - Use consistent form layouts
  - Provide clear error messages
  - Use appropriate input types
  - Implement proper form validation
  - Include appropriate loading states

- UI patterns:
  - Use consistent loading patterns (skeletons preferred)
  - Implement proper empty states
  - Provide clear error states with recovery options
  - Use consistent notification patterns
  - Follow established navigation patterns

- Icons and imagery:
  - Use lucide-react icons consistently
  - Maintain consistent icon sizes
  - Optimize images for performance
  - Use SVGs when possible
  - Ensure proper alt text for images 