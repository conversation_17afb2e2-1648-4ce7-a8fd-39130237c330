---
description: 
globs: 
alwaysApply: false
---
# Quote Workflow Rules

- Quote Status Flow:
  - Respect the standard quote status progression:
    1. pending
    2. sent_to_affiliates
    3. rate_requested / quote_ready (depending on submission type)
    4. quote_assigned
    5. accepted / rejected
    6. completed / cancelled

- Status Transitions:
  - Always update timeline entries when changing quote status
  - Validate state transitions according to the workflow rules
  - Use the appropriate API endpoints for transitions
  - Implement proper error handling for failed transitions
  - Add fallback mechanisms for critical transitions

- Role-Based Access Control:
  - Admin: Full quote management capabilities
  - Affiliate: Limited to quotes sent to or assigned to them
  - Customer: Limited to their own quotes
  - Enforce these restrictions at both UI and API levels

- UI Conventions:
  - Display quotes in tabs according to their status
  - Show elapsed time indicators with appropriate urgency colors
  - Use consistent icons for quote states
  - Follow the design system for all UI elements

- Quote Data Standards:
  - Validate all quote fields before submission
  - Ensure all required fields are present
  - Use proper formatting for date/time fields
  - Maintain consistency in location data format

- Quote Assignment Process:
  - Check for available affiliates in the quote's city
  - Provide fallback mechanism for cities without affiliates
  - Track all quote offers and responses
  - Maintain a complete timeline of assignment activities 