---
description: Database Access and Supabase Integration Standards
globs: ["app/api/**/*.ts", "app/lib/**/*db*.ts", "app/lib/**/*supabase*.ts", "lib/**/*db*.ts", "lib/**/*supabase*.ts", "supabase/**/*.ts"]
alwaysApply: true
---
# Database Access and Supabase Integration

- Database access patterns:
  - Use Supabase client for database operations
  - Implement proper RLS policies for security
  - Use typed responses for Supabase queries
  - Handle database errors appropriately

- Data validation:
  - Validate data before inserting/updating
  - Use Zod schemas for validation
  - Ensure data integrity with constraints
  - Implement proper error handling for validation failures

- Row Level Security:
  - Implement RLS policies for all tables
  - Test RLS policies thoroughly
  - Use appropriate policies for different user roles
  - Document RLS policies clearly

- Query optimization:
  - Select only needed columns
  - Use indexes for frequently queried columns
  - Implement pagination for large datasets
  - Optimize joins and complex queries
  - Use count() with caution

- Transaction handling:
  - Use transactions for related operations
  - Implement proper error handling and rollbacks
  - Document transaction boundaries clearly
  - Test transaction failure scenarios

- Foreign key constraints:
  - Implement proper foreign key constraints
  - <PERSON><PERSON> cascading deletes appropriately
  - Ensure referential integrity
  - Test constraint violations

- Migrations:
  - Document all schema changes
  - Use migration files for schema changes
  - Test migrations thoroughly
  - Implement rollback procedures
  - Update types after schema changes 