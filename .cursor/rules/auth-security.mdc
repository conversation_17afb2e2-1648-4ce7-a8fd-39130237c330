---
description: Authentication and Security Standards
globs: ["app/**/auth/**/*.ts", "app/**/auth/**/*.tsx", "middleware.ts", "app/api/**/*.ts"]
alwaysApply: true
---
# Authentication and Security

- Authentication patterns:
  - Use Supabase Auth for authentication
  - Implement proper session management
  - Validate auth tokens on both client and server
  - Use middleware.ts for protected routes

- Security best practices:
  - Never expose API keys or secrets in client-side code
  - Implement proper CORS policies
  - Use HTTPS for all API calls
  - Sanitize user inputs to prevent XSS

- Role-Based Access Control:
  - Use Supabase RLS policies for database access control
  - Verify user roles in API handlers
  - Validate permissions before performing operations
  - Implement least privilege principle

- Session management:
  - Check for expired sessions
  - Provide proper session refresh mechanisms
  - Handle session timeouts gracefully
  - Implement secure logout procedures

- API security:
  - Validate all input data
  - Implement rate limiting for public endpoints
  - Add CSRF protection for form submissions
  - Use parameterized queries to prevent SQL injection

- Error handling:
  - Never expose sensitive information in error messages
  - Log security events for audit purposes
  - Implement proper error boundaries in UI components
  - Provide user-friendly error messages 