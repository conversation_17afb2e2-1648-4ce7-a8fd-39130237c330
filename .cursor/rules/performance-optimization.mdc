---
description: Performance Optimization Standards
globs: ["app/**/*.tsx", "app/**/*.ts", "src/**/*.tsx", "src/**/*.ts"]
alwaysApply: true
---
# Performance Optimization

- React optimization:
  - Use React.memo for components that render often
  - Use useMemo for expensive calculations
  - Use useCallback for event handlers passed to child components
  - Implement virtualization for long lists (react-window, react-virtualized)

- Data fetching optimization:
  - Use React Query for efficient data fetching and caching
  - Implement stale-while-revalidate patterns
  - Avoid unnecessary data fetching
  - Use appropriate fetch policies (cache-first, network-only)
  - Implement pagination for large datasets

- Render optimization:
  - Avoid unnecessary re-renders
  - Use proper key props for lists
  - Avoid anonymous functions in render
  - Move expensive calculations outside of render
  - Implement code-splitting for large components

- API call debouncing:
  - Always implement throttling or debouncing for API calls triggered by UI events
  - Use appropriate timeout values (e.g., 300ms for search inputs)
  - Cancel in-flight requests when new ones are made
  - Show loading indicators for long-running operations

- Bundle optimization:
  - Use dynamic imports for code-splitting
  - Optimize dependencies to reduce bundle size
  - Implement tree-shaking for unused code
  - Use appropriate image formats and sizes
  - Lazy-load non-critical resources

- Performance monitoring:
  - Monitor client-side performance metrics
  - Implement performance budgets
  - Track and optimize Core Web Vitals
  - Use React DevTools Profiler for identifying performance issues 