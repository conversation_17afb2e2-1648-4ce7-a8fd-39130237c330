---
description: 
globs: 
alwaysApply: false
---
 ---
description: React Component Structure Standards
globs: ["app/components/**/*.tsx", "app/components/**/*.jsx", "src/components/**/*.tsx", "src/components/**/*.jsx"]
alwaysApply: true
---
# React Component Structure

- Components should follow a consistent structure:
  - Imports first (React, then third-party, then local)
  - Type definitions and interfaces
  - Component function with proper typing
  - Export statement

- Components that manage async operations should:
  - Implement loading states (skeletons preferred)
  - Handle and display error states
  - Use try/catch for all async operations
  - Provide user feedback on success/failure

- State management guidelines:
  - Use context providers for shared state
  - Use useState/useReducer for component-specific state
  - Use useMemo/useCallback for derived values and handlers
  - Consider XState for complex state machines

- Performance optimizations:
  - Memoize expensive calculations with useMemo
  - Memoize callback handlers with useCallback
  - Use React.memo for pure components that render often
  - Virtualize long lists

- Component organization:
  - Place UI components in app/components/ui/
  - Place feature components in app/components/features/
  - Place page components in appropriate route folders
  - Place shared components in app/components/shared/

- Styling guidelines:
  - Use Tailwind CSS for styling
  - Use class-variance-authority for component variants
  - Use `cn` utility for merging class names
  - Follow design system constraints

- Each component should have a single responsibility
- Avoid nested ternary operators for conditional rendering
- Destructure props at the component function level
- Use TypeScript interfaces for all component props