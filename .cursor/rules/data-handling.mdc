---
description: 
globs: 
alwaysApply: false
---
 ---
description: Data Handling and Type Safety Standards
globs: ["app/**/*.tsx", "app/**/*.ts", "src/**/*.tsx", "src/**/*.ts"]
alwaysApply: true
---
# Data Handling and Type Safety

- Always define explicit interface types for API responses
- Use Zod schemas for runtime validation of API data
- Handle date/time values safely:
  - Always validate date objects before formatting or calculations
  - Use helper functions like safelyExtractDate/safelyExtractTime
  - Handle invalid dates gracefully

- Error handling for API calls:
  - Wrap all async operations in try/catch blocks
  - Provide fallbacks for critical operations
  - Log detailed error information
  - Display user-friendly error messages

- Data fetching best practices:
  - Use React Query for data fetching and caching
  - Implement stale-while-revalidate patterns
  - Provide fallbacks when API calls fail
  - Consider offline-first approaches for critical features

- Form validation:
  - Use react-hook-form for form state management
  - Use zod with @hookform/resolvers for validation
  - Provide inline validation feedback
  - Disable submit buttons when forms are invalid

- Safe data access patterns:
  - Use optional chaining (obj?.prop) for potentially undefined values
  - Use nullish coalescing (value ?? fallback) for default values
  - Use array methods that handle empty arrays gracefully
  - Destructure with default values

- Defensive coding:
  - Validate all function inputs
  - Check for null/undefined before operations
  - Provide meaningful default values
  - Avoid type coercion
  - Use TypeScript strict mode