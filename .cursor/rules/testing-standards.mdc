---
description: Testing Standards and Best Practices
globs: ["**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "tests/**/*.ts", "tests/**/*.tsx"]
alwaysApply: true
---
# Testing Standards

- Unit testing:
  - Use Jest for unit testing
  - Test all utility functions
  - Test complex logic in isolation
  - Follow the AAA pattern (Arrange, Act, Assert)
  - Mock dependencies appropriately

- Component testing:
  - Use React Testing Library for component tests
  - Test component behavior, not implementation details
  - Focus on user interactions and accessibility
  - Test different component states (loading, error, success)
  - Test responsive behavior

- API testing:
  - Test API endpoints for correct behavior
  - Test error handling and edge cases
  - Use mocks for external dependencies
  - Test authentication and authorization
  - Verify response formats match specifications

- End-to-end testing:
  - Use Playwright for E2E tests
  - Test critical user flows
  - Test on multiple browsers
  - Include mobile viewport testing
  - Set up proper test data

- Testing principles:
  - Write tests before or alongside implementation
  - Keep tests independent and isolated
  - Use descriptive test names
  - Focus on behavior, not implementation
  - Test edge cases and error conditions

- Test organization:
  - Place tests near the code they test
  - Use consistent naming patterns
  - Group related tests together
  - Separate test utilities and helpers
  - Maintain test data fixtures 