// check-local-superadmin-roles.cjs
require('dotenv').config();
const { Client } = require('pg');

const client = new Client({
  connectionString: process.env.DATABASE_URL,
});

async function main() {
  await client.connect();

  const res = await client.query(`
    SELECT id, email, raw_user_meta_data, raw_app_meta_data
    FROM auth.users
    ORDER BY email
  `);

  let foundSuperAdmin = false;

  for (const row of res.rows) {
    const userMeta = row.raw_user_meta_data || {};
    const appMeta = row.raw_app_meta_data || {};

    // Parse JSON if needed (pg may return as object or string)
    const userMetaObj = typeof userMeta === 'string' ? JSON.parse(userMeta) : userMeta;
    const appMetaObj = typeof appMeta === 'string' ? JSON.parse(appMeta) : appMeta;

    // Try both user and app meta for roles
    const roles =
      (Array.isArray(userMetaObj.roles) && userMetaObj.roles) ||
      (Array.isArray(appMetaObj.roles) && appMetaObj.roles) ||
      [];

    const hasSuperAdmin = roles.includes('SUPER_ADMIN');

    if (hasSuperAdmin) foundSuperAdmin = true;

    console.log(
      `User: ${row.email} (${row.id})\n  roles: ${JSON.stringify(roles)}\n  SUPER_ADMIN: ${hasSuperAdmin ? '✅' : '❌'}\n`
    );

    if (!Array.isArray(roles)) {
      console.warn(`  ⚠️  roles is not an array for user ${row.email}`);
    }
    if (!hasSuperAdmin) {
      console.warn(`  ⚠️  User ${row.email} does NOT have SUPER_ADMIN role`);
    }
  }

  if (!foundSuperAdmin) {
    console.error('\n❌ No users with SUPER_ADMIN role found!');
  } else {
    console.log('\n✅ At least one SUPER_ADMIN found.');
  }

  await client.end();
}

main().catch((err) => {
  console.error('Error:', err);
  process.exit(1);
});