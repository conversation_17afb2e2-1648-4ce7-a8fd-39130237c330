#!/bin/bash

# Environment Switcher for WWMS
# Easily switch between local and cloud database configurations

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

function log() {
    echo -e "${2}${1}${NC}"
}

function show_current_env() {
    if [ -f ".env.local" ]; then
        local supabase_url=$(grep "NEXT_PUBLIC_SUPABASE_URL" .env.local | cut -d'=' -f2)
        if [[ $supabase_url == *"127.0.0.1"* ]] || [[ $supabase_url == *"localhost"* ]]; then
            log "📍 Current Environment: LOCAL" "$GREEN"
        else
            log "📍 Current Environment: CLOUD" "$BLUE"
        fi
        log "   URL: $supabase_url" "$YELLOW"
    else
        log "📍 No .env.local file found" "$RED"
    fi
}

function switch_to_local() {
    log "🔄 Switching to LOCAL environment..." "$BLUE"
    
    # Backup current .env.local if it exists
    if [ -f ".env.local" ]; then
        cp .env.local .env.local.backup.$(date +%Y%m%d_%H%M%S)
        log "✅ Backed up current .env.local" "$GREEN"
    fi
    
    # Create local environment configuration
    cat > .env.local << EOF
# Local Development Configuration - Generated $(date)
PORT=3333
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3333

# Local Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

# Local Database Configuration
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres
POSTGRES_HOST=127.0.0.1
POSTGRES_PORT=54322
POSTGRES_DB=postgres
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres

# Auth Configuration
NEXTAUTH_URL=http://localhost:3333
NEXTAUTH_SECRET=development_secret_key_change_in_production

# Development Settings
BYPASS_AUTH=false
EOF
    
    log "✅ Switched to LOCAL environment" "$GREEN"
    log "💡 Make sure to run: npx supabase start" "$YELLOW"
}

function switch_to_cloud() {
    log "🔄 Switching to CLOUD environment..." "$BLUE"
    
    # Check if cloud configuration exists
    if [ ! -f ".env.cloud.production" ]; then
        log "❌ Cloud configuration not found!" "$RED"
        log "💡 Please run the migration first: npm run migrate:cloud" "$YELLOW"
        exit 1
    fi
    
    # Backup current .env.local if it exists
    if [ -f ".env.local" ]; then
        cp .env.local .env.local.backup.$(date +%Y%m%d_%H%M%S)
        log "✅ Backed up current .env.local" "$GREEN"
    fi
    
    # Copy cloud configuration
    cp .env.cloud.production .env.local
    
    log "✅ Switched to CLOUD environment" "$GREEN"
    log "💡 Test connection with: npm run test:cloud" "$YELLOW"
}

function test_connection() {
    log "🧪 Testing database connection..." "$BLUE"
    
    if command -v node &> /dev/null; then
        npm run test:cloud
    else
        log "❌ Node.js not found. Please install Node.js to test connection." "$RED"
    fi
}

function show_help() {
    log "🔧 WWMS Environment Switcher" "$BLUE"
    log "============================" "$BLUE"
    echo ""
    log "Usage: $0 [COMMAND]" "$YELLOW"
    echo ""
    log "Commands:" "$GREEN"
    echo "  local     Switch to local development environment"
    echo "  cloud     Switch to cloud production environment"
    echo "  status    Show current environment status"
    echo "  test      Test current database connection"
    echo "  help      Show this help message"
    echo ""
    log "Examples:" "$GREEN"
    echo "  $0 local    # Switch to local Supabase"
    echo "  $0 cloud    # Switch to cloud database"
    echo "  $0 status   # Check current environment"
    echo "  $0 test     # Test database connection"
}

# Main script logic
case "${1:-help}" in
    "local")
        show_current_env
        echo ""
        switch_to_local
        echo ""
        show_current_env
        ;;
    "cloud")
        show_current_env
        echo ""
        switch_to_cloud
        echo ""
        show_current_env
        ;;
    "status")
        show_current_env
        ;;
    "test")
        test_connection
        ;;
    "help"|*)
        show_help
        ;;
esac
