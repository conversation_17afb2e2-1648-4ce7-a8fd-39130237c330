-- Multi-Tenant Test Data Seeding Script
-- Creates comprehensive test data for multi-tenant architecture testing

BEGIN;

-- 1. TENANT SETUP
-- Create multi-tenant structure with different tenant types
INSERT INTO public.tenants (id, name, slug, tenant_type, status, branding, settings) VALUES
  ('11111111-1111-1111-1111-111111111111', 'TransFlow SaaS', 'transflow-saas', 'shared', 'active', 
   '{"logo_url": "/logos/transflow.png", "primary_color": "#3B82F6", "secondary_color": "#1E40AF"}',
   '{"allowGlobalNetwork": true, "upsell_threshold": 10}'),
  ('22222222-2222-2222-2222-222222222222', 'LIMO123 Network', 'limo123', 'segregated', 'active',
   '{"logo_url": "/logos/limo123.png", "primary_color": "#DC2626", "secondary_color": "#991B1B"}',
   '{"allowGlobalNetwork": false, "upsell_threshold": 5}'),
  ('33333333-3333-3333-3333-333333333333', 'Marriott Downtown', 'marriott-downtown', 'white_label', 'active',
   '{"logo_url": "/logos/marriott.png", "primary_color": "#7C2D12", "secondary_color": "#451A03"}',
   '{"allowGlobalNetwork": false, "upsell_threshold": 20}')
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  tenant_type = EXCLUDED.tenant_type,
  branding = EXCLUDED.branding,
  settings = EXCLUDED.settings;

-- 2. CUSTOMERS SETUP
-- Create test customers for each tenant
INSERT INTO public.customers (id, name, email, phone, tenant_id) VALUES
  ('c1111111-1111-1111-1111-111111111111', 'John Smith - ABC Corp', '<EMAIL>', '******-2001', '11111111-1111-1111-1111-111111111111'),
  ('c2222222-2222-2222-2222-222222222222', 'Sarah Johnson - Tech Startup', '<EMAIL>', '******-2002', '11111111-1111-1111-1111-111111111111'),
  ('c3333333-3333-3333-3333-333333333333', 'Mike Wilson - Global Events', '<EMAIL>', '******-2003', '11111111-1111-1111-1111-111111111111'),
  ('c4444444-4444-4444-4444-444444444444', 'Emily Davis - LIMO123 Client', '<EMAIL>', '******-2004', '22222222-2222-2222-2222-222222222222'),
  ('c5555555-5555-5555-5555-555555555555', 'Robert Brown - Dallas Business', '<EMAIL>', '******-2005', '22222222-2222-2222-2222-222222222222'),
  ('c6666666-6666-6666-6666-666666666666', 'Lisa Garcia - Marriott Events', '<EMAIL>', '******-2006', '33333333-3333-3333-3333-333333333333')
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  tenant_id = EXCLUDED.tenant_id;

-- 3. AFFILIATE COMPANIES SETUP
-- Create affiliates with different statuses and network participation
INSERT INTO public.affiliate_companies (id, name, status, city, state, country, phone, email,
  network_participation, application_status, created_at, updated_at) VALUES
  ('a1111111-1111-1111-1111-111111111111', 'Elite Limo Services', 'active', 'New York', 'NY', 'USA',
   '******-0101', '<EMAIL>',
   '{"global_network": true, "exclusive_brands": [], "rate_sharing": "transparent", "preferred_volume": "maximum"}',
   'approved', NOW(), NOW()),
  ('a2222222-2222-2222-2222-222222222222', 'Dallas Executive Transport', 'pending', 'Dallas', 'TX', 'USA',
   '******-0102', '<EMAIL>',
   '{"global_network": false, "exclusive_brands": ["limo123"], "rate_sharing": "brand-specific", "preferred_volume": "selective"}',
   'pending', NOW(), NOW()),
  ('a3333333-3333-3333-3333-333333333333', 'Metro Car Service', 'active', 'Los Angeles', 'CA', 'USA',
   '******-0103', '<EMAIL>',
   '{"global_network": true, "exclusive_brands": [], "rate_sharing": "transparent", "preferred_volume": "maximum"}',
   'approved', NOW(), NOW()),
  ('a4444444-4444-4444-4444-444444444444', 'Luxury Rides Miami', 'rejected', 'Miami', 'FL', 'USA',
   '******-0104', '<EMAIL>',
   '{"global_network": false, "exclusive_brands": [], "rate_sharing": "transparent", "preferred_volume": "maximum"}',
   'rejected', NOW(), NOW()),
  ('a5555555-5555-5555-5555-555555555555', 'Boston Premier Limo', 'suspended', 'Boston', 'MA', 'USA',
   '******-0105', '<EMAIL>',
   '{"global_network": true, "exclusive_brands": [], "rate_sharing": "transparent", "preferred_volume": "maximum"}',
   'approved', NOW(), NOW()),
  ('a6666666-6666-6666-6666-666666666666', 'Chicago Black Car', 'active', 'Chicago', 'IL', 'USA',
   '******-0106', '<EMAIL>',
   '{"global_network": true, "exclusive_brands": [], "rate_sharing": "transparent", "preferred_volume": "maximum"}',
   'approved', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  status = EXCLUDED.status,
  application_status = EXCLUDED.application_status,
  network_participation = EXCLUDED.network_participation;

-- 4. TENANT-AFFILIATE RELATIONSHIPS
-- Define which affiliates work with which tenants
INSERT INTO public.tenant_affiliates (tenant_id, affiliate_id, relationship_type, commission_override, priority_level) VALUES
  -- TransFlow SaaS (shared) - has access to global network
  ('11111111-1111-1111-1111-111111111111', 'a1111111-1111-1111-1111-111111111111', 'shared', NULL, 1),
  ('11111111-1111-1111-1111-111111111111', 'a3333333-3333-3333-3333-333333333333', 'shared', NULL, 1),
  ('11111111-1111-1111-1111-111111111111', 'a6666666-6666-6666-6666-666666666666', 'shared', NULL, 1),
  -- LIMO123 (segregated) - has exclusive relationships
  ('22222222-2222-2222-2222-222222222222', 'a2222222-2222-2222-2222-222222222222', 'exclusive', 15.00, 1),
  ('22222222-2222-2222-2222-222222222222', 'a1111111-1111-1111-1111-111111111111', 'preferred', 12.00, 2),
  -- Marriott (white_label) - has preferred relationships
  ('33333333-3333-3333-3333-333333333333', 'a1111111-1111-1111-1111-111111111111', 'preferred', 10.00, 1),
  ('33333333-3333-3333-3333-333333333333', 'a3333333-3333-3333-3333-333333333333', 'preferred', 10.00, 2)
ON CONFLICT (tenant_id, affiliate_id) DO UPDATE SET
  relationship_type = EXCLUDED.relationship_type,
  commission_override = EXCLUDED.commission_override;

-- 5. EVENTS SETUP
-- Create events for different tenants using existing auth users
INSERT INTO public.events (id, name, description, customer_id, start_date, end_date, location, tenant_id, status, created_at) VALUES
  ('e1111111-1111-1111-1111-111111111111', 'Annual Board Meeting', 'Executive board meeting with airport transfers', '8a272683-44d1-49f1-b379-db0a00e59b21',
   '2025-07-15 09:00:00+00', '2025-07-15 17:00:00+00', 'Manhattan Conference Center, NYC', '11111111-1111-1111-1111-111111111111', 'published', NOW()),
  ('e2222222-2222-2222-2222-222222222222', 'Tech Conference 2025', 'Multi-day tech conference with shuttle service', '8a272683-44d1-49f1-b379-db0a00e59b21',
   '2025-08-20 08:00:00+00', '2025-08-22 18:00:00+00', 'Austin Convention Center, TX', '11111111-1111-1111-1111-111111111111', 'published', NOW()),
  ('e3333333-3333-3333-3333-333333333333', 'Global Sales Summit', 'International sales team meeting', '8a272683-44d1-49f1-b379-db0a00e59b21',
   '2025-09-10 10:00:00+00', '2025-09-10 16:00:00+00', 'Chicago Business Center, IL', '11111111-1111-1111-1111-111111111111', 'published', NOW()),
  ('e4444444-4444-4444-4444-444444444444', 'VIP Client Dinner', 'Exclusive dinner event for premium clients', '8a272683-44d1-49f1-b379-db0a00e59b21',
   '2025-07-30 18:00:00+00', '2025-07-30 23:00:00+00', 'Beverly Hills Hotel, CA', '22222222-2222-2222-2222-222222222222', 'published', NOW()),
  ('e5555555-5555-5555-5555-555555555555', 'Corporate Retreat', 'Executive team building retreat', '8a272683-44d1-49f1-b379-db0a00e59b21',
   '2025-08-15 07:00:00+00', '2025-08-17 18:00:00+00', 'Dallas Resort & Spa, TX', '22222222-2222-2222-2222-222222222222', 'published', NOW()),
  ('e6666666-6666-6666-6666-666666666666', 'Hotel Guest Transfers', 'Daily guest transfer service', '8a272683-44d1-49f1-b379-db0a00e59b21',
   '2025-08-01 06:00:00+00', '2025-08-01 22:00:00+00', 'Marriott Downtown, Various', '33333333-3333-3333-3333-333333333333', 'published', NOW())
ON CONFLICT (id) DO UPDATE SET
  tenant_id = EXCLUDED.tenant_id;

-- 6. BASIC QUOTES SETUP (simplified for testing)
-- Create a few basic quotes to test the system
INSERT INTO public.quotes (reference_number, customer_id, service_type, vehicle_type,
  pickup_location, dropoff_location, date, time, duration, distance, status,
  passenger_count, tenant_id, created_at, updated_at) VALUES
  -- TransFlow SaaS quotes
  ('TF-2025-001', '8a272683-44d1-49f1-b379-db0a00e59b21', 'airport', 'sedan',
   'JFK Airport, NY', 'Manhattan Conference Center, NYC', '2025-07-15', '08:00:00', '1 hour', '25 miles', 'pending',
   4, '11111111-1111-1111-1111-111111111111', NOW(), NOW()),
  ('TF-2025-002', '8a272683-44d1-49f1-b379-db0a00e59b21', 'airport', 'van',
   'Austin Airport, TX', 'Austin Convention Center, TX', '2025-08-20', '07:30:00', '45 minutes', '18 miles', 'accepted',
   8, '11111111-1111-1111-1111-111111111111', NOW(), NOW()),
  ('TF-2025-003', '8a272683-44d1-49f1-b379-db0a00e59b21', 'airport', 'luxury_sedan',
   'Chicago O''Hare, IL', 'Chicago Business Center, IL', '2025-09-10', '09:00:00', '50 minutes', '22 miles', 'accepted',
   2, '11111111-1111-1111-1111-111111111111', NOW(), NOW()),
  -- LIMO123 quotes
  ('L123-2025-001', '8a272683-44d1-49f1-b379-db0a00e59b21', 'airport', 'luxury_sedan',
   'LAX Airport, CA', 'Beverly Hills Hotel, CA', '2025-07-30', '17:00:00', '1 hour', '30 miles', 'pending',
   2, '22222222-2222-2222-2222-222222222222', NOW(), NOW()),
  ('L123-2025-002', '8a272683-44d1-49f1-b379-db0a00e59b21', 'airport', 'suv',
   'Dallas Airport, TX', 'Dallas Resort & Spa, TX', '2025-08-15', '06:30:00', '1.5 hours', '35 miles', 'accepted',
   6, '22222222-2222-2222-2222-222222222222', NOW(), NOW()),
  -- Marriott quotes
  ('MAR-2025-001', '8a272683-44d1-49f1-b379-db0a00e59b21', 'airport', 'sedan',
   'Downtown Airport', 'Marriott Downtown', '2025-08-01', '05:30:00', '30 minutes', '12 miles', 'accepted',
   3, '33333333-3333-3333-3333-333333333333', NOW(), NOW())
ON CONFLICT (reference_number) DO UPDATE SET
  tenant_id = EXCLUDED.tenant_id,
  status = EXCLUDED.status;

COMMIT;
