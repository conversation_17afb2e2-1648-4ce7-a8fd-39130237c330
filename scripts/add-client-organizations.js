// <PERSON>ript to add client organizations for testing the ORG switcher
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://127.0.0.1:54321';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addClientOrganizations() {
  console.log('Adding client organizations...');

  // First, delete the default organization
  const { error: deleteError } = await supabase
    .from('organizations')
    .delete()
    .eq('name', 'Default Organization');

  if (deleteError) {
    console.error('Error deleting default organization:', deleteError);
  } else {
    console.log('✅ Deleted default organization');
  }

  // Add client organizations
  const clientOrgs = [
    {
      id: '11111111-1111-1111-1111-111111111111',
      name: 'City Tours LLC',
      slug: 'city-tours-llc',
      description: 'Premium city tour and transportation services',
      industry: 'Tourism & Events',
      tenant_id: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
      status: 'active'
    },
    {
      id: '22222222-2222-2222-2222-222222222222',
      name: 'Miami Elite Transportation',
      slug: 'miami-elite-transport',
      description: 'Luxury transportation for corporate events and VIPs',
      industry: 'Corporate Transportation',
      tenant_id: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
      status: 'active'
    },
    {
      id: '33333333-3333-3333-3333-333333333333',
      name: 'Sunset Wedding Services',
      slug: 'sunset-wedding-services',
      description: 'Wedding and special event transportation coordination',
      industry: 'Wedding & Events',
      tenant_id: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
      status: 'active'
    },
    {
      id: '44444444-4444-4444-4444-444444444444',
      name: 'Corporate Connect Group',
      slug: 'corporate-connect-group',
      description: 'Business travel and corporate transportation management',
      industry: 'Corporate Services',
      tenant_id: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
      status: 'active'
    }
  ];

  const { data, error } = await supabase
    .from('organizations')
    .insert(clientOrgs);

  if (error) {
    console.error('Error adding client organizations:', error);
  } else {
    console.log('✅ Added client organizations:', clientOrgs.length);
    clientOrgs.forEach(org => {
      console.log(`  - ${org.name} (${org.industry})`);
    });
  }

  // Verify the organizations were added
  const { data: allOrgs, error: fetchError } = await supabase
    .from('organizations')
    .select('*');

  if (fetchError) {
    console.error('Error fetching organizations:', fetchError);
  } else {
    console.log(`\n✅ Total organizations in database: ${allOrgs.length}`);
    allOrgs.forEach(org => {
      console.log(`  - ${org.name} (${org.industry})`);
    });
  }
}

addClientOrganizations().catch(console.error);
