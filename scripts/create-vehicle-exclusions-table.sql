-- Create table to track affiliate vehicle type exclusions
-- This supports the minimal friction onboarding approach

CREATE TABLE IF NOT EXISTS affiliate_vehicle_exclusions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  affiliate_company_id UUID NOT NULL REFERENCES affiliate_companies(id) ON DELETE CASCADE,
  vehicle_type VARCHAR(100) NOT NULL,
  excluded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  reason VARCHAR(255) DEFAULT 'We don''t carry this vehicle in our fleet',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure one exclusion per affiliate per vehicle type
  UNIQUE(affiliate_company_id, vehicle_type)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_affiliate_vehicle_exclusions_company_id 
ON affiliate_vehicle_exclusions(affiliate_company_id);

CREATE INDEX IF NOT EXISTS idx_affiliate_vehicle_exclusions_vehicle_type 
ON affiliate_vehicle_exclusions(vehicle_type);

-- Add RLS policies
ALTER TABLE affiliate_vehicle_exclusions ENABLE ROW LEVEL SECURITY;

-- Policy: Affiliates can only see and manage their own exclusions
CREATE POLICY "Affiliates can manage their own vehicle exclusions" ON affiliate_vehicle_exclusions
FOR ALL USING (
  affiliate_company_id IN (
    SELECT company_id FROM affiliate_user_companies 
    WHERE user_id = auth.uid()
  )
);

-- Policy: Super admins can see all exclusions
CREATE POLICY "Super admins can view all vehicle exclusions" ON affiliate_vehicle_exclusions
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = auth.uid() 
    AND 'SUPER_ADMIN' = ANY(roles)
  )
);

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION update_affiliate_vehicle_exclusions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_affiliate_vehicle_exclusions_updated_at
  BEFORE UPDATE ON affiliate_vehicle_exclusions
  FOR EACH ROW
  EXECUTE FUNCTION update_affiliate_vehicle_exclusions_updated_at();

-- Add comments for documentation
COMMENT ON TABLE affiliate_vehicle_exclusions IS 'Tracks vehicle types that affiliates have explicitly excluded from receiving offers for';
COMMENT ON COLUMN affiliate_vehicle_exclusions.affiliate_company_id IS 'Reference to the affiliate company';
COMMENT ON COLUMN affiliate_vehicle_exclusions.vehicle_type IS 'Vehicle type to exclude (e.g., suv, sedan, luxury sedan)';
COMMENT ON COLUMN affiliate_vehicle_exclusions.reason IS 'Reason for exclusion (default: We don''t carry this vehicle in our fleet)';
COMMENT ON COLUMN affiliate_vehicle_exclusions.excluded_at IS 'When the exclusion was created';

-- Sample data for testing (optional)
-- INSERT INTO affiliate_vehicle_exclusions (affiliate_company_id, vehicle_type, reason)
-- VALUES 
--   ('some-uuid', 'bus', 'We don''t carry buses in our fleet'),
--   ('some-uuid', 'luxury sedan', 'We specialize in SUVs only');
