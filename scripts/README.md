# WWMS Database Migration Scripts

This directory contains scripts for migrating your WWMS database to the cloud and managing environment configurations.

## Quick Start

### 1. Migrate to Cloud Database

```bash
# 1. Create .env.cloud with your Supabase credentials
cp .env.cloud.template .env.cloud
# Edit .env.cloud with your actual values

# 2. Run migration
npm run migrate:cloud

# 3. Test connection
npm run test:cloud
```

### 2. Switch Between Environments

```bash
# Switch to local development
npm run env:local

# Switch to cloud database
npm run env:cloud

# Check current environment
npm run env:status

# Test current connection
npm run env:test
```

## Available Scripts

### Migration Scripts
- `npm run migrate:cloud` - Migrate local database to cloud
- `npm run test:cloud` - Test cloud database connection
- `npm run test:db-connection` - Alias for test:cloud

### Environment Management
- `npm run env:local` - Switch to local Supabase
- `npm run env:cloud` - Switch to cloud database
- `npm run env:status` - Show current environment
- `npm run env:test` - Test current database connection

## Files Overview

### Migration Tools
- `migrate-to-cloud.sh` - Main migration script
- `test-cloud-connection.js` - Database connection tester
- `switch-environment.sh` - Environment switcher

### Configuration Templates
- `.env.cloud.template` - Template for cloud configuration
- `.env.cloud` - Your cloud credentials (create from template)
- `.env.cloud.production` - Generated production config

## Migration Process

1. **Setup**: Create Supabase cloud project and get credentials
2. **Configure**: Copy template and fill in your values
3. **Migrate**: Run migration script to transfer data
4. **Test**: Verify connection and data integrity
5. **Deploy**: Use cloud configuration for production

## Environment Switching

The environment switcher allows you to easily switch between:

- **Local**: Uses local Supabase instance (127.0.0.1:54321)
- **Cloud**: Uses Supabase cloud instance (your-project.supabase.co)

This is useful for:
- Testing cloud database locally
- Switching between development and production
- Remote agent development and testing

## Security Notes

- Never commit `.env.cloud` or `.env.cloud.production` files
- Use different API keys for development and production
- Rotate service role keys regularly in production
- Enable Row Level Security (RLS) policies

## Troubleshooting

### Migration Issues
```bash
# Check local database status
npx supabase status

# Verify cloud credentials
npm run test:cloud

# Check migration logs
ls -la supabase/cloud-migration/
```

### Connection Problems
```bash
# Test individual components
npm run env:status
npm run env:test

# Check network connectivity
ping db.your-project-ref.supabase.co
```

### Environment Issues
```bash
# Reset to local environment
npm run env:local

# Check current configuration
npm run env:status

# Test connection
npm run env:test
```

## Remote Agent Setup

Once migrated, remote agents can connect using:

```javascript
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://your-project.supabase.co',
  'your-anon-key'
);
```

## Performance Benefits

### Cloud vs Local Tunneling

| Feature | Cloud Database | Local + ngrok |
|---------|---------------|---------------|
| Latency | ✅ Low | ❌ High |
| Reliability | ✅ High | ❌ Medium |
| Security | ✅ Enterprise | ⚠️ Basic |
| Scalability | ✅ Auto-scale | ❌ Limited |
| Maintenance | ✅ Managed | ❌ Manual |

## Support

- See `docs/CLOUD_DATABASE_MIGRATION.md` for detailed guide
- Check Supabase documentation for cloud-specific features
- Create issues for script bugs or improvements
