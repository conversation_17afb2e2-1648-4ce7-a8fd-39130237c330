-- Seed a quote for City Tours LLC for testing organization filtering
-- This will help us test the super-admin quotes API with organization filtering

-- Insert a test quote for the City Tours LLC user
INSERT INTO public.quotes (
    id,
    reference_number,
    customer_id,
    service_type,
    vehicle_type,
    pickup_location,
    dropoff_location,
    date,
    time,
    duration,
    distance,
    passenger_count,
    luggage_count,
    special_requests,
    status,
    priority,
    total_amount,
    city,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid(),
    'CT-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-001',
    'f5d4b9f4-27d4-49f7-bbc5-dd17bb154455', -- User associated with City Tours LLC
    'point',
    'luxury-sedan',
    'Miami International Airport (MIA)',
    'City Tours LLC Office - 123 Ocean Drive, Miami Beach, FL',
    CURRENT_DATE + INTERVAL '1 day',
    '14:30',
    '30 minutes',
    '15 miles',
    3,
    2,
    ARRAY['VIP client pickup for city tour package'],
    'pending',
    'high',
    250.00,
    'Miami',
    NOW(),
    NOW()
),
(
    gen_random_uuid(),
    'CT-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-002',
    'f5d4b9f4-27d4-49f7-bbc5-dd17bb154455', -- User associated with City Tours LLC
    'hourly',
    'luxury-suv',
    'South Beach Hotel',
    'Art Deco District Tour',
    CURRENT_DATE + INTERVAL '2 days',
    '10:00',
    '4 hours',
    '25 miles',
    6,
    4,
    ARRAY['4-hour city tour with multiple stops'],
    'pending',
    'medium',
    480.00,
    'Miami',
    NOW(),
    NOW()
);

-- Verify the quotes were created
SELECT 
    q.id,
    q.reference_number,
    q.customer_id,
    q.pickup_location,
    q.dropoff_location,
    q.total_amount,
    q.status,
    uo.organization_id,
    o.name as organization_name
FROM public.quotes q
LEFT JOIN public.user_organizations uo ON q.customer_id = uo.user_id
LEFT JOIN public.organizations o ON uo.organization_id = o.id
WHERE q.customer_id = 'f5d4b9f4-27d4-49f7-bbc5-dd17bb154455'
ORDER BY q.created_at DESC;
