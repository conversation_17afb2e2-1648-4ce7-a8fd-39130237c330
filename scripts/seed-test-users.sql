-- Seed test users for granular permissions testing
-- This creates real users in auth.users and profiles tables for testing the permissions system

-- First, let's check if we have any existing users
-- SELECT id, email, full_name FROM profiles WHERE email LIKE '%test%' OR email LIKE '%demo%';

-- Insert test users into auth.users table (simulating Supabase auth)
-- Note: In a real environment, these would be created through Supabase Auth
-- For testing purposes, we'll create them directly

-- 1. City Tours LLC Admin (Fully Managed Client)
INSERT INTO auth.users (
    id,
    instance_id,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    raw_app_meta_data,
    raw_user_meta_data,
    is_super_admin,
    role,
    aud
) VALUES (
    '11111111-2222-3333-4444-555555555555',
    '00000000-0000-0000-0000-000000000000',
    '<EMAIL>',
    '$2a$10$dummy.encrypted.password.hash.for.testing.purposes.only',
    NOW(),
    NOW(),
    NOW(),
    '{"provider": "email", "providers": ["email"]}',
    '{"full_name": "<PERSON>", "company": "City Tours LLC"}',
    false,
    'authenticated',
    'authenticated'
) ON CONFLICT (id) DO NOTHING;

-- 2. LIMO123 TNC Owner (TNC Full Access)
INSERT INTO auth.users (
    id,
    instance_id,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    raw_app_meta_data,
    raw_user_meta_data,
    is_super_admin,
    role,
    aud
) VALUES (
    '22222222-3333-4444-5555-666666666666',
    '00000000-0000-0000-0000-000000000000',
    '<EMAIL>',
    '$2a$10$dummy.encrypted.password.hash.for.testing.purposes.only',
    NOW(),
    NOW(),
    NOW(),
    '{"provider": "email", "providers": ["email"]}',
    '{"full_name": "Michael Rodriguez", "company": "LIMO123"}',
    false,
    'authenticated',
    'authenticated'
) ON CONFLICT (id) DO NOTHING;

-- 3. Medical Transport Specialist (Healthcare)
INSERT INTO auth.users (
    id,
    instance_id,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    raw_app_meta_data,
    raw_user_meta_data,
    is_super_admin,
    role,
    aud
) VALUES (
    '33333333-4444-5555-6666-777777777777',
    '00000000-0000-0000-0000-000000000000',
    '<EMAIL>',
    '$2a$10$dummy.encrypted.password.hash.for.testing.purposes.only',
    NOW(),
    NOW(),
    NOW(),
    '{"provider": "email", "providers": ["email"]}',
    '{"full_name": "Dr. Emily Chen", "company": "Miami General Hospital"}',
    false,
    'authenticated',
    'authenticated'
) ON CONFLICT (id) DO NOTHING;

-- 4. Hotel Concierge (White Label Client)
INSERT INTO auth.users (
    id,
    instance_id,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    raw_app_meta_data,
    raw_user_meta_data,
    is_super_admin,
    role,
    aud
) VALUES (
    '44444444-5555-6666-7777-888888888888',
    '00000000-0000-0000-0000-000000000000',
    '<EMAIL>',
    '$2a$10$dummy.encrypted.password.hash.for.testing.purposes.only',
    NOW(),
    NOW(),
    NOW(),
    '{"provider": "email", "providers": ["email"]}',
    '{"full_name": "James Wilson", "company": "Ocean View Hotel"}',
    false,
    'authenticated',
    'authenticated'
) ON CONFLICT (id) DO NOTHING;

-- 5. WWLIMO Regional Partner (Limited TNC)
INSERT INTO auth.users (
    id,
    instance_id,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    raw_app_meta_data,
    raw_user_meta_data,
    is_super_admin,
    role,
    aud
) VALUES (
    '55555555-6666-7777-8888-999999999999',
    '00000000-0000-0000-0000-000000000000',
    '<EMAIL>',
    '$2a$10$dummy.encrypted.password.hash.for.testing.purposes.only',
    NOW(),
    NOW(),
    NOW(),
    '{"provider": "email", "providers": ["email"]}',
    '{"full_name": "Lisa Thompson", "company": "WWLIMO"}',
    false,
    'authenticated',
    'authenticated'
) ON CONFLICT (id) DO NOTHING;

-- Now create corresponding profiles
INSERT INTO public.profiles (
    id,
    email,
    full_name,
    roles,
    created_at,
    updated_at
) VALUES 
(
    '11111111-2222-3333-4444-555555555555',
    '<EMAIL>',
    'Sarah Johnson',
    ARRAY['CLIENT_ADMIN'],
    NOW(),
    NOW()
),
(
    '22222222-3333-4444-5555-666666666666',
    '<EMAIL>',
    'Michael Rodriguez',
    ARRAY['TNC_OWNER'],
    NOW(),
    NOW()
),
(
    '33333333-4444-5555-6666-777777777777',
    '<EMAIL>',
    'Dr. Emily Chen',
    ARRAY['MEDICAL_ADMIN'],
    NOW(),
    NOW()
),
(
    '44444444-5555-6666-7777-888888888888',
    '<EMAIL>',
    'James Wilson',
    ARRAY['WHITE_LABEL_USER'],
    NOW(),
    NOW()
),
(
    '55555555-6666-7777-8888-999999999999',
    '<EMAIL>',
    'Lisa Thompson',
    ARRAY['TNC_MANAGER'],
    NOW(),
    NOW()
)
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = EXCLUDED.full_name,
    roles = EXCLUDED.roles,
    updated_at = NOW();

-- Create user-organization relationships
INSERT INTO public.user_organizations (
    user_id,
    organization_id,
    role
) VALUES 
-- Sarah Johnson (City Tours LLC Admin) -> City Tours LLC
(
    '11111111-2222-3333-4444-555555555555',
    'a7e12f2c-063c-4abc-b783-e172272e755c', -- City Tours LLC ID
    'admin'
),
-- Dr. Emily Chen (Medical) -> Create a hospital organization
(
    '33333333-4444-5555-6666-777777777777',
    'a7e12f2c-063c-4abc-b783-e172272e755c', -- For now, use City Tours LLC, we can create hospital org later
    'admin'
),
-- James Wilson (Hotel) -> Create a hotel organization  
(
    '44444444-5555-6666-7777-888888888888',
    'a7e12f2c-063c-4abc-b783-e172272e755c', -- For now, use City Tours LLC, we can create hotel org later
    'user'
)
ON CONFLICT (user_id, organization_id) DO NOTHING;

-- Create some sample permission overrides to test the system
INSERT INTO public.user_permission_overrides (
    user_id,
    tenant_id,
    organization_id,
    feature_permissions,
    ui_customizations,
    access_controls,
    template_id,
    notes,
    granted_by
) VALUES 
-- Sarah Johnson (City Tours LLC) - Fully Managed Client
(
    '11111111-2222-3333-4444-555555555555',
    NULL,
    'a7e12f2c-063c-4abc-b783-e172272e755c',
    '{
        "quotes.view": true,
        "quotes.create": false,
        "trips.view": true,
        "affiliates.view": false,
        "analytics.basic": true,
        "financial.view_costs": true,
        "financial.view_rates": false
    }',
    '{
        "sidebar.affiliates": "hidden",
        "section.affiliate_rates": "hidden",
        "page.affiliate_management": "hidden"
    }',
    '{
        "white_label": false,
        "data_scope": "organization"
    }',
    (SELECT id FROM permission_templates WHERE name = 'Fully Managed Client' LIMIT 1),
    'Applied Fully Managed template for City Tours LLC admin',
    'f5d4b9f4-27d4-49f7-bbc5-dd17bb154455' -- Super admin user
),
-- Michael Rodriguez (LIMO123) - TNC Full Access
(
    '22222222-3333-4444-5555-666666666666',
    (SELECT id FROM tenants WHERE name = 'LIMO123' LIMIT 1),
    NULL,
    '{
        "quotes.view": true,
        "quotes.create": true,
        "quotes.edit": true,
        "trips.view": true,
        "trips.manage": true,
        "affiliates.view": true,
        "affiliates.manage": true,
        "analytics.advanced": true,
        "financial.view_rates": true,
        "customers.register": true
    }',
    '{
        "branding.custom": "visible",
        "page.customer_registration": "visible"
    }',
    '{
        "white_label": true,
        "data_scope": "tenant"
    }',
    (SELECT id FROM permission_templates WHERE name = 'TNC Full Access' LIMIT 1),
    'Applied TNC Full Access template for LIMO123 owner',
    'f5d4b9f4-27d4-49f7-bbc5-dd17bb154455' -- Super admin user
)
ON CONFLICT (user_id, tenant_id, organization_id) DO UPDATE SET
    feature_permissions = EXCLUDED.feature_permissions,
    ui_customizations = EXCLUDED.ui_customizations,
    access_controls = EXCLUDED.access_controls,
    template_id = EXCLUDED.template_id,
    notes = EXCLUDED.notes,
    updated_at = NOW();

-- Verify the data
SELECT 
    p.id,
    p.email,
    p.full_name,
    p.roles,
    uo.role as org_role,
    o.name as organization_name,
    CASE 
        WHEN upo.id IS NOT NULL THEN 'Has Custom Permissions'
        ELSE 'Default Permissions'
    END as permission_status
FROM profiles p
LEFT JOIN user_organizations uo ON p.id = uo.user_id
LEFT JOIN organizations o ON uo.organization_id = o.id
LEFT JOIN user_permission_overrides upo ON p.id = upo.user_id
WHERE p.email IN (
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
)
ORDER BY p.email;
