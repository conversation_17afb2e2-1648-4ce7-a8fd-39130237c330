#!/usr/bin/env node

/**
 * Test the affiliate matching API to verify it finds Miami affiliates
 */

require('dotenv').config();

async function testAffiliateMatching() {
  console.log('🧪 Testing Affiliate Matching API...\n');

  const testCases = [
    {
      name: 'Miami SUV Request',
      params: {
        city: 'Miami',
        vehicleType: 'suv',
        serviceType: 'point-to-point',
        date: '2025-06-28T01:45:00.000Z',
        passengers: 1
      }
    },
    {
      name: 'Miami Luxury Sedan Request',
      params: {
        city: 'Miami',
        vehicleType: 'luxury sedan',
        serviceType: 'point-to-point',
        date: '2025-06-28T01:45:00.000Z',
        passengers: 1
      }
    },
    {
      name: 'Miami Any Vehicle Request',
      params: {
        city: 'Miami',
        serviceType: 'point-to-point',
        date: '2025-06-28T01:45:00.000Z',
        passengers: 1
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n🎯 Testing: ${testCase.name}`);
    console.log('Parameters:', testCase.params);

    try {
      // Build query string
      const queryParams = new URLSearchParams();
      Object.entries(testCase.params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });

      const url = `http://localhost:3003/api/event-manager/quotes/match-affiliates?${queryParams.toString()}`;
      console.log('Request URL:', url);

      const response = await fetch(url);
      const data = await response.json();

      if (response.ok) {
        console.log(`✅ Success! Found ${data.affiliates?.length || 0} affiliates:`);
        
        if (data.affiliates && data.affiliates.length > 0) {
          data.affiliates.forEach((affiliate, index) => {
            console.log(`   ${index + 1}. ${affiliate.company_name}`);
            console.log(`      - Vehicle: ${affiliate.vehicle_type} (${affiliate.vehicle_model})`);
            console.log(`      - Price: $${affiliate.total_price}`);
            console.log(`      - Tier: ${affiliate.tier}`);
            console.log(`      - Response Time: ${affiliate.avg_response_time}`);
          });
        } else {
          console.log('   No affiliates found');
          if (data.message) {
            console.log(`   Message: ${data.message}`);
          }
        }
      } else {
        console.log(`❌ Error: ${response.status} - ${data.error || 'Unknown error'}`);
      }

    } catch (error) {
      console.log(`❌ Request failed: ${error.message}`);
    }

    console.log('─'.repeat(60));
  }

  console.log('\n🎉 Affiliate matching tests completed!');
}

// Check if the server is running
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3003/api/health');
    if (response.ok) {
      console.log('✅ Server is running on localhost:3003');
      return true;
    }
  } catch (error) {
    console.log('❌ Server is not running on localhost:3003');
    console.log('   Please start the development server with: npm run dev');
    return false;
  }
}

// Run the test
async function main() {
  const serverRunning = await checkServer();
  if (serverRunning) {
    await testAffiliateMatching();
  }
}

main();
