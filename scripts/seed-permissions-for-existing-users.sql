-- Create permission overrides for existing users to test the permissions system
-- This uses the existing users in the auth.users table

-- First, let's see what users we have
SELECT 
    au.id,
    au.email,
    p.full_name,
    p.role,
    p.roles,
    p.company_name
FROM auth.users au
LEFT JOIN profiles p ON au.id = p.id
ORDER BY au.email;

-- Update existing profiles with better test data
UPDATE profiles SET
    full_name = CASE 
        WHEN email = '<EMAIL>' THEN 'Super Admin User'
        WHEN email = '<EMAIL>' THEN '<PERSON> (City Tours Admin)'
        WHEN email = '<EMAIL>' THEN '<PERSON> (LIMO123 Owner)'
        WHEN email = '<EMAIL>' THEN 'Dr. <PERSON> (Medical Transport)'
        WHEN email = '<EMAIL>' THEN '<PERSON> (Hotel Concierge)'
        ELSE full_name
    END,
    company_name = CASE 
        WHEN email = '<EMAIL>' THEN 'TransFlow'
        WHEN email = '<EMAIL>' THEN 'City Tours LLC'
        WHEN email = '<EMAIL>' THEN 'LIMO123'
        WHEN email = '<EMAIL>' THEN 'Miami General Hospital'
        WHEN email = '<EMAIL>' THEN 'Ocean View Hotel'
        ELSE company_name
    END,
    updated_at = NOW()
WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>');

-- Ensure user-organization relationships exist
INSERT INTO public.user_organizations (
    user_id,
    organization_id,
    role
) VALUES 
-- Sarah Johnson (<EMAIL>) -> City Tours LLC
(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
    'a7e12f2c-063c-4abc-b783-e172272e755c', -- City Tours LLC ID
    'admin'
),
-- Michael Rodriguez (<EMAIL>) -> City Tours LLC (for testing)
(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
    'a7e12f2c-063c-4abc-b783-e172272e755c',
    'user'
),
-- Dr. Emily Chen (<EMAIL>) -> City Tours LLC (for testing)
(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
    'a7e12f2c-063c-4abc-b783-e172272e755c',
    'user'
),
-- James Wilson (<EMAIL>) -> City Tours LLC (for testing)
(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
    'a7e12f2c-063c-4abc-b783-e172272e755c',
    'user'
)
ON CONFLICT (user_id, organization_id) DO NOTHING;

-- Create sample permission overrides for testing
INSERT INTO public.user_permission_overrides (
    user_id,
    tenant_id,
    organization_id,
    feature_permissions,
    ui_customizations,
    access_controls,
    template_id,
    notes,
    granted_by
) VALUES 
-- Sarah Johnson (<EMAIL>) - Fully Managed Client
(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
    NULL,
    'a7e12f2c-063c-4abc-b783-e172272e755c',
    '{
        "quotes.view": true,
        "quotes.create": false,
        "trips.view": true,
        "affiliates.view": false,
        "analytics.basic": true,
        "financial.view_costs": true,
        "financial.view_rates": false,
        "white_label": false
    }',
    '{
        "sidebar.affiliates": "hidden",
        "section.affiliate_rates": "hidden",
        "page.affiliate_management": "hidden"
    }',
    '{
        "white_label": false,
        "data_scope": "organization"
    }',
    (SELECT id FROM permission_templates WHERE name = 'Fully Managed Client' LIMIT 1),
    'Applied Fully Managed template for City Tours LLC admin - testing view-only permissions',
    (SELECT id FROM auth.users WHERE email = '<EMAIL>') -- Super admin user
),
-- Michael Rodriguez (<EMAIL>) - TNC Full Access
(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
    (SELECT id FROM tenants WHERE name = 'LIMO123' LIMIT 1),
    NULL,
    '{
        "quotes.view": true,
        "quotes.create": true,
        "quotes.edit": true,
        "trips.view": true,
        "trips.manage": true,
        "affiliates.view": true,
        "affiliates.manage": true,
        "analytics.advanced": true,
        "financial.view_rates": true,
        "customers.register": true,
        "white_label": true
    }',
    '{
        "branding.custom": "visible",
        "page.customer_registration": "visible"
    }',
    '{
        "white_label": true,
        "data_scope": "tenant"
    }',
    (SELECT id FROM permission_templates WHERE name = 'TNC Full Access' LIMIT 1),
    'Applied TNC Full Access template for LIMO123 owner - testing white label and full permissions',
    (SELECT id FROM auth.users WHERE email = '<EMAIL>') -- Super admin user
),
-- Dr. Emily Chen (<EMAIL>) - Medical Transport Specialist
(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
    NULL,
    'a7e12f2c-063c-4abc-b783-e172272e755c',
    '{
        "quotes.view": true,
        "quotes.create": true,
        "trips.view": true,
        "trips.manage": true,
        "affiliates.view": true,
        "affiliates.medical_only": true,
        "analytics.medical": true,
        "compliance.hipaa": true,
        "white_label": true
    }',
    '{
        "sidebar.commercial_features": "hidden",
        "section.hipaa_compliance": "visible"
    }',
    '{
        "white_label": true,
        "data_scope": "organization"
    }',
    (SELECT id FROM permission_templates WHERE name = 'Medical Transport Specialist' LIMIT 1),
    'Applied Medical Transport template for hospital user - testing HIPAA compliance and medical features',
    (SELECT id FROM auth.users WHERE email = '<EMAIL>') -- Super admin user
),
-- James Wilson (<EMAIL>) - Self-Service Client
(
    (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
    NULL,
    'a7e12f2c-063c-4abc-b783-e172272e755c',
    '{
        "quotes.view": true,
        "quotes.create": true,
        "quotes.edit": true,
        "trips.view": true,
        "trips.manage": true,
        "affiliates.view": true,
        "affiliates.select": true,
        "affiliates.manage": false,
        "analytics.basic": true,
        "analytics.advanced": false,
        "financial.view_costs": true,
        "financial.view_rates": false,
        "white_label": false
    }',
    '{
        "sidebar.affiliate_rates": "hidden",
        "page.affiliate_management": "restricted",
        "section.rate_negotiation": "hidden"
    }',
    '{
        "white_label": false,
        "data_scope": "organization"
    }',
    (SELECT id FROM permission_templates WHERE name = 'Self-Service Client' LIMIT 1),
    'Applied Self-Service template for hotel concierge - testing limited self-service permissions',
    (SELECT id FROM auth.users WHERE email = '<EMAIL>') -- Super admin user
)
ON CONFLICT (user_id, tenant_id, organization_id) DO UPDATE SET
    feature_permissions = EXCLUDED.feature_permissions,
    ui_customizations = EXCLUDED.ui_customizations,
    access_controls = EXCLUDED.access_controls,
    template_id = EXCLUDED.template_id,
    notes = EXCLUDED.notes,
    updated_at = NOW();

-- Verify the final result
SELECT 
    au.email,
    p.full_name,
    p.company_name,
    p.role,
    uo.role as org_role,
    o.name as organization_name,
    CASE 
        WHEN upo.id IS NOT NULL THEN 'Has Custom Permissions'
        ELSE 'Default Permissions'
    END as permission_status,
    CASE 
        WHEN upo.access_controls->>'white_label' = 'true' THEN 'White Label Enabled'
        ELSE 'Standard Branding'
    END as branding_status,
    pt.name as template_name
FROM auth.users au
LEFT JOIN profiles p ON au.id = p.id
LEFT JOIN user_organizations uo ON p.id = uo.user_id
LEFT JOIN organizations o ON uo.organization_id = o.id
LEFT JOIN user_permission_overrides upo ON p.id = upo.user_id
LEFT JOIN permission_templates pt ON upo.template_id = pt.id
WHERE au.email IN (
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>',
    '<EMAIL>'
)
ORDER BY au.email;
