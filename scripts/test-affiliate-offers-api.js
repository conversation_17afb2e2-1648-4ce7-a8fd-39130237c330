#!/usr/bin/env node

/**
 * Test the affiliate offers API to verify it shows offers correctly
 */

require('dotenv').config();

async function testAffiliateOffersAPI() {
  console.log('🧪 Testing Affiliate Offers API...\n');

  try {
    // Test the API endpoint directly
    const url = 'http://localhost:3003/api/affiliate/offers';
    console.log('Testing URL:', url);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Note: In a real test, we'd need proper authentication cookies
      },
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (response.ok) {
      const data = await response.json();
      console.log('\n✅ API Response Success:');
      console.log('Offers count:', data.offers?.length || 0);
      
      if (data.offers && data.offers.length > 0) {
        console.log('\n📋 Offers found:');
        data.offers.forEach((offer, index) => {
          console.log(`\n${index + 1}. Offer ID: ${offer.id}`);
          console.log(`   Quote ID: ${offer.quote_id}`);
          console.log(`   Company ID: ${offer.company_id}`);
          console.log(`   Status: ${offer.status}`);
          console.log(`   Rate: $${offer.rate_amount || 'N/A'}`);
          console.log(`   Created: ${offer.created_at}`);
          
          if (offer.quote) {
            console.log(`   Quote Details:`);
            console.log(`     - Service: ${offer.quote.service_type}`);
            console.log(`     - Vehicle: ${offer.quote.vehicle_type}`);
            console.log(`     - City: ${offer.quote.city}`);
            console.log(`     - Pickup: ${offer.quote.pickup_location}`);
            console.log(`     - Date: ${offer.quote.date}`);
          }
        });
      } else {
        console.log('\n📭 No offers found');
        if (data.message) {
          console.log('Message:', data.message);
        }
      }

      if (data.hasMore !== undefined) {
        console.log(`\nHas more: ${data.hasMore}`);
      }
      if (data.count !== undefined) {
        console.log(`Total count: ${data.count}`);
      }

    } else {
      const errorData = await response.text();
      console.log('\n❌ API Response Error:');
      console.log('Status:', response.status);
      console.log('Error:', errorData);
    }

  } catch (error) {
    console.log('\n❌ Request failed:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Tip: Make sure the development server is running:');
      console.log('   npm run dev');
    }
  }

  console.log('\n🎉 Affiliate offers API test completed!');
}

// Check if the server is running first
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3003/api/health');
    if (response.ok) {
      console.log('✅ Server is running on localhost:3003\n');
      return true;
    }
  } catch (error) {
    console.log('❌ Server is not running on localhost:3003');
    console.log('   Please start the development server with: npm run dev\n');
    return false;
  }
}

// Run the test
async function main() {
  const serverRunning = await checkServer();
  if (serverRunning) {
    await testAffiliateOffersAPI();
  }
}

main();
