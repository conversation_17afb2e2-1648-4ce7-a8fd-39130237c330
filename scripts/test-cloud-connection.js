#!/usr/bin/env node

/**
 * Cloud Database Connection Test
 * Tests connectivity to Supabase cloud database
 */

const { createClient } = require('@supabase/supabase-js');
const { Pool } = require('pg');
require('dotenv').config({ path: '.env.cloud' });

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function testSupabaseConnection() {
  log('blue', '🔗 Testing Supabase API Connection...');
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.CLOUD_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.CLOUD_SUPABASE_ANON_KEY;
  
  if (!supabaseUrl || !supabaseKey) {
    log('red', '❌ Missing Supabase credentials in environment');
    return false;
  }
  
  try {
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Test basic connection
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (error && error.code !== 'PGRST116') { // PGRST116 is "table not found" which is ok
      throw error;
    }
    
    log('green', '✅ Supabase API connection successful');
    log('cyan', `   URL: ${supabaseUrl}`);
    return true;
  } catch (error) {
    log('red', '❌ Supabase API connection failed');
    log('red', `   Error: ${error.message}`);
    return false;
  }
}

async function testDirectDatabaseConnection() {
  log('blue', '🔗 Testing Direct Database Connection...');
  
  const dbUrl = process.env.DATABASE_URL;
  
  if (!dbUrl) {
    log('red', '❌ Missing DATABASE_URL in environment');
    return false;
  }
  
  const pool = new Pool({
    connectionString: dbUrl,
    ssl: {
      rejectUnauthorized: false
    }
  });
  
  try {
    const client = await pool.connect();
    
    // Test basic query
    const result = await client.query('SELECT version()');
    
    log('green', '✅ Direct database connection successful');
    log('cyan', `   PostgreSQL Version: ${result.rows[0].version.split(' ')[1]}`);
    
    // Test table count
    const tableResult = await client.query(`
      SELECT COUNT(*) as table_count 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);
    
    log('cyan', `   Public Tables: ${tableResult.rows[0].table_count}`);
    
    client.release();
    await pool.end();
    return true;
  } catch (error) {
    log('red', '❌ Direct database connection failed');
    log('red', `   Error: ${error.message}`);
    await pool.end();
    return false;
  }
}

async function testDatabaseSchema() {
  log('blue', '🔍 Testing Database Schema...');
  
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.CLOUD_SUPABASE_URL;
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.CLOUD_SUPABASE_SERVICE_KEY;
  
  if (!supabaseUrl || !serviceKey) {
    log('yellow', '⚠️  Missing service role key, skipping schema test');
    return true;
  }
  
  try {
    const supabase = createClient(supabaseUrl, serviceKey);
    
    // Test key tables
    const tables = ['profiles', 'affiliate_companies', 'quotes', 'vehicles'];
    const results = {};
    
    for (const table of tables) {
      try {
        const { count, error } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true });
        
        if (error) {
          results[table] = `Error: ${error.message}`;
        } else {
          results[table] = `${count} records`;
        }
      } catch (err) {
        results[table] = `Error: ${err.message}`;
      }
    }
    
    log('green', '✅ Schema test completed');
    for (const [table, result] of Object.entries(results)) {
      const color = result.startsWith('Error') ? 'red' : 'cyan';
      log(color, `   ${table}: ${result}`);
    }
    
    return true;
  } catch (error) {
    log('red', '❌ Schema test failed');
    log('red', `   Error: ${error.message}`);
    return false;
  }
}

async function main() {
  log('magenta', '🧪 WWMS Cloud Database Connection Test');
  log('magenta', '=====================================');
  
  // Check environment file
  const envFile = process.env.NODE_ENV === 'production' ? '.env.cloud.production' : '.env.cloud';
  log('blue', `📁 Using environment file: ${envFile}`);
  
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL && !process.env.CLOUD_SUPABASE_URL) {
    log('red', '❌ No cloud configuration found');
    log('yellow', '💡 Please create .env.cloud file with your Supabase credentials');
    log('yellow', '   Use .env.cloud.template as a reference');
    process.exit(1);
  }
  
  const tests = [
    testSupabaseConnection,
    testDirectDatabaseConnection,
    testDatabaseSchema
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      log('red', `❌ Test failed with exception: ${error.message}`);
      failed++;
    }
    console.log(''); // Add spacing
  }
  
  log('magenta', '📊 Test Summary');
  log('magenta', '===============');
  log('green', `✅ Passed: ${passed}`);
  log('red', `❌ Failed: ${failed}`);
  
  if (failed === 0) {
    log('green', '🎉 All tests passed! Your cloud database is ready.');
  } else {
    log('yellow', '⚠️  Some tests failed. Please check your configuration.');
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  log('red', '❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

if (require.main === module) {
  main().catch(error => {
    log('red', `❌ Fatal error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { testSupabaseConnection, testDirectDatabaseConnection, testDatabaseSchema };
