-- Simple seed script to create test users for permissions testing
-- This directly inserts into profiles table with valid roles

-- Insert test users directly into profiles table
INSERT INTO public.profiles (
    id,
    email,
    full_name,
    role,
    roles,
    company_name,
    created_at,
    updated_at
) VALUES 
-- 1. City Tours LLC Admin (Fully Managed Client)
(
    '11111111-2222-3333-4444-555555555555',
    '<EMAIL>',
    '<PERSON>',
    'C<PERSON>IENT',
    ARRAY['CLIENT'],
    'City Tours LLC',
    NOW(),
    NOW()
),
-- 2. LIMO123 TNC Owner (TNC Full Access)
(
    '22222222-3333-4444-5555-666666666666',
    '<EMAIL>',
    '<PERSON>',
    'CLIENT',
    ARRAY['CLIENT'],
    'LIMO123',
    NOW(),
    NOW()
),
-- 3. Medical Transport Specialist (Healthcare)
(
    '33333333-4444-5555-6666-777777777777',
    '<EMAIL>',
    'Dr. <PERSON>',
    'CLIENT',
    ARRAY['CLIENT'],
    'Miami General Hospital',
    NOW(),
    NOW()
),
-- 4. Hotel Concierge (White Label Client)
(
    '44444444-5555-6666-7777-888888888888',
    '<EMAIL>',
    'James Wilson',
    'CLIENT',
    ARRAY['CLIENT'],
    'Ocean View Hotel',
    NOW(),
    NOW()
),
-- 5. WWLIMO Regional Partner (Limited TNC)
(
    '55555555-6666-7777-8888-999999999999',
    '<EMAIL>',
    'Lisa Thompson',
    'CLIENT',
    ARRAY['CLIENT'],
    'WWLIMO',
    NOW(),
    NOW()
),
-- 6. Test Affiliate Company
(
    '66666666-7777-8888-9999-aaaaaaaaaaaa',
    '<EMAIL>',
    'Carlos Martinez',
    'AFFILIATE',
    ARRAY['AFFILIATE'],
    'Elite Rides',
    NOW(),
    NOW()
),
-- 7. Test Driver
(
    '77777777-8888-9999-aaaa-bbbbbbbbbbbb',
    '<EMAIL>',
    'David Kim',
    'DRIVER',
    ARRAY['DRIVER'],
    'TransFlow',
    NOW(),
    NOW()
)
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = EXCLUDED.full_name,
    role = EXCLUDED.role,
    roles = EXCLUDED.roles,
    company_name = EXCLUDED.company_name,
    updated_at = NOW();

-- Create user-organization relationships for the client users
INSERT INTO public.user_organizations (
    user_id,
    organization_id,
    role
) VALUES 
-- Sarah Johnson (City Tours LLC Admin) -> City Tours LLC
(
    '11111111-2222-3333-4444-555555555555',
    'a7e12f2c-063c-4abc-b783-e172272e755c', -- City Tours LLC ID
    'admin'
),
-- Michael Rodriguez (LIMO123) -> City Tours LLC (for testing, we can create separate orgs later)
(
    '22222222-3333-4444-5555-666666666666',
    'a7e12f2c-063c-4abc-b783-e172272e755c',
    'user'
),
-- Dr. Emily Chen (Medical) -> City Tours LLC (for testing)
(
    '33333333-4444-5555-6666-777777777777',
    'a7e12f2c-063c-4abc-b783-e172272e755c',
    'user'
),
-- James Wilson (Hotel) -> City Tours LLC (for testing)
(
    '44444444-5555-6666-7777-888888888888',
    'a7e12f2c-063c-4abc-b783-e172272e755c',
    'user'
),
-- Lisa Thompson (WWLIMO) -> City Tours LLC (for testing)
(
    '55555555-6666-7777-8888-999999999999',
    'a7e12f2c-063c-4abc-b783-e172272e755c',
    'user'
)
ON CONFLICT (user_id, organization_id) DO NOTHING;

-- Create some sample permission overrides to test the system
INSERT INTO public.user_permission_overrides (
    user_id,
    tenant_id,
    organization_id,
    feature_permissions,
    ui_customizations,
    access_controls,
    template_id,
    notes,
    granted_by
) VALUES 
-- Sarah Johnson (City Tours LLC) - Fully Managed Client
(
    '11111111-2222-3333-4444-555555555555',
    NULL,
    'a7e12f2c-063c-4abc-b783-e172272e755c',
    '{
        "quotes.view": true,
        "quotes.create": false,
        "trips.view": true,
        "affiliates.view": false,
        "analytics.basic": true,
        "financial.view_costs": true,
        "financial.view_rates": false
    }',
    '{
        "sidebar.affiliates": "hidden",
        "section.affiliate_rates": "hidden",
        "page.affiliate_management": "hidden"
    }',
    '{
        "white_label": false,
        "data_scope": "organization"
    }',
    (SELECT id FROM permission_templates WHERE name = 'Fully Managed Client' LIMIT 1),
    'Applied Fully Managed template for City Tours LLC admin - testing permissions system',
    'f5d4b9f4-27d4-49f7-bbc5-dd17bb154455' -- Super admin user
),
-- Michael Rodriguez (LIMO123) - TNC Full Access
(
    '22222222-3333-4444-5555-666666666666',
    (SELECT id FROM tenants WHERE name = 'LIMO123' LIMIT 1),
    NULL,
    '{
        "quotes.view": true,
        "quotes.create": true,
        "quotes.edit": true,
        "trips.view": true,
        "trips.manage": true,
        "affiliates.view": true,
        "affiliates.manage": true,
        "analytics.advanced": true,
        "financial.view_rates": true,
        "customers.register": true
    }',
    '{
        "branding.custom": "visible",
        "page.customer_registration": "visible"
    }',
    '{
        "white_label": true,
        "data_scope": "tenant"
    }',
    (SELECT id FROM permission_templates WHERE name = 'TNC Full Access' LIMIT 1),
    'Applied TNC Full Access template for LIMO123 owner - testing white label permissions',
    'f5d4b9f4-27d4-49f7-bbc5-dd17bb154455' -- Super admin user
),
-- Dr. Emily Chen (Medical Transport) - Medical Specialist
(
    '33333333-4444-5555-6666-777777777777',
    NULL,
    'a7e12f2c-063c-4abc-b783-e172272e755c',
    '{
        "quotes.view": true,
        "quotes.create": true,
        "trips.view": true,
        "trips.manage": true,
        "affiliates.view": true,
        "affiliates.medical_only": true,
        "analytics.medical": true,
        "compliance.hipaa": true
    }',
    '{
        "sidebar.commercial_features": "hidden",
        "section.hipaa_compliance": "visible"
    }',
    '{
        "white_label": true,
        "data_scope": "organization"
    }',
    (SELECT id FROM permission_templates WHERE name = 'Medical Transport Specialist' LIMIT 1),
    'Applied Medical Transport template for hospital user - testing HIPAA compliance features',
    'f5d4b9f4-27d4-49f7-bbc5-dd17bb154455' -- Super admin user
)
ON CONFLICT (user_id, tenant_id, organization_id) DO UPDATE SET
    feature_permissions = EXCLUDED.feature_permissions,
    ui_customizations = EXCLUDED.ui_customizations,
    access_controls = EXCLUDED.access_controls,
    template_id = EXCLUDED.template_id,
    notes = EXCLUDED.notes,
    updated_at = NOW();

-- Verify the data
SELECT 
    p.id,
    p.email,
    p.full_name,
    p.role,
    p.roles,
    p.company_name,
    uo.role as org_role,
    o.name as organization_name,
    CASE 
        WHEN upo.id IS NOT NULL THEN 'Has Custom Permissions'
        ELSE 'Default Permissions'
    END as permission_status,
    CASE 
        WHEN upo.access_controls->>'white_label' = 'true' THEN 'White Label Enabled'
        ELSE 'Standard Branding'
    END as branding_status
FROM profiles p
LEFT JOIN user_organizations uo ON p.id = uo.user_id
LEFT JOIN organizations o ON uo.organization_id = o.id
LEFT JOIN user_permission_overrides upo ON p.id = upo.user_id
WHERE p.email IN (
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
)
ORDER BY p.email;
