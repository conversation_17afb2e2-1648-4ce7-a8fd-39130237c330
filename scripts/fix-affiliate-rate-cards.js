#!/usr/bin/env node

/**
 * Fix affiliate rate cards with missing special_rates data
 * This script updates rate cards that have null special_rates to include basic P2P pricing
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

async function fixAffiliateRateCards() {
  console.log('🔧 Fixing affiliate rate cards with missing special_rates...\n');

  // Create Supabase client with service role key
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  );

  try {
    // First, let's see what rate cards need fixing
    const { data: brokenRateCards, error: fetchError } = await supabase
      .from('rate_cards')
      .select('id, company_id, vehicle_type, base_rate, special_rates, affiliate_companies(name)')
      .is('special_rates', null);

    if (fetchError) {
      console.error('❌ Error fetching rate cards:', fetchError);
      return;
    }

    console.log(`Found ${brokenRateCards?.length || 0} rate cards with missing special_rates:`);
    brokenRateCards?.forEach(card => {
      console.log(`- ${card.affiliate_companies?.name}: ${card.vehicle_type} (base_rate: ${card.base_rate})`);
    });

    if (!brokenRateCards || brokenRateCards.length === 0) {
      console.log('✅ No rate cards need fixing!');
      return;
    }

    // Fix each rate card
    for (const card of brokenRateCards) {
      const baseRate = parseFloat(card.base_rate) || 175; // Default to $175 if no base rate
      
      const specialRates = {
        pricing_model_type: 'P2P',
        p2p_point_to_point_rate: baseRate,
        p2p_extra_hour_rate: Math.round(baseRate * 0.3), // 30% of base rate for extra hours
      };

      console.log(`\n🔧 Fixing rate card for ${card.affiliate_companies?.name} (${card.vehicle_type}):`);
      console.log(`   Setting P2P rate: $${baseRate}`);
      console.log(`   Setting extra hour rate: $${specialRates.p2p_extra_hour_rate}`);

      const { error: updateError } = await supabase
        .from('rate_cards')
        .update({ special_rates: specialRates })
        .eq('id', card.id);

      if (updateError) {
        console.error(`❌ Error updating rate card ${card.id}:`, updateError);
      } else {
        console.log(`✅ Successfully updated rate card for ${card.affiliate_companies?.name}`);
      }
    }

    console.log('\n🎉 Rate card fixing completed!');

    // Verify the fixes
    console.log('\n🔍 Verifying fixes...');
    const { data: verifyCards, error: verifyError } = await supabase
      .from('rate_cards')
      .select('id, company_id, vehicle_type, special_rates, affiliate_companies(name)')
      .in('id', brokenRateCards.map(c => c.id));

    if (verifyError) {
      console.error('❌ Error verifying fixes:', verifyError);
      return;
    }

    verifyCards?.forEach(card => {
      const specialRates = card.special_rates || {};
      console.log(`✅ ${card.affiliate_companies?.name} (${card.vehicle_type}): P2P rate = $${specialRates.p2p_point_to_point_rate}`);
    });

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the fix
fixAffiliateRateCards();
