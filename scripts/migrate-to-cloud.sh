#!/bin/bash

# Cloud Database Migration Script
# This script migrates your local Supabase database to a cloud instance

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
LOCAL_DB_URL="postgresql://postgres:postgres@127.0.0.1:54322/postgres"
BACKUP_DIR="./supabase/cloud-migration"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

echo -e "${BLUE}🚀 WWMS Cloud Database Migration Tool${NC}"
echo -e "${BLUE}======================================${NC}"

# Check if cloud credentials are provided
if [ -z "$CLOUD_SUPABASE_URL" ] || [ -z "$CLOUD_SUPABASE_SERVICE_KEY" ]; then
    echo -e "${YELLOW}⚠️  Cloud credentials not found in environment variables${NC}"
    echo -e "${YELLOW}Please set the following environment variables:${NC}"
    echo "export CLOUD_SUPABASE_URL='https://your-project.supabase.co'"
    echo "export CLOUD_SUPABASE_SERVICE_KEY='your-service-role-key'"
    echo ""
    echo -e "${BLUE}Or create a .env.cloud file with these variables${NC}"
    
    if [ -f ".env.cloud" ]; then
        echo -e "${GREEN}✅ Found .env.cloud file, loading...${NC}"
        source .env.cloud
    else
        echo -e "${RED}❌ No cloud credentials found. Exiting.${NC}"
        exit 1
    fi
fi

# Create backup directory
mkdir -p "$BACKUP_DIR"

echo -e "${BLUE}📋 Migration Steps:${NC}"
echo "1. Export local database schema and data"
echo "2. Apply migrations to cloud database"
echo "3. Import data to cloud database"
echo "4. Verify migration"
echo "5. Update environment configuration"
echo ""

read -p "Continue with migration? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Migration cancelled.${NC}"
    exit 0
fi

echo -e "${BLUE}Step 1: Exporting local database...${NC}"

# Check if local database is running
if ! pg_isready -h 127.0.0.1 -p 54322 -U postgres > /dev/null 2>&1; then
    echo -e "${RED}❌ Local database is not running. Please start it first:${NC}"
    echo "npx supabase start"
    exit 1
fi

# Export schema (structure only)
echo -e "${YELLOW}📤 Exporting schema...${NC}"
pg_dump "$LOCAL_DB_URL" \
    --schema-only \
    --no-owner \
    --no-privileges \
    --exclude-schema=information_schema \
    --exclude-schema=pg_catalog \
    --exclude-schema=pg_toast \
    > "$BACKUP_DIR/schema_${TIMESTAMP}.sql"

# Export data (data only, excluding auth tables for security)
echo -e "${YELLOW}📤 Exporting data...${NC}"
pg_dump "$LOCAL_DB_URL" \
    --data-only \
    --no-owner \
    --no-privileges \
    --exclude-table=auth.* \
    --exclude-table=storage.* \
    --exclude-table=supabase_migrations.* \
    > "$BACKUP_DIR/data_${TIMESTAMP}.sql"

# Export migrations
echo -e "${YELLOW}📤 Copying migration files...${NC}"
cp -r ./supabase/migrations "$BACKUP_DIR/migrations_${TIMESTAMP}"

echo -e "${GREEN}✅ Local database exported successfully${NC}"
echo -e "${BLUE}Files created:${NC}"
echo "- Schema: $BACKUP_DIR/schema_${TIMESTAMP}.sql"
echo "- Data: $BACKUP_DIR/data_${TIMESTAMP}.sql"
echo "- Migrations: $BACKUP_DIR/migrations_${TIMESTAMP}/"

echo -e "${BLUE}Step 2: Setting up cloud database...${NC}"

# Extract project reference from URL
PROJECT_REF=$(echo "$CLOUD_SUPABASE_URL" | sed 's/.*\/\/\([^.]*\).*/\1/')
CLOUD_DB_URL="postgresql://postgres:${CLOUD_SUPABASE_SERVICE_KEY}@db.${PROJECT_REF}.supabase.co:5432/postgres"

echo -e "${YELLOW}🔗 Connecting to cloud database...${NC}"

# Test cloud connection
if ! pg_isready -h "db.${PROJECT_REF}.supabase.co" -p 5432 -U postgres > /dev/null 2>&1; then
    echo -e "${RED}❌ Cannot connect to cloud database. Please check your credentials.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Cloud database connection successful${NC}"

echo -e "${BLUE}Step 3: Applying migrations...${NC}"

# Apply schema
echo -e "${YELLOW}📥 Applying schema to cloud database...${NC}"
psql "$CLOUD_DB_URL" -f "$BACKUP_DIR/schema_${TIMESTAMP}.sql" > /dev/null 2>&1

# Apply data
echo -e "${YELLOW}📥 Importing data to cloud database...${NC}"
psql "$CLOUD_DB_URL" -f "$BACKUP_DIR/data_${TIMESTAMP}.sql" > /dev/null 2>&1

echo -e "${GREEN}✅ Migration completed successfully!${NC}"

echo -e "${BLUE}Step 4: Verification${NC}"

# Count tables in both databases
LOCAL_TABLES=$(psql "$LOCAL_DB_URL" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | xargs)
CLOUD_TABLES=$(psql "$CLOUD_DB_URL" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | xargs)

echo -e "${BLUE}Table count comparison:${NC}"
echo "Local: $LOCAL_TABLES tables"
echo "Cloud: $CLOUD_TABLES tables"

if [ "$LOCAL_TABLES" -eq "$CLOUD_TABLES" ]; then
    echo -e "${GREEN}✅ Table count matches${NC}"
else
    echo -e "${YELLOW}⚠️  Table count mismatch - please verify manually${NC}"
fi

echo -e "${BLUE}Step 5: Environment Configuration${NC}"

# Create new environment file for cloud
cat > .env.cloud.production << EOF
# Cloud Database Configuration - Generated $(date)
NEXT_PUBLIC_SUPABASE_URL=$CLOUD_SUPABASE_URL
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=$CLOUD_SUPABASE_SERVICE_KEY

# Database URL for direct connections
DATABASE_URL=**********************************************************************************/postgres

# Production settings
NODE_ENV=production
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-production-secret-here
EOF

echo -e "${GREEN}✅ Created .env.cloud.production file${NC}"

echo -e "${BLUE}🎉 Migration Summary:${NC}"
echo -e "${GREEN}✅ Schema migrated successfully${NC}"
echo -e "${GREEN}✅ Data migrated successfully${NC}"
echo -e "${GREEN}✅ Environment configuration created${NC}"

echo -e "${YELLOW}📝 Next Steps:${NC}"
echo "1. Update .env.cloud.production with your actual anon key"
echo "2. Test the cloud database connection"
echo "3. Update your deployment configuration"
echo "4. Consider setting up database backups"

echo -e "${BLUE}🔧 To use the cloud database locally for testing:${NC}"
echo "cp .env.cloud.production .env.local"

echo -e "${GREEN}Migration completed! 🚀${NC}"
