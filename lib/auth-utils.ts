/**
 * Utility functions for authentication management
 */
import { createBrowserClient } from '@supabase/ssr';
import { hasRole, Session } from '@/app/lib/auth';
import { toUserRoles, UserRole } from '@/src/types/roles';

// Create a Supabase client instance for use in these utility functions
// Ensure environment variables are available in the client-side context
const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

/**
 * Clears all authentication-related data from localStorage and cookies
 */
export function clearAllAuthData() {
  if (typeof window === 'undefined') return;
  
  console.log('Clearing all authentication data');
  
  // Clear localStorage items
  const authKeys = [
    'sb-access-token',
    'sb-refresh-token',
    'supabase.auth.token',
    'sb-127-auth-token',
    'wwms-auth-session',
    'authStateChangeCount',
    'login_successful',
    'login_time',
    'last_refresh_time',
    'auth-cookies-synced',
    'bypass_super_admin'
  ];
  
  authKeys.forEach(key => {
    try {
      localStorage.removeItem(key);
      console.log(`Removed localStorage item: ${key}`);
    } catch (e) {
      console.warn(`Failed to remove localStorage item: ${key}`, e);
    }
  });
  
  // Clear cookies
  const cookiesToClear = [
    'sb-access-token',
    'sb-refresh-token',
    'supabase.auth.token',
    'sb-127-auth-token',
    'wwms-auth-session',
    'last_refresh_time',
    'auth-cookies-synced',
    'user-id',
    'tenant-id'
  ];
  
  cookiesToClear.forEach(name => {
    try {
      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
      console.log(`Cleared cookie: ${name}`);
    } catch (e) {
      console.warn(`Failed to clear cookie: ${name}`, e);
    }
  });
  
  console.log('Authentication data cleared');
}

/**
 * Fixes cookie issues by ensuring the correct cookie name is used
 */
export function fixCookieIssues() {
  if (typeof window === 'undefined') return;

  try {
    // Try to extract a valid JWT from localStorage (could be a JSON object or a raw JWT)
    const raw = localStorage.getItem('sb-127-auth-token');
    let jwt: string | null = null;
    if (raw) {
      try {
        // Try to parse as JSON and extract access_token
        const parsed = JSON.parse(raw);
        if (parsed && typeof parsed.access_token === 'string') {
          jwt = parsed.access_token;
        }
      } catch {
        // Not JSON, maybe it's already a JWT
        if (typeof raw === 'string' && raw.split('.').length === 3) {
          jwt = raw;
        }
      }
    }
    // Validate JWT format
    if (jwt && jwt.split('.').length === 3) {
      const expiryDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toUTCString();
      document.cookie = `sb-127-auth-token=${encodeURIComponent(jwt)}; expires=${expiryDate}; path=/;`;
      console.log('sb-127-auth-token cookie set to valid JWT');
    } else {
      // Clear the cookie if invalid
      document.cookie = 'sb-127-auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      if (raw) {
        console.warn('sb-127-auth-token in localStorage is not a valid JWT, cookie cleared. Value (first 40):', raw.slice(0, 40));
      }
    }
  } catch (e) {
    console.error('Error fixing cookie issues:', e);
  }
}

/**
 * Performs a full authentication reset and redirects to login
 */
export function resetAuthAndRedirectToLogin() {
  clearAllAuthData();
  window.location.href = '/login';
}

/**
 * Automatically fixes authentication issues by redirecting to the fix-session endpoint
 * @param redirectTo The URL to redirect to after fixing the session
 */
export function autoFixAuthIssues(redirectTo?: string) {
  if (typeof window === 'undefined') {
    console.log('autoFixAuthIssues called in server context, ignoring');
    return;
  }
  
  try {
    console.log('Auto-fixing authentication issues');
    
    // Get the current path if redirectTo is not provided
    const currentPath = redirectTo || window.location.pathname + window.location.search;
    console.log('Current path or redirectTo:', currentPath);
    
    // Get the client session ID if available
    let clientSessionId = null;
    try {
      const authToken = localStorage.getItem('sb-127-auth-token');
      console.log('Auth token exists in localStorage:', !!authToken);
      
      if (authToken) {
        const tokenData = JSON.parse(authToken);
        console.log('Token data parsed successfully');
        
        if (tokenData.user?.id) {
          clientSessionId = tokenData.user.id;
          console.log('Found client session ID:', clientSessionId);
        } else {
          console.log('No user ID in token data');
        }
      }
    } catch (e) {
      console.warn('Failed to get client session ID:', e);
    }
    
    // Build the URL for the fix-session endpoint
    let fixUrl = `/api/auth/fix-session?redirectTo=${encodeURIComponent(currentPath)}`;
    if (clientSessionId) {
      fixUrl += `&clientSessionId=${encodeURIComponent(clientSessionId)}`;
    }
    
    console.log(`Redirecting to fix-session endpoint: ${fixUrl}`);
    
    // Add a small delay to ensure logs are visible
    setTimeout(() => {
      window.location.href = fixUrl;
    }, 100);
  } catch (e) {
    console.error('Error auto-fixing auth issues:', e);
    // Fallback to reset and redirect to login
    resetAuthAndRedirectToLogin();
  }
}

// Function to ensure auth tokens are properly synchronized
export async function ensureAuthTokens() {
  if (typeof window === 'undefined') return;

  try {
    // First check if we have a session in Supabase client
    const { data: { session: rawSupabaseSession } } = await supabase.auth.getSession();
    
    // Manually construct our custom Session object from the raw Supabase session
    const session: Session | null = rawSupabaseSession ? {
      id: rawSupabaseSession.user?.id || null,
      email: rawSupabaseSession.user?.email ?? null,
      access_token: rawSupabaseSession.access_token || null,
      roles: toUserRoles(
        (rawSupabaseSession.user?.user_metadata?.roles as UserRole[] ||
        rawSupabaseSession.user?.app_metadata?.roles as UserRole[] ||
        rawSupabaseSession.user?.user_metadata?.role as UserRole ||
        rawSupabaseSession.user?.app_metadata?.role as UserRole ||
        [])
      ),
      user: {
        id: rawSupabaseSession.user?.id || '',
        email: rawSupabaseSession.user?.email ?? null,
        app_metadata: rawSupabaseSession.user?.app_metadata || {},
        user_metadata: rawSupabaseSession.user?.user_metadata || {},
      },
      expires_at: rawSupabaseSession.expires_at,
      expires_in: rawSupabaseSession.expires_in,
      refresh_token: rawSupabaseSession.refresh_token,
      token_type: rawSupabaseSession.token_type,
    } : null;

    if (session) {
      console.log('Found active session in Supabase client, syncing to cookies');
      fixCookieIssues();
      return true;
    }
    // If no session in client, check localStorage
    fixCookieIssues();
    return false;
  } catch (error) {
    console.error('Error ensuring auth tokens:', error);
    return false;
  }
}

// Function to ensure user profile has correct roles
export async function ensureUserRoles() {
  if (typeof window === 'undefined') return false;
  
  try {
    // Get the current session using the Supabase client
    const { data: { session: rawSupabaseSession } } = await supabase.auth.getSession();
    
    // Manually construct our custom Session object from the raw Supabase session
    const session: Session | null = rawSupabaseSession ? {
      id: rawSupabaseSession.user?.id || null,
      email: rawSupabaseSession.user?.email ?? null,
      access_token: rawSupabaseSession.access_token || null,
      roles: toUserRoles(
        (rawSupabaseSession.user?.user_metadata?.roles as UserRole[] ||
        rawSupabaseSession.user?.app_metadata?.roles as UserRole[] ||
        rawSupabaseSession.user?.user_metadata?.role as UserRole ||
        rawSupabaseSession.user?.app_metadata?.role as UserRole ||
        [])
      ),
      user: {
        id: rawSupabaseSession.user?.id || '',
        email: rawSupabaseSession.user?.email ?? null,
        app_metadata: rawSupabaseSession.user?.app_metadata || {},
        user_metadata: rawSupabaseSession.user?.user_metadata || {},
      },
      expires_at: rawSupabaseSession.expires_at,
      expires_in: rawSupabaseSession.expires_in,
      refresh_token: rawSupabaseSession.refresh_token,
      token_type: rawSupabaseSession.token_type,
    } : null;

    if (!session || !session.user) {
      console.log('No session found in ensureUserRoles, cannot ensure user roles');
      return false;
    }
    
    console.log(`Ensuring roles for user: ${session.user.id} (${session.user.email})`);
    
    // Get the user's profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('id, roles, email') // Select id and email to ensure we have it for new profiles
      .eq('id', session.user.id)
      .single();
    
    // Handle profile not found
    if (profileError) {
      console.log('Profile error in ensureUserRoles:', profileError);
      
      // If the profile doesn't exist, create it with roles from session if available, else default to CLIENT
      if (profileError.code === 'PGRST116') {
        console.log('Profile not found, attempting to create new profile with basic info');
        
        // Use roles from the session if available, otherwise default to CLIENT
        const rolesToAssign = session.roles && session.roles.length > 0 ? session.roles : ['CLIENT'];
        
        const { error: createError } = await supabase
          .from('profiles')
          .insert({
            id: session.user.id,
            email: session.user.email,
            roles: rolesToAssign
          });
        
        if (createError) {
          console.error('Error creating user profile in ensureUserRoles:', createError);
          return false;
        }
        
        console.log(`Created new profile for user ${session.user.id} with roles: ${rolesToAssign.join(', ')}`);
        return true;
      }
      
      // For other profile errors, return false
      return false;
    }
    
    // If profile exists, ensure its roles match the session's roles
    // and update if there's a discrepancy. This keeps the database in sync
    // with the session, but getSession is the ultimate source of truth.
    const currentProfileRoles = profile?.roles || [];
    const sessionRoles = session.roles || [];

    const areRolesEqual = JSON.stringify([...currentProfileRoles].sort()) === JSON.stringify([...sessionRoles].sort());

    if (!areRolesEqual) {
      console.log('Profile roles differ from session roles. Updating profile roles.');
      console.log('Current profile roles:', currentProfileRoles);
      console.log('Session roles:', sessionRoles);

      const { error: updateError } = await supabase
        .from('profiles')
        .update({ roles: sessionRoles })
        .eq('id', session.user.id);
      
      if (updateError) {
        console.error('Error updating user profile roles:', updateError);
        return false;
      }
      console.log('User profile roles updated to:', sessionRoles);
    } else {
      console.log('User profile roles are already in sync with session roles.');
    }
    
    return true;
  } catch (error) {
    console.error('Error ensuring user roles:', error);
    return false;
  }
}

/**
 * Prevent refresh loops by checking if we've refreshed too recently
 * @returns true if we should skip the refresh, false if we should proceed
 */
export function shouldSkipRefresh(): boolean {
  if (typeof window === 'undefined') return true;
  
  const path = window.location.pathname.toLowerCase();
  const skipPaths = [
    '/login',
    '/api/auth',
    '/bypass',
    '/debug',
    '/direct-login'
  ];
  
  // Skip refresh on certain paths
  return skipPaths.some(skipPath => path.startsWith(skipPath));
} 

/**
 * Forces a refresh of the authentication state and ensures cookies are correctly set
 * Useful when server and client auth states are out of sync
 */
export async function forceRefreshAuthState() {
  if (typeof window === 'undefined') {
    console.log('forceRefreshAuthState called in server context, ignoring');
    return;
  }

  console.log('Forcing refresh of auth state');
  try {
    const { data: { session: rawSupabaseSession } } = await supabase.auth.refreshSession(); // Re-fetch session after refresh

    // Manually construct our custom Session object from the raw Supabase session
    const session: Session | null = rawSupabaseSession ? {
      id: rawSupabaseSession.user?.id || null,
      email: rawSupabaseSession.user?.email ?? null,
      access_token: rawSupabaseSession.access_token || null,
      roles: toUserRoles(
        (rawSupabaseSession.user?.user_metadata?.roles as UserRole[] ||
        rawSupabaseSession.user?.app_metadata?.roles as UserRole[] ||
        rawSupabaseSession.user?.user_metadata?.role as UserRole ||
        rawSupabaseSession.user?.app_metadata?.role as UserRole ||
        [])
      ),
      user: {
        id: rawSupabaseSession.user?.id || '',
        email: rawSupabaseSession.user?.email ?? null,
        app_metadata: rawSupabaseSession.user?.app_metadata || {},
        user_metadata: rawSupabaseSession.user?.user_metadata || {},
      },
      expires_at: rawSupabaseSession.expires_at,
      expires_in: rawSupabaseSession.expires_in,
      refresh_token: rawSupabaseSession.refresh_token,
      token_type: rawSupabaseSession.token_type,
    } : null;

    if (!session) {
      console.warn('No session returned after refresh. User might be logged out.');
      clearAllAuthData();
      return;
    }
    console.log('Session refreshed. New session:', session);
    // Ensure cookies are re-synced after refresh
    fixCookieIssues();
  } catch (error) {
    console.error('Exception during forceRefreshAuthState:', error);
    clearAllAuthData();
  }
}

/**
 * Check if a user has any of the specified roles
 * 
 * @param session The user's session
 * @param requiredRoles Array of roles to check against
 * @returns Object containing hasRole boolean and error if any
 */
export async function checkRoles(
  session: Session | null, // Use our standardized Session type
  requiredRoles: UserRole[]
): Promise<{ hasRole: boolean; error?: Error }> {
  try {
    if (!session || !session.user) {
      return { hasRole: false, error: new Error('No active session or user in session') };
    }

    // The primary source of roles is now session.roles, directly from our Session type.
    const userRoles = session.roles || [];

    // If roles are empty in session, consider it unauthorized for role-based checks.
    if (userRoles.length === 0 && requiredRoles.length > 0) {
      console.warn('Session has no roles, but required roles were specified. Denying access.');
      return { hasRole: false, error: new Error('Session has no roles or roles could not be determined.') };
    }

    // Check if user has any of the required roles using our centralized hasRole utility
    const hasRequiredRole = hasRole(userRoles, requiredRoles);
    return { hasRole: hasRequiredRole };
  } catch (error) {
    console.error('Error checking roles:', error);
    return { 
      hasRole: false, 
      error: error instanceof Error ? error : new Error('Unknown error checking roles')
    };
  }
}

// NOTE: JWT cookie hacks are deprecated. Only base64-encoded session cookies are supported for SSR/API.