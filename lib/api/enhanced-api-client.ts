import { supabase } from '../supabase';
import { Database } from '@/lib/types/supabase';
import {
  APIConfig,
  APIResponse,
  APIErrorResponse,
  CacheEntry,
  Quote,
  QuoteResponse,
  CustomerQuoteParams,
  AdminQuoteParams,
  QuoteUpdateData
} from './types';
import { hasRole } from '@/app/lib/auth';
import { toUserRoles } from '@/src/types/roles';
import type { UserRole } from '@/src/types/roles';

// Error types for better error handling
export class APIError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number,
    public originalError?: any
  ) {
    super(message);
    this.name = 'APIError';
  }
}

// API Configuration
const API_CONFIG: APIConfig = {
  timeout: 15000,
  retries: 2,
  backoff: {
    initial: 1000,
    multiplier: 1.5,
    maxWait: 5000
  },
  endpoints: {
    customer: {
      quotes: '/api/customer/quotes',
      profile: '/api/customer/profile',
      billing: '/api/customer/billing'
    },
    admin: {
      quotes: '/api/super-admin/quotes',
      users: '/api/super-admin/users',
      analytics: '/api/super-admin/analytics'
    }
  },
  cache: {
    ttl: 5000, // 5 seconds
    prefixes: {
      customer: 'customer-',
      admin: 'admin-'
    }
  },
  superAdmin: {
    quotes: '/api/super-admin/quotes',
    users: '/api/super-admin/users',
    analytics: '/api/super-admin/analytics',
  },
};

// Request cache implementation with proper typing
const requestCache = new Map<string, CacheEntry<any>>();

// Retry logic with exponential backoff
async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  retries: number = API_CONFIG.retries,
  initialDelay: number = API_CONFIG.backoff.initial
): Promise<T> {
  let lastError: Error | null = null;
  let delay = initialDelay;

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === retries) break;
      
      // Don't retry on certain errors
      if (error instanceof APIError) {
        if ([401, 403, 400].includes(error.statusCode || 0)) {
          throw error;
        }
      }
      
      // Calculate next delay with exponential backoff
      delay = Math.min(
        delay * API_CONFIG.backoff.multiplier,
        API_CONFIG.backoff.maxWait
      );
      
      console.warn(
        `API call failed, retrying in ${delay}ms (attempt ${attempt + 1}/${retries})`,
        error
      );
      
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
}

// Enhanced fetch with proper timeout and cleanup
async function enhancedFetch<T>(
  url: string,
  options: RequestInit = {},
  timeoutMs = API_CONFIG.timeout
): Promise<T> {
  const controller = new AbortController();
  const { signal } = controller;
  
  const timeoutPromise = new Promise<never>((_, reject) => {
    const timeoutId = setTimeout(() => {
      controller.abort();
      reject(new APIError(
        `Request timeout after ${timeoutMs}ms`,
        'REQUEST_TIMEOUT',
        408
      ));
    }, timeoutMs);
    
    signal.addEventListener('abort', () => clearTimeout(timeoutId));
  });
  
  try {
    const fetchPromise = fetch(url, {
      ...options,
      signal,
      credentials: 'include',
      headers: {
        ...options.headers,
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });
    
    const response = await Promise.race([fetchPromise, timeoutPromise]);
    const data = await response.json();
    
    if (!response.ok) {
      throw new APIError(
        data.error?.message || 'API request failed',
        data.error?.code || 'UNKNOWN_ERROR',
        response.status,
        data.error
      );
    }
    
    return data as T;
  } catch (error) {
    if (error instanceof APIError) throw error;
    if (error instanceof Error && error.name === 'AbortError') {
      throw new APIError(
        'Request aborted',
        'REQUEST_ABORTED',
        499,
        error
      );
    }
    throw new APIError(
      'Network error',
      'NETWORK_ERROR',
      0,
      error
    );
  }
}

// Role validation with proper typing
async function validateRole(supabase: any, requiredRole: string): Promise<void> {
  const { data: { session }, error: sessionError } = await supabase.auth.getSession();
  if (sessionError) {
    throw new APIError(
      'Authentication error',
      'AUTH_ERROR',
      401,
      sessionError
    );
  }
  if (!session) {
    throw new APIError(
      'No active session',
      'NO_SESSION',
      401
    );
  }
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('roles')
    .eq('id', session.user.id)
    .single();
  if (profileError) {
    throw new APIError(
      'Error fetching user profile',
      'PROFILE_ERROR',
      500,
      profileError
    );
  }
  const userRoles = toUserRoles(profile?.roles || []);
  // Map legacy roles to canonical UserRole values
  const legacyToCanonical: Record<string, UserRole> = {
    'ADMIN': 'SUPER_ADMIN',
    'CUSTOMER': 'PASSENGER',
    'CLIENT': 'CLIENT',
    'PASSENGER': 'PASSENGER',
    'AFFILIATE': 'AFFILIATE',
    'AFFILIATE_DISPATCH': 'AFFILIATE_DISPATCH',
    'CLIENT_COORDINATOR': 'CLIENT_COORDINATOR',
    'DRIVER': 'DRIVER',
    'SUPER_ADMIN': 'SUPER_ADMIN',
  };
  const canonicalRole: UserRole | undefined = legacyToCanonical[requiredRole];
  if (!canonicalRole) {
    throw new APIError(
      `Unknown or unsupported role: ${requiredRole}`,
      'UNSUPPORTED_ROLE',
      403
    );
  }
  if (!hasRole(userRoles, canonicalRole)) {
    throw new APIError(
      `Unauthorized - ${canonicalRole} access required`,
      'UNAUTHORIZED_ROLE',
      403
    );
  }
}

// Customer API Client with proper typing
export class CustomerAPIClient {
  private supabase;
  
  constructor() {
    this.supabase = supabase;
  }
  
  private async validateCustomerAccess(): Promise<void> {
    await validateRole(this.supabase, 'CUSTOMER');
  }
  
  async getQuotes(params: CustomerQuoteParams = {}): Promise<QuoteResponse> {
    const cacheKey = `${API_CONFIG.cache.prefixes.customer}quotes-${JSON.stringify(params)}`;
    const cached = requestCache.get(cacheKey);
    
    if (cached && (Date.now() - cached.timestamp) < API_CONFIG.cache.ttl) {
      return cached.promise;
    }
    
    const promise = retryWithBackoff(async () => {
      await this.validateCustomerAccess();
      
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) queryParams.set(key, value.toString());
      });
      
      return await enhancedFetch<APIResponse<QuoteResponse>>(
        `${API_CONFIG.endpoints.customer.quotes}?${queryParams}`
      ).then(response => response.data);
    });
    
    requestCache.set(cacheKey, {
      promise,
      timestamp: Date.now()
    });
    
    return promise;
  }
}

// Admin API Client with proper typing
export class AdminAPIClient {
  private supabase;
  
  constructor() {
    this.supabase = supabase;
  }
  
  private async validateAdminAccess(): Promise<void> {
    await validateRole(this.supabase, 'ADMIN');
  }
  
  async getQuotes(params: AdminQuoteParams = {}): Promise<QuoteResponse> {
    const cacheKey = `${API_CONFIG.cache.prefixes.admin}quotes-${JSON.stringify(params)}`;
    const cached = requestCache.get(cacheKey);
    
    if (cached && (Date.now() - cached.timestamp) < API_CONFIG.cache.ttl) {
      return cached.promise;
    }
    
    const promise = retryWithBackoff(async () => {
      await this.validateAdminAccess();
      
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) queryParams.set(key, value.toString());
      });
      
      return await enhancedFetch<APIResponse<QuoteResponse>>(
        `${API_CONFIG.endpoints.admin.quotes}?${queryParams}`
      ).then(response => response.data);
    });
    
    requestCache.set(cacheKey, {
      promise,
      timestamp: Date.now()
    });
    
    return promise;
  }
  
  async updateQuote(id: string, data: QuoteUpdateData): Promise<Quote> {
    await this.validateAdminAccess();
    
    const response = await retryWithBackoff(() =>
      enhancedFetch<APIResponse<Quote>>(
        API_CONFIG.endpoints.admin.quotes,
        {
          method: 'PATCH',
          body: JSON.stringify({ id, ...data })
        }
      )
    );
    
    // Invalidate relevant caches
    Array.from(requestCache.entries()).forEach(([key]) => {
      if (key.startsWith(API_CONFIG.cache.prefixes.admin)) {
        requestCache.delete(key);
      }
    });
    
    return response.data;
  }
  
  async deleteQuote(id: string): Promise<void> {
    await this.validateAdminAccess();
    
    await retryWithBackoff(() =>
      enhancedFetch<APIResponse<void>>(
        `${API_CONFIG.endpoints.admin.quotes}?id=${id}`,
        {
          method: 'DELETE'
        }
      )
    );
    
    // Invalidate relevant caches
    Array.from(requestCache.entries()).forEach(([key]) => {
      if (key.startsWith(API_CONFIG.cache.prefixes.admin)) {
        requestCache.delete(key);
      }
    });
  }
}

// Export singleton instances
export const customerAPI = new CustomerAPIClient();
export const adminAPI = new AdminAPIClient(); 