import { getSupabaseClient } from "@/lib/supabase";
import { supabaseAdmin } from "@/lib/supabaseAdmin";
import { sendQuoteToAffiliates as sendQuote } from "./quote-responses";

export interface Affiliate {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  rating?: number;
  status: "active" | "inactive" | "pending";
  service_areas?: string[];
  vehicle_types?: string[];
  response_time?: string;
  on_time_percentage?: number;
  completion_rate?: number;
  base_rates?: Record<string, number>;
}

export interface AffiliateWithStats extends Affiliate {
  // Additional calculated fields for UI
  avgResponse: string;
  onTime: string;
  similarTrips: number;
  baseRate: number;
  totalRate: number;
  priceRange: {
    min: number;
    max: number;
  };
  distanceToPickup?: string;
}

export interface AffiliateCompany {
  id: string;
  name: string;
  owner_id: string;
  status: "active" | "inactive" | "pending";
  email: string;
  phone: string;
  address: string;
  city: string | null;
  state: string;
  zip: string;
  country: string;
  website?: string;
  logo_url?: string;
  business_license?: string;
  insurance_info?: any;
  rating?: string;
  completion_rate?: string;
  created_at: string;
  updated_at: string;
}

// Helper function to retry failed queries
async function retryQuery<T>(
  queryFn: () => Promise<{ data: T | null; error: any }>,
  retries = 3,
  delay = 1000
): Promise<{ data: T | null; error: any }> {
  for (let i = 0; i < retries; i++) {
    try {
      const result = await queryFn();
      if (!result.error || i === retries - 1) {
        return result;
      }
      console.log(
        `[retryQuery] Retry ${i + 1}/${retries} after error:`,
        result.error
      );
      await new Promise((resolve) => setTimeout(resolve, delay * (i + 1)));
    } catch (error) {
      if (i === retries - 1) {
        return { data: null, error };
      }
      console.log(`[retryQuery] Retry ${i + 1}/${retries} after error:`, error);
      await new Promise((resolve) => setTimeout(resolve, delay * (i + 1)));
    }
  }
  return { data: null, error: new Error("Max retries reached") };
}

/**
 * Get all affiliates
 * @returns A promise with the affiliates data or an error
 */
export async function getAffiliates(): Promise<{
  data: Affiliate[] | null;
  error: any;
}> {
  const supabase = getSupabaseClient();
  if (!supabase) {
    console.error("[getAffiliates] Supabase client is not available.");
    return { data: null, error: { message: "Supabase client not available" } };
  }
  try {
    const { data, error } = await supabase
      .from("affiliate_companies")
      .select(
        `
        id,
        name,
        email,
        phone,
        address,
        city,
        state,
        zip,
        country,
        rating,
        status,
        completion_rate
      `
      )
      .eq("status", "active");

    if (error) {
      console.error("[getAffiliates] Error fetching affiliates:", error);
      return { data: null, error };
    }

    // Get rate cards for each affiliate
    const { data: rateCards, error: rateCardsError } = await supabase
      .from("rate_cards")
      .select("*")
      .in(
        "company_id",
        data.map((a) => a.id)
      );

    if (rateCardsError) {
      console.warn(
        "[getAffiliates] Error fetching rate cards:",
        rateCardsError
      );
      // Continue without rate cards
    }

    // Transform the data to match our interface
    const affiliates: Affiliate[] = data.map((affiliate: any) => {
      // Group rate cards by vehicle type
      const affiliateRateCards =
        rateCards?.filter((rc) => rc.company_id === affiliate.id) || [];
      const baseRates: Record<string, number> = {};
      const vehicleTypes = new Set<string>();

      affiliateRateCards.forEach((rc) => {
        baseRates[rc.vehicle_type] = rc.base_rate || 0;
        vehicleTypes.add(rc.vehicle_type);
      });

      return {
        id: affiliate.id,
        name: affiliate.name,
        email: affiliate.email || undefined,
        phone: affiliate.phone,
        address: affiliate.address,
        city: affiliate.city,
        state: affiliate.state,
        zip_code: affiliate.zip,
        country: affiliate.country,
        rating: affiliate.rating,
        status: affiliate.status as "active" | "inactive" | "pending",
        service_areas: [], // TODO: Add service areas
        vehicle_types: Array.from(vehicleTypes),
        base_rates: baseRates,
        response_time: "30m", // Default value
        on_time_percentage: Math.round(affiliate.completion_rate || 95),
      };
    });

    return { data: affiliates, error: null };
  } catch (error) {
    console.error("[getAffiliates] Unexpected error:", error);
    return { data: null, error };
  }
}

/**
 * Get an affiliate by ID
 * @param id The affiliate ID
 * @returns A promise with the affiliate data or an error
 */
export async function getAffiliateById(
  id: string
): Promise<{ data: Affiliate | null; error: any }> {
  const supabase = getSupabaseClient();
  if (!supabase) {
    console.error("[getAffiliateById] Supabase client is not available.");
    return { data: null, error: { message: "Supabase client not available" } };
  }
  try {
    const { data, error } = await supabase
      .from("affiliate_companies")
      .select(
        `
        id,
        name,
        email,
        phone,
        address,
        city,
        state,
        zip,
        country,
        rating,
        status,
        completion_rate
      `
      )
      .eq("id", id)
      .single();

    if (error) {
      console.error("[getAffiliateById] Error fetching affiliate:", error);
      return { data: null, error };
    }

    // Get rate cards for the affiliate
    const { data: rateCards, error: rateCardsError } = await supabase
      .from("rate_cards")
      .select("*")
      .eq("company_id", id);

    if (rateCardsError) {
      console.warn(
        "[getAffiliateById] Error fetching rate cards:",
        rateCardsError
      );
      // Continue without rate cards
    }

    // Transform the data to match our interface
    const baseRates: Record<string, number> = {};
    const vehicleTypes = new Set<string>();

    rateCards?.forEach((rc) => {
      baseRates[rc.vehicle_type] = rc.base_rate || 0;
      vehicleTypes.add(rc.vehicle_type);
    });

    const affiliate: Affiliate = {
      id: data.id,
      name: data.name,
      email: data.email || undefined,
      phone: data.phone || undefined,
      address: data.address || undefined,
      city: data.city || undefined,
      state: data.state || undefined,
      zip_code: data.zip || undefined,
      country: data.country || undefined,
      rating: data.rating || undefined,
      status: data.status as "active" | "inactive" | "pending",
      service_areas: [], // TODO: Add service areas
      vehicle_types: Array.from(vehicleTypes),
      base_rates: baseRates,
      response_time: "30m", // Default value
      on_time_percentage: Math.round(data.completion_rate || 95),
    };

    return { data: affiliate, error: null };
  } catch (error) {
    console.error("[getAffiliateById] Unexpected error:", error);
    return { data: null, error };
  }
}

/**
 * Normalize city name by trimming whitespace and standardizing format
 */
function normalizeCity(city: string | null | undefined): string | null {
  if (!city) {
    return null;
  }

  // Just trim whitespace for exact matching (per user's instructions)
  const normalized = city.trim();

  return normalized === "" ? null : normalized;
}

/**
 * Get affiliates for a specific quote
 */
export async function getAffiliatesForQuote(
  city: string
): Promise<AffiliateWithStats[]> {
  // Use appropriate client based on environment
  const isServerSide = typeof window === "undefined";
  const supabase = isServerSide ? supabaseAdmin : getSupabaseClient();

  console.log(
    `[getAffiliatesForQuote] Using ${isServerSide ? "admin" : "browser"} client`
  );

  if (!supabase) {
    console.error("[getAffiliatesForQuote] Supabase client is not available.");
    return [];
  }
  try {
    // Handle empty city early
    if (!city || city.trim() === "") {
      console.error("[getAffiliatesForQuote] Empty city provided");
      return [];
    }

    const normalizedCity = normalizeCity(city);

    // If no city provided, return empty list
    if (!normalizedCity) {
      return [];
    }

    // Get active affiliates for this city using flexible matching
    console.log(
      `[getAffiliatesForQuote] Searching for affiliates in city: "${normalizedCity}"`
    );

    const { data, error } = await supabase
      .from("affiliate_companies")
      .select(
        `
        id,
        name,
        email,
        phone,
        address,
        city,
        state,
        zip,
        country,
        rating,
        status,
        completion_rate
      `
      )
      .eq("status", "active")
      .or(`city.eq.${normalizedCity},city.ilike.%${normalizedCity}%`);

    if (error) {
      console.error(
        "[getAffiliatesForQuote] Error fetching affiliates for city:",
        error
      );
      return [];
    }

    console.log(
      `[getAffiliatesForQuote] Found ${data?.length || 0} affiliates for city "${normalizedCity}"`
    );
    if (data && data.length > 0) {
      console.log(
        "[getAffiliatesForQuote] Affiliate details:",
        data.map((a) => ({ id: a.id, name: a.name, city: a.city }))
      );
    }

    if (!data || data.length === 0) {
      console.log(
        `[getAffiliatesForQuote] No affiliates found for city "${normalizedCity}"`
      );
      return [];
    }

    // Get rate cards for each affiliate
    const { data: rateCards, error: rateCardsError } = await supabase
      .from("rate_cards")
      .select("*")
      .in(
        "company_id",
        data.map((a) => a.id)
      );

    if (rateCardsError) {
      console.warn(
        "[getAffiliatesForQuote] Error fetching rate cards:",
        rateCardsError
      );
      // Continue without rate cards
    }

    // Transform to include UI-specific fields
    const affiliatesWithStats: AffiliateWithStats[] = data.map(
      (affiliate: any) => {
        // Calculate base rate and total rate (placeholder values for now)
        const baseRate = 75;
        const totalRate = 125;

        // Group rate cards by vehicle type
        const affiliateRateCards =
          rateCards?.filter((rc) => rc.company_id === affiliate.id) || [];
        const baseRates: Record<string, number> = {};
        const vehicleTypes = new Set<string>();

        affiliateRateCards.forEach((rc) => {
          baseRates[rc.vehicle_type] = rc.base_rate || 0;
          vehicleTypes.add(rc.vehicle_type);
        });

        // Create the enriched affiliate object
        return {
          id: affiliate.id,
          name: affiliate.name,
          email: affiliate.email || undefined,
          phone: affiliate.phone || undefined,
          address: affiliate.address || undefined,
          city: affiliate.city || undefined,
          state: affiliate.state || undefined,
          zip_code: affiliate.zip || undefined,
          country: affiliate.country || undefined,
          rating: affiliate.rating || undefined,
          status: affiliate.status as "active" | "inactive" | "pending",
          service_areas: [], // TODO: Add service areas
          vehicle_types: Array.from(vehicleTypes),
          base_rates: baseRates,
          response_time: "30m", // Default value
          on_time_percentage: Math.round(affiliate.completion_rate || 95),
          completion_rate: affiliate.completion_rate,

          // UI-specific calculated fields
          avgResponse: "15m",
          onTime: "95%",
          similarTrips: Math.floor(Math.random() * 20) + 5,
          baseRate: baseRate,
          totalRate: totalRate,
          priceRange: {
            min: baseRate,
            max: totalRate * 1.5,
          },
          distanceToPickup: "2.5 mi",
        };
      }
    );

    return affiliatesWithStats;
  } catch (error) {
    console.error("[getAffiliatesForQuote] Unexpected error:", error);
    return [];
  }
}

/**
 * Send a quote to selected affiliates
 * @param quoteId The quote ID
 * @param affiliateIds Array of affiliate IDs to send the quote to
 * @param offerType Whether this is a fixed offer or rate request
 * @param expiryHours How many hours until the offer expires
 * @param overrideRate Optional override rate for fixed offers
 * @returns A promise with the result or an error
 */
export async function sendQuoteToAffiliates(
  quoteId: string,
  affiliateIds: string[],
  offerType: "fixed_offer" | "rate_request",
  expiryHours: number,
  overrideRate?: number
): Promise<{ success: boolean; error: any }> {
  const supabase = getSupabaseClient();
  if (!supabase) {
    console.error("[sendQuoteToAffiliates] Supabase client is not available.");
    return {
      success: false,
      error: { message: "Supabase client not available" },
    };
  }
  try {
    const result = await sendQuote(
      quoteId,
      affiliateIds,
      offerType,
      expiryHours,
      overrideRate
    );
    return { success: true, error: null };
  } catch (error) {
    console.error("[sendQuoteToAffiliates] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error : new Error("Unknown error"),
    };
  }
}

/**
 * Get affiliate responses for a quote
 * @param quoteId The quote ID
 * @returns A promise with the affiliate responses or an error
 */
export async function getAffiliateResponses(
  quoteId: string
): Promise<{ data: any[] | null; error: any }> {
  const supabase = getSupabaseClient();
  if (!supabase) {
    console.error("[getAffiliateResponses] Supabase client is not available.");
    return { data: null, error: { message: "Supabase client not available" } };
  }
  console.warn(
    "getAffiliateResponses is deprecated. Use getQuoteResponses from quote-responses.ts instead"
  );
  return { data: [], error: null };
}

/**
 * Accept an affiliate's response to a quote
 * @param quoteId The quote ID
 * @param responseId The affiliate response ID
 * @returns A promise with the result or an error
 */
export async function acceptAffiliateResponse(
  quoteId: string,
  responseId: string
): Promise<{ success: boolean; error: any }> {
  const supabase = getSupabaseClient();
  if (!supabase) {
    console.error(
      "[acceptAffiliateResponse] Supabase client is not available."
    );
    return {
      success: false,
      error: { message: "Supabase client not available" },
    };
  }
  console.warn(
    "acceptAffiliateResponse is deprecated. Use acceptQuoteResponse from quote-responses.ts instead"
  );
  return { success: false, error: new Error("Method deprecated") };
}

/**
 * Reject an affiliate's response to a quote
 * @param responseId The affiliate response ID
 * @returns A promise with the result or an error
 */
export async function rejectAffiliateResponse(
  responseId: string
): Promise<{ success: boolean; error: any }> {
  const supabase = getSupabaseClient();
  if (!supabase) {
    console.error(
      "[rejectAffiliateResponse] Supabase client is not available."
    );
    return {
      success: false,
      error: { message: "Supabase client not available" },
    };
  }
  console.warn(
    "rejectAffiliateResponse is deprecated. Use rejectQuoteResponse from quote-responses.ts instead"
  );
  return { success: false, error: new Error("Method deprecated") };
}

/**
 * Update an affiliate company's details
 */
export async function updateAffiliateCompany(
  companyId: string,
  data: Partial<AffiliateCompany>
): Promise<{ success: boolean; error: any }> {
  const supabase = getSupabaseClient();
  if (!supabase) {
    console.error("[updateAffiliateCompany] Supabase client is not available.");
    return {
      success: false,
      error: { message: "Supabase client not available" },
    };
  }
  try {
    // Normalize city if provided
    if (data.city) {
      data.city = normalizeCity(data.city);
    }

    const { error } = await supabase
      .from("affiliate_companies")
      .update(data)
      .eq("id", companyId);

    if (error) {
      console.error("[updateAffiliateCompany] Error updating company:", error);
      return { success: false, error };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error("[updateAffiliateCompany] Unexpected error:", error);
    return { success: false, error };
  }
}

/**
 * Create a new affiliate company
 */
export async function createAffiliateCompany(
  data: Omit<AffiliateCompany, "id" | "created_at" | "updated_at">
): Promise<{ success: boolean; error: any; id?: string }> {
  const supabase = getSupabaseClient();
  if (!supabase) {
    console.error("[createAffiliateCompany] Supabase client is not available.");
    return {
      success: false,
      error: { message: "Supabase client not available" },
    };
  }
  try {
    // Normalize city
    data.city = normalizeCity(data.city);

    const { data: result, error } = await supabase
      .from("affiliate_companies")
      .insert(data)
      .select("id")
      .single();

    if (error) {
      console.error("[createAffiliateCompany] Error creating company:", error);
      return { success: false, error };
    }

    return { success: true, error: null, id: result.id };
  } catch (error) {
    console.error("[createAffiliateCompany] Unexpected error:", error);
    return { success: false, error };
  }
}

function processAffiliateResults(affiliates: any[]): AffiliateWithStats[] {
  return affiliates.map((affiliate) => ({
    // Basic info
    id: affiliate.id,
    name: affiliate.name,
    city: affiliate.city,
    state: affiliate.state,
    status: affiliate.status || "active",
    email: affiliate.email,
    phone: affiliate.phone,
    rating: parseFloat(affiliate.rating) || 4.5,
    completion_rate: parseFloat(affiliate.completion_rate) || 95,
    country: affiliate.country || "USA",

    // Performance metrics
    avgResponse: "30m",
    onTime: `${parseFloat(affiliate.completion_rate) || 95}%`,
    similarTrips: 42,

    // Rate information
    baseRate: parseFloat(affiliate.base_rate) || 150,
    totalRate: Math.round((parseFloat(affiliate.base_rate) || 150) * 1.2),
    priceRange: {
      min: parseFloat(affiliate.min_rate) || 150,
      max: parseFloat(affiliate.max_rate) || 200,
    },

    // Location info
    distanceToPickup: "2.5",

    // Additional fields
    address: affiliate.address,
    zip_code: affiliate.zip,
    vehicle_types: affiliate.vehicle_types || ["Sedan", "SUV"],
    base_rates: affiliate.base_rates || { Sedan: 150, SUV: 180 },
    response_time: "30m",
    on_time_percentage: parseFloat(affiliate.completion_rate) || 95,
    service_areas: [],

    // UI state
    selected: false,
    selectionOrder: undefined,
    isSkipped: false,
  }));
}
