import { PostgrestSingleResponse, SupabaseClient } from "@supabase/supabase-js";
import { getSupabaseClient } from "@/lib/supabase";
import {
  transitionToQuoteReady,
  transitionToRateRequested,
  transitionToAccepted,
} from "../utils/quote-status-transitions";
import { Database } from "../database.types";
// type Database = any; // Removed temporary workaround

type Tables = Database["public"]["Tables"];
type Quote = Tables["quotes"]["Row"];
type RateCard = Tables["rate_cards"]["Row"];
type QuoteOfferRow = Tables["quote_offers"]["Row"];

export interface QuoteResponse {
  id: string;
  quote_id: string | null;
  company_id: string | null;
  rate_amount: number | null;
  rate_currency: string | null;
  status: string | null;
  expiry_time?: string | null;
  type?: "fixed_offer" | "rate_proposal" | null;
  affiliate_name?: string | null;
  rate?: number | null;
}

export interface QuoteStatusTransitionPayload {
  quoteId: string;
  affiliateIds: string[];
}

export async function sendQuoteToAffiliates(
  quoteId: string,
  affiliateIds: string[],
  submissionType: "fixed_offer" | "rate_request",
  expiryHours: number = 24,
  overrideRate?: number
): Promise<{ success: boolean; error?: any }> {
  const supabaseClient = getSupabaseClient();
  if (!supabaseClient) {
    const err = new Error(
      "Supabase client not available in sendQuoteToAffiliates"
    );
    console.error(err.message);
    return { success: false, error: err };
  }
  console.log(
    `[sendQuoteToAffiliates] Starting for quote ${quoteId} with affiliates:`,
    affiliateIds
  );
  console.log(
    `[sendQuoteToAffiliates] Type: ${submissionType}, expiryHours: ${expiryHours}`
  );

  try {
    // Get quote details with all necessary fields
    const { data: quote, error: quoteError } = await supabaseClient
      .from("quotes")
      .select(
        `
        id,
        reference_number,
        service_type,
        vehicle_type,
        pickup_location,
        dropoff_location,
        date,
        time,
        passenger_count,
        luggage_count,
        special_requests,
        distance,
        duration,
        customer_id,
        contact_email,
        contact_name,
        contact_phone,
        priority
      `
      )
      .eq("id", quoteId)
      .single();

    if (quoteError) {
      console.error(
        "[sendQuoteToAffiliates] Error fetching quote:",
        quoteError
      );
      throw quoteError;
    }

    if (!quote) {
      console.error("[sendQuoteToAffiliates] Quote not found:", quoteId);
      throw new Error("Quote not found");
    }

    console.log("[sendQuoteToAffiliates] Found quote:", quote);

    // Calculate expiry time
    const expiryTime = new Date();
    expiryTime.setHours(expiryTime.getHours() + expiryHours);

    if (submissionType === "fixed_offer") {
      // For fixed offer, create quote offers for each affiliate
      console.log(
        `[sendQuoteToAffiliates] Creating fixed offers for ${affiliateIds.length} affiliates`
      );

      for (const affiliateId of affiliateIds) {
        console.log(
          `[sendQuoteToAffiliates] Creating fixed offer for affiliate ${affiliateId}`
        );

        // Create quote offer entry
        const { error: offerError } = await supabaseClient
          .from("quote_offers")
          .insert({
            quote_id: quoteId,
            company_id: affiliateId,
            status: "pending",
            rate_amount: overrideRate || 0,
            rate_currency: "USD",
            timeout_at: expiryTime.toISOString(),
          });

        if (offerError) {
          console.error(
            `[sendQuoteToAffiliates] Error creating fixed offer for affiliate ${affiliateId}:`,
            offerError
          );
          throw offerError;
        }
      }

      // Update the quote status using the centralized service
      console.log(
        `[sendQuoteToAffiliates] Updating quote status to quote ready via transition service`
      );
      const { success, error } = await transitionToQuoteReady(
        quoteId,
        affiliateIds
      );
      if (error) {
        console.error(
          "[sendQuoteToAffiliates] Error transitioning quote status:",
          error
        );

        // Fallback approach: directly update status
        const { error: directUpdateError } = await supabaseClient
          .from("quotes")
          .update({
            status: "quote_ready",
            updated_at: new Date().toISOString(),
          })
          .eq("id", quoteId);

        if (directUpdateError) {
          console.error(
            "[sendQuoteToAffiliates] Error on fallback direct status update:",
            directUpdateError
          );
        } else {
          console.log(
            "[sendQuoteToAffiliates] Successfully applied fallback status update"
          );
        }
      }
    } else {
      // For rate request, create rate proposal entries for each affiliate
      console.log(
        `[sendQuoteToAffiliates] Creating rate proposals for ${affiliateIds.length} affiliates`
      );

      for (const affiliateId of affiliateIds) {
        console.log(
          `[sendQuoteToAffiliates] Creating rate proposal for affiliate ${affiliateId}`
        );

        // Create rate proposal entry
        const { error: proposalError } = await supabaseClient
          .from("rate_proposals")
          .insert({
            quote_id: quoteId,
            company_id: affiliateId,
            status: "pending",
            timeout_at: expiryTime.toISOString(),
            rate_currency: "USD", // Default currency
          });

        if (proposalError) {
          console.error(
            `[sendQuoteToAffiliates] Error creating rate proposal for affiliate ${affiliateId}:`,
            proposalError
          );
          throw proposalError;
        }
      }

      // Update the quote status using the centralized service
      console.log(
        `[sendQuoteToAffiliates] Updating quote status to rate requested via transition service`
      );
      const { success, error } = await transitionToRateRequested(
        quoteId,
        affiliateIds
      );
      if (error) {
        console.error(
          "[sendQuoteToAffiliates] Error transitioning quote status:",
          error
        );

        // Fallback approach: directly update status
        const { error: directUpdateError } = await supabaseClient
          .from("quotes")
          .update({
            status: "rate_requested",
            updated_at: new Date().toISOString(),
          })
          .eq("id", quoteId);

        if (directUpdateError) {
          console.error(
            "[sendQuoteToAffiliates] Error on fallback direct status update:",
            directUpdateError
          );
        } else {
          console.log(
            "[sendQuoteToAffiliates] Successfully applied fallback status update"
          );
        }
      }
    }

    // Add timeline entry
    try {
      const {
        data: { user },
      } = await supabaseClient.auth.getUser();
      if (user?.id) {
        const { error: timelineError } = await supabaseClient
          .from("quote_timeline")
          .insert({
            quote_id: quoteId,
            user_id: user.id,
            action:
              submissionType === "fixed_offer"
                ? "quote_ready"
                : "rate_requested",
            details:
              submissionType === "fixed_offer"
                ? `Quote sent to ${affiliateIds.length} affiliates with fixed rate`
                : `Rate request sent to ${affiliateIds.length} affiliates`,
            metadata: {
              affiliate_count: affiliateIds.length,
              submission_type: submissionType,
              expiry_hours: expiryHours,
            },
          });

        if (timelineError) {
          console.error(
            "[sendQuoteToAffiliates] Error adding timeline entry:",
            timelineError
          );
          // Log the error but don't throw since the main operation succeeded
        } else {
          console.log(
            "[sendQuoteToAffiliates] Successfully added timeline entry"
          );
        }
      }
    } catch (timelineError) {
      console.error(
        "[sendQuoteToAffiliates] Error adding timeline entry:",
        timelineError
      );
      // Continue since the main operation succeeded
    }

    return { success: true };
  } catch (error) {
    console.error("[sendQuoteToAffiliates] Error:", error);
    return { success: false, error };
  }
}

export async function getQuoteResponses(
  quoteId: string
): Promise<QuoteResponse[]> {
  const supabaseClient = getSupabaseClient();
  if (!supabaseClient) {
    console.error("Supabase client not available in getQuoteResponses");
    throw new Error("Supabase client not available");
  }
  try {
    // Get quote offers (legacy system)
    const { data: offers, error: offersError } = await supabaseClient
      .from("quote_offers")
      .select("*")
      .eq("quote_id", quoteId);

    if (offersError) {
      console.error("[getQuoteResponses] Error fetching offers:", offersError);
      // Don't throw - this table might not exist yet
    }

    // Get rate proposals (legacy system)
    const { data: proposals, error: proposalsError } = await supabaseClient
      .from("rate_proposals")
      .select("*")
      .eq("quote_id", quoteId);

    if (proposalsError) {
      console.error(
        "[getQuoteResponses] Error fetching proposals:",
        proposalsError
      );
      // Don't throw - this table might not exist yet
    }

    // Get affiliate offers (new system)
    const { data: affiliateOffers, error: affiliateOffersError } =
      await supabaseClient
        .from("quote_affiliate_offers")
        .select(
          `
        id,
        quote_id,
        company_id,
        status,
        rate_amount,
        notes,
        created_at,
        affiliate_companies (
          name
        )
      `
        )
        .eq("quote_id", quoteId);

    if (affiliateOffersError) {
      console.error(
        "[getQuoteResponses] Error fetching affiliate offers:",
        affiliateOffersError
      );
      // Don't throw - continue with other sources
    }

    // Combine and format responses
    const responses: QuoteResponse[] = [];

    // Add legacy quote offers
    if (offers) {
      responses.push(
        ...offers.map((offer) => ({
          id: offer.id,
          quote_id: offer.quote_id,
          company_id: offer.company_id,
          rate_amount: offer.rate_amount,
          rate_currency: offer.rate_currency,
          status: offer.status,
          expiry_time: offer.timeout_at,
          type: "fixed_offer" as const,
        }))
      );
    }

    // Add legacy rate proposals
    if (proposals) {
      responses.push(
        ...proposals.map((proposal) => ({
          id: proposal.id,
          quote_id: proposal.quote_id,
          company_id: proposal.company_id,
          rate_amount: proposal.rate_amount,
          rate_currency: proposal.rate_currency,
          status: proposal.status,
          expiry_time: proposal.timeout_at,
          type: "rate_proposal" as const,
        }))
      );
    }

    // Add new affiliate offers
    if (affiliateOffers) {
      responses.push(
        ...affiliateOffers.map((offer) => ({
          id: offer.id,
          quote_id: offer.quote_id,
          company_id: offer.company_id,
          rate_amount: offer.rate_amount,
          rate_currency: "USD", // Default currency
          status: offer.status?.toLowerCase() || "pending", // Normalize status
          expiry_time: null, // No expiry for affiliate offers
          type: "fixed_offer" as const,
          affiliate_name:
            (offer.affiliate_companies as any)?.name || "Unknown Affiliate",
        }))
      );
    }

    console.log(
      `[getQuoteResponses] Found ${responses.length} total responses for quote ${quoteId}`
    );
    return responses;
  } catch (error) {
    console.error("[getQuoteResponses] Error:", error);
    throw error;
  }
}

export async function subscribeToQuoteResponses(
  quoteId: string,
  callback: (response: QuoteResponse) => void
): Promise<() => void> {
  const supabaseClient = getSupabaseClient();
  if (!supabaseClient) {
    console.error("Supabase client not available in subscribeToQuoteResponses");
    // Return a no-op unsubscribe function or throw
    return () => {};
  }
  // Subscribe to quote offers
  const offersSubscription = supabaseClient
    .channel(`quote_offers:${quoteId}`)
    .on(
      "postgres_changes",
      {
        event: "*",
        schema: "public",
        table: "quote_offers",
        filter: `quote_id=eq.${quoteId}`,
      },
      (payload) => {
        if (payload.new) {
          const data = payload.new as QuoteOfferRow;
          callback({
            id: data.id,
            quote_id: data.quote_id,
            company_id: data.company_id,
            rate_amount: data.rate_amount,
            rate_currency: data.rate_currency,
            status: data.status,
            expiry_time: data.timeout_at,
          });
        }
      }
    )
    .subscribe();

  // Subscribe to rate proposals
  const proposalsSubscription = supabaseClient
    .channel(`rate_proposals:${quoteId}`)
    .on(
      "postgres_changes",
      {
        event: "*",
        schema: "public",
        table: "rate_proposals",
        filter: `quote_id=eq.${quoteId}`,
      },
      (payload) => {
        if (payload.new) {
          const data = payload.new as QuoteOfferRow;
          callback({
            id: data.id,
            quote_id: data.quote_id,
            company_id: data.company_id,
            rate_amount: data.rate_amount,
            rate_currency: data.rate_currency,
            status: data.status,
            expiry_time: data.timeout_at,
          });
        }
      }
    )
    .subscribe();

  // Return unsubscribe function
  return () => {
    offersSubscription.unsubscribe();
    proposalsSubscription.unsubscribe();
  };
}

/**
 * Check if the required tables exist
 */
async function checkTablesExist(): Promise<{
  quotesOffersExists: boolean;
  rateProposalsExists: boolean;
}> {
  const supabaseClient = getSupabaseClient();
  if (!supabaseClient) {
    console.error("Supabase client not available in checkTablesExist");
    // Return default false or throw
    return { quotesOffersExists: false, rateProposalsExists: false };
  }
  let quotesOffersExists = false;
  try {
    // Instead of querying information_schema, we'll try to select from the tables directly
    // with a limit of 0 to avoid fetching any data

    // Check quote_offers table
    const { error: offersError } = await supabaseClient
      .from("quote_offers")
      .select("id")
      .limit(0);

    // Check rate_proposals table
    const { error: proposalsError } = await supabaseClient
      .from("rate_proposals")
      .select("id")
      .limit(0);

    quotesOffersExists = !offersError;
  } catch (error) {
    console.error("Error checking tables:", error);
  }
  return { quotesOffersExists, rateProposalsExists: quotesOffersExists };
}

/**
 * Accept a quote response
 */
export async function acceptQuoteResponse(
  quoteId: string,
  responseId: string,
  type: "fixed_offer" | "rate_proposal"
): Promise<{ success: boolean; error: Error | null }> {
  const supabaseClient = getSupabaseClient();
  if (!supabaseClient) {
    const err = new Error(
      "Supabase client not available in acceptQuoteResponse"
    );
    console.error(err.message);
    return { success: false, error: err };
  }
  try {
    const { data: session } = await supabaseClient.auth.getSession();
    if (!session) {
      throw new Error("No authenticated session");
    }

    // First, try to handle as affiliate offer (new system)
    const { data: affiliateOffer, error: affiliateOfferError } =
      await supabaseClient
        .from("quote_affiliate_offers")
        .select("company_id, rate_amount")
        .eq("id", responseId)
        .eq("quote_id", quoteId)
        .single();

    if (!affiliateOfferError && affiliateOffer) {
      console.log(
        `[acceptQuoteResponse] Handling as affiliate offer: ${responseId}`
      );

      // Update the affiliate offer status
      const { error: updateError } = await supabaseClient
        .from("quote_affiliate_offers")
        .update({ status: "accepted" })
        .eq("id", responseId)
        .eq("quote_id", quoteId);

      if (updateError) throw updateError;

      // Reject all other affiliate offers for this quote
      const { error: rejectError } = await supabaseClient
        .from("quote_affiliate_offers")
        .update({ status: "rejected" })
        .eq("quote_id", quoteId)
        .neq("id", responseId);

      if (rejectError) throw rejectError;

      // Update quote status using the centralized service
      const { success, error } = await transitionToAccepted(
        quoteId,
        affiliateOffer.company_id,
        affiliateOffer.rate_amount
      );

      if (error) throw error;
      return { success: true, error: null };
    }

    // Fall back to legacy system
    console.log(
      `[acceptQuoteResponse] Falling back to legacy system for: ${responseId}`
    );

    // Check if tables exist
    const { quotesOffersExists, rateProposalsExists } =
      await checkTablesExist();

    if (
      (type === "fixed_offer" && !quotesOffersExists) ||
      (type === "rate_proposal" && !rateProposalsExists)
    ) {
      return {
        success: false,
        error: new Error(
          `Required table for ${type} does not exist yet. Please run the migration first.`
        ),
      };
    }

    if (type === "fixed_offer") {
      // Update the offer status
      const { error: updateError } = await supabaseClient
        .from("quote_offers")
        .update({ status: "accepted" })
        .eq("id", responseId)
        .eq("quote_id", quoteId);

      if (updateError) throw updateError;

      // Reject all other offers
      const { error: rejectError } = await supabaseClient
        .from("quote_offers")
        .update({ status: "rejected" })
        .eq("quote_id", quoteId)
        .neq("id", responseId);

      if (rejectError) throw rejectError;

      // Get the affiliate_id from the offer
      const { data: offer, error: offerError } = await supabaseClient
        .from("quote_offers")
        .select("company_id, rate_amount")
        .eq("id", responseId)
        .single();

      if (offerError) throw offerError;

      if (!offer || !offer.company_id) {
        console.error(
          `[acceptQuoteResponse] Offer ${responseId} is missing company_id.`
        );
        throw new Error(
          `Cannot accept offer: company ID is missing for offer ${responseId}`
        );
      }
      if (offer.rate_amount === null || offer.rate_amount === undefined) {
        console.error(
          `[acceptQuoteResponse] Offer ${responseId} is missing rate_amount.`
        );
        throw new Error(
          `Cannot accept offer: rate_amount is missing for offer ${responseId}`
        );
      }

      // Update quote status using the centralized service
      const { success, error } = await transitionToAccepted(
        quoteId,
        offer.company_id,
        offer.rate_amount
      );

      if (error) throw error;
    } else {
      // Update the selected proposal
      const { error: updateError } = await supabaseClient
        .from("rate_proposals")
        .update({ is_selected: true })
        .eq("id", responseId)
        .eq("quote_id", quoteId);

      if (updateError) throw updateError;

      // Update all other proposals
      const { error: rejectError } = await supabaseClient
        .from("rate_proposals")
        .update({ is_selected: false })
        .eq("quote_id", quoteId)
        .neq("id", responseId);

      if (rejectError) throw rejectError;

      // Get the affiliate_id from the proposal
      const { data: proposal, error: proposalError } = await supabaseClient
        .from("rate_proposals")
        .select("company_id, rate_amount")
        .eq("id", responseId)
        .single();

      if (proposalError) throw proposalError;

      if (!proposal || !proposal.company_id) {
        console.error(
          `[acceptQuoteResponse] Proposal ${responseId} is missing company_id.`
        );
        throw new Error(
          `Cannot accept proposal: company ID is missing for proposal ${responseId}`
        );
      }
      if (proposal.rate_amount === null || proposal.rate_amount === undefined) {
        console.error(
          `[acceptQuoteResponse] Proposal ${responseId} is missing rate_amount.`
        );
        throw new Error(
          `Cannot accept proposal: rate_amount is missing for proposal ${responseId}`
        );
      }

      // Update quote status using the centralized service
      const { success, error } = await transitionToAccepted(
        quoteId,
        proposal.company_id,
        proposal.rate_amount
      );

      if (error) throw error;
    }

    return { success: true, error: null };
  } catch (error) {
    console.error("Error accepting quote response:", error);
    return { success: false, error: error as Error };
  }
}

/**
 * Reject a quote response
 */
export async function rejectQuoteResponse(
  quoteId: string,
  responseId: string,
  type: "fixed_offer" | "rate_proposal"
): Promise<{ success: boolean; error: Error | null }> {
  const supabaseClient = getSupabaseClient();
  if (!supabaseClient) {
    const err = new Error(
      "Supabase client not available in rejectQuoteResponse"
    );
    console.error(err.message);
    return { success: false, error: err };
  }
  try {
    const { data: session } = await supabaseClient.auth.getSession();
    if (!session) {
      throw new Error("No authenticated session");
    }

    // First, try to handle as affiliate offer (new system)
    const { data: affiliateOffer, error: affiliateOfferError } =
      await supabaseClient
        .from("quote_affiliate_offers")
        .select("id")
        .eq("id", responseId)
        .eq("quote_id", quoteId)
        .single();

    if (!affiliateOfferError && affiliateOffer) {
      console.log(
        `[rejectQuoteResponse] Handling as affiliate offer: ${responseId}`
      );

      // Update the affiliate offer status
      const { error: updateError } = await supabaseClient
        .from("quote_affiliate_offers")
        .update({ status: "rejected" })
        .eq("id", responseId)
        .eq("quote_id", quoteId);

      if (updateError) throw updateError;
      return { success: true, error: null };
    }

    // Fall back to legacy system
    console.log(
      `[rejectQuoteResponse] Falling back to legacy system for: ${responseId}`
    );

    // Check if tables exist
    const { quotesOffersExists, rateProposalsExists } =
      await checkTablesExist();

    if (
      (type === "fixed_offer" && !quotesOffersExists) ||
      (type === "rate_proposal" && !rateProposalsExists)
    ) {
      return {
        success: false,
        error: new Error(
          `Required table for ${type} does not exist yet. Please run the migration first.`
        ),
      };
    }

    if (type === "fixed_offer") {
      // Update the offer status
      const { error: updateError } = await supabaseClient
        .from("quote_offers")
        .update({ status: "rejected" })
        .eq("id", responseId)
        .eq("quote_id", quoteId);

      if (updateError) throw updateError;
    } else {
      // Delete the rate proposal (or mark as rejected if we want to keep history)
      const { error: deleteError } = await supabaseClient
        .from("rate_proposals")
        .delete()
        .eq("id", responseId)
        .eq("quote_id", quoteId);

      if (deleteError) throw deleteError;
    }

    return { success: true, error: null };
  } catch (error) {
    console.error("Error rejecting quote response:", error);
    return { success: false, error: error as Error };
  }
}
