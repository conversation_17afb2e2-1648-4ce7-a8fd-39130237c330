/**
 * Account Types and Permissions System
 * Handles different user roles and their capabilities in the quote workflow
 */

export type AccountType = 
  | 'super_admin'      // TransFlow SaaS Super Admin
  | 'tnc_admin'        // TNC Admin
  | 'super_client'     // Super Client (custom permissions)
  | 'regular_client'   // Regular Client
  | 'affiliate'        // Affiliate Company User
  | 'driver'           // Driver

export type WorkflowType = 
  | 'pure_saas'        // Direct client-affiliate interaction
  | 'tnc_managed'      // TNC intermediates between client and affiliates
  | 'white_label'      // White-label solution

export interface AccountPermissions {
  // Quote Management
  canCreateQuotes: boolean
  canEditQuotes: boolean
  canDeleteQuotes: boolean
  canViewAllQuotes: boolean
  
  // Affiliate Management
  canSelectAffiliates: boolean
  canSendToAffiliates: boolean
  canViewAffiliateResponses: boolean
  canAcceptAffiliateOffers: boolean
  canRejectAffiliateOffers: boolean
  
  // Rate Management
  canUseRateRequest: boolean
  canUseFixedOffer: boolean
  canSetCustomRates: boolean
  canViewRateCards: boolean
  
  // Monitoring & Intervention
  canMonitorAllQuotes: boolean
  canIntervenOnBehalf: boolean
  canOverrideDecisions: boolean
  
  // Communication
  canContactCustomers: boolean
  canContactAffiliates: boolean
  canViewCommunications: boolean
  
  // System Administration
  canManageUsers: boolean
  canManageAffiliates: boolean
  canViewAnalytics: boolean
  canExportData: boolean
}

export interface AccountContext {
  accountType: AccountType
  workflowType: WorkflowType
  organizationId?: string
  tenantId?: string
  isQuoteOwner?: boolean
  customPermissions?: Partial<AccountPermissions>
}

/**
 * Get permissions for a specific account type
 */
export function getAccountPermissions(context: AccountContext): AccountPermissions {
  const { accountType, workflowType, isQuoteOwner = false, customPermissions = {} } = context

  let basePermissions: AccountPermissions

  switch (accountType) {
    case 'super_admin':
      basePermissions = {
        // Quote Management - Full access
        canCreateQuotes: true,
        canEditQuotes: true,
        canDeleteQuotes: true,
        canViewAllQuotes: true,
        
        // Affiliate Management - Full access
        canSelectAffiliates: true,
        canSendToAffiliates: true,
        canViewAffiliateResponses: true,
        canAcceptAffiliateOffers: workflowType === 'tnc_managed', // Only in TNC mode
        canRejectAffiliateOffers: workflowType === 'tnc_managed', // Only in TNC mode
        
        // Rate Management - Full access
        canUseRateRequest: true,
        canUseFixedOffer: true,
        canSetCustomRates: true,
        canViewRateCards: true,
        
        // Monitoring & Intervention - Full access
        canMonitorAllQuotes: true,
        canIntervenOnBehalf: workflowType === 'pure_saas', // Emergency intervention in Pure SaaS
        canOverrideDecisions: true,
        
        // Communication - Full access
        canContactCustomers: true,
        canContactAffiliates: true,
        canViewCommunications: true,
        
        // System Administration - Full access
        canManageUsers: true,
        canManageAffiliates: true,
        canViewAnalytics: true,
        canExportData: true
      }
      break

    case 'tnc_admin':
      basePermissions = {
        // Quote Management - Full access within TNC
        canCreateQuotes: true,
        canEditQuotes: true,
        canDeleteQuotes: true,
        canViewAllQuotes: true,
        
        // Affiliate Management - Full TNC control
        canSelectAffiliates: true,
        canSendToAffiliates: true,
        canViewAffiliateResponses: true,
        canAcceptAffiliateOffers: true, // TNC decides which affiliates to use
        canRejectAffiliateOffers: true,
        
        // Rate Management - TNC specific
        canUseRateRequest: true,
        canUseFixedOffer: true,
        canSetCustomRates: true,
        canViewRateCards: true,
        
        // Monitoring & Intervention - Limited to TNC network
        canMonitorAllQuotes: true,
        canIntervenOnBehalf: false, // TNC doesn't intervene on behalf of clients
        canOverrideDecisions: false,
        
        // Communication - TNC network
        canContactCustomers: true,
        canContactAffiliates: true,
        canViewCommunications: true,
        
        // System Administration - Limited
        canManageUsers: false,
        canManageAffiliates: true, // Only their network
        canViewAnalytics: true,
        canExportData: true
      }
      break

    case 'super_client':
      basePermissions = {
        // Quote Management - Enhanced client access
        canCreateQuotes: true,
        canEditQuotes: true,
        canDeleteQuotes: isQuoteOwner,
        canViewAllQuotes: false, // Only their quotes
        
        // Affiliate Management - Enhanced client control
        canSelectAffiliates: true,
        canSendToAffiliates: true, // Can use admin-style interface
        canViewAffiliateResponses: true,
        canAcceptAffiliateOffers: true,
        canRejectAffiliateOffers: true,
        
        // Rate Management - Custom permissions
        canUseRateRequest: true, // Super clients can use rate requests
        canUseFixedOffer: true,
        canSetCustomRates: false,
        canViewRateCards: false,
        
        // Monitoring & Intervention - None
        canMonitorAllQuotes: false,
        canIntervenOnBehalf: false,
        canOverrideDecisions: false,
        
        // Communication - Limited
        canContactCustomers: false,
        canContactAffiliates: true,
        canViewCommunications: true,
        
        // System Administration - None
        canManageUsers: false,
        canManageAffiliates: false,
        canViewAnalytics: false,
        canExportData: true
      }
      break

    case 'regular_client':
      basePermissions = {
        // Quote Management - Basic client access
        canCreateQuotes: true,
        canEditQuotes: isQuoteOwner,
        canDeleteQuotes: isQuoteOwner,
        canViewAllQuotes: false, // Only their quotes
        
        // Affiliate Management - Pure SaaS client control
        canSelectAffiliates: workflowType === 'pure_saas',
        canSendToAffiliates: false, // No admin interface
        canViewAffiliateResponses: true,
        canAcceptAffiliateOffers: workflowType === 'pure_saas', // Direct acceptance in Pure SaaS
        canRejectAffiliateOffers: workflowType === 'pure_saas',
        
        // Rate Management - No advanced features
        canUseRateRequest: false, // Only TNC and Super Clients
        canUseFixedOffer: false,
        canSetCustomRates: false,
        canViewRateCards: false,
        
        // Monitoring & Intervention - None
        canMonitorAllQuotes: false,
        canIntervenOnBehalf: false,
        canOverrideDecisions: false,
        
        // Communication - Limited
        canContactCustomers: false,
        canContactAffiliates: false,
        canViewCommunications: true,
        
        // System Administration - None
        canManageUsers: false,
        canManageAffiliates: false,
        canViewAnalytics: false,
        canExportData: false
      }
      break

    case 'affiliate':
      basePermissions = {
        // Quote Management - View only
        canCreateQuotes: false,
        canEditQuotes: false,
        canDeleteQuotes: false,
        canViewAllQuotes: false,
        
        // Affiliate Management - Response only
        canSelectAffiliates: false,
        canSendToAffiliates: false,
        canViewAffiliateResponses: false,
        canAcceptAffiliateOffers: true, // Accept quote offers
        canRejectAffiliateOffers: true,
        
        // Rate Management - Submit rates
        canUseRateRequest: false,
        canUseFixedOffer: false,
        canSetCustomRates: true, // Set their own rates
        canViewRateCards: true, // Their own rate cards
        
        // Monitoring & Intervention - None
        canMonitorAllQuotes: false,
        canIntervenOnBehalf: false,
        canOverrideDecisions: false,
        
        // Communication - Limited
        canContactCustomers: false,
        canContactAffiliates: false,
        canViewCommunications: true,
        
        // System Administration - None
        canManageUsers: false,
        canManageAffiliates: false,
        canViewAnalytics: false,
        canExportData: false
      }
      break

    case 'driver':
      basePermissions = {
        // Quote Management - None
        canCreateQuotes: false,
        canEditQuotes: false,
        canDeleteQuotes: false,
        canViewAllQuotes: false,
        
        // Affiliate Management - None
        canSelectAffiliates: false,
        canSendToAffiliates: false,
        canViewAffiliateResponses: false,
        canAcceptAffiliateOffers: false,
        canRejectAffiliateOffers: false,
        
        // Rate Management - None
        canUseRateRequest: false,
        canUseFixedOffer: false,
        canSetCustomRates: false,
        canViewRateCards: false,
        
        // Monitoring & Intervention - None
        canMonitorAllQuotes: false,
        canIntervenOnBehalf: false,
        canOverrideDecisions: false,
        
        // Communication - Limited
        canContactCustomers: true, // During trips
        canContactAffiliates: true,
        canViewCommunications: true,
        
        // System Administration - None
        canManageUsers: false,
        canManageAffiliates: false,
        canViewAnalytics: false,
        canExportData: false
      }
      break

    default:
      // Default to most restrictive permissions
      basePermissions = {
        canCreateQuotes: false,
        canEditQuotes: false,
        canDeleteQuotes: false,
        canViewAllQuotes: false,
        canSelectAffiliates: false,
        canSendToAffiliates: false,
        canViewAffiliateResponses: false,
        canAcceptAffiliateOffers: false,
        canRejectAffiliateOffers: false,
        canUseRateRequest: false,
        canUseFixedOffer: false,
        canSetCustomRates: false,
        canViewRateCards: false,
        canMonitorAllQuotes: false,
        canIntervenOnBehalf: false,
        canOverrideDecisions: false,
        canContactCustomers: false,
        canContactAffiliates: false,
        canViewCommunications: false,
        canManageUsers: false,
        canManageAffiliates: false,
        canViewAnalytics: false,
        canExportData: false
      }
  }

  // Apply custom permission overrides
  return { ...basePermissions, ...customPermissions }
}

/**
 * Determine account type from user context
 */
export function determineAccountType(userRoles: string[], organizationId?: string): AccountType {
  if (userRoles.includes('SUPER_ADMIN')) return 'super_admin'
  if (userRoles.includes('TNC_ADMIN')) return 'tnc_admin'
  if (userRoles.includes('SUPER_CLIENT')) return 'super_client'
  if (userRoles.includes('AFFILIATE')) return 'affiliate'
  if (userRoles.includes('DRIVER')) return 'driver'
  return 'regular_client' // Default
}

/**
 * Determine workflow type from context
 */
export function determineWorkflowType(accountType: AccountType, organizationId?: string): WorkflowType {
  if (accountType === 'tnc_admin') return 'tnc_managed'
  // Add logic to determine white_label vs pure_saas based on organization settings
  return 'pure_saas' // Default for now
}
