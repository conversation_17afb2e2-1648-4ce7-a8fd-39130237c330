import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { Session } from '@supabase/supabase-js';
import { hasRole } from '@/app/lib/auth';
import { toUserRoles } from '@/src/types/roles';
import type { UserRole } from '@/src/types/roles';

/**
 * Check if a user has any of the specified roles
 * 
 * @param session The user's session
 * @param requiredRoles Array of roles to check against
 * @returns Object containing hasRole boolean and error if any
 */
export async function checkRoles(
  session: Session,
  requiredRoles: UserRole[]
): Promise<{ hasRole: boolean; error?: Error }> {
  try {
    if (!session || !session.user) {
      return { hasRole: false, error: new Error('No active session') };
    }

    const supabase = createRouteHandlerClient({ cookies });
    
    // Fetch user's profile to get roles
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role, roles')
      .eq('id', session.user.id)
      .single();

    if (profileError) {
      console.error('Error fetching profile roles:', profileError);
      return { hasRole: false, error: new Error('Error fetching user role') };
    }

    if (!profile) {
      return { hasRole: false, error: new Error('User profile not found') };
    }

    // Check if user has any of the required roles
    // Always use the shared hasRole utility for role checks
    const userRoles = toUserRoles(profile.roles || [profile.role]);
    const hasRequiredRole = hasRole(userRoles, requiredRoles);
    return { hasRole: hasRequiredRole };
  } catch (error) {
    console.error('Error checking roles:', error);
    return { 
      hasRole: false, 
      error: error instanceof Error ? error : new Error('Unknown error checking roles')
    };
  }
} 