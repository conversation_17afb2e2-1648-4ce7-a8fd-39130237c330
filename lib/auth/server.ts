import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { User } from '@supabase/supabase-js'
import { hasRole as sharedHasRole } from '@/app/lib/auth'
import { toUserRoles } from '@/src/types/roles'
import type { UserRole } from '@/src/types/roles'

/**
 * Production-ready server-side authentication utilities
 * Centralized auth handling for consistency across API routes
 */

export interface AuthResult {
  user: User | null
  error: string | null
  isAuthenticated: boolean
}

/**
 * Create a properly configured Supabase server client
 * Uses the same pattern as middleware for consistency
 */
export async function createAuthenticatedSupabaseClient() {
  const cookieStore = await cookies()

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set({ name, value, ...options })
          } catch (error) {
            // Ignore errors from Server Components
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set({ name, value: '', ...options })
          } catch (error) {
            // Ignore errors from Server Components
          }
        },
      },
    }
  )
}

/**
 * Authenticate user for API routes
 * Returns standardized auth result
 */
export async function authenticateUser(): Promise<AuthResult> {
  try {
    const supabase = await createAuthenticatedSupabaseClient()

    const { data: { user }, error } = await supabase.auth.getUser()

    if (error) {
      // Check if this is a "User from sub claim in JWT does not exist" error
      if (error.message.includes('User from sub claim in JWT does not exist')) {
        console.log('Auth: JWT contains non-existent user, clearing session')
        // Clear the invalid session
        await supabase.auth.signOut()
        return {
          user: null,
          error: 'Session expired, please log in again',
          isAuthenticated: false
        }
      }

      return {
        user: null,
        error: error.message,
        isAuthenticated: false
      }
    }

    if (!user) {
      return {
        user: null,
        error: 'User not authenticated',
        isAuthenticated: false
      }
    }

    return {
      user,
      error: null,
      isAuthenticated: true
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Authentication error'

    // Handle JWT errors gracefully
    if (errorMessage.includes('User from sub claim in JWT does not exist')) {
      console.log('Auth: JWT contains non-existent user, clearing session')
      try {
        const supabase = await createAuthenticatedSupabaseClient()
        await supabase.auth.signOut()
      } catch (signOutError) {
        console.error('Error signing out invalid session:', signOutError)
      }
      return {
        user: null,
        error: 'Session expired, please log in again',
        isAuthenticated: false
      }
    }

    return {
      user: null,
      error: errorMessage,
      isAuthenticated: false
    }
  }
}

/**
 * Require authentication for API routes
 * Returns authenticated user or throws with proper HTTP response
 */
export async function requireAuth(): Promise<User> {
  const auth = await authenticateUser()

  if (!auth.isAuthenticated || !auth.user) {
    throw new Error(`Unauthorized: ${auth.error || 'Authentication required'}`)
  }

  return auth.user
}

/**
 * Check if user has specific roles
 */
export async function hasRole(requiredRoles: UserRole[]): Promise<boolean> {
  const auth = await authenticateUser()

  if (!auth.isAuthenticated || !auth.user) {
    return false
  }

  try {
    const supabase = await createAuthenticatedSupabaseClient()

    const { data: profile } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', auth.user.id)
      .single()

    if (!profile?.roles) {
      return false
    }

    return sharedHasRole(profile.roles, requiredRoles)
  } catch {
    return false
  }
}

/**
 * Require specific roles for API routes
 */
export async function requireRole(requiredRoles: string[]): Promise<User> {
  const user = await requireAuth()

  const hasRequiredRole = await hasRole(toUserRoles(requiredRoles))

  if (!hasRequiredRole) {
    throw new Error(`Forbidden: Requires one of roles: ${requiredRoles.join(', ')}`)
  }

  return user
}