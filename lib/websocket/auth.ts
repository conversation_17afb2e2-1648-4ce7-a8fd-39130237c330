import { getSupabaseClient } from '@/lib/supabase'
import { getConnectionManager } from './connection-manager'
import { toUserRoles } from '@/src/types/roles'
import { hasRole as sharedHasRole } from '@/app/lib/auth'
import type { UserRole } from '@/src/types/roles'

export interface WebSocketAuthState {
  isAuthenticated: boolean
  userId?: string
  userEmail?: string
  roles?: string[]
  error?: string
}

class WebSocketAuth {
  private authState: WebSocketAuthState = {
    isAuthenticated: false
  }
  private listeners: Set<(state: WebSocketAuthState) => void> = new Set()
  private authCheckInterval: NodeJS.Timeout | null = null

  constructor() {
    this.initializeAuth()
  }

  private async initializeAuth() {
    await this.checkAuthStatus()
    this.startAuthMonitoring()
  }

  private async checkAuthStatus(): Promise<void> {
    try {
      const supabase = getSupabaseClient()
      if (!supabase) {
        this.updateAuthState({
          isAuthenticated: false,
          error: 'Supabase client not available'
        })
        return
      }

      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error) {
        console.error('[WebSocketAuth] Error checking session:', error)
        this.updateAuthState({
          isAuthenticated: false,
          error: error.message
        })
        return
      }

      if (session?.user) {
        // Get user profile for roles
        const { data: profile } = await supabase
          .from('profiles')
          .select('roles')
          .eq('id', session.user.id)
          .single()

        this.updateAuthState({
          isAuthenticated: true,
          userId: session.user.id,
          userEmail: session.user.email,
          roles: profile?.roles || [],
          error: undefined
        })

        // Ensure WebSocket connection is established for authenticated users
        const connectionManager = getConnectionManager()
        if (!connectionManager.isConnected()) {
          connectionManager.connect().catch(error => {
            console.error('[WebSocketAuth] Failed to establish WebSocket connection:', error)
          })
        }
      } else {
        this.updateAuthState({
          isAuthenticated: false,
          userId: undefined,
          userEmail: undefined,
          roles: undefined,
          error: undefined
        })

        // Disconnect WebSocket for unauthenticated users
        const connectionManager = getConnectionManager()
        connectionManager.disconnect()
      }
    } catch (error) {
      console.error('[WebSocketAuth] Error in checkAuthStatus:', error)
      this.updateAuthState({
        isAuthenticated: false,
        error: error instanceof Error ? error.message : 'Authentication check failed'
      })
    }
  }

  private updateAuthState(updates: Partial<WebSocketAuthState>) {
    this.authState = { ...this.authState, ...updates }
    this.notifyListeners()
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.authState))
  }

  private startAuthMonitoring() {
    // Check auth status every 30 seconds
    this.authCheckInterval = setInterval(() => {
      this.checkAuthStatus()
    }, 30000)

    // Listen for auth state changes from Supabase
    const supabase = getSupabaseClient()
    if (supabase) {
      supabase.auth.onAuthStateChange((event, session) => {
        console.log('[WebSocketAuth] Auth state changed:', event, session?.user?.id)
        
        if (event === 'SIGNED_IN' && session?.user) {
          this.checkAuthStatus()
        } else if (event === 'SIGNED_OUT') {
          this.updateAuthState({
            isAuthenticated: false,
            userId: undefined,
            userEmail: undefined,
            roles: undefined,
            error: undefined
          })
          
          // Disconnect WebSocket
          const connectionManager = getConnectionManager()
          connectionManager.disconnect()
        } else if (event === 'TOKEN_REFRESHED' && session?.user) {
          // Don't refresh auth state on token refresh to prevent loops
          console.log('[WebSocketAuth] TOKEN_REFRESHED event - skipping checkAuthStatus to prevent loop')
        }
      })
    }
  }

  public getAuthState(): WebSocketAuthState {
    return { ...this.authState }
  }

  public addListener(listener: (state: WebSocketAuthState) => void) {
    this.listeners.add(listener)
  }

  public removeListener(listener: (state: WebSocketAuthState) => void) {
    this.listeners.delete(listener)
  }

  public async refreshAuth(): Promise<void> {
    await this.checkAuthStatus()
  }

  public isAuthenticated(): boolean {
    return this.authState.isAuthenticated
  }

  public getUserId(): string | undefined {
    return this.authState.userId
  }

  public getUserRoles(): string[] {
    return this.authState.roles || []
  }

  /**
   * Check if the user has a specific role (normalized)
   * Prefer using the shared hasRole utility from app/lib/auth for all role checks if possible.
   */
  public hasRole(role: UserRole): boolean {
    const normalizedRoles: UserRole[] = toUserRoles(this.authState.roles || []);
    return sharedHasRole(normalizedRoles, role);
  }

  public destroy() {
    if (this.authCheckInterval) {
      clearInterval(this.authCheckInterval)
      this.authCheckInterval = null
    }
    this.listeners.clear()
  }
}

// Singleton instance
let webSocketAuth: WebSocketAuth | null = null

export function getWebSocketAuth(): WebSocketAuth {
  if (!webSocketAuth) {
    webSocketAuth = new WebSocketAuth()
  }
  return webSocketAuth
}

export { WebSocketAuth }

/**
 * Utility function to check if user has permission for WebSocket operations
 */
export function hasWebSocketPermission(operation: string, userRoles: string[] = []): boolean {
  // Define role-based permissions for WebSocket operations
  const permissions: Record<string, string[]> = {
    'quote_updates': ['event_manager', 'customer', 'affiliate', 'super_admin'],
    'quote_offers': ['event_manager', 'customer', 'super_admin'],
    'trip_updates': ['event_manager', 'customer', 'affiliate', 'driver', 'super_admin'],
    'notifications': ['event_manager', 'customer', 'affiliate', 'driver', 'super_admin'],
    'admin_operations': ['super_admin'],
    'affiliate_operations': ['affiliate', 'super_admin']
  }

  const requiredRoles = permissions[operation] || [];
  const normalizedUserRoles: UserRole[] = toUserRoles(userRoles);
  const normalizedRequiredRoles: UserRole[] = toUserRoles(requiredRoles);
  if (normalizedRequiredRoles.length === 0) {
    console.warn(`[WebSocketAuth] Unknown operation: ${operation}`)
    return false
  }
  return sharedHasRole(normalizedUserRoles, normalizedRequiredRoles)
}

/**
 * Create authenticated channel name with user context
 */
export function createAuthenticatedChannelName(baseChannel: string, userId?: string): string {
  if (!userId) {
    return baseChannel
  }
  return `${baseChannel}:${userId}`
}

/**
 * Validate WebSocket subscription permissions
 */
export function validateSubscriptionPermissions(
  channelName: string, 
  userRoles: string[] = []
): boolean {
  // Extract operation type from channel name
  const operation = channelName.split(':')[0]
  return hasWebSocketPermission(operation, userRoles)
}
