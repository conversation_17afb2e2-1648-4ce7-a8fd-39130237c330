import { createClient } from "@supabase/supabase-js";
import { Database } from "./database.types";

// Default to local Supabase instance if environment variables are not set
const supabaseUrl =
  process.env.NEXT_PUBLIC_SUPABASE_URL || "http://127.0.0.1:54321";
const supabaseServiceRoleKey =
  process.env.SUPABASE_SERVICE_ROLE_KEY ||
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU";

console.log("Initializing Supabase admin client with URL:", supabaseUrl);

export const supabaseAdmin = createClient<Database>(
  supabaseUrl,
  supabaseServiceRoleKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
    global: {
      fetch: customFetch,
    },
  }
);

// Custom fetch implementation with retry logic for network errors
async function customFetch(
  url: RequestInfo | URL,
  options?: RequestInit
): Promise<Response> {
  const MAX_RETRIES = 3;
  const RETRY_DELAY = 1000; // 1 second

  let retries = 0;
  let lastError: Error | null = null;

  while (retries < MAX_RETRIES) {
    try {
      // Set a reasonable timeout for the fetch request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const fetchOptions = {
        ...options,
        signal: controller.signal,
      };

      const response = await fetch(url, fetchOptions);
      clearTimeout(timeoutId);
      return response;
    } catch (error: any) {
      lastError = error;

      // Only retry on network errors like ECONNRESET
      if (
        error.name === "AbortError" ||
        (error.cause && error.cause.code === "ECONNRESET") ||
        error.message.includes("fetch failed")
      ) {
        console.warn(
          `Admin fetch attempt ${retries + 1} failed, retrying in ${RETRY_DELAY}ms:`,
          error.message
        );
        retries++;

        // Wait before retrying
        await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
      } else {
        // For other errors, don't retry
        break;
      }
    }
  }

  // If we've exhausted retries or hit a non-retryable error, throw the last error
  console.error("Admin fetch failed after retries:", lastError);
  throw lastError;
}
