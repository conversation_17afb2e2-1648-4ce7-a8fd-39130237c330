import { createBrowserClient } from "@supabase/ssr";
import { Database } from "./database.types"; // TODO: Ensure database.types.ts is correctly populated

// Create a singleton instance for browser environments
let supabaseClientInstance: ReturnType<
  typeof createBrowserClient<Database>
> | null = null; // Changed any to Database

export const getSupabaseClient = () => {
  // Ensure this runs only in the browser
  if (typeof window === "undefined") {
    // This function is intended for browser use only.
    // Server-side code should use createServerClient from @supabase/ssr.
    console.warn(
      "getSupabaseClient (browser client) was called on the server. This is not supported. Returning null."
    );
    return null;
  }

  if (!supabaseClientInstance) {
    // Get environment variables at runtime in the browser
    const supabaseUrl =
      process.env.NEXT_PUBLIC_SUPABASE_URL || "http://127.0.0.1:54321";
    const supabaseAnonKey =
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ||
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0";

    console.log("CLIENT_LOG: Creating Supabase client with URL:", supabaseUrl);
    console.log("CLIENT_LOG: Anon key available:", !!supabaseAnonKey);

    supabaseClientInstance = createBrowserClient<Database>(
      supabaseUrl,
      supabaseAnonKey
    ); // Changed any to Database
  }
  return supabaseClientInstance;
};

// Consumers should call getSupabaseClient() themselves.

// Add an explicit export for the Database type if needed elsewhere, though it's primarily for internal use here.
// export type { Database };
