import {
  NextResponse,
  type NextRequest as MiddlewareNextRequest,
} from "next/server";
import type { NextRequest } from "next/server";
import { createServerClient, type CookieOptions } from "@supabase/ssr";
import { UserRole } from "@/src/types/roles";
import { clearCorruptedAuthCookies } from "@/lib/utils/cookie-parser";
import { Session, User, SupabaseClient } from "@supabase/supabase-js";

// Define valid user roles as string literals, matching the UserRole type/enum values
const validUserRoles: UserRole[] = [
  "CLIENT",
  "AFFILIATE",
  "SUPER_ADMIN",
  "CLIENT_COORDINATOR",
  "PASSENGER",
  "AFFILIATE_DISPATCH",
  "DRIVER",
];

const DEVELOPMENT_MODE = process.env.NODE_ENV === "development";

// Public routes that don't require authentication
const publicRoutes = [
  "/",
  "/login",
  "/register",
  "/forgot-password",
  "/reset-password",
  "/sw.js",
  "/manifest.json",
  "/auth/callback",
  "/auth/confirm",
  "/unauthorized",
  "/.well-known",
  "/api/auth/register",
  "/api/auth/login",
  "/api/auth/logout",
  "/api/auth/request-password-reset",
  "/api/auth/reset-password",
  "/api/health",
  "/api/trpc/health.check", // Publicly accessible health check
  "/routes",
  "/debug",
  "/direct-login",
  "/api/test/assign-boston-affiliate",
  "/test/session",
  "/test/api",
  "/api/test/create-test-quote-offer",
  "/test/submit-proposal",
  "/public-test",
  "/api/test-auth",
  "/ui-demo",
  "/toast-demo",
  "/super-admin-bypass",
  "/direct-super-admin",
  "/avatars/:path*",
  "/img/:path*",
  "/static/:path*",
];

// Define protected routes and their required roles
const protectedRoutes: Record<string, UserRole[]> = {
  "/super-admin": ["SUPER_ADMIN"],
  "/api/super-admin": ["SUPER_ADMIN"],
  "/event-manager": ["CLIENT"], // CLIENT role now maps to /event-manager
  "/api/event-manager": ["CLIENT"], // API routes for CLIENT role
  "/customer": ["PASSENGER"], // PASSENGER role now maps to /customer
  "/api/customer": ["PASSENGER"], // API routes for PASSENGER role
  "/affiliate": ["AFFILIATE", "AFFILIATE_DISPATCH"],
  "/api/affiliate": ["AFFILIATE", "AFFILIATE_DISPATCH"],
  "/client-coordinator": ["CLIENT_COORDINATOR"],
  "/api/client-coordinator": ["CLIENT_COORDINATOR"],
  "/driver": ["DRIVER"],
  "/api/driver": ["DRIVER"],
} as const;

// Role to dashboard mapping - prioritize EVENT_MANAGER over CUSTOMER
const getDashboardRoute = (roles: UserRole[]): string => {
  console.log("getDashboardRoute received roles:", JSON.stringify(roles));

  // Prioritize SUPER_ADMIN first
  if (roles.includes("SUPER_ADMIN")) {
    console.log(
      "User has SUPER_ADMIN role, redirecting to super-admin dashboard"
    );
    return "/super-admin/dashboard";
  }

  // Prioritize AFFILIATE over CLIENT
  if (roles.includes("AFFILIATE")) {
    console.log("User has AFFILIATE role, redirecting to affiliate dashboard");
    return "/affiliate/dashboard";
  }

  if (roles.includes("CLIENT")) {
    console.log("User has CLIENT role, redirecting to client dashboard");
    return "/event-manager/dashboard";
  }

  if (roles.includes("PASSENGER")) {
    console.log("User has PASSENGER role, redirecting to passenger dashboard");
    return "/customer/dashboard";
  }

  if (roles.includes("CLIENT_COORDINATOR")) {
    console.log(
      "User has CLIENT_COORDINATOR role, needs dashboard path defined"
    );
    return "/client-coordinator/dashboard";
  }
  if (roles.includes("AFFILIATE_DISPATCH")) {
    console.log(
      "User has AFFILIATE_DISPATCH role, needs dashboard path defined"
    );
    return "/affiliate-dispatch/dashboard";
  }
  if (roles.includes("DRIVER")) {
    console.log("User has DRIVER role, needs dashboard path defined");
    return "/driver/dashboard";
  }

  console.log(
    "No specific dashboard for roles, defaulting to /:",
    JSON.stringify(roles)
  );
  return "/profile";
};

// Check if we're in a refresh loop
function checkRefreshLoop(request: NextRequest): boolean {
  // If we're on the login page, don't check for refresh loops
  if (request.nextUrl.pathname === "/login") {
    return false;
  }

  // Get the refresh_attempts cookie
  const refreshAttempts = request.cookies.get("refresh_attempts")?.value;

  if (refreshAttempts && parseInt(refreshAttempts) > 3) {
    // Reduced from 5 to 3
    console.log(`Detected potential refresh loop: ${refreshAttempts} attempts`);
    return true;
  }

  // Check the last_refresh_time cookie
  const lastRefreshTime = request.cookies.get("last_refresh_time")?.value;

  if (lastRefreshTime) {
    const timeSinceLastRefresh = Date.now() - parseInt(lastRefreshTime);
    const MIN_REFRESH_INTERVAL = 1000; // 1 second - even more lenient

    // Only consider it a refresh loop if we've had multiple rapid refreshes
    // This helps prevent false positives
    if (timeSinceLastRefresh < MIN_REFRESH_INTERVAL) {
      // Check if we have a refresh_count cookie to track consecutive rapid refreshes
      const refreshCount = parseInt(
        request.cookies.get("refresh_count")?.value || "0"
      );

      // Only trigger the loop detection if we've had multiple consecutive rapid refreshes
      if (refreshCount > 2) {
        // Reduced from 3 to 2
        console.log(
          `Detected rapid refreshes: ${timeSinceLastRefresh}ms since last refresh, count: ${refreshCount}`
        );
        return true;
      } else {
        console.log(
          `Fast refresh detected (${timeSinceLastRefresh}ms) but not yet a loop (count: ${refreshCount})`
        );
        // We'll increment the count in the middleware function
        return false;
      }
    }
  }

  return false;
}

// Check if the request is coming from a refresh redirect
function isFromRefresh(request: NextRequest): boolean {
  const referer = request.headers.get("referer") || "";
  return referer.includes("/api/auth/refresh");
}

// Check if the request has a refresh_loop error parameter
function hasRefreshLoopError(request: NextRequest): boolean {
  return request.nextUrl.searchParams.get("error") === "refresh_loop";
}

// Clear all auth cookies from the response
function clearAuthCookies(response: NextResponse): NextResponse {
  const cookiesToClear = [
    "sb-auth-token",
    "last_refresh_time",
    "refresh_attempts",
    "refresh_count",
    "wwms-auth-session",
    "session_id",
  ];

  cookiesToClear.forEach((name) => {
    response.cookies.delete(name);
    console.log(`Middleware: Deleted cookie: ${name}`);
  });

  return response;
}

// Check if the request is for the login page with an error parameter
function isLoginPageWithError(request: NextRequest): boolean {
  const { pathname, search } = request.nextUrl;
  return pathname === "/login" && search.includes("error=");
}

// Check if a route is public (doesn't require authentication)
function isPublicRoute(pathname: string, routesArray: string[]): boolean {
  return routesArray.some((route) => {
    if (route === "/") {
      return pathname === "/"; // Exact match for root
    }
    // For API routes or asset folders, startsWith is often appropriate
    // For other specific pages, consider exact matches or more tailored logic if needed
    return pathname.startsWith(route);
  });
}

// Track recent test account processing to avoid excessive operations
const testAccountProcessed = new Map<string, number>();
const TEST_ACCOUNT_COOLDOWN = 10000; // 10 seconds between processing the same test account

// Get the session data from Supabase
async function checkSession(
  supabase: SupabaseClient,
  req: NextRequest
): Promise<{
  session: Session | null;
  userRoles: UserRole[];
  userId: string | null;
  userEmail: string | null;
}> {
  let activeSession: Session | null = null;
  let currentUser: User | null = null;
  let currentRoles: UserRole[] = [];

  const allCookiesFromReq = req.cookies.getAll();
  const supabaseAuthCookiePattern = /^sb-.*-auth-token$/;
  const hasAuthCookie = allCookiesFromReq.some((cookie) =>
    supabaseAuthCookiePattern.test(cookie.name)
  );
  console.log(
    "checkSession: Supabase SSR Auth cookie present in headers (pattern match):",
    hasAuthCookie
  );

  try {
    const {
      data: { user },
      error: getUserError,
    } = await supabase.auth.getUser();

    if (getUserError) {
      console.error(
        "checkSession: Error calling supabase.auth.getUser():",
        getUserError.message
      );
    } else if (user) {
      console.log(
        "checkSession: supabase.auth.getUser() returned user:",
        user.id,
        user.email
      );
      currentUser = user;

      try {
        const {
          data: { session: sessionFromGetUser },
          error: getSessionError,
        } = await supabase.auth.getSession();
        if (getSessionError) {
          console.error(
            "checkSession: Error calling supabase.auth.getSession() after getting user:",
            getSessionError.message
          );
        } else if (sessionFromGetUser) {
          activeSession = sessionFromGetUser;
          console.log(
            "checkSession: supabase.auth.getSession() returned session for user:",
            activeSession.user.id
          );
        }
      } catch (sessionError) {
        console.error(
          "checkSession: Exception during supabase.auth.getSession():",
          sessionError
        );
      }
    }

    if (!currentUser && hasAuthCookie) {
      console.log(
        "checkSession: Auth cookie present but getUser() failed. Trying getSession() directly."
      );
      try {
        const {
          data: { session },
          error: directSessionError,
        } = await supabase.auth.getSession();
        if (directSessionError) {
          console.error(
            "checkSession: Error in direct getSession() call:",
            directSessionError.message
          );
        } else if (session) {
          console.log(
            "checkSession: Direct getSession() call succeeded:",
            session.user.id
          );
          activeSession = session;
          currentUser = session.user;
        }
      } catch (directSessionError) {
        console.error(
          "checkSession: Exception during direct getSession() call:",
          directSessionError
        );
      }
    }
  } catch (e) {
    console.error("checkSession: Critical exception during auth check:", e);
  }

  if (currentUser) {
    // ALWAYS use metadata roles to avoid database calls and infinite loops
    // Check both app_metadata.role and user_metadata.roles for compatibility
    const appMetadataRole = currentUser.app_metadata?.role;
    const userMetadataRoles = currentUser.user_metadata?.roles || [];

    let metadataRoles: string[] = [];

    // If there's a single role in app_metadata, use it
    if (appMetadataRole) {
      metadataRoles = [appMetadataRole];
    }
    // Otherwise, use the roles array from user_metadata
    else if (userMetadataRoles.length > 0) {
      metadataRoles = userMetadataRoles;
    }

    if (metadataRoles.length > 0) {
      currentRoles = metadataRoles.filter((role) =>
        validUserRoles.includes(role as UserRole)
      ) as UserRole[];

      if (currentRoles.length === 0) {
        console.warn(
          `checkSession: User ${currentUser.id} has invalid metadata roles. Defaulting to CLIENT.`
        );
        currentRoles = ["CLIENT"];
      }
    } else {
      // Default to CLIENT role if no metadata roles exist
      // This prevents database calls in middleware which cause infinite loops
      console.log(
        `checkSession: No metadata roles found for user ${currentUser.id}. Defaulting to CLIENT.`
      );
      currentRoles = ["CLIENT"];
    }
    return {
      session: activeSession,
      userRoles: currentRoles,
      userId: currentUser.id,
      userEmail: currentUser.email ?? null,
    };
  } else {
    console.log(
      "checkSession: No user identified. Returning empty roles and null session/user."
    );
    return { session: null, userRoles: [], userId: null, userEmail: null };
  }
}

// Get the route segment for role-based access control
function getRouteSegment(pathname: string): string | null {
  for (const route of Object.keys(protectedRoutes)) {
    if (pathname.startsWith(route)) {
      return route;
    }
  }
  return null;
}

// Get the allowed roles for a route segment
function getAllowedRoles(routeSegment: string): UserRole[] {
  return protectedRoutes[routeSegment] || [];
}

// Request deduplication to prevent infinite loops
const requestCache = new Map<string, number>();
const CACHE_DURATION = 1000; // 1 second

export async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  // Create a unique key for this request
  const requestKey = `${pathname}-${
    req.headers.get("user-agent")?.slice(0, 50) || "unknown"
  }`;
  const now = Date.now();

  // Check if we've processed this request recently
  const lastProcessed = requestCache.get(requestKey);
  if (lastProcessed && now - lastProcessed < CACHE_DURATION) {
    console.log(`Middleware: Skipping duplicate request for ${pathname}`);
    return NextResponse.next();
  }

  // Update cache
  requestCache.set(requestKey, now);

  // Clean up old entries
  if (requestCache.size > 100) {
    const cutoff = now - CACHE_DURATION * 2;
    const keysToDelete: string[] = [];
    requestCache.forEach((timestamp, key) => {
      if (timestamp < cutoff) {
        keysToDelete.push(key);
      }
    });
    keysToDelete.forEach((key) => requestCache.delete(key));
  }

  // Clear corrupted Supabase cookies to prevent parsing errors
  const corruptedCookies = clearCorruptedAuthCookies(req);

  if (corruptedCookies.length > 0) {
    console.log(
      `Middleware: Clearing ${corruptedCookies.length} corrupted cookies:`,
      corruptedCookies
    );
    // Return early with cookie clearing response
    const clearResponse = NextResponse.next();
    corruptedCookies.forEach((cookieName) => {
      clearResponse.cookies.set(cookieName, "", {
        expires: new Date(0),
        path: "/",
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
      });
    });
    return clearResponse;
  }

  // Create a response object early to be able to set cookies on it
  const response = NextResponse.next({
    request: {
      headers: new Headers(req.headers), // Pass along request headers
    },
  });

  // Log all cookies received by the middleware
  const requestCookieStore = req.cookies; // Use req.cookies for reading incoming cookies
  const allCookies: { [key: string]: string } = {};
  for (const cookie of requestCookieStore.getAll()) {
    allCookies[cookie.name] = cookie.value;
  }
  console.log("Middleware received cookies:", JSON.stringify(allCookies));

  // Use createServerClient from @supabase/ssr
  // const cookieHeaderStore = cookies(); // This is read-only from next/headers
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          // Read from the incoming request cookies
          return requestCookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            // Set on the outgoing response cookies
            response.cookies.set({ name, value, ...options });
          } catch (error) {
            // Handle or log error if setting cookie fails
            console.error(`Error setting cookie ${name}:`, error);
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            // Set an empty value on the outgoing response cookies to remove it
            response.cookies.set({ name, value: "", ...options });
          } catch (error) {
            // Handle or log error if removing cookie fails
            console.error(`Error removing cookie ${name}:`, error);
          }
        },
      },
    }
  );

  console.log(`Middleware processing route: ${pathname}`);

  const isPublic = isPublicRoute(pathname, publicRoutes);

  if (isPublic) {
    console.log(`Public route detected: ${pathname}`);
    if (pathname === "/") {
      console.log(`Middleware: Processing root path /`);
      // Pass the response object to checkSession if it needs to set cookies,
      // though checkSession primarily reads. The supabase client used by checkSession
      // is the one configured above, so it will use response.cookies for set/remove.
      const { userRoles, userId, userEmail, session } = await checkSession(
        supabase,
        req
      );
      console.log(
        `Middleware: Root path checkSession results - UserRoles: ${JSON.stringify(
          userRoles
        )}, UserId: ${userId}, UserEmail: ${userEmail}, Session: ${
          session ? "exists (" + session.user.id + ")" : "null"
        }`
      );

      if (userRoles && userRoles.length > 0 && userId) {
        const dashboardRoute = getDashboardRoute(userRoles);
        console.log(
          "Middleware: User roles for dashboard redirect:",
          JSON.stringify(userRoles)
        );
        console.log(
          `Middleware: User is authenticated on root path, redirecting from / to ${dashboardRoute}`
        );
        // If redirecting, the new cookies set on 'response' might be lost
        // unless the redirect response itself is the one accumulating cookies.
        // NextResponse.redirect creates a new response.
        // It's generally better to modify 'response' and return it,
        // or if redirecting, ensure the redirect response gets the cookies.
        // For now, let's assume Supabase SSR handles this by setting cookies
        // on any NextReponse it's given access to manipulate.
        // The current setup of supabase client using response.cookies.set should handle this.
        return NextResponse.redirect(new URL(dashboardRoute, req.url));
      }
    }
    return response;
  }

  console.log(`Protected route detected: ${pathname}`);
  const {
    session: currentSession,
    userRoles,
    userId,
  } = await checkSession(supabase, req);

  if (!currentSession || !userId) {
    console.log(
      "Middleware: No active session for protected route, redirecting to login."
    );
    const url = new URL("/login", req.url);
    // When redirecting, ensure cookies that might have been set (e.g., by a refresh attempt)
    // are carried over or that the redirect itself doesn't clear them.
    // The current approach of using response.cookies.set on the 'response' object
    // means these cookies are set. If we redirect, this 'response' object is discarded.
    // This needs careful handling.
    // A common pattern is to redirect and let the browser make a new request
    // which will then have the (hopefully) updated cookies.

    // If Supabase client updated cookies on 'response', and we redirect,
    // those cookies on 'response' are NOT sent with THIS redirect.
    // However, the @supabase/ssr library is designed to set cookies
    // such that subsequent requests from the browser will have them.
    // So, a simple redirect here is usually fine.
    return NextResponse.redirect(url);
  }

  if (pathname === "/") {
    console.log(
      `Middleware: Root path (pathname === '/') detected with session. User roles: ${JSON.stringify(
        userRoles
      )}`
    );
    const dashboardUrl = getDashboardRoute(userRoles);
    if (dashboardUrl && dashboardUrl !== "/") {
      console.log(`Middleware: Redirecting from / to ${dashboardUrl}`);
      return NextResponse.redirect(new URL(dashboardUrl, req.url));
    }
  }

  const routeSegment = getRouteSegment(pathname);
  if (routeSegment) {
    const allowedRoles = getAllowedRoles(routeSegment);
    const hasRequiredRole = userRoles.some((role) =>
      allowedRoles.includes(role)
    );

    if (!hasRequiredRole) {
      console.log(
        `User lacks required role for ${pathname}. User roles: ${userRoles.join(
          ", "
        )}, Required roles: ${allowedRoles.join(", ")}`
      );
      return NextResponse.redirect(
        new URL(getDashboardRoute(userRoles), req.url)
      );
    }
  }

  return response; // This response will have any cookies set by Supabase client
}

// Updated matcher to exclude static assets and API routes more effectively.
// This pattern includes:
// - Negative lookaheads for /api, /_next/static, /_next/image, /favicon.ico, /avatars, /img, /static
// - It matches all other paths.
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - avatars (avatar images)
     * - img (other images in /public/img)
     * - static (other static assets in /public/static)
     * - sw.js (service worker)
     * - manifest.json (PWA manifest)
     * - .well-known (well-known URIs)
     */
    "/((?!api|_next/static|_next/image|favicon.ico|avatars|img|static|sw.js|manifest.json|.well-known).*)",
  ],
};
