export type UserRole = 
  | 'SUPER_ADMIN'
  | 'CLIENT'
  | 'PASSENGER'
  | 'AFFILIATE'
  | 'AFFILIATE_DISPATCH'
  | 'CLIENT_COORDINATOR'
  | 'DRIVER';

// Map of roles to their portal paths
export const ROLE_PORTAL_MAP = {
  'SUPER_ADMIN': '/super-admin',
  'CLIENT': '/event-manager',
  'PASSENGER': '/customer',
  'AFFILIATE': '/affiliate',
  'AFFILIATE_DISPATCH': '/affiliate',
  'CLIENT_COORDINATOR': '/event-manager',
  'DRIVER': '/driver'
} as const;

// Map of roles to their display names
export const ROLE_DISPLAY_MAP = {
  'SUPER_ADMIN': 'Super Admin',
  'CLIENT': 'Client',
  'PASSENGER': 'Passenger',
  'AFFILIATE': 'Affiliate',
  'AFFILIATE_DISPATCH': 'Affiliate Dispatch',
  'CLIENT_COORDINATOR': 'Client Coordinator',
  'DRIVER': 'Driver'
} as const;

// Map database role names to application role names
export const mapDbRoleToAppRole = (dbRole: string): UserRole | null => {
  const roleMap: Record<string, UserRole> = {
    'MEMBER': 'CLIENT',
    'VIEWER': 'PASSENGER',
    'AFFILIATE_OWNER': 'AFFILIATE',
    'AFFILIATE_DISPATCHER': 'AFFILIATE_DISPATCH',
    'AFFILIATE_DRIVER': 'DRIVER',
    'CLIENT_COORDINATOR': 'CLIENT_COORDINATOR',
    'EVENT_MANAGER': 'CLIENT', // Legacy role mapping
    'SUPER_ADMIN': 'SUPER_ADMIN',
    'CLIENT': 'CLIENT',
    'PASSENGER': 'PASSENGER',
    'AFFILIATE': 'AFFILIATE',
    'AFFILIATE_DISPATCH': 'AFFILIATE_DISPATCH',
    'DRIVER': 'DRIVER'
  };
  return roleMap[dbRole] || null;
};

// Helper function to check if a string is a valid role
export function isValidRole(role: string): role is UserRole {
  return [
    'SUPER_ADMIN',
    'CLIENT',
    'PASSENGER',
    'AFFILIATE',
    'AFFILIATE_DISPATCH',
    'CLIENT_COORDINATOR',
    'DRIVER'
  ].includes(role);
}

// Helper function to get the portal path for a role
export function getPortalPath(role: UserRole): string {
  return ROLE_PORTAL_MAP[role];
}

// Helper function to get the display name for a role
export function getDisplayName(role: UserRole): string {
  return ROLE_DISPLAY_MAP[role];
}

export interface UserProfile {
  id: string;
  roles: UserRole[];
  email: string | null;
  first_name: string | null;
  last_name: string | null;
  company_name: string | null;
  phone_number: string | null;
  created_at: string;
  updated_at: string | null;
}

import { hasRole as sharedHasRole } from '@/app/lib/auth';

export const canManageEvents = (profile: UserProfile): boolean => {
  return sharedHasRole(profile.roles, ['CLIENT', 'CLIENT_COORDINATOR', 'SUPER_ADMIN']);
};

export const isClient = (profile: UserProfile): boolean => {
  return sharedHasRole(profile.roles, 'CLIENT');
};

export const canUpgradeToClientCoordinator = (profile: UserProfile): boolean => {
  return sharedHasRole(profile.roles, 'CLIENT') && !sharedHasRole(profile.roles, ['SUPER_ADMIN', 'CLIENT_COORDINATOR']);
};

export function toUserRoles(roles: string | string[]): UserRole[] {
  // Normalize input to array
  const rolesArray = Array.isArray(roles) ? roles : [roles];
  return rolesArray
    .map(role => {
      if (!role) return null;
      // Try to map the role
      const mappedRole = mapDbRoleToAppRole(role);
      if (mappedRole) return mappedRole;
      // If role is already in the correct format, return it
      const validRoles: UserRole[] = [
        'SUPER_ADMIN',
        'CLIENT',
        'PASSENGER',
        'AFFILIATE',
        'AFFILIATE_DISPATCH',
        'CLIENT_COORDINATOR',
        'DRIVER'
      ];
      if (validRoles.includes(role as UserRole)) {
        return role as UserRole;
      }
      if (process.env.NODE_ENV !== 'production') {
        console.warn('Unknown role encountered and filtered out:', role);
      }
      return null;
    })
    .filter((role): role is UserRole => role !== null);
} 