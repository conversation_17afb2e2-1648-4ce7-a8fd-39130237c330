import { useState, useEffect, useCallback } from 'react';
// UI Components
import { useToast } from '@/app/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/app/components/ui/dialog';
import { Button } from '@/app/components/ui/button';
import { Input } from '@/app/components/ui/input';
import { Label } from '@/app/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select';
import { Avatar, AvatarFallback } from '@/app/components/ui/avatar';

// Icons
import { Search, Loader2, UserPlus } from 'lucide-react';

// Types
type UserRole = 'ADMIN' | 'MEMBER' | 'VIEWER';

interface UserProfile {
  id: string;
  email: string;
  first_name: string | null;
  last_name: string | null;
  avatar_url?: string | null;
}

interface TenantUser {
  id: string;
  user_id: string;
  email: string;
  full_name: string | null;
  role: UserRole;
}

interface AddUserDialogProps {
  tenantId: string;
  user: TenantUser | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => Promise<void> | void;
}

const ROLES = [
  { value: 'ADMIN' as const, label: 'Admin' },
  { value: 'MEMBER' as const, label: 'Member' },
  { value: 'VIEWER' as const, label: 'Viewer' },
];

const DEBOUNCE_DELAY = 300;

export function AddUserDialog({
  tenantId,
  user,
  open,
  onOpenChange,
  onSuccess,
}: AddUserDialogProps) {
  const { toast } = useToast();
  const [email, setEmail] = useState('');
  const [role, setRole] = useState<UserRole>('MEMBER' as UserRole);
  const [isLoading, setIsLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<UserProfile[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (user) {
      setEmail(user.email);
      setRole(user.role as UserRole);
    } else {
      setEmail('');
      setRole('MEMBER' as UserRole);
    }
  }, [user, open]);

  const searchUsers = useCallback(async (query: string) => {
    if (query.length < 3) {
      setSearchResults([]);
      return;
    }

    try {
      setIsSearching(true);
      const response = await fetch(
        `/api/users/search?q=${encodeURIComponent(query)}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || 'Failed to search users. Please try again.'
        );
      }

      const { data } = await response.json();
      setSearchResults(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error searching users:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to search users',
        variant: 'destructive',
      });
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, [toast]);

  // Debounce search
  useEffect(() => {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    if (searchQuery.trim().length >= 3) {
      const timer = setTimeout(() => {
        searchUsers(searchQuery);
      }, DEBOUNCE_DELAY);
      setDebounceTimer(timer);
    } else {
      setSearchResults([]);
    }

    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [searchQuery, searchUsers]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleRoleChange = (value: string) => {
    setRole(value as UserRole);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      toast({
        title: 'Error',
        description: 'Please enter an email address',
        variant: 'destructive',
      });
      return;
    }
    
    if (!role) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsLoading(true);
      
      // First, check if user exists and get their ID
      const userResponse = await fetch(`/api/users/lookup?email=${encodeURIComponent(email)}`);
      if (!userResponse.ok) {
        throw new Error('Failed to find user');
      }
      
      const userData = await userResponse.json();
      if (!userData?.id) {
        throw new Error('User not found');
      }

      const url = user 
        ? `/api/tenants/${tenantId}/users/${user.id}` 
        : `/api/tenants/${tenantId}/users`;
      
      const method = user ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ 
          userId: userData.id, 
          email, 
          role,
          tenantId 
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || 'Failed to save user. Please try again.'
        );
      }

      const result = await response.json();
      
      toast({
        title: 'Success',
        description: user ? 'User updated successfully' : 'User added successfully',
      });
      
      // Reset form
      if (!user) {
        setEmail('');
        setRole('MEMBER');
        setSearchQuery('');
        setSearchResults([]);
      }
      
      onOpenChange(false);
      
      // Call onSuccess callback if provided
      if (typeof onSuccess === 'function') {
        await Promise.resolve(onSuccess());
      }
    } catch (error) {
      console.error('Error saving user:', error);
      
      let errorMessage = 'Failed to save user';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }
      
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            {user ? 'Edit User Access' : 'Invite User to Tenant'}
          </DialogTitle>
          <DialogDescription className="text-muted-foreground">
            {user 
              ? 'Update user permissions for this tenant' 
              : 'Search for a user by email to add them to this tenant'}
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="search">Search by Email</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  id="search"
                  type="email"
                  value={searchQuery}
                  onChange={handleSearchChange}
                  placeholder="<EMAIL>"
                  className="pl-10"
                  disabled={!!user}
                />
                {isSearching && (
                  <Loader2 className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 animate-spin text-muted-foreground" />
                )}
              </div>
              
              {searchResults.length > 0 && (
                <div className="mt-2 rounded-md border bg-popover">
                  {searchResults.map((result) => (
                    <div
                      key={result.id}
                      className="flex cursor-pointer items-center space-x-3 p-3 hover:bg-accent"
                      onClick={() => {
                        setEmail(result.email || '');
                        setSearchResults([]);
                      }}
                    >
                      <Avatar className="h-8 w-8">
                        <AvatarFallback>
                          {result.first_name?.[0] || result.email?.[0] || 'U'}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <p className="text-sm font-medium">
                          {result.first_name && result.last_name
                            ? `${result.first_name} ${result.last_name}`
                            : result.email?.split('@')[0]}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {result.email}
                        </p>
                      </div>
                      <UserPlus className="h-4 w-4 text-muted-foreground" />
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            {email && (
              <div className="space-y-2">
                <Label>Selected User</Label>
                <div className="flex items-center space-x-3 rounded-md border p-3">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback>
                      {email[0].toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="font-medium">{email}</p>
                  </div>
                </div>
              </div>
            )}
            
            <div className="space-y-2">
              <Label>Role</Label>
              <Select 
                value={role} 
                onValueChange={(value: string) => handleRoleChange(value as UserRole)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  {ROLES.map((r) => (
                    <SelectItem key={r.value} value={r.value}>
                      {r.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                {role === 'ADMIN' 
                  ? 'Can manage all tenant settings and users'
                  : role === 'MEMBER'
                  ? 'Can create and edit content'
                  : 'Can only view content'}
              </p>
            </div>
          </div>
          
          <DialogFooter className="flex justify-end gap-2">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => {
                onOpenChange(false);
                setSearchQuery('');
                setSearchResults([]);
              }}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isLoading || !email || !role}
              className="min-w-[120px]"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {user ? 'Updating...' : 'Inviting...'}
                </>
              ) : user ? (
                'Update User'
              ) : (
                'Send Invite'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}