# Cloud Database Configuration Template
# Copy this file to .env.cloud and fill in your actual values

# ==============================================
# SUPABASE CLOUD CONFIGURATION
# ==============================================
# Get these values from your Supabase project dashboard
CLOUD_SUPABASE_URL=https://your-project-ref.supabase.co
CLOUD_SUPABASE_ANON_KEY=your-anon-key-here
CLOUD_SUPABASE_SERVICE_KEY=your-service-role-key-here

# ==============================================
# PRODUCTION ENVIRONMENT VARIABLES
# ==============================================

# Application Configuration
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com
PORT=3000

# Supabase Configuration (for production)
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
SUPABASE_JWT_SECRET=your-jwt-secret-here

# Database Configuration (direct connection)
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
POSTGRES_HOST=db.your-project-ref.supabase.co
POSTGRES_PORT=5432
POSTGRES_DB=postgres
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-service-role-key

# Authentication Configuration
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your-production-secret-key-change-this

# Security Configuration
JWT_EXPIRY=24h
BYPASS_AUTH=false

# ==============================================
# OPTIONAL: EXTERNAL SERVICES
# ==============================================

# Email Configuration (if using SMTP)
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=your-smtp-user
SMTP_PASS=your-smtp-password
SMTP_FROM=<EMAIL>

# Storage Configuration (if using external S3)
S3_ACCESS_KEY=your-s3-access-key
S3_SECRET_KEY=your-s3-secret-key
S3_REGION=us-east-1
S3_BUCKET=your-bucket-name

# Monitoring Configuration
SENTRY_DSN=your-sentry-dsn
NEXT_PUBLIC_SENTRY_DSN=your-public-sentry-dsn

# ==============================================
# INSTRUCTIONS
# ==============================================
# 1. Copy this file: cp .env.cloud.template .env.cloud
# 2. Fill in your actual Supabase project values
# 3. Generate a secure NEXTAUTH_SECRET: openssl rand -base64 32
# 4. Run the migration script: ./scripts/migrate-to-cloud.sh
# 5. Test the connection: npm run test:db-connection
