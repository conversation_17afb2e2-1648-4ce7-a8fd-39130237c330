*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}
Middleware processing route: /super-admin/users/40897edc-6346-415c-9229-8b6c5fc2d770/permissions
Protected route detected: /super-admin/users/40897edc-6346-415c-9229-8b6c5fc2d770/permissions
checkSession: Supabase SSR Auth cookie present in headers (pattern match): true
checkSession: supabase.auth.getUser() returned user: 46c506c9-d04a-4f1b-ad10-33247526f770 <EMAIL>
Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.
checkSession: supabase.auth.getSession() returned session for user: 46c506c9-d04a-4f1b-ad10-33247526f770
 ○ Compiling /super-admin/users/[userId]/permissions ...

warn - The `darkMode` option in your Tailwind CSS configuration is set to `false`, which now behaves the same as `media`.
warn - Change `darkMode` to `media` or remove it entirely.
warn - https://tailwindcss.com/docs/upgrade-guide#remove-dark-mode-configuration
 ✓ Compiled /super-admin/users/[userId]/permissions in 6.1s (2923 modules)
 ✓ Compiled in 289ms (1426 modules)
CLIENT_LOG: RootLayout - Component rendering (top level)
 GET /super-admin/users/40897edc-6346-415c-9229-8b6c5fc2d770/permissions 200 in 6709ms
 ○ Compiling /api/super-admin/users/[userId]/permissions ...
 ⚠ ./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
./node_modules/@supabase/realtime-js/dist/main/index.js
./node_modules/@supabase/supabase-js/dist/module/index.js
./app/api/super-admin/tenants/route.ts
 ⚠ ./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
./node_modules/@supabase/realtime-js/dist/main/index.js
./node_modules/@supabase/supabase-js/dist/module/index.js
./app/api/super-admin/tenants/route.ts
 ⚠ ./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
./node_modules/@supabase/realtime-js/dist/main/index.js
./node_modules/@supabase/supabase-js/dist/module/index.js
./app/api/super-admin/tenants/route.ts
[DEBUG] Session in tenants API: {
  id: '46c506c9-d04a-4f1b-ad10-33247526f770',
  email: '<EMAIL>',
  access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dRglIphJG-m3c9B70WlG_tq3ucnB71LkyycG-E0O9zs',
  token_type: 'bearer',
  expires_in: 3600,
  expires_at: **********,
  refresh_token: '1s97bRWhGWBEL2hxX7kjWg',
  user: {
    id: '46c506c9-d04a-4f1b-ad10-33247526f770',
    aud: 'authenticated',
    role: 'authenticated',
    email: '<EMAIL>',
    email_confirmed_at: '2025-06-14T21:38:43.806328Z',
    phone: '',
    confirmed_at: '2025-06-14T21:38:43.806328Z',
    last_sign_in_at: '2025-06-15T17:23:09.295985Z',
    app_metadata: { provider: 'email', providers: [Array] },
    user_metadata: {
      email: '<EMAIL>',
      email_verified: true,
      phone_verified: false,
      roles: [Array],
      sub: '46c506c9-d04a-4f1b-ad10-33247526f770'
    },
    identities: [ [Object] ],
    created_at: '2025-06-14T21:38:43.673958Z',
    updated_at: '2025-06-15T18:38:47.586336Z',
    is_anonymous: false
  },
  roles: [ 'SUPER_ADMIN' ]
}
Super-admin tenants API - Starting request
Super-admin tenants API - Auth header present: false
Super-admin tenants API - Found 2 tenants
 GET /api/super-admin/tenants 200 in 1233ms
[DEBUG] Session in tenants API: {
  id: '46c506c9-d04a-4f1b-ad10-33247526f770',
  email: '<EMAIL>',
  access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dRglIphJG-m3c9B70WlG_tq3ucnB71LkyycG-E0O9zs',
  token_type: 'bearer',
  expires_in: 3600,
  expires_at: **********,
  refresh_token: '1s97bRWhGWBEL2hxX7kjWg',
  user: {
    id: '46c506c9-d04a-4f1b-ad10-33247526f770',
    aud: 'authenticated',
    role: 'authenticated',
    email: '<EMAIL>',
    email_confirmed_at: '2025-06-14T21:38:43.806328Z',
    phone: '',
    confirmed_at: '2025-06-14T21:38:43.806328Z',
    last_sign_in_at: '2025-06-15T17:23:09.295985Z',
    app_metadata: { provider: 'email', providers: [Array] },
    user_metadata: {
      email: '<EMAIL>',
      email_verified: true,
      phone_verified: false,
      roles: [Array],
      sub: '46c506c9-d04a-4f1b-ad10-33247526f770'
    },
    identities: [ [Object] ],
    created_at: '2025-06-14T21:38:43.673958Z',
    updated_at: '2025-06-15T18:38:47.586336Z',
    is_anonymous: false
  },
  roles: [ 'SUPER_ADMIN' ]
}
Super-admin tenants API - Starting request
Super-admin tenants API - Auth header present: false
Super-admin tenants API - Found 2 tenants
 GET /api/super-admin/tenants 200 in 34ms
[DEBUG] Session in tenants API: {
  id: '46c506c9-d04a-4f1b-ad10-33247526f770',
  email: '<EMAIL>',
  access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dRglIphJG-m3c9B70WlG_tq3ucnB71LkyycG-E0O9zs',
  token_type: 'bearer',
  expires_in: 3600,
  expires_at: **********,
  refresh_token: '1s97bRWhGWBEL2hxX7kjWg',
  user: {
    id: '46c506c9-d04a-4f1b-ad10-33247526f770',
    aud: 'authenticated',
    role: 'authenticated',
    email: '<EMAIL>',
    email_confirmed_at: '2025-06-14T21:38:43.806328Z',
    phone: '',
    confirmed_at: '2025-06-14T21:38:43.806328Z',
    last_sign_in_at: '2025-06-15T17:23:09.295985Z',
    app_metadata: { provider: 'email', providers: [Array] },
    user_metadata: {
      email: '<EMAIL>',
      email_verified: true,
      phone_verified: false,
      roles: [Array],
      sub: '46c506c9-d04a-4f1b-ad10-33247526f770'
    },
    identities: [ [Object] ],
    created_at: '2025-06-14T21:38:43.673958Z',
    updated_at: '2025-06-15T18:38:47.586336Z',
    is_anonymous: false
  },
  roles: [ 'SUPER_ADMIN' ]
}
Super-admin tenants API - Starting request
Super-admin tenants API - Auth header present: false
Super-admin tenants API - Found 2 tenants
 GET /api/super-admin/tenants 200 in 27ms
[DEBUG] Session in tenants API: {
  id: '46c506c9-d04a-4f1b-ad10-33247526f770',
  email: '<EMAIL>',
  access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dRglIphJG-m3c9B70WlG_tq3ucnB71LkyycG-E0O9zs',
  token_type: 'bearer',
  expires_in: 3600,
  expires_at: **********,
  refresh_token: '1s97bRWhGWBEL2hxX7kjWg',
  user: {
    id: '46c506c9-d04a-4f1b-ad10-33247526f770',
    aud: 'authenticated',
    role: 'authenticated',
    email: '<EMAIL>',
    email_confirmed_at: '2025-06-14T21:38:43.806328Z',
    phone: '',
    confirmed_at: '2025-06-14T21:38:43.806328Z',
    last_sign_in_at: '2025-06-15T17:23:09.295985Z',
    app_metadata: { provider: 'email', providers: [Array] },
    user_metadata: {
      email: '<EMAIL>',
      email_verified: true,
      phone_verified: false,
      roles: [Array],
      sub: '46c506c9-d04a-4f1b-ad10-33247526f770'
    },
    identities: [ [Object] ],
    created_at: '2025-06-14T21:38:43.673958Z',
    updated_at: '2025-06-15T18:38:47.586336Z',
    is_anonymous: false
  },
  roles: [ 'SUPER_ADMIN' ]
}
Super-admin tenants API - Starting request
Super-admin tenants API - Auth header present: false
Super-admin tenants API - Found 2 tenants
 GET /api/super-admin/tenants 200 in 26ms
[DEBUG] Session in tenants API: {
  id: '46c506c9-d04a-4f1b-ad10-33247526f770',
  email: '<EMAIL>',
  access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dRglIphJG-m3c9B70WlG_tq3ucnB71LkyycG-E0O9zs',
  token_type: 'bearer',
  expires_in: 3600,
  expires_at: **********,
  refresh_token: '1s97bRWhGWBEL2hxX7kjWg',
  user: {
    id: '46c506c9-d04a-4f1b-ad10-33247526f770',
    aud: 'authenticated',
    role: 'authenticated',
    email: '<EMAIL>',
    email_confirmed_at: '2025-06-14T21:38:43.806328Z',
    phone: '',
    confirmed_at: '2025-06-14T21:38:43.806328Z',
    last_sign_in_at: '2025-06-15T17:23:09.295985Z',
    app_metadata: { provider: 'email', providers: [Array] },
    user_metadata: {
      email: '<EMAIL>',
      email_verified: true,
      phone_verified: false,
      roles: [Array],
      sub: '46c506c9-d04a-4f1b-ad10-33247526f770'
    },
    identities: [ [Object] ],
    created_at: '2025-06-14T21:38:43.673958Z',
    updated_at: '2025-06-15T18:38:47.586336Z',
    is_anonymous: false
  },
  roles: [ 'SUPER_ADMIN' ]
}
Super-admin tenants API - Starting request
Super-admin tenants API - Auth header present: false
Super-admin tenants API - Found 2 tenants
 GET /api/super-admin/tenants 200 in 32ms
[DEBUG] Session in tenants API: {
  id: '46c506c9-d04a-4f1b-ad10-33247526f770',
  email: '<EMAIL>',
  access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dRglIphJG-m3c9B70WlG_tq3ucnB71LkyycG-E0O9zs',
  token_type: 'bearer',
  expires_in: 3600,
  expires_at: **********,
  refresh_token: '1s97bRWhGWBEL2hxX7kjWg',
  user: {
    id: '46c506c9-d04a-4f1b-ad10-33247526f770',
    aud: 'authenticated',
    role: 'authenticated',
    email: '<EMAIL>',
    email_confirmed_at: '2025-06-14T21:38:43.806328Z',
    phone: '',
    confirmed_at: '2025-06-14T21:38:43.806328Z',
    last_sign_in_at: '2025-06-15T17:23:09.295985Z',
    app_metadata: { provider: 'email', providers: [Array] },
    user_metadata: {
      email: '<EMAIL>',
      email_verified: true,
      phone_verified: false,
      roles: [Array],
      sub: '46c506c9-d04a-4f1b-ad10-33247526f770'
    },
    identities: [ [Object] ],
    created_at: '2025-06-14T21:38:43.673958Z',
    updated_at: '2025-06-15T18:38:47.586336Z',
    is_anonymous: false
  },
  roles: [ 'SUPER_ADMIN' ]
}
Super-admin tenants API - Starting request
Super-admin tenants API - Auth header present: false
Super-admin tenants API - Found 2 tenants
 GET /api/super-admin/tenants 200 in 33ms
[DEBUG] Session in tenants API: {
  id: '46c506c9-d04a-4f1b-ad10-33247526f770',
  email: '<EMAIL>',
  access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dRglIphJG-m3c9B70WlG_tq3ucnB71LkyycG-E0O9zs',
  token_type: 'bearer',
  expires_in: 3600,
  expires_at: **********,
  refresh_token: '1s97bRWhGWBEL2hxX7kjWg',
  user: {
    id: '46c506c9-d04a-4f1b-ad10-33247526f770',
    aud: 'authenticated',
    role: 'authenticated',
    email: '<EMAIL>',
    email_confirmed_at: '2025-06-14T21:38:43.806328Z',
    phone: '',
    confirmed_at: '2025-06-14T21:38:43.806328Z',
    last_sign_in_at: '2025-06-15T17:23:09.295985Z',
    app_metadata: { provider: 'email', providers: [Array] },
    user_metadata: {
      email: '<EMAIL>',
      email_verified: true,
      phone_verified: false,
      roles: [Array],
      sub: '46c506c9-d04a-4f1b-ad10-33247526f770'
    },
    identities: [ [Object] ],
    created_at: '2025-06-14T21:38:43.673958Z',
    updated_at: '2025-06-15T18:38:47.586336Z',
    is_anonymous: false
  },
  roles: [ 'SUPER_ADMIN' ]
}
Super-admin tenants API - Starting request
Super-admin tenants API - Auth header present: false
Super-admin tenants API - Found 2 tenants
 GET /api/super-admin/tenants 200 in 37ms
[DEBUG] Session in tenants API: {
  id: '46c506c9-d04a-4f1b-ad10-33247526f770',
  email: '<EMAIL>',
  access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dRglIphJG-m3c9B70WlG_tq3ucnB71LkyycG-E0O9zs',
  token_type: 'bearer',
  expires_in: 3600,
  expires_at: **********,
  refresh_token: '1s97bRWhGWBEL2hxX7kjWg',
  user: {
    id: '46c506c9-d04a-4f1b-ad10-33247526f770',
    aud: 'authenticated',
    role: 'authenticated',
    email: '<EMAIL>',
    email_confirmed_at: '2025-06-14T21:38:43.806328Z',
    phone: '',
    confirmed_at: '2025-06-14T21:38:43.806328Z',
    last_sign_in_at: '2025-06-15T17:23:09.295985Z',
    app_metadata: { provider: 'email', providers: [Array] },
    user_metadata: {
      email: '<EMAIL>',
      email_verified: true,
      phone_verified: false,
      roles: [Array],
      sub: '46c506c9-d04a-4f1b-ad10-33247526f770'
    },
    identities: [ [Object] ],
    created_at: '2025-06-14T21:38:43.673958Z',
    updated_at: '2025-06-15T18:38:47.586336Z',
    is_anonymous: false
  },
  roles: [ 'SUPER_ADMIN' ]
}
Super-admin tenants API - Starting request
Super-admin tenants API - Auth header present: false
[PERMISSIONS API] Request headers: {
  accept: '*/*',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'en-US,en;q=0.7',
  connection: 'keep-alive',
  cookie: 'sb-127-auth-token=base64-eyJhY2Nlc3NfdG9rZW4iOiJleUpoYkdjaU9pSklVekkxTmlJc0luUjVjQ0k2SWtwWFZDSjkuZXlKcGMzTWlPaUpvZEhSd09pOHZNVEkzTGpBdU1DNHhPalUwTXpJeEwyRjFkR2d2ZGpFaUxDSnpkV0lpT2lJME5tTTFNRFpqT1Mxa01EUmhMVFJtTVdJdFlXUXhNQzB6TXpJME56VXlObVkzTnpBaUxDSmhkV1FpT2lKaGRYUm9aVzUwYVdOaGRHVmtJaXdpWlhod0lqb3hOelV3TURFMk16STNMQ0pwWVhRaU9qRTNOVEF3TVRJM01qY3NJbVZ0WVdsc0lqb2lZV1J0YVc1dmRuTnJhVGRBWjIxaGFXd3VZMjl0SWl3aWNHaHZibVVpT2lJaUxDSmhjSEJmYldWMFlXUmhkR0VpT25zaWNISnZkbWxrWlhJaU9pSmxiV0ZwYkNJc0luQnliM1pwWkdWeWN5STZXeUpsYldGcGJDSmRmU3dpZFhObGNsOXRaWFJoWkdGMFlTSTZleUpsYldGcGJDSTZJbUZrYldsdWIzWnphMmszUUdkdFlXbHNMbU52YlNJc0ltVnRZV2xzWDNabGNtbG1hV1ZrSWpwMGNuVmxMQ0p3YUc5dVpWOTJaWEpwWm1sbFpDSTZabUZzYzJVc0luSnZiR1Z6SWpwYklsTlZVRVZTWDBGRVRVbE9JbDBzSW5OMVlpSTZJalEyWXpVd05tTTVMV1F3TkdFdE5HWXhZaTFoWkRFd0xUTXpNalEzTlRJMlpqYzNNQ0o5TENKeWIy*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
  host: 'localhost:3003',
  referer: 'http://localhost:3003/super-admin/users/40897edc-6346-415c-9229-8b6c5fc2d770/permissions',
  'sec-ch-ua': '"Brave";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"macOS"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-origin',
  'sec-gpc': '1',
  'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'x-forwarded-for': '::1',
  'x-forwarded-host': 'localhost:3003',
  'x-forwarded-port': '3003',
  'x-forwarded-proto': 'http'
}
[PERMISSIONS API] Cookies received: [
  {
    name: 'sb-127-auth-token',
    value: 'base64-eyJhY2Nlc3NfdG9rZW4iOiJleUpoYkdjaU9pSklVekkxTmlJc0luUjVjQ0k2SWtwWFZDSjkuZXlKcGMzTWlPaUpvZEhSd09pOHZNVEkzTGpBdU1DNHhPalUwTXpJeEwyRjFkR2d2ZGpFaUxDSnpkV0lpT2lJME5tTTFNRFpqT1Mxa01EUmhMVFJtTVdJdFlXUXhNQzB6TXpJME56VXlObVkzTnpBaUxDSmhkV1FpT2lKaGRYUm9aVzUwYVdOaGRHVmtJaXdpWlhod0lqb3hOelV3TURFMk16STNMQ0pwWVhRaU9qRTNOVEF3TVRJM01qY3NJbVZ0WVdsc0lqb2lZV1J0YVc1dmRuTnJhVGRBWjIxaGFXd3VZMjl0SWl3aWNHaHZibVVpT2lJaUxDSmhjSEJmYldWMFlXUmhkR0VpT25zaWNISnZkbWxrWlhJaU9pSmxiV0ZwYkNJc0luQnliM1pwWkdWeWN5STZXeUpsYldGcGJDSmRmU3dpZFhObGNsOXRaWFJoWkdGMFlTSTZleUpsYldGcGJDSTZJbUZrYldsdWIzWnphMmszUUdkdFlXbHNMbU52YlNJc0ltVnRZV2xzWDNabGNtbG1hV1ZrSWpwMGNuVmxMQ0p3YUc5dVpWOTJaWEpwWm1sbFpDSTZabUZzYzJVc0luSnZiR1Z6SWpwYklsTlZVRVZTWDBGRVRVbE9JbDBzSW5OMVlpSTZJalEyWXpVd05tTTVMV1F3TkdFdE5HWXhZaTFoWkRFd0xUTXpNalEzTlRJMlpqYzNNQ0o5TENKeWIy*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
  }
]
[PERMISSIONS API] Handler called for user: 40897edc-6346-415c-9229-8b6c5fc2d770
[PERMISSIONS API] About to call getSession
[PERMISSIONS API] getSession result: {
  id: '46c506c9-d04a-4f1b-ad10-33247526f770',
  email: '<EMAIL>',
  access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dRglIphJG-m3c9B70WlG_tq3ucnB71LkyycG-E0O9zs',
  token_type: 'bearer',
  expires_in: 3600,
  expires_at: **********,
  refresh_token: '1s97bRWhGWBEL2hxX7kjWg',
  user: {
    id: '46c506c9-d04a-4f1b-ad10-33247526f770',
    aud: 'authenticated',
    role: 'authenticated',
    email: '<EMAIL>',
    email_confirmed_at: '2025-06-14T21:38:43.806328Z',
    phone: '',
    confirmed_at: '2025-06-14T21:38:43.806328Z',
    last_sign_in_at: '2025-06-15T17:23:09.295985Z',
    app_metadata: { provider: 'email', providers: [Array] },
    user_metadata: {
      email: '<EMAIL>',
      email_verified: true,
      phone_verified: false,
      roles: [Array],
      sub: '46c506c9-d04a-4f1b-ad10-33247526f770'
    },
    identities: [ [Object] ],
    created_at: '2025-06-14T21:38:43.673958Z',
    updated_at: '2025-06-15T18:38:47.586336Z',
    is_anonymous: false
  },
  roles: [ 'SUPER_ADMIN' ]
}
Session roles array: [ 'SUPER_ADMIN' ]
Normalized user roles: [ 'SUPER_ADMIN' ]
Supabase client initialized successfully
Super-admin tenants API - Found 2 tenants
 GET /api/super-admin/tenants 200 in 23ms
Current user profile: {
  "id": "46c506c9-d04a-4f1b-ad10-33247526f770",
  "role": "SUPER_ADMIN",
  "full_name": "adminovski7",
  "avatar_url": null,
  "email": "<EMAIL>",
  "created_at": "2025-06-14T21:38:43.668039+00:00",
  "updated_at": "2025-06-14T21:38:43.668039+00:00",
  "roles": [
    "SUPER_ADMIN"
  ],
  "first_name": null,
  "last_name": null,
  "phone_number": null,
  "company_name": null
}
User has SUPER_ADMIN role: true
Fetching permissions for user ID: 40897edc-6346-415c-9229-8b6c5fc2d770
[DEBUG] Session in tenants API: {
  id: '46c506c9-d04a-4f1b-ad10-33247526f770',
  email: '<EMAIL>',
  access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dRglIphJG-m3c9B70WlG_tq3ucnB71LkyycG-E0O9zs',
  token_type: 'bearer',
  expires_in: 3600,
  expires_at: **********,
  refresh_token: '1s97bRWhGWBEL2hxX7kjWg',
  user: {
    id: '46c506c9-d04a-4f1b-ad10-33247526f770',
    aud: 'authenticated',
    role: 'authenticated',
    email: '<EMAIL>',
    email_confirmed_at: '2025-06-14T21:38:43.806328Z',
    phone: '',
    confirmed_at: '2025-06-14T21:38:43.806328Z',
    last_sign_in_at: '2025-06-15T17:23:09.295985Z',
    app_metadata: { provider: 'email', providers: [Array] },
    user_metadata: {
      email: '<EMAIL>',
      email_verified: true,
      phone_verified: false,
      roles: [Array],
      sub: '46c506c9-d04a-4f1b-ad10-33247526f770'
    },
    identities: [ [Object] ],
    created_at: '2025-06-14T21:38:43.673958Z',
    updated_at: '2025-06-15T18:38:47.586336Z',
    is_anonymous: false
  },
  roles: [ 'SUPER_ADMIN' ]
}
Super-admin tenants API - Starting request
Super-admin tenants API - Auth header present: false
Super-admin tenants API - Found 2 tenants
 GET /api/super-admin/tenants 200 in 22ms
Found permissions: []
Fetching target user profile for ID: 40897edc-6346-415c-9229-8b6c5fc2d770
Target user profile: {
  "id": "40897edc-6346-415c-9229-8b6c5fc2d770",
  "email": "<EMAIL>",
  "full_name": "Test Client",
  "roles": [
    "CLIENT"
  ],
  "created_at": "2025-06-14T20:55:14.624466+00:00"
}
Fetching organizations for user ID: 40897edc-6346-415c-9229-8b6c5fc2d770
Error fetching user organizations: {
  code: '42703',
  details: null,
  hint: null,
  message: 'column user_organizations.tenant_id does not exist'
}
Continuing without organization data
 GET /api/super-admin/users/40897edc-6346-415c-9229-8b6c5fc2d770/permissions 200 in 1494ms
[DEBUG] Session in tenants API: {
  id: '46c506c9-d04a-4f1b-ad10-33247526f770',
  email: '<EMAIL>',
  access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dRglIphJG-m3c9B70WlG_tq3ucnB71LkyycG-E0O9zs',
  token_type: 'bearer',
  expires_in: 3600,
  expires_at: **********,
  refresh_token: '1s97bRWhGWBEL2hxX7kjWg',
  user: {
    id: '46c506c9-d04a-4f1b-ad10-33247526f770',
    aud: 'authenticated',
    role: 'authenticated',
    email: '<EMAIL>',
    email_confirmed_at: '2025-06-14T21:38:43.806328Z',
    phone: '',
    confirmed_at: '2025-06-14T21:38:43.806328Z',
    last_sign_in_at: '2025-06-15T17:23:09.295985Z',
    app_metadata: { provider: 'email', providers: [Array] },
    user_metadata: {
      email: '<EMAIL>',
      email_verified: true,
      phone_verified: false,
      roles: [Array],
      sub: '46c506c9-d04a-4f1b-ad10-33247526f770'
    },
    identities: [ [Object] ],
    created_at: '2025-06-14T21:38:43.673958Z',
    updated_at: '2025-06-15T18:38:47.586336Z',
    is_anonymous: false
  },
  roles: [ 'SUPER_ADMIN' ]
}
Super-admin tenants API - Starting request
Super-admin tenants API - Auth header present: false
Super-admin tenants API - Found 2 tenants
 GET /api/super-admin/tenants 200 in 51ms
 ⚠ ./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
./node_modules/@supabase/realtime-js/dist/main/index.js
./node_modules/@supabase/supabase-js/dist/module/index.js
./app/api/super-admin/tenants/route.ts
 ⚠ ./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
./node_modules/@supabase/realtime-js/dist/main/index.js
./node_modules/@supabase/supabase-js/dist/module/index.js
./app/api/super-admin/users/[userId]/permissions/route.ts
 ⚠ ./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
./node_modules/@supabase/realtime-js/dist/main/index.js
./node_modules/@supabase/supabase-js/dist/module/index.js
./app/api/super-admin/users/[userId]/permissions/route.ts
 ⚠ ./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
Critical dependency: the request of a dependency is an expression

Import trace for requested module:
./node_modules/@supabase/realtime-js/dist/main/RealtimeClient.js
./node_modules/@supabase/realtime-js/dist/main/index.js
./node_modules/@supabase/supabase-js/dist/module/index.js
./app/api/super-admin/tenants/route.ts
[PERMISSIONS API] Request headers: {
  accept: '*/*',
  'accept-encoding': 'gzip, deflate, br, zstd',
  'accept-language': 'en-US,en;q=0.7',
  connection: 'keep-alive',
  cookie: 'sb-127-auth-token=base64-eyJhY2Nlc3NfdG9rZW4iOiJleUpoYkdjaU9pSklVekkxTmlJc0luUjVjQ0k2SWtwWFZDSjkuZXlKcGMzTWlPaUpvZEhSd09pOHZNVEkzTGpBdU1DNHhPalUwTXpJeEwyRjFkR2d2ZGpFaUxDSnpkV0lpT2lJME5tTTFNRFpqT1Mxa01EUmhMVFJtTVdJdFlXUXhNQzB6TXpJME56VXlObVkzTnpBaUxDSmhkV1FpT2lKaGRYUm9aVzUwYVdOaGRHVmtJaXdpWlhod0lqb3hOelV3TURFMk16STNMQ0pwWVhRaU9qRTNOVEF3TVRJM01qY3NJbVZ0WVdsc0lqb2lZV1J0YVc1dmRuTnJhVGRBWjIxaGFXd3VZMjl0SWl3aWNHaHZibVVpT2lJaUxDSmhjSEJmYldWMFlXUmhkR0VpT25zaWNISnZkbWxrWlhJaU9pSmxiV0ZwYkNJc0luQnliM1pwWkdWeWN5STZXeUpsYldGcGJDSmRmU3dpZFhObGNsOXRaWFJoWkdGMFlTSTZleUpsYldGcGJDSTZJbUZrYldsdWIzWnphMmszUUdkdFlXbHNMbU52YlNJc0ltVnRZV2xzWDNabGNtbG1hV1ZrSWpwMGNuVmxMQ0p3YUc5dVpWOTJaWEpwWm1sbFpDSTZabUZzYzJVc0luSnZiR1Z6SWpwYklsTlZVRVZTWDBGRVRVbE9JbDBzSW5OMVlpSTZJalEyWXpVd05tTTVMV1F3TkdFdE5HWXhZaTFoWkRFd0xUTXpNalEzTlRJMlpqYzNNQ0o5TENKeWIy*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
  host: 'localhost:3003',
  referer: 'http://localhost:3003/super-admin/users/40897edc-6346-415c-9229-8b6c5fc2d770/permissions',
  'sec-ch-ua': '"Brave";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"macOS"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-origin',
  'sec-gpc': '1',
  'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'x-forwarded-for': '::1',
  'x-forwarded-host': 'localhost:3003',
  'x-forwarded-port': '3003',
  'x-forwarded-proto': 'http'
}
[PERMISSIONS API] Cookies received: [
  {
    name: 'sb-127-auth-token',
    value: 'base64-eyJhY2Nlc3NfdG9rZW4iOiJleUpoYkdjaU9pSklVekkxTmlJc0luUjVjQ0k2SWtwWFZDSjkuZXlKcGMzTWlPaUpvZEhSd09pOHZNVEkzTGpBdU1DNHhPalUwTXpJeEwyRjFkR2d2ZGpFaUxDSnpkV0lpT2lJME5tTTFNRFpqT1Mxa01EUmhMVFJtTVdJdFlXUXhNQzB6TXpJME56VXlObVkzTnpBaUxDSmhkV1FpT2lKaGRYUm9aVzUwYVdOaGRHVmtJaXdpWlhod0lqb3hOelV3TURFMk16STNMQ0pwWVhRaU9qRTNOVEF3TVRJM01qY3NJbVZ0WVdsc0lqb2lZV1J0YVc1dmRuTnJhVGRBWjIxaGFXd3VZMjl0SWl3aWNHaHZibVVpT2lJaUxDSmhjSEJmYldWMFlXUmhkR0VpT25zaWNISnZkbWxrWlhJaU9pSmxiV0ZwYkNJc0luQnliM1pwWkdWeWN5STZXeUpsYldGcGJDSmRmU3dpZFhObGNsOXRaWFJoWkdGMFlTSTZleUpsYldGcGJDSTZJbUZrYldsdWIzWnphMmszUUdkdFlXbHNMbU52YlNJc0ltVnRZV2xzWDNabGNtbG1hV1ZrSWpwMGNuVmxMQ0p3YUc5dVpWOTJaWEpwWm1sbFpDSTZabUZzYzJVc0luSnZiR1Z6SWpwYklsTlZVRVZTWDBGRVRVbE9JbDBzSW5OMVlpSTZJalEyWXpVd05tTTVMV1F3TkdFdE5HWXhZaTFoWkRFd0xUTXpNalEzTlRJMlpqYzNNQ0o5TENKeWIy*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
  }
]
[PERMISSIONS API] Handler called for user: 40897edc-6346-415c-9229-8b6c5fc2d770
[PERMISSIONS API] About to call getSession
[PERMISSIONS API] getSession result: {
  id: '46c506c9-d04a-4f1b-ad10-33247526f770',
  email: '<EMAIL>',
  access_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dRglIphJG-m3c9B70WlG_tq3ucnB71LkyycG-E0O9zs',
  token_type: 'bearer',
  expires_in: 3600,
  expires_at: **********,
  refresh_token: '1s97bRWhGWBEL2hxX7kjWg',
  user: {
    id: '46c506c9-d04a-4f1b-ad10-33247526f770',
    aud: 'authenticated',
    role: 'authenticated',
    email: '<EMAIL>',
    email_confirmed_at: '2025-06-14T21:38:43.806328Z',
    phone: '',
    confirmed_at: '2025-06-14T21:38:43.806328Z',
    last_sign_in_at: '2025-06-15T17:23:09.295985Z',
    app_metadata: { provider: 'email', providers: [Array] },
    user_metadata: {
      email: '<EMAIL>',
      email_verified: true,
      phone_verified: false,
      roles: [Array],
      sub: '46c506c9-d04a-4f1b-ad10-33247526f770'
    },
    identities: [ [Object] ],
    created_at: '2025-06-14T21:38:43.673958Z',
    updated_at: '2025-06-15T18:38:47.586336Z',
    is_anonymous: false
  },
  roles: [ 'SUPER_ADMIN' ]
}
Session roles array: [ 'SUPER_ADMIN' ]
Normalized user roles: [ 'SUPER_ADMIN' ]
Supabase client initialized successfully
 GET /api/super-admin/permission-templates 403 in 613ms
 GET /api/super-admin/permission-templates 403 in 43ms
Current user profile: {
  "id": "46c506c9-d04a-4f1b-ad10-33247526f770",
  "role": "SUPER_ADMIN",
  "full_name": "adminovski7",
  "avatar_url": null,
  "email": "<EMAIL>",
  "created_at": "2025-06-14T21:38:43.668039+00:00",
  "updated_at": "2025-06-14T21:38:43.668039+00:00",
  "roles": [
    "SUPER_ADMIN"
  ],
  "first_name": null,
  "last_name": null,
  "phone_number": null,
  "company_name": null
}
User has SUPER_ADMIN role: true
Fetching permissions for user ID: 40897edc-6346-415c-9229-8b6c5fc2d770
Found permissions: []
Fetching target user profile for ID: 40897edc-6346-415c-9229-8b6c5fc2d770
Target user profile: {
  "id": "40897edc-6346-415c-9229-8b6c5fc2d770",
  "email": "<EMAIL>",
  "full_name": "Test Client",
  "roles": [
    "CLIENT"
  ],
  "created_at": "2025-06-14T20:55:14.624466+00:00"
}
Fetching organizations for user ID: 40897edc-6346-415c-9229-8b6c5fc2d770
Error fetching user organizations: {
  code: '42703',
  details: null,
  hint: null,
  message: 'column user_organizations.tenant_id does not exist'
}
Continuing without organization data
 GET /api/super-admin/users/40897edc-6346-415c-9229-8b6c5fc2d770/permissions 200 in 791ms