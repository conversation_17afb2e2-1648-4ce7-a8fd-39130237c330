import "./globals.css";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { Toaster } from "@/app/components/ui/toaster";
import QueryProvider from "@/app/components/providers/query-provider";
// Removed ThemeProvider import as it's already included in AppProvider
import { MobileNav } from "@/app/components/features/navigation/mobile-nav";
import { Building2, Calendar, Car, FileText, Home, Users } from "lucide-react";
// import { AuthSync } from "@/app/components/ui/auth-sync" // Removed import
import { AppProvider } from "@/lib/providers/AppProvider";
import { AuthProvider } from "@/lib/providers/auth-provider";
import { TenantProvider } from "@/app/contexts/TenantContext";
import { AuthFixButton } from "@/app/components/ui/auth-fix-button";
import { TooltipProvider } from "@/app/components/ui/tooltip";
import { ErrorBoundary } from "@/app/components/error-boundary";

const inter = Inter({ subsets: ["latin"] });

export const dynamic = "force-dynamic";
export const revalidate = 0;

export const metadata: Metadata = {
  title: "WWMS DIY",
  description: "Worldwide Mobility Solutions - DIY Edition",
};

const navigationItems = [
  {
    title: "Dashboard",
    href: "/admin/dashboard",
    icon: <Home className="h-4 w-4" />,
  },
  {
    title: "Events",
    href: "/admin/events",
    icon: <Calendar className="h-4 w-4" />,
  },
  {
    title: "Quotes",
    href: "/admin/quotes",
    icon: <FileText className="h-4 w-4" />,
  },
  {
    title: "Companies",
    href: "/admin/companies",
    icon: <Building2 className="h-4 w-4" />,
  },
  {
    title: "Affiliates",
    href: "/admin/affiliates",
    icon: <Car className="h-4 w-4" />,
  },
  {
    title: "Coverage",
    href: "/admin/coverage",
    icon: <Users className="h-4 w-4" />,
  },
];

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  console.log("CLIENT_LOG: RootLayout - Component rendering (top level)");
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ErrorBoundary>
          <AppProvider>
            <AuthProvider>
              <TenantProvider>
                <TooltipProvider>
                  {/* <AuthSync /> */}
                  {/* Removed usage */}
                  <div className="fixed bottom-4 right-4 z-50">
                    <AuthFixButton />
                  </div>
                  <QueryProvider>
                    <div className="flex min-h-screen flex-col">
                      <main className="flex-1">{children}</main>
                    </div>
                    <Toaster />
                  </QueryProvider>
                </TooltipProvider>
              </TenantProvider>
            </AuthProvider>
          </AppProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
