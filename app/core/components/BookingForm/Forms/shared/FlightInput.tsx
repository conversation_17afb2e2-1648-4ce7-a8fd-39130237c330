import { useState, useEffect } from "react";
import { useFlightTracking } from "@core/hooks/useFlightTracking";
import {
  PaperAirplaneIcon,
  ClockIcon,
  CalendarIcon,
} from "@heroicons/react/24/outline";

// First, add the interfaces at the top
interface FlightStats {
  onTimePerformance: number;
  averageDuration: string;
  baggageClaim?: string;
}

interface EnhancedFlightStats extends FlightStats {
  aircraftType: string;
  flightDistance: string;
  terminalTransferTime: string;
  weatherDeparture: {
    temperature: string;
    condition: string;
  };
  weatherArrival: {
    temperature: string;
    condition: string;
  };
  previousFlightStatus: {
    date: string;
    status: string;
    delay: number;
  }[];
}

interface FlightInputProps {
  selectedDate: Date;
  onFlightScheduleFound: (flightInfo: any) => void;
}

export const FlightInput = ({
  selectedDate,
  onFlightScheduleFound,
}: FlightInputProps) => {
  const [flightNumber, setFlightNumber] = useState("");
  const { flightInfo, loading, error, trackFlight } = useFlightTracking();
  // Add the new state
  const [enhancedStats, setEnhancedStats] =
    useState<EnhancedFlightStats | null>(null);

  // Add effect to simulate fetching enhanced stats
  useEffect(() => {
    if (flightInfo) {
      // Simulate API call - replace with actual API call in production
      setEnhancedStats({
        onTimePerformance: 85,
        averageDuration: "2h 45m",
        baggageClaim: "Terminal 2, Carousel 4",
        aircraftType: "Boeing 737-800",
        flightDistance: "1,463 miles",
        terminalTransferTime: "~15 minutes",
        weatherDeparture: {
          temperature: "72°F",
          condition: "Sunny",
        },
        weatherArrival: {
          temperature: "68°F",
          condition: "Partly Cloudy",
        },
        previousFlightStatus: [
          { date: "May 1", status: "On Time", delay: 0 },
          { date: "Apr 30", status: "Delayed", delay: 25 },
          { date: "Apr 29", status: "On Time", delay: 0 },
        ],
      });
    }
  }, [flightInfo]);

  useEffect(() => {
    if (flightInfo) {
      onFlightScheduleFound(flightInfo);
    }
  }, [flightInfo, onFlightScheduleFound]);

  const handleFlightNumberChange = (value: string) => {
    setFlightNumber(value);
    if (value.length >= 5) {
      trackFlight(value, selectedDate);
    }
  };

  return (
    <div className="space-y-3">
      {/* Flight Input Field */}
      <div className="relative">
        <div className="flex items-center space-x-2">
          <div className="relative flex-1">
            <input
              type="text"
              placeholder="Enter flight number (e.g. AA123)"
              className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
              value={flightNumber}
              onChange={(e) => handleFlightNumberChange(e.target.value)}
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              <PaperAirplaneIcon className="w-5 h-5 text-gray-400" />
            </div>
          </div>
        </div>
      </div>

      {loading && (
        <div className="flex items-center space-x-2 text-sm text-white/50">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#111827]"></div>
          <span>Checking flight details...</span>
        </div>
      )}

      {error && (
        <div className="bg-red-500/10 text-red-500 text-sm px-3 py-2 rounded-lg">
          {error}
        </div>
      )}

      {flightInfo && enhancedStats && (
        <div className="mt-4 pt-4 border-t border-white/10">
          <div className="grid grid-cols-2 gap-x-6 gap-y-4">
            <div>
              <div className="text-sm text-white/50 mb-1">Aircraft</div>
              <div className="text-white font-medium">
                {enhancedStats.aircraftType}
              </div>
            </div>
            {/* ... rest of the enhanced stats display ... */}
          </div>

          {/* Previous Flight History */}
          <div className="mt-4 pt-4 border-t border-white/10">
            {/* ... flight history display ... */}
          </div>
        </div>
      )}
    </div>
  );
};
