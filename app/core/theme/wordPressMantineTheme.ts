import { createTheme, MantineColorsTuple } from "@mantine/core";

// WordPress-compatible Mantine theme that uses our CSS variables
// This ensures consistent styling between standalone and WordPress versions

// Define a custom primary color that matches our WordPress theme
const primaryColor: MantineColorsTuple = [
  "#f4f1ed", // lightest
  "#e8ddd4",
  "#d4c4b3",
  "#c0ab92",
  "#ac9271",
  "#987950", // base shade
  "#111827", // our primary color
  "#5d472f",
  "#443421",
  "#2b2113", // darkest
];

export const wordPressMantineTheme = createTheme({
  primaryColor: "primary",
  colors: {
    primary: primaryColor,
  },
  defaultRadius: "md",
  fontFamily: "Raleway, Inter, sans-serif",
  primaryShade: { light: 6, dark: 6 }, // Use our primary color as the main shade

  // WordPress-compatible component styles using our CSS variables
  components: {
    TextInput: {
      defaultProps: {
        styles: {
          input: {
            backgroundColor: "var(--limo-surface)",
            borderColor: "var(--limo-border)",
            color: "var(--limo-text-primary)",
            "&:focus": {
              borderColor: "var(--limo-primary)",
              boxShadow: "0 0 0 2px var(--limo-primary-transparent)",
            },
            "&::placeholder": {
              color: "var(--limo-text-muted)",
            },
          },
          label: {
            color: "var(--limo-text-primary)",
            fontWeight: 500,
          },
        },
      },
    },

    Textarea: {
      defaultProps: {
        styles: {
          input: {
            backgroundColor: "var(--limo-surface)",
            borderColor: "var(--limo-border)",
            color: "var(--limo-text-primary)",
            "&:focus": {
              borderColor: "var(--limo-primary)",
              boxShadow: "0 0 0 2px var(--limo-primary-transparent)",
            },
            "&::placeholder": {
              color: "var(--limo-text-muted)",
            },
          },
          label: {
            color: "var(--limo-text-primary)",
            fontWeight: 500,
          },
        },
      },
    },

    Button: {
      defaultProps: {
        color: "primary",
        styles: {
          root: {
            backgroundColor: "var(--limo-primary)",
            color: "#ffffff",
            "&:hover": {
              backgroundColor: "var(--limo-primary-light)",
            },
            "&:active": {
              backgroundColor: "var(--limo-primary-dark)",
            },
          },
        },
      },
    },

    Paper: {
      defaultProps: {
        styles: {
          root: {
            backgroundColor: "var(--limo-surface)",
            color: "var(--limo-text-primary)",
          },
        },
      },
    },

    Card: {
      defaultProps: {
        styles: {
          root: {
            backgroundColor: "var(--limo-surface)",
            borderColor: "var(--limo-border)",
            color: "var(--limo-text-primary)",
          },
        },
      },
    },

    Switch: {
      defaultProps: {
        color: "primary",
        styles: {
          track: {
            "&[data-checked]": {
              backgroundColor: "var(--limo-primary)",
            },
          },
        },
      },
    },

    Select: {
      defaultProps: {
        styles: {
          input: {
            backgroundColor: "var(--limo-surface)",
            borderColor: "var(--limo-border)",
            color: "var(--limo-text-primary)",
            "&:focus": {
              borderColor: "var(--limo-primary)",
              boxShadow: "0 0 0 2px var(--limo-primary-transparent)",
            },
          },
          label: {
            color: "var(--limo-text-primary)",
            fontWeight: 500,
          },
          dropdown: {
            backgroundColor: "var(--limo-surface)",
            borderColor: "var(--limo-border)",
          },
          option: {
            color: "var(--limo-text-primary)",
            "&[data-selected]": {
              backgroundColor: "var(--limo-primary)",
              color: "#ffffff",
            },
            "&:hover": {
              backgroundColor: "var(--limo-surface-light)",
            },
          },
        },
      },
    },

    Group: {
      defaultProps: {
        styles: {
          root: {
            color: "var(--limo-text-primary)",
          },
        },
      },
    },

    Text: {
      defaultProps: {
        styles: {
          root: {
            color: "var(--limo-text-primary)",
          },
        },
      },
    },

    Title: {
      defaultProps: {
        styles: {
          root: {
            color: "var(--limo-text-primary)",
          },
        },
      },
    },

    Tabs: {
      defaultProps: {
        styles: {
          tab: {
            color: "var(--limo-text-secondary)",
            "&[data-active]": {
              color: "#ffffff",
              backgroundColor: "var(--limo-primary)",
            },
            "&:hover": {
              backgroundColor: "var(--limo-surface-light)",
            },
          },
          tabsList: {
            borderBottomColor: "var(--limo-border)",
          },
        },
      },
    },

    Modal: {
      defaultProps: {
        styles: {
          content: {
            backgroundColor: "var(--limo-surface)",
            color: "var(--limo-text-primary)",
          },
          header: {
            backgroundColor: "var(--limo-surface)",
            borderBottomColor: "var(--limo-border)",
          },
          title: {
            color: "var(--limo-text-primary)",
          },
        },
      },
    },

    NumberInput: {
      defaultProps: {
        styles: {
          input: {
            backgroundColor: "var(--limo-surface)",
            borderColor: "var(--limo-border)",
            color: "var(--limo-text-primary)",
            "&:focus": {
              borderColor: "var(--limo-primary)",
              boxShadow: "0 0 0 2px var(--limo-primary-transparent)",
            },
          },
          label: {
            color: "var(--limo-text-primary)",
            fontWeight: 500,
          },
          control: {
            backgroundColor: "var(--limo-surface)",
            borderColor: "var(--limo-border)",
            color: "var(--limo-text-primary)",
            "&:hover": {
              backgroundColor: "var(--limo-surface-light)",
            },
          },
        },
      },
    },

    Checkbox: {
      defaultProps: {
        color: "primary",
        styles: {
          label: {
            color: "var(--limo-text-primary)",
          },
        },
      },
    },

    Radio: {
      defaultProps: {
        color: "primary",
        styles: {
          label: {
            color: "var(--limo-text-primary)",
          },
        },
      },
    },
  },
});
