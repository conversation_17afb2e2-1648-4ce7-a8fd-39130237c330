<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Limo Booking Form - WordPress Integration Example</title>
    <link rel="stylesheet" href="../styles/wordpress-plugin.css" />
    <link
      href="https://fonts.googleapis.com/css2?family=Raleway:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <style>
      /* Example WordPress theme styles */
      :root {
        /* Override plugin variables with WordPress theme colors */
        --wp-primary: #111827;
        --wp-primary-light: #8b6d4c;
        --wp-primary-dark: #5d472f;
        --wp-background: #000000;
        --wp-surface: #141414;
        --wp-surface-dark: #1a1a1a;
        --wp-surface-light: rgba(255, 255, 255, 0.03);
        --wp-text-primary: #ffffff;
        --wp-text-secondary: #e5e7eb;
        --wp-text-muted: #9ca3af;
        --wp-border: #333333;
      }

      /* Demo page styles (not part of the plugin) */
      body {
        font-family: "Raleway", system-ui, -apple-system, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f1f5f9;
      }

      .demo-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      .demo-header {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #e5e7eb;
      }

      .theme-toggle {
        margin: 20px 0;
      }
    </style>
  </head>
  <body>
    <div class="demo-container">
      <div class="demo-header">
        <h1>Limo Booking Form - WordPress Integration Example</h1>
        <p>
          This example demonstrates how to use the namespaced CSS approach for
          WordPress integration.
        </p>

        <div class="theme-toggle">
          <label>
            <input type="checkbox" id="theme-toggle" />
            Toggle Light/Dark Theme
          </label>
        </div>
      </div>

      <!-- Begin Limo Booking Plugin -->
      <div
        class="limo-booking-plugin"
        data-theme="light"
        id="booking-container"
      >
        <!-- Progress Steps -->
        <div
          class="limo-bg-surface"
          style="padding: 16px; border-radius: 8px; margin-bottom: 16px"
        >
          <div
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
            "
          >
            <div style="display: flex; align-items: center; gap: 8px">
              <div
                style="
                  width: 32px;
                  height: 32px;
                  border-radius: 50%;
                  background-color: var(--limo-primary);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: white;
                  font-weight: bold;
                "
              >
                1
              </div>
              <span class="limo-text">Trip Details</span>
            </div>
            <div
              style="
                flex-grow: 1;
                height: 2px;
                background-color: var(--limo-border);
                margin: 0 8px;
              "
            ></div>
            <div style="display: flex; align-items: center; gap: 8px">
              <div
                style="
                  width: 32px;
                  height: 32px;
                  border-radius: 50%;
                  background-color: var(--limo-border);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: var(--limo-text-muted);
                  font-weight: bold;
                "
              >
                2
              </div>
              <span class="limo-text-muted">Vehicle</span>
            </div>
            <div
              style="
                flex-grow: 1;
                height: 2px;
                background-color: var(--limo-border);
                margin: 0 8px;
              "
            ></div>
            <div style="display: flex; align-items: center; gap: 8px">
              <div
                style="
                  width: 32px;
                  height: 32px;
                  border-radius: 50%;
                  background-color: var(--limo-border);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: var(--limo-text-muted);
                  font-weight: bold;
                "
              >
                3
              </div>
              <span class="limo-text-muted">Confirmation</span>
            </div>
          </div>
        </div>

        <!-- Booking Tabs -->
        <div class="limo-tabs" style="margin-bottom: 16px">
          <button class="limo-tab" data-active="true">Point to Point</button>
          <button class="limo-tab">Hourly</button>
          <button class="limo-tab">Airport</button>
        </div>

        <div style="display: flex; flex-direction: column; gap: 24px">
          <!-- Main Form Content -->
          <div style="flex: 1">
            <!-- Booking Form -->
            <div
              class="limo-bg-surface"
              style="padding: 24px; border-radius: 8px; margin-bottom: 24px"
            >
              <h3
                class="limo-text"
                style="
                  font-size: 18px;
                  font-weight: 600;
                  margin-top: 0;
                  margin-bottom: 16px;
                "
              >
                Trip Details
              </h3>

              <div style="margin-bottom: 16px">
                <label
                  class="limo-text"
                  style="display: block; margin-bottom: 8px; font-weight: 500"
                  >Pickup Location</label
                >
                <input
                  type="text"
                  class="limo-input"
                  placeholder="Enter pickup address"
                  style="width: 100%; padding: 12px; box-sizing: border-box"
                />
              </div>

              <div style="margin-bottom: 16px">
                <label
                  class="limo-text"
                  style="display: block; margin-bottom: 8px; font-weight: 500"
                  >Dropoff Location</label
                >
                <input
                  type="text"
                  class="limo-input"
                  placeholder="Enter destination address"
                  style="width: 100%; padding: 12px; box-sizing: border-box"
                />
              </div>

              <!-- Add Stops Section -->
              <div style="margin-bottom: 16px">
                <button
                  class="limo-button"
                  style="
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    background: transparent;
                    border: 1px dashed var(--limo-border);
                    width: 100%;
                    justify-content: center;
                    padding: 10px;
                  "
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    style="width: 20px; height: 20px"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M12 4.5v15m7.5-7.5h-15"
                    />
                  </svg>
                  <span>Add Stop</span>
                </button>
              </div>

              <div style="display: flex; gap: 16px; margin-bottom: 16px">
                <div style="flex: 1">
                  <label
                    class="limo-text"
                    style="display: block; margin-bottom: 8px; font-weight: 500"
                    >Date</label
                  >
                  <input
                    type="date"
                    class="limo-input"
                    style="width: 100%; padding: 12px; box-sizing: border-box"
                  />
                </div>
                <div style="flex: 1">
                  <label
                    class="limo-text"
                    style="display: block; margin-bottom: 8px; font-weight: 500"
                    >Time</label
                  >
                  <input
                    type="time"
                    class="limo-input"
                    style="width: 100%; padding: 12px; box-sizing: border-box"
                  />
                </div>
              </div>

              <div style="margin-bottom: 16px">
                <label
                  class="limo-text"
                  style="display: block; margin-bottom: 8px; font-weight: 500"
                  >Passengers</label
                >
                <div style="display: flex; align-items: center; gap: 12px">
                  <button
                    class="limo-button"
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      width: 36px;
                      height: 36px;
                      padding: 0;
                      border: 1px solid var(--limo-border);
                      background: transparent;
                    "
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                      style="width: 20px; height: 20px"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M19.5 12h-15"
                      />
                    </svg>
                  </button>
                  <span
                    class="limo-text"
                    style="font-size: 18px; min-width: 30px; text-align: center"
                    >1</span
                  >
                  <button
                    class="limo-button"
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      width: 36px;
                      height: 36px;
                      padding: 0;
                      border: 1px solid var(--limo-border);
                      background: transparent;
                    "
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                      style="width: 20px; height: 20px"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M12 4.5v15m7.5-7.5h-15"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              <!-- Round Trip Toggle -->
              <div style="margin-bottom: 24px">
                <div
                  style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                  "
                >
                  <label class="limo-text" style="font-weight: 500"
                    >Round Trip</label
                  >
                  <div
                    class="limo-toggle"
                    style="
                      position: relative;
                      display: inline-block;
                      width: 50px;
                      height: 24px;
                    "
                  >
                    <input
                      type="checkbox"
                      style="opacity: 0; width: 0; height: 0"
                    />
                    <span
                      style="
                        position: absolute;
                        cursor: pointer;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background-color: var(--limo-border);
                        transition: 0.4s;
                        border-radius: 34px;
                      "
                    >
                      <span
                        style="
                          position: absolute;
                          content: '';
                          height: 18px;
                          width: 18px;
                          left: 3px;
                          bottom: 3px;
                          background-color: white;
                          transition: 0.4s;
                          border-radius: 50%;
                        "
                      ></span>
                    </span>
                  </div>
                </div>
              </div>

              <button
                class="limo-button limo-button-primary"
                style="width: 100%; padding: 14px; font-size: 16px"
              >
                Next: Select Vehicle
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke-width="1.5"
                  stroke="currentColor"
                  style="width: 20px; height: 20px; margin-left: 8px"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M8.25 4.5l7.5 7.5-7.5 7.5"
                  />
                </svg>
              </button>
            </div>
          </div>

          <!-- Trip Summary -->
          <div
            class="limo-bg-surface"
            style="padding: 24px; border-radius: 8px"
          >
            <h3
              class="limo-text"
              style="
                font-size: 18px;
                font-weight: 600;
                margin-top: 0;
                margin-bottom: 16px;
              "
            >
              Trip Summary
            </h3>

            <div style="margin-bottom: 16px">
              <div style="display: flex; gap: 12px; margin-bottom: 12px">
                <div
                  style="
                    min-width: 24px;
                    display: flex;
                    justify-content: center;
                  "
                >
                  <div
                    style="
                      width: 24px;
                      height: 24px;
                      border-radius: 50%;
                      background-color: #4caf50;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      color: white;
                      font-weight: bold;
                      font-size: 12px;
                    "
                  >
                    P
                  </div>
                </div>
                <div>
                  <p class="limo-text" style="margin: 0; font-weight: 500">
                    Pickup
                  </p>
                  <p class="limo-text-muted" style="margin: 4px 0 0 0">
                    Enter pickup location
                  </p>
                </div>
              </div>

              <div style="display: flex; gap: 12px">
                <div
                  style="
                    min-width: 24px;
                    display: flex;
                    justify-content: center;
                  "
                >
                  <div
                    style="
                      width: 24px;
                      height: 24px;
                      border-radius: 50%;
                      background-color: #f44336;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      color: white;
                      font-weight: bold;
                      font-size: 12px;
                    "
                  >
                    D
                  </div>
                </div>
                <div>
                  <p class="limo-text" style="margin: 0; font-weight: 500">
                    Dropoff
                  </p>
                  <p class="limo-text-muted" style="margin: 4px 0 0 0">
                    Enter dropoff location
                  </p>
                </div>
              </div>
            </div>

            <div
              style="
                border-top: 1px solid var(--limo-border);
                padding-top: 16px;
                margin-bottom: 16px;
              "
            >
              <div
                style="
                  display: flex;
                  justify-content: space-between;
                  margin-bottom: 8px;
                "
              >
                <span class="limo-text">Date:</span>
                <span class="limo-text-muted">Not selected</span>
              </div>
              <div
                style="
                  display: flex;
                  justify-content: space-between;
                  margin-bottom: 8px;
                "
              >
                <span class="limo-text">Time:</span>
                <span class="limo-text-muted">Not selected</span>
              </div>
              <div style="display: flex; justify-content: space-between">
                <span class="limo-text">Passengers:</span>
                <span class="limo-text-muted">1</span>
              </div>
            </div>

            <div
              style="
                border-top: 1px solid var(--limo-border);
                padding-top: 16px;
              "
            >
              <div
                style="
                  display: flex;
                  justify-content: space-between;
                  font-weight: 600;
                "
              >
                <span class="limo-text">Estimated Price:</span>
                <span class="limo-text">$0.00</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- End Limo Booking Plugin -->
    </div>

    <script>
      // Simple theme toggle for demo purposes
      document
        .getElementById("theme-toggle")
        .addEventListener("change", function () {
          const container = document.getElementById("booking-container");
          if (this.checked) {
            container.setAttribute("data-theme", "dark");
          } else {
            container.setAttribute("data-theme", "light");
          }
        });

      // Make tabs interactive for demo
      document.querySelectorAll(".limo-tab").forEach((tab) => {
        tab.addEventListener("click", function () {
          document.querySelectorAll(".limo-tab").forEach((t) => {
            t.removeAttribute("data-active");
          });
          this.setAttribute("data-active", "true");
        });
      });

      // Toggle for round trip
      const toggleInput = document.querySelector(".limo-toggle input");
      const toggleSpan = document.querySelector(".limo-toggle span span");

      toggleInput.addEventListener("change", function () {
        if (this.checked) {
          toggleSpan.style.transform = "translateX(26px)";
          toggleSpan.parentElement.style.backgroundColor =
            "var(--limo-primary)";
        } else {
          toggleSpan.style.transform = "translateX(0)";
          toggleSpan.parentElement.style.backgroundColor = "var(--limo-border)";
        }
      });
    </script>
  </body>
</html>
