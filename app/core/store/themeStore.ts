import { create } from "zustand";
import { persist } from "zustand/middleware";
import { ColorScheme, BackgroundMode, FontColors } from "@core/types/theme";
import { themeService } from "@core/services/themeService";

interface ThemeState {
  colorScheme: ColorScheme;
  backgroundMode: BackgroundMode;
  fontColors: FontColors;
  mapTheme: "dark" | "light";
  initialized: boolean;
  setColorScheme: (scheme: ColorScheme) => void;
  setBackgroundMode: (mode: BackgroundMode) => void;
  setFontColors: (colors: FontColors) => void;
  setMapTheme: (theme: "dark" | "light") => void;
  initializeFromWordPress: (config: any) => void;
}

// Default theme values with explicit types
const defaultTheme = {
  colorScheme: {
    primary: "#111827",
    primaryLight: "#8b6d4c",
    primaryDark: "#5d472f",
  } as ColorScheme,
  backgroundMode: {
    background: "#000000",
    surface: "#141414",
    surfaceDark: "#1a1a1a",
  } as BackgroundMode,
  fontColors: {
    textPrimary: "#ffffff",
    textSecondary: "#e5e7eb",
    textMuted: "#9ca3af",
    textDisabled: "#6b7280",
  } as FontColors,
  mapTheme: "dark" as "dark" | "light",
};

// Detect WordPress environment safely
const isWordPressEnvironment = (): boolean => {
  try {
    return (
      typeof window !== "undefined" &&
      !!window.limoBookingConfig &&
      !!window.limoBookingConfig.isWordPress
    );
  } catch (e) {
    console.error("Error detecting WordPress environment:", e);
    return false;
  }
};

// Get WordPress config safely
const getWordPressConfig = () => {
  try {
    return typeof window !== "undefined" && window.limoBookingConfig?.theme
      ? { ...window.limoBookingConfig.theme }
      : null;
  } catch (e) {
    console.error("Error getting WordPress config:", e);
    return null;
  }
};

// Check if we're in WordPress environment
const isWordPress = isWordPressEnvironment();

// Get WordPress theme configuration if available
const wpConfig = getWordPressConfig();

console.log("Theme Store Initialization:", {
  isWordPress,
  hasConfig: !!wpConfig,
  timestamp: new Date().toISOString(),
});

// Helper function to apply theme with fallbacks
const applyThemeToDOM = (theme: any) => {
  if (!theme) {
    console.warn("No theme provided to applyThemeToDOM, using defaults");
    // Apply default theme if none provided
    themeService.applyTheme({
      color: {
        base: defaultTheme.colorScheme.primary,
        light: defaultTheme.colorScheme.primaryLight,
        dark: defaultTheme.colorScheme.primaryDark,
      },
      background: {
        main: defaultTheme.backgroundMode.background,
        surface: defaultTheme.backgroundMode.surface,
        surfaceDark: defaultTheme.backgroundMode.surfaceDark,
      },
      font: {
        primary: defaultTheme.fontColors.textPrimary,
        secondary: defaultTheme.fontColors.textSecondary,
        muted: defaultTheme.fontColors.textMuted,
        disabled: defaultTheme.fontColors.textDisabled,
      },
    });
    return;
  }

  console.log("Applying theme to DOM:", {
    theme,
    timestamp: new Date().toISOString(),
  });

  // Ensure we have fallbacks for all theme properties
  const safeTheme = {
    primary_color: theme.primary_color || defaultTheme.colorScheme.primary,
    primary_light: theme.primary_light || defaultTheme.colorScheme.primaryLight,
    primary_dark: theme.primary_dark || defaultTheme.colorScheme.primaryDark,
    background: theme.background || defaultTheme.backgroundMode.background,
    surface: theme.surface || defaultTheme.backgroundMode.surface,
    surface_dark: theme.surface_dark || defaultTheme.backgroundMode.surfaceDark,
    text_primary: theme.text_primary || defaultTheme.fontColors.textPrimary,
    text_secondary:
      theme.text_secondary || defaultTheme.fontColors.textSecondary,
    text_muted: theme.text_muted || defaultTheme.fontColors.textMuted,
    text_disabled: theme.text_disabled || defaultTheme.fontColors.textDisabled,
  };

  themeService.applyTheme({
    color: {
      base: safeTheme.primary_color,
      light: safeTheme.primary_light,
      dark: safeTheme.primary_dark,
    },
    background: {
      main: safeTheme.background,
      surface: safeTheme.surface,
      surfaceDark: safeTheme.surface_dark,
    },
    font: {
      primary: safeTheme.text_primary,
      secondary: safeTheme.text_secondary,
      muted: safeTheme.text_muted,
      disabled: safeTheme.text_disabled,
    },
  });
};

// Apply WordPress theme immediately if available
if (isWordPress && wpConfig) {
  console.log("Applying WordPress theme configuration immediately");
  try {
    applyThemeToDOM(wpConfig);
  } catch (e) {
    console.error("Error applying initial WordPress theme:", e);
  }
}

const createStore = () => {
  // Common state mutation functions
  const createStateUpdaters = (set: any, get: any) => ({
    setColorScheme: (scheme: ColorScheme) => {
      set({ colorScheme: { ...scheme } });

      if (!isWordPress) {
        const state = get();
        try {
          themeService.applyTheme({
            color: {
              base: scheme.primary,
              light: scheme.primaryLight,
              dark: scheme.primaryDark,
            },
            background: {
              main: state.backgroundMode.background,
              surface: state.backgroundMode.surface,
              surfaceDark: state.backgroundMode.surfaceDark,
            },
            font: {
              primary: state.fontColors.textPrimary,
              secondary: state.fontColors.textSecondary,
              muted: state.fontColors.textMuted,
              disabled: state.fontColors.textDisabled,
            },
          });
        } catch (e) {
          console.error("Error applying colorScheme:", e);
        }
      }
    },
    setBackgroundMode: (mode: BackgroundMode) => {
      set({ backgroundMode: { ...mode } });

      if (!isWordPress) {
        const state = get();
        try {
          themeService.applyTheme({
            color: {
              base: state.colorScheme.primary,
              light: state.colorScheme.primaryLight,
              dark: state.colorScheme.primaryDark,
            },
            background: {
              main: mode.background,
              surface: mode.surface,
              surfaceDark: mode.surfaceDark,
            },
            font: {
              primary: state.fontColors.textPrimary,
              secondary: state.fontColors.textSecondary,
              muted: state.fontColors.textMuted,
              disabled: state.fontColors.textDisabled,
            },
          });
        } catch (e) {
          console.error("Error applying backgroundMode:", e);
        }
      }
    },
    setFontColors: (colors: FontColors) => {
      set({ fontColors: { ...colors } });

      if (!isWordPress) {
        const state = get();
        try {
          themeService.applyTheme({
            color: {
              base: state.colorScheme.primary,
              light: state.colorScheme.primaryLight,
              dark: state.colorScheme.primaryDark,
            },
            background: {
              main: state.backgroundMode.background,
              surface: state.backgroundMode.surface,
              surfaceDark: state.backgroundMode.surfaceDark,
            },
            font: {
              primary: colors.textPrimary,
              secondary: colors.textSecondary,
              muted: colors.textMuted,
              disabled: colors.textDisabled,
            },
          });
        } catch (e) {
          console.error("Error applying fontColors:", e);
        }
      }
    },
    setMapTheme: (theme: "dark" | "light") => {
      set({ mapTheme: theme });
    },
  });

  if (isWordPress) {
    // In WordPress, create store without persistence
    return create<ThemeState>((set, get) => ({
      colorScheme: {
        primary: wpConfig?.primary_color || defaultTheme.colorScheme.primary,
        primaryLight:
          wpConfig?.primary_light || defaultTheme.colorScheme.primaryLight,
        primaryDark:
          wpConfig?.primary_dark || defaultTheme.colorScheme.primaryDark,
      },
      backgroundMode: {
        background:
          wpConfig?.background || defaultTheme.backgroundMode.background,
        surface: wpConfig?.surface || defaultTheme.backgroundMode.surface,
        surfaceDark:
          wpConfig?.surface_dark || defaultTheme.backgroundMode.surfaceDark,
      },
      fontColors: {
        textPrimary:
          wpConfig?.text_primary || defaultTheme.fontColors.textPrimary,
        textSecondary:
          wpConfig?.text_secondary || defaultTheme.fontColors.textSecondary,
        textMuted: wpConfig?.text_muted || defaultTheme.fontColors.textMuted,
        textDisabled:
          wpConfig?.text_disabled || defaultTheme.fontColors.textDisabled,
      },
      mapTheme: wpConfig?.mapStyle?.includes("dark") ? "dark" : "light",
      initialized: !!wpConfig,
      ...createStateUpdaters(set, get),
      initializeFromWordPress: (config) => {
        if (!config) {
          console.warn("No config provided to initializeFromWordPress");
          return;
        }

        try {
          console.log("Initializing from WordPress config:", config);

          // Create a safe config with fallbacks
          const safeConfig = {
            primary_color:
              config.primary_color || defaultTheme.colorScheme.primary,
            primary_light:
              config.primary_light || defaultTheme.colorScheme.primaryLight,
            primary_dark:
              config.primary_dark || defaultTheme.colorScheme.primaryDark,
            background:
              config.background || defaultTheme.backgroundMode.background,
            surface: config.surface || defaultTheme.backgroundMode.surface,
            surface_dark:
              config.surface_dark || defaultTheme.backgroundMode.surfaceDark,
            text_primary:
              config.text_primary || defaultTheme.fontColors.textPrimary,
            text_secondary:
              config.text_secondary || defaultTheme.fontColors.textSecondary,
            text_muted: config.text_muted || defaultTheme.fontColors.textMuted,
            text_disabled:
              config.text_disabled || defaultTheme.fontColors.textDisabled,
            mapStyle: config.mapStyle || "mapbox://styles/mapbox/dark-v11",
          };

          // Determine map theme from mapStyle or use a fallback
          const mapTheme =
            typeof safeConfig.mapStyle === "string" &&
            safeConfig.mapStyle.includes("dark")
              ? "dark"
              : "light";

          // Update the state with a complete new object
          set({
            colorScheme: {
              primary: safeConfig.primary_color,
              primaryLight: safeConfig.primary_light,
              primaryDark: safeConfig.primary_dark,
            },
            backgroundMode: {
              background: safeConfig.background,
              surface: safeConfig.surface,
              surfaceDark: safeConfig.surface_dark,
            },
            fontColors: {
              textPrimary: safeConfig.text_primary,
              textSecondary: safeConfig.text_secondary,
              textMuted: safeConfig.text_muted,
              textDisabled: safeConfig.text_disabled,
            },
            mapTheme: mapTheme,
            initialized: true,
          });

          // After state update, apply theme to DOM
          applyThemeToDOM(safeConfig);
        } catch (error) {
          console.error("Error initializing from WordPress config:", error);

          // Fall back to defaults on error
          set({
            colorScheme: { ...defaultTheme.colorScheme },
            backgroundMode: { ...defaultTheme.backgroundMode },
            fontColors: { ...defaultTheme.fontColors },
            mapTheme: defaultTheme.mapTheme,
            initialized: false,
          });
        }
      },
    }));
  }

  // In standalone mode, create store with persistence
  return create<ThemeState>()(
    persist(
      (set, get) => ({
        colorScheme: { ...defaultTheme.colorScheme },
        backgroundMode: { ...defaultTheme.backgroundMode },
        fontColors: { ...defaultTheme.fontColors },
        mapTheme: defaultTheme.mapTheme,
        initialized: false,
        ...createStateUpdaters(set, get),
        initializeFromWordPress: () => {
          console.log(
            "initializeFromWordPress called in standalone mode - no effect"
          );
        },
      }),
      {
        name: "limo-booking-theme",
        skipHydration: isWordPress,
      }
    )
  );
};

export const useThemeStore = createStore();
