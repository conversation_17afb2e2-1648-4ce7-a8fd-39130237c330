import { themeService } from "./themeService";
import { getCityFromCoordinates, getApiKey } from "./hereApi";
import { createClient } from "@supabase/supabase-js"; // Use a generic supabase client
import { useTenant } from "@/app/contexts/TenantContext";

interface ContactInfo {
  fullName: string;
  email: string;
  phone: string;
  specialRequests?: string;
  createAccount: boolean;
}

interface Location {
  address: string;
  coordinates: [number, number];
}

interface BaseBookingData {
  selectedVehicle: string;
  pickupDate: string;
  pickupLocation: Location;
  adults: number;
  children?: number;
  needCarSeats?: boolean;
  infantSeats?: number;
  toddlerSeats?: number;
  boosterSeats?: number;
  luggage?: number;
}

interface PointToPointData extends BaseBookingData {
  dropoffLocation: Location;
  stops?: Array<{ id: string; location: Location }>;
  isRoundTrip?: boolean;
  returnDate?: string;
  returnTime?: string;
}

interface HourlyData extends BaseBookingData {
  hours: number;
}

interface AirportData extends BaseBookingData {
  dropoffLocation: Location;
  departureFlight?: string;
  returnFlight?: string;
  isRoundTrip?: boolean;
  returnDate?: string;
  returnTime?: string;
}

interface MultiDayData extends BaseBookingData {
  days: Array<{
    pickupDate: string;
    pickupTime: string;
    pickupLocation: Location;
    dropoffLocation: Location;
    stops?: Array<{ id: string; location: Location }>;
  }>;
}

interface BookingDetails {
  formType: "point-to-point" | "hourly" | "airport" | "multi-day";
  pointToPointData?: PointToPointData;
  hourlyData?: HourlyData;
  airportData?: AirportData;
  multiDayData?: MultiDayData;
  contactInfo?: ContactInfo; // Make this optional to match existing code
}

interface BookingData extends ContactInfo {
  bookingDetails: BookingDetails;
  dealId?: string | null;
}

export interface SalesmateResponse {
  success: boolean;
  contactId: string | null;
  dealId: string | null;
  message: string;
}

// Retry configuration
const RETRY_CONFIG = {
  maxRetries: 4,
  interval: 2000,
  shouldContinue: true,
};

// Helper function to handle retries
const fetchWithRetry = async (
  url: string,
  options: RequestInit,
  retryCount = 0
) => {
  try {
    // Use relative URL if it's a Salesmate API call or HERE API call
    const isSalesmateAPI = url.includes("salesmate.io");
    const isHereAPI = url.includes("hereapi.com");

    let finalUrl = url;
    if (isSalesmateAPI) {
      finalUrl = `/api/proxy/${url.split("/apis/")[1]}`;
    } else if (isHereAPI) {
      finalUrl = `/api/proxy/here${url.split("hereapi.com")[1]}`;
    }

    console.log("Making request to:", finalUrl);
    console.log("Original URL:", url);
    console.log("Request options:", options);

    const response = await fetch(finalUrl, options);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.message || `HTTP error! status: ${response.status}`
      );
    }

    return await response.json();
  } catch (error) {
    console.error("Request failed:", error);

    if (retryCount < RETRY_CONFIG.maxRetries && RETRY_CONFIG.shouldContinue) {
      console.log(
        `Retrying... Attempt ${retryCount + 1} of ${RETRY_CONFIG.maxRetries}`
      );
      await new Promise((resolve) =>
        setTimeout(resolve, RETRY_CONFIG.interval)
      );
      return fetchWithRetry(url, options, retryCount + 1);
    }

    throw error;
  }
};

// Helper function for making Salesmate API calls
const salesmateFetch = async ({
  endpoint,
  method = "GET",
  body = null,
  queryParams = null,
}: {
  endpoint: string;
  method?: string;
  body?: any;
  queryParams?: Record<string, string> | null;
}) => {
  // DIAGNOSTIC LOG - This should appear in the console if our updated code is running
  console.log("🚨🚨🚨 UPDATED CODE RUNNING - VERSION 1.2 🚨🚨🚨");
  console.log("Current URL path:", window.location.pathname);
  console.log("Current hostname:", window.location.hostname);

  // Check if we're in development mode
  const isDev = process.env.NODE_ENV === "development";

  // IMPORTANT FIX: Properly detect standalone mode from environment variable
  // The string 'true' needs to be compared as a string, not converted to boolean
  const isStandalone = process.env.VITE_STANDALONE === "true";

  console.log("Environment:", {
    NODE_ENV: process.env.NODE_ENV,
    VITE_STANDALONE: process.env.VITE_STANDALONE,
    isDev,
    isStandalone,
  });

  // Construct headers exactly as they work in the successful request
  const accessToken = process.env.NEXT_PUBLIC_SALESMATE_API_KEY || "";

  const headers = {
    "Content-Type": "application/json",
    "x-linkname": "wwlimo.salesmate.io",
    accessToken: accessToken,
  };

  // Build query string if params exist
  const queryString = queryParams
    ? `?${new URLSearchParams(queryParams).toString()}`
    : "";

  // Get current hostname
  const currentHostname =
    typeof window !== "undefined" ? window.location.hostname : "";

  // Only force WordPress mode for specific domains, NOT in standalone mode
  const FORCE_WORDPRESS_MODE =
    (currentHostname.includes("jetsetvilla.com") ||
      currentHostname.includes("wwmobilitysolutions.com")) &&
    !isStandalone; // Never force WordPress mode in standalone

  // Direct URL construction to ensure it always uses the correct endpoint
  let url;

  if (FORCE_WORDPRESS_MODE) {
    // HARD-CODED path for known WordPress sites - bypass all other detection
    url = `/wp-json/limo-booking/v1/proxy/${endpoint}${queryString}`;
    console.log("🚨 EMERGENCY OVERRIDE: Forcing WordPress REST API path");
    console.log("Using forced WordPress endpoint:", url);
  } else if (isStandalone) {
    // STANDALONE MODE: Always use the proxy endpoint format for standalone
    const fixedEndpoint = endpoint.startsWith("/")
      ? endpoint.substring(1)
      : endpoint;
    url = `/api/proxy/${fixedEndpoint}${queryString}`;
    console.log(
      "🚨 STANDALONE MODE DETECTED: Using standalone proxy endpoint:",
      url
    );
  } else {
    // Only proceed with detection logic if not in forced mode or standalone mode
    // More robust WordPress environment detection function
    const detectWordPress = (): {
      isWordPress: boolean;
      detectionMethod: string;
      apiBaseUrl?: string;
    } => {
      // If we're explicitly in standalone mode, don't detect WordPress
      if (isStandalone) {
        return { isWordPress: false, detectionMethod: "standalone-mode" };
      }

      if (typeof window === "undefined") {
        return { isWordPress: false, detectionMethod: "no-window" };
      }

      // METHOD 1: Check for limoBookingConfig with explicit isWordPress flag
      if (window.limoBookingConfig?.isWordPress === true) {
        return {
          isWordPress: true,
          detectionMethod: "config-flag",
          apiBaseUrl: window.limoBookingConfig.apiBaseUrl || "",
        };
      }

      // METHOD 2: Check for WordPress REST API link in document head
      if (document.querySelector('link[rel="https://api.w.org/"]')) {
        const linkElement = document.querySelector(
          'link[rel="https://api.w.org/"]'
        ) as HTMLLinkElement;
        const wpRestUrl = linkElement?.href || "";

        // Extract the base REST URL (normally ends with wp-json/)
        const baseRestUrl = wpRestUrl.replace(/\/wp-json\/?$/, "");

        return {
          isWordPress: true,
          detectionMethod: "rest-api-link",
          apiBaseUrl: `${baseRestUrl}/wp-json/limo-booking/v1/proxy/emails`,
        };
      }

      return { isWordPress: false, detectionMethod: "none" };
    };
    const wpEnv = detectWordPress();
    if (wpEnv.isWordPress && wpEnv.apiBaseUrl) {
      url = wpEnv.apiBaseUrl;
      console.log("[Salesmate API] Using WordPress API Base URL:", url);
    } else {
      url = `https://wwlimo.salesmate.io/apis/crm/v1/${endpoint}${queryString}`;
    }
  }

  // Final check to ensure URL is defined
  if (!url) {
    console.error("Salesmate API URL is undefined. Cannot proceed.");
    throw new Error("Salesmate API URL is undefined.");
  }

  const options: RequestInit = {
        method,
        headers,
    redirect: "follow", // Ensure redirects are followed
    cache: "no-store", // Crucial for fresh data
  };

  if (body && (method === "POST" || method === "PUT" || method === "PATCH")) {
    options.body = JSON.stringify(body);
  }

  try {
    const response = await fetch(url, options);

    if (!response.ok) {
      const errorBody = await response.text(); // Read as text to avoid JSON parsing errors
      console.error(
        `Salesmate API Error: ${response.status} ${response.statusText}`,
        errorBody
      );
      throw new Error(
        `Salesmate API Error: ${response.status} - ${errorBody || "Unknown error"}`
      );
    }

    const contentType = response.headers.get("content-type");
    if (contentType && contentType.includes("application/json")) {
      return await response.json();
      } else {
      // If not JSON, return text or throw an error based on expected response
      return await response.text();
    }
  } catch (error) {
    console.error("Error in salesmateFetch:", error);
    throw error;
  }
};

// Add version tracking at the top of the file
const API_VERSION = "2.0.0"; // Increment this when making significant changes
console.log(`[API Service] Loading API service version ${API_VERSION}`);

// Email service integration using proxy server
export const sendConfirmationEmail = async (data: BookingData) => {
  console.log("[Email Service] Version 2.0.0 - Starting email process");
  console.log("[Email Service] Preparing to send confirmation email");

  const { currentTenant } = useTenant(); // Get current tenant context

  try {
    // Log the booking data structure for debugging
    console.log(
      "[Email Service] Booking data structure:",
      JSON.stringify({
        formType: data.bookingDetails.formType,
        hasCoordinates: getPickupCoordinates(data) !== null,
        contactInfo: {
          name: data.fullName,
          email: data.email,
        },
      })
    );

    // Get the pickup city for the email subject
    console.log("[Email Service] Getting pickup city for email subject");
    const pickupCity = await getPickupCity(data);
    console.log("[Email Service] Pickup city retrieved:", pickupCity);

    // Get the pickup date
    const pickupDate = getPickupDate(data);
    console.log("[Email Service] Pickup date:", pickupDate);

    // Format the date for better readability in the email subject
    let formattedDate = pickupDate;
    try {
      // Format the date directly without dynamic import to ensure it works
      const date = new Date(pickupDate);
      if (!isNaN(date.getTime())) {
        formattedDate = date.toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
          year: "numeric",
        });
        console.log(
          "[Email Service] Date formatted successfully:",
          formattedDate
        );
      } else {
        console.log("[Email Service] Invalid date format, using original date");
      }
    } catch (error) {
      console.error("[Email Service] Error formatting date:", error);
    }

    // Get the vehicle name
    const vehicleId = getVehicleId(data);
    const vehicleName = formatVehicleName(vehicleId);
    console.log("[Email Service] Vehicle name:", vehicleName);

    // Get the customer name
    const customerName = data.fullName;
    console.log("[Email Service] Customer name:", customerName);

    // Create the email subject
    // Format: "City Quote Request - Date - Vehicle - Customer Name"
    const emailSubject = `${pickupCity} Quote Request - ${formattedDate} - ${vehicleName} - ${customerName}`;
    console.log("[Email Service] Email subject:", emailSubject);

    // Remove local HTML generation and theme color
    // const emailHtml = generateEmailTemplate(data);
    // const themeColor = getThemeColor();

    // Determine the from email and name based on tenant
    const fromEmail = process.env.NEXT_PUBLIC_SYSTEM_EMAIL_FROM_ADDRESS || "<EMAIL>";
    const fromName = currentTenant?.name || "TransFlow Team"; // Dynamically set from name
    console.log("[Email Service] From Email:", fromEmail);
    console.log("[Email Service] From Name:", fromName);
    console.log(
      "[Email Service] Sending email from:",
      `${fromName} <${fromEmail}>`
    );

    // Detect if we're in WordPress environment
    const wpEnv = detectWordPressEnv();

    // Determine the API endpoint
    let apiEndpoint = "/api/emails";

    // Override for WordPress environment
    if (wpEnv.isWordPress && wpEnv.apiBaseUrl) {
      apiEndpoint = wpEnv.apiBaseUrl;
    }

    // EMERGENCY OVERRIDE for specific domains
    const currentDomain = typeof window !== 'undefined' ? window.location.hostname : "";
    if (currentDomain.includes("jetsetvilla.com")) {
      console.log(
        "[Email Service] 🚨 EMERGENCY OVERRIDE: Forcing WordPress email endpoint on domain:",
        currentDomain
      );
      apiEndpoint = "/wp-json/limo-booking/v1/proxy/emails";
      console.log(
        "[Email Service] Using forced WordPress endpoint:",
        apiEndpoint
      );
    }

    console.log(
      "[Email Service] Final email endpoint:",
      JSON.stringify({ apiEndpoint })
    );

    // Prepare the email payload to send to our new /api/emails route
    const emailPayload = {
      to: data.email,
      from: `${fromName} <${fromEmail}>`, // Ensure this is always a string in the format "Name <email>"
      subject: emailSubject,
      type: "quote_confirmation", // Indicate the template type to use on the server
      tenantId: currentTenant?.id || null, // Pass tenant ID to server for template lookup
      templateVariables: { // Pass all necessary data for templating
        pickupCity,
        formattedDate,
        vehicleName,
        customerName: data.fullName,
        customerEmail: data.email,
        customerPhone: data.phone,
        specialRequests: data.specialRequests || "",
        bookingDetails: data.bookingDetails, // Pass entire booking details if needed for complex templates
      dealId: data.dealId || null,
        // Add other variables needed by the email template here
      },
      bcc: "<EMAIL>", // Add BCC
      dealId: data.dealId || null,
    };

    // Add additional logging to diagnose the issue
    console.log(
      "[Email Service] Email payload before sending:",
      JSON.stringify(emailPayload)
    );
    console.log("[Email Service] From field type:", typeof emailPayload.from);

    // CRITICAL FIX: Ensure the from field is a string
    // This is a defensive measure in case something is transforming our payload
    const stringifiedPayload = JSON.stringify(emailPayload);
    const parsedPayload = JSON.parse(stringifiedPayload);

    // Check if the from field has been transformed into an object
    if (typeof parsedPayload.from === "object" && parsedPayload.from !== null) {
      console.log(
        "[Email Service] 🚨 CRITICAL FIX: From field was transformed into an object. Fixing it..."
      );
      // Fix it by converting it back to a string
      parsedPayload.from = `${fromName} <${fromEmail}>`;
      console.log("[Email Service] Fixed from field:", parsedPayload.from);
    }

    // Send the email
    const response = await fetch(apiEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(parsedPayload), // Use the fixed payload
    });

    // Add additional logging after sending
    console.log(
      "[Email Service] Request body sent:",
      JSON.stringify(parsedPayload)
    );

    console.log("[Email Service] Response status:", response.status);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to send email: ${JSON.stringify(errorData)}`);
    }

    console.log("[Email Service] Email sent successfully");
    return { success: true };
  } catch (error) {
    console.error("[Email Service] Error sending confirmation email:", error);
    return { success: false, error };
  }
};

// Helper function to get vehicle ID from booking data
const getVehicleId = (data: BookingData): string => {
  const { bookingDetails } = data;
  const { formType } = bookingDetails;

  if (formType === "point-to-point" && bookingDetails.pointToPointData) {
    return bookingDetails.pointToPointData.selectedVehicle;
  } else if (formType === "hourly" && bookingDetails.hourlyData) {
    return bookingDetails.hourlyData.selectedVehicle;
  } else if (formType === "airport" && bookingDetails.airportData) {
    return bookingDetails.airportData.selectedVehicle;
  } else if (formType === "multi-day" && bookingDetails.multiDayData) {
    return bookingDetails.multiDayData.selectedVehicle;
  }

  return "";
};

// Helper function to get vehicle details
const getVehicleDetails = (vehicleId: string | null) => {
  const vehicles = {
    sedan: {
      name: "Sedan",
      capacity: 3,
      description: "Leather seats, Tinted privacy glass, Premium sound system",
    },
    "luxury-sedan": {
      name: "Luxury Sedan",
      capacity: 3,
      description:
        "Plush leather seating, Rear climate control, Ambient lighting",
    },
    suv: {
      name: "SUV",
      capacity: 6,
      description:
        "Spacious interior, Leather seats, All-wheel drive, Premium sound system",
    },
    "luxury-suv": {
      name: "Luxury SUV",
      capacity: 6,
      description:
        "Leather seating with extra legroom, Advanced climate control, On-board WiFi",
    },
    sprinter: {
      name: "Mercedes Sprinter",
      capacity: 12,
      description:
        "Spacious interior, Premium sound system, On-board WiFi, USB charging ports",
    },
    stretch: {
      name: "Stretch Limousine",
      capacity: 10,
      description:
        "Leather seating, Mood lighting, Mini bar, Premium surround sound system",
    },
    hummer: {
      name: "Hummer Limousine",
      capacity: 14,
      description:
        "Leather seating, LED lighting, Mini bar, State-of-the-art sound system",
    },
    "party-bus": {
      name: "Party Bus",
      capacity: 20,
      description:
        "Dance floor, LED lighting, High-end sound system, Bar area, Lounge seating",
    },
    "mini-bus": {
      name: "Mini Bus",
      capacity: 24,
      description:
        "Comfortable seating, Ample legroom, Air conditioning, Generous luggage space",
    },
    "coach-bus": {
      name: "Coach Bus",
      capacity: 50,
      description:
        "Reclining seats, Climate control, On-board restroom, Entertainment system",
    },
  };

  return vehicleId ? vehicles[vehicleId as keyof typeof vehicles] : null;
};

// Helper function to get pickup coordinates
const getPickupCoordinates = (data: BookingData): [number, number] | null => {
  const formType = data.bookingDetails.formType;
  let pickupLocation = null;

  if (formType === "point-to-point" && data.bookingDetails.pointToPointData) {
    pickupLocation = data.bookingDetails.pointToPointData.pickupLocation;
  } else if (formType === "hourly" && data.bookingDetails.hourlyData) {
    pickupLocation = data.bookingDetails.hourlyData.pickupLocation;
  } else if (formType === "airport" && data.bookingDetails.airportData) {
    pickupLocation = data.bookingDetails.airportData.pickupLocation;
  } else if (formType === "multi-day" && data.bookingDetails.multiDayData) {
    pickupLocation = data.bookingDetails.multiDayData.days[0]?.pickupLocation;
  }

  return pickupLocation?.coordinates || null;
};

// Helper function to get pickup address
const getPickupAddress = (data: BookingData): string | null => {
  const formType = data.bookingDetails.formType;
  let pickupLocation = null;

  if (formType === "point-to-point" && data.bookingDetails.pointToPointData) {
    pickupLocation = data.bookingDetails.pointToPointData.pickupLocation;
  } else if (formType === "hourly" && data.bookingDetails.hourlyData) {
    pickupLocation = data.bookingDetails.hourlyData.pickupLocation;
  } else if (formType === "airport" && data.bookingDetails.airportData) {
    pickupLocation = data.bookingDetails.airportData.pickupLocation;
  } else if (formType === "multi-day" && data.bookingDetails.multiDayData) {
    pickupLocation = data.bookingDetails.multiDayData.days[0]?.pickupLocation;
  }

  return pickupLocation?.address || null;
};

/**
 * Get the pickup city from coordinates using only reverse geocoding APIs
 * @version 3.0.0 - Using only coordinate-based city extraction with no text parsing
 * @param formData The form data containing pickup information
 * @returns The city name or null if not found
 */
export const getPickupCity = async (formData: any): Promise<string | null> => {
  console.log(
    "[getPickupCity] Version 3.0.0 - Getting pickup city from coordinates only"
  );

  // Import both API modules upfront to avoid dynamic import issues
  const { getCityFromCoordinates: getHereCity } = await import(
    "./hereApi"
  );
  const { getCityFromCoordinates: getMapboxCity } = await import(
    "./mapboxApi"
  );

  try {
    // Extract coordinates from the form data
    const pickupCoordinates =
      formData.pickupCoordinates ||
      formData.bookingDetails?.pointToPointData?.pickupLocation?.coordinates ||
      formData.bookingDetails?.airportData?.pickupLocation?.coordinates ||
      formData.bookingDetails?.hourlyData?.pickupLocation?.coordinates;

    if (!pickupCoordinates) {
      console.log("[getPickupCity] No coordinates found in form data");
      return null;
    }

    console.log(
      "[getPickupCity] Extracting city from coordinates:",
      pickupCoordinates
    );

    // Ensure coordinates are in the correct format
    let coordinates: [number, number];

    // Check if coordinates are in object format {latitude, longitude}
    if (
      typeof pickupCoordinates === "object" &&
      "latitude" in pickupCoordinates &&
      "longitude" in pickupCoordinates
    ) {
      console.log(
        "[getPickupCity] Converting coordinates from object format to array format"
      );
      // Convert to array format [longitude, latitude] for HERE API
      coordinates = [pickupCoordinates.longitude, pickupCoordinates.latitude];
    } else if (
      Array.isArray(pickupCoordinates) &&
      pickupCoordinates.length === 2
    ) {
      // Already in array format, but we need to ensure the order is correct
      coordinates = [...pickupCoordinates] as [number, number];

      // Check if coordinates might be in the wrong order (latitude, longitude)
      // Longitude should be between -180 and 180, latitude between -90 and 90
      if (Math.abs(coordinates[0]) > 90 && Math.abs(coordinates[1]) <= 180) {
        // Coordinates are likely in the wrong order, swap them
        console.log(
          "[getPickupCity] Coordinates appear to be in wrong order, swapping"
        );
        coordinates = [coordinates[1], coordinates[0]];
      }
    } else {
      console.error(
        "[getPickupCity] Invalid coordinates format:",
        pickupCoordinates
      );
      return null;
    }

    // Validate coordinates are in reasonable ranges
    if (Math.abs(coordinates[0]) > 180 || Math.abs(coordinates[1]) > 90) {
      console.error(
        "[getPickupCity] Coordinates out of valid range:",
        coordinates
      );
      return null;
    }

    // Try to get city from HERE API
    console.log(
      "[getPickupCity] Trying HERE API with coordinates:",
      coordinates
    );
    const hereCity = await getHereCity(coordinates);

    if (hereCity) {
      console.log("[getPickupCity] City found from HERE API:", hereCity);
      return hereCity;
    }

    console.log(
      "[getPickupCity] No city found from HERE API, trying Mapbox API"
    );

    // For Mapbox, we need to ensure coordinates are in [longitude, latitude] order
    const mapboxCity = await getMapboxCity(coordinates);

    if (mapboxCity) {
      console.log("[getPickupCity] City found from Mapbox API:", mapboxCity);
      return mapboxCity;
    }

    console.log("[getPickupCity] No city found from any API service");
    return null;
  } catch (error) {
    console.error("[getPickupCity] Error getting pickup city:", error);
    return null;
  }
};

const getPickupDate = (data: BookingData): string => {
  const formType = data.bookingDetails.formType;

  if (formType === "point-to-point" && data.bookingDetails.pointToPointData) {
    return data.bookingDetails.pointToPointData.pickupDate;
  } else if (formType === "hourly" && data.bookingDetails.hourlyData) {
    return data.bookingDetails.hourlyData.pickupDate;
  } else if (formType === "airport" && data.bookingDetails.airportData) {
    return data.bookingDetails.airportData.pickupDate;
  } else if (formType === "multi-day" && data.bookingDetails.multiDayData) {
    return data.bookingDetails.multiDayData.pickupDate;
  }

  return "";
};

const getTotalPassengers = (data: BookingData): string => {
  const formType = data.bookingDetails.formType;
  let adults = 0;
  let children = 0;

  if (formType === "point-to-point" && data.bookingDetails.pointToPointData) {
    adults = data.bookingDetails.pointToPointData.adults;
    children = data.bookingDetails.pointToPointData.children || 0;
  } else if (formType === "hourly" && data.bookingDetails.hourlyData) {
    adults = data.bookingDetails.hourlyData.adults;
    children = data.bookingDetails.hourlyData.children || 0;
  } else if (formType === "airport" && data.bookingDetails.airportData) {
    adults = data.bookingDetails.airportData.adults;
    children = data.bookingDetails.airportData.children || 0;
  } else if (formType === "multi-day" && data.bookingDetails.multiDayData) {
    adults = data.bookingDetails.multiDayData.adults;
    children = data.bookingDetails.multiDayData.children || 0;
  }

  return `${adults + children} pax`;
};

// Format date for email display
const formatDateForEmail = (date: string) => {
  return new Date(date).toLocaleString("en-US", {
    weekday: "short",
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

// Detect if we're in WordPress environment
const detectWordPressEnv = (): {
  isWordPress: boolean;
  apiBaseUrl?: string;
} => {
  if (typeof window === "undefined") {
    return { isWordPress: false };
  }

  // Check for WordPress-specific global variables or paths
  const isWordPress =
    // Check URL patterns
    window.location.pathname.includes("/wp-") ||
    window.location.href.includes("/wp-") ||
    // Check for WordPress global
    typeof window["wp"] !== "undefined";

  if (isWordPress) {
    return {
      isWordPress: true,
      apiBaseUrl: "/wp-json/limo-booking/v1/proxy/emails",
    };
  }

  return { isWordPress: false };
};
