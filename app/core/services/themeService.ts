interface ThemeState {
  color: {
    base: string;
    light: string;
    dark: string;
  };
  background: {
    main: string;
    surface: string;
    surfaceDark: string;
  };
  font: {
    primary: string;
    secondary: string;
    muted: string;
    disabled: string;
  };
}

// Immediate logging when file loads
console.log('ThemeService: Loading...', {
  windowExists: typeof window !== 'undefined',
  hasConfig: !!window?.limoBookingConfig,
  config: window?.limoBookingConfig,
  timestamp: new Date().toISOString()
});

// Check if we're in WordPress mode
const isWordPress = typeof window !== 'undefined' && !!window.limoBookingConfig?.isWordPress;

// Define WordPress theme interface
interface WordPressTheme {
  primary_color: string;
  primary_light: string;
  primary_dark: string;
  background: string;
  surface: string;
  surface_dark: string;
  text_primary: string;
  text_secondary: string;
  text_muted: string;
  text_disabled: string;
  mapStyle: string;
  mapTheme: 'dark' | 'light';
  is_custom_theme?: boolean;
  theme_scheme?: string;
}

// Log WordPress config immediately
console.log('ThemeService: WordPress Config Check', {
  isWordPress,
  theme: window.limoBookingConfig?.theme,
  mapStyle: window.limoBookingConfig?.theme?.mapStyle,
  timestamp: new Date().toISOString()
});

export const themeService = {
  currentTheme: null as ThemeState | null,
  _isApplyingTheme: false, // Add recursion guard

  saveTheme(theme: ThemeState) {
    console.log('ThemeService: Saving theme', { theme });
    this.currentTheme = theme;
    
    // Only apply theme if we're not already in the process of applying one
    if (!this._isApplyingTheme) {
      this.applyTheme(theme);
    }
  },

  loadTheme(): ThemeState | null {
    console.log('ThemeService: Loading theme');
    try {
      // Try WordPress config first
      if (isWordPress && window.limoBookingConfig?.theme) {
        console.log('ThemeService: WordPress theme found', window.limoBookingConfig.theme);
        const wpTheme = window.limoBookingConfig.theme;
        const theme = {
          color: {
            base: wpTheme.primary_color,
            light: wpTheme.primary_light,
            dark: wpTheme.primary_dark
          },
          background: {
            main: wpTheme.background,
            surface: wpTheme.surface,
            surfaceDark: wpTheme.surface_dark
          },
          font: {
            primary: wpTheme.text_primary,
            secondary: wpTheme.text_secondary,
            muted: wpTheme.text_muted,
            disabled: wpTheme.text_disabled
          }
        };
        this.currentTheme = theme;
        return theme;
      }

      // Fall back to defaults
      return this.currentTheme || {
        color: {
          base: '#765a3d',
          light: '#8b6d4c',
          dark: '#5d472f'
        },
        background: {
          main: '#000000',
          surface: '#141414',
          surfaceDark: '#1a1a1a'
        },
        font: {
          primary: '#ffffff',
          secondary: '#e5e7eb',
          muted: '#9ca3af',
          disabled: '#6b7280'
        }
      };
    } catch (error) {
      console.error('ThemeService: Error loading theme:', error);
      return null;
    }
  },

  applyTheme(theme: ThemeState) {
    // CRITICAL FIX: If theme is being directly applied by WordPress component, don't process it
    if ((window as any).isDirectlyApplyingTheme) {
      console.log('ThemeService: Theme is being directly applied by WordPress, skipping to prevent feedback loops');
      this.currentTheme = theme; // Still update current theme for consistency
      return;
    }
    
    // Guard against recursion
    if (this._isApplyingTheme) {
      console.log('ThemeService: Already applying theme, skipping to prevent recursion');
      return;
    }
    
    this._isApplyingTheme = true;
    console.log('ThemeService: Applying theme', { theme });
    
    try {
      const root = document.documentElement;
      this.currentTheme = theme;

      // Always apply theme values
      root.style.setProperty('--primary', theme.color.base);
      root.style.setProperty('--primary-light', theme.color.light);
      root.style.setProperty('--primary-dark', theme.color.dark);
      
      root.style.setProperty('--background', theme.background.main);
      root.style.setProperty('--surface', theme.background.surface);
      root.style.setProperty('--surface-dark', theme.background.surfaceDark);
      
      root.style.setProperty('--text-primary', theme.font.primary);
      root.style.setProperty('--text-secondary', theme.font.secondary);
      root.style.setProperty('--text-muted', theme.font.muted);
      root.style.setProperty('--text-disabled', theme.font.disabled);

      // Calculate and set transparent variants
      const rgbBase = this.hexToRgb(theme.color.base);
      if (rgbBase) {
        root.style.setProperty(
          '--primary-transparent',
          `rgba(${rgbBase.r}, ${rgbBase.g}, ${rgbBase.b}, 0.5)`
        );
      }

      // Dispatch theme change event to notify components
      this.dispatchThemeChangeEvent();

      // Log final state after changes
      const style = getComputedStyle(root);
      console.log('ThemeService: Theme state', {
        theme,
        appliedValues: {
          primary: style.getPropertyValue('--primary').trim(),
          background: style.getPropertyValue('--background').trim(),
          textPrimary: style.getPropertyValue('--text-primary').trim(),
          surface: style.getPropertyValue('--surface').trim()
        }
      });

      // CRITICAL FIX: Only send theme to WordPress if:
      // 1. We're in WordPress mode
      // 2. We're not in a recursion scenario 
      // 3. This change came from user interaction, not WordPress itself
      if (isWordPress && 
          window.limoBookingConfig && 
          !(window as any).isDirectlyApplyingTheme && 
          !(window as any).isWordPressThemeEventActive) {
        
        // Set a flag to prevent recursion
        (window as any).isWordPressThemeEventActive = true;
        
        try {
          // Update WordPress config with new theme values
          window.limoBookingConfig.theme = {
            ...window.limoBookingConfig.theme,
            primary_color: theme.color.base,
            primary_light: theme.color.light,
            primary_dark: theme.color.dark,
            background: theme.background.main,
            surface: theme.background.surface,
            surface_dark: theme.background.surfaceDark,
            text_primary: theme.font.primary,
            text_secondary: theme.font.secondary,
            text_muted: theme.font.muted,
            text_disabled: theme.font.disabled,
          } as WordPressTheme;
          
          // Add custom theme properties
          (window.limoBookingConfig.theme as WordPressTheme).is_custom_theme = true;
          (window.limoBookingConfig.theme as WordPressTheme).theme_scheme = 'custom';
          
          // Use a dedicated event name for the admin panel
          console.log('ThemeService: Dispatching admin panel theme update event');
          const wpAdminEvent = new CustomEvent('wp_admin_theme_update', {
            detail: {
              theme: window.limoBookingConfig.theme,
              source: 'react_app'
            }
          });
          window.dispatchEvent(wpAdminEvent);
          console.log('ThemeService: Admin panel theme update event dispatched');
        } finally {
          // Clear the flag
          (window as any).isWordPressThemeEventActive = false;
        }
      }
    } catch (error) {
      console.error('ThemeService: Error applying theme:', error);
    } finally {
      // Always reset the recursion guard
      this._isApplyingTheme = false;
    }
  },

  hexToRgb(hex: string) {
    try {
      // Remove # if present
      hex = hex.replace(/^#/, '');
      
      // Parse both 3-digit and 6-digit hex codes
      const bigint = parseInt(hex.length === 3 
        ? hex.split('').map(c => c + c).join('')
        : hex, 16);
      
      return {
        r: (bigint >> 16) & 255,
        g: (bigint >> 8) & 255,
        b: bigint & 255
      };
    } catch (error) {
      console.error('Error converting hex to RGB:', error);
      return null;
    }
  },

  calculateBrightness(hex: string): number {
    try {
      const rgb = this.hexToRgb(hex);
      if (!rgb) return 0;
      return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
    } catch (error) {
      console.error('Error calculating brightness:', error);
      return 0;
    }
  },

  isParentThemeDark(): boolean {
    try {
      const root = document.documentElement;
      const style = getComputedStyle(root);
      const bgColor = style.backgroundColor;
      
      // Check if background color is available
      if (bgColor) {
        // Extract RGB values
        const rgb = bgColor.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/i);
        if (rgb) {
          const r = parseInt(rgb[1], 10);
          const g = parseInt(rgb[2], 10);
          const b = parseInt(rgb[3], 10);
          
          // Calculate brightness using the same formula as calculateBrightness
          const brightness = (r * 299 + g * 587 + b * 114) / 1000;
          
          // Return true if dark (brightness < 128)
          return brightness < 128;
        }
      }
      
      // Check if prefers-color-scheme is available
      if (window.matchMedia) {
        return window.matchMedia('(prefers-color-scheme: dark)').matches;
      }
      
      // Default to dark if we can't determine
      return true;
    } catch (error) {
      console.error('Error detecting parent theme:', error);
      return true; // Default to dark theme on error
    }
  },

  getThemeColors() {
    try {
      const root = document.documentElement;
      const style = getComputedStyle(root);
      return {
        primary: style.getPropertyValue('--primary').trim(),
        background: style.getPropertyValue('--background').trim(),
        surface: style.getPropertyValue('--surface').trim(),
        text: style.getPropertyValue('--text-primary').trim(),
      };
    } catch (error) {
      console.error('Error getting theme colors:', error);
      return {
        primary: '#765a3d',
        background: '#000000',
        surface: '#141414',
        text: '#ffffff',
      };
    }
  },

  dispatchThemeChangeEvent() {
    // Use static counter to prevent endless loops
    if (!(window as any).__themeChangeCooldown) {
      (window as any).__themeChangeCooldown = 0;
    }
    
    // If we've dispatched too many events too quickly, just stop
    const now = Date.now();
    if (now - (window as any).__themeChangeCooldown < 1000) {
      console.log('ThemeService: Too many theme events, cooling down');
      return;
    }
    
    // Important: Skip event if we're in an apply cycle to prevent recursion
    if (this._isApplyingTheme || (window as any).isDirectlyApplyingTheme) {
      console.log('ThemeService: Skipping event dispatch during theme application');
      return;
    }
    
    // Set cooldown
    (window as any).__themeChangeCooldown = now;
    
    try {
      // Don't create new events when we're already in the event handler
      if ((window as any).__inThemeEvent) {
        console.log('ThemeService: Already in theme event handler');
        return;
      }
      
      (window as any).__inThemeEvent = true;
      
      try {
        // Create and dispatch a custom event for theme changes, with minimal information
        const eventData = { 
          isDark: this.isParentThemeDark(),
          timestamp: now
        };
        
        // Use event constructor directly, avoiding complex object references
        const themeChangeEvent = new CustomEvent('themechange', {
          detail: eventData
        });
        
        window.dispatchEvent(themeChangeEvent);
        console.log('ThemeService: Theme change event dispatched', eventData);
      } finally {
        // Always clean up event flag
        (window as any).__inThemeEvent = false;
      }
    } catch (error) {
      console.error('ThemeService: Error dispatching theme change event:', error);
    }
  }
}; 