/**
 * Limo Booking Form WordPress Plugin Styles
 * This file contains namespaced styles for the WordPress plugin integration
 * using proper CSS specificity instead of !important declarations
 */

/* Root namespace for the plugin */
.limo-booking-plugin {
  /* Use CSS variables for theming */
  --limo-primary: var(--wp-primary, #111827);
  --limo-primary-light: var(--wp-primary-light, #8b6d4c);
  --limo-primary-dark: var(--wp-primary-dark, #5d472f);
  --limo-primary-transparent: var(--wp-primary-transparent, rgba(118, 90, 61, 0.5));
  --limo-background: var(--wp-background, #000000);
  --limo-surface: var(--wp-surface, #141414);
  --limo-surface-dark: var(--wp-surface-dark, #1A1A1A);
  --limo-surface-light: var(--wp-surface-light, rgba(255, 255, 255, 0.03));
  --limo-text-primary: var(--wp-text-primary, #ffffff);
  --limo-text-secondary: var(--wp-text-secondary, #e5e7eb);
  --limo-text-muted: var(--wp-text-muted, #9ca3af);
  --limo-text-disabled: var(--wp-text-disabled, rgba(255, 255, 255, 0.5));
  --limo-border: var(--wp-border, #333333);
  --limo-border-hover: var(--wp-border-hover, #4D4D4D);
  --limo-success: #4CAF50;
  --limo-error: #F44336;
  --limo-warning: #FFC107;

  /* Apply base styles to the container */
  font-family: 'Raleway', sans-serif;
  color: var(--limo-text-primary);
  background-color: var(--limo-background);
}

/* Light theme variant */
.limo-booking-plugin[data-theme="light"] {
  --limo-text-primary: #000000;
  --limo-text-secondary: #4b5563;
  --limo-text-muted: #6b7280;
  --limo-text-disabled: rgba(0, 0, 0, 0.5);
  --limo-background: #ffffff;
  --limo-surface: #f3f4f6;
  --limo-surface-dark: #e5e7eb;
  --limo-surface-light: rgba(0, 0, 0, 0.03);
  --limo-border: #e5e7eb;
  --limo-border-hover: #d1d5db;
}

/* Text styles */
.limo-booking-plugin .limo-text {
  color: var(--limo-text-primary);
}

.limo-booking-plugin .limo-text-secondary {
  color: var(--limo-text-secondary);
}

.limo-booking-plugin .limo-text-muted {
  color: var(--limo-text-muted);
}

.limo-booking-plugin .limo-text-disabled {
  color: var(--limo-text-disabled);
}

/* Background styles */
.limo-booking-plugin .limo-bg-primary {
  background-color: var(--limo-primary);
  color: #ffffff;
}

.limo-booking-plugin .limo-bg-primary * {
  color: #ffffff;
}

.limo-booking-plugin .limo-bg-primary svg {
  color: #ffffff;
  fill: #ffffff;
  stroke: #ffffff;
}

.limo-booking-plugin .limo-bg-surface {
  background-color: var(--limo-surface);
}

.limo-booking-plugin .limo-bg-surface-dark {
  background-color: var(--limo-surface-dark);
}

.limo-booking-plugin .limo-bg-surface-light {
  background-color: var(--limo-surface-light);
}

/* Button styles */
.limo-booking-plugin .limo-button {
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.limo-booking-plugin .limo-button-primary {
  background-color: var(--limo-primary);
  color: #ffffff;
}

.limo-booking-plugin .limo-button-primary:hover {
  background-color: var(--limo-primary-light);
}

.limo-booking-plugin .limo-button-primary:active {
  background-color: var(--limo-primary-dark);
}

.limo-booking-plugin .limo-button-primary * {
  color: #ffffff;
}

.limo-booking-plugin .limo-button-primary svg {
  color: #ffffff;
  fill: #ffffff;
  stroke: #ffffff;
}

/* Form input styles */
.limo-booking-plugin .limo-input {
  background-color: var(--limo-surface);
  border: 1px solid var(--limo-border);
  color: var(--limo-text-primary);
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
}

.limo-booking-plugin .limo-input:focus {
  border-color: var(--limo-primary);
  outline: none;
  box-shadow: 0 0 0 2px var(--limo-primary-transparent);
}

.limo-booking-plugin .limo-input::placeholder {
  color: var(--limo-text-muted);
}

/* Tabs styles */
.limo-booking-plugin .limo-tabs {
  display: flex;
  border-bottom: 1px solid var(--limo-border);
}

.limo-booking-plugin .limo-tab {
  padding: 0.75rem 1rem;
  color: var(--limo-text-secondary);
  cursor: pointer;
}

.limo-booking-plugin .limo-tab[data-active="true"],
.limo-booking-plugin .limo-tab[aria-selected="true"] {
  color: #ffffff;
  background-color: var(--limo-primary);
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
}

.limo-booking-plugin .limo-tab[data-active="true"] *,
.limo-booking-plugin .limo-tab[aria-selected="true"] * {
  color: #ffffff;
}

/* Map markers */
.limo-booking-plugin .limo-marker {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-weight: 600;
  font-size: 14px;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s;
  cursor: pointer;
  background-color: var(--limo-primary);
}

.limo-booking-plugin .limo-marker:hover {
  transform: scale(1.1);
}

.limo-booking-plugin .limo-pickup-marker {
  background-color: var(--limo-success);
}

.limo-booking-plugin .limo-stop-marker {
  background-color: var(--limo-primary);
}

.limo-booking-plugin .limo-dropoff-marker {
  background-color: var(--limo-error);
}

/* Toggle button styles */
.limo-booking-plugin .limo-toggle {
  position: relative;
  display: inline-flex;
  height: 1.5rem;
  width: 2.75rem;
  align-items: center;
  border-radius: 9999px;
  transition-property: background-color;
  transition-duration: 200ms;
  transition-timing-function: ease-in-out;
}

.limo-booking-plugin .limo-toggle[aria-checked="true"] {
  background-color: var(--limo-primary);
}

.limo-booking-plugin .limo-toggle[aria-checked="false"] {
  background-color: var(--limo-text-disabled);
}

.limo-booking-plugin .limo-toggle-thumb {
  position: absolute;
  height: 1rem;
  width: 1rem;
  background-color: white;
  border-radius: 9999px;
  transition-property: transform;
  transition-duration: 200ms;
  transition-timing-function: ease-in-out;
  transform: translateX(0.25rem);
}

.limo-booking-plugin .limo-toggle[aria-checked="true"] .limo-toggle-thumb {
  transform: translateX(1.5rem);
}

/* Datepicker overrides */
.limo-booking-plugin .react-datepicker {
  background-color: var(--limo-surface);
  border: 1px solid var(--limo-border);
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.limo-booking-plugin .react-datepicker__header {
  background-color: var(--limo-surface);
  border-bottom: 1px solid var(--limo-border);
}

.limo-booking-plugin .react-datepicker__current-month {
  color: var(--limo-text-primary);
}

.limo-booking-plugin .react-datepicker__day-name {
  color: var(--limo-text-muted);
}

.limo-booking-plugin .react-datepicker__day {
  color: var(--limo-text-primary);
}

.limo-booking-plugin .react-datepicker__day:hover {
  background-color: var(--limo-primary);
  color: #ffffff;
}

.limo-booking-plugin .react-datepicker__day--selected,
.limo-booking-plugin .react-datepicker__day--keyboard-selected {
  background-color: var(--limo-primary);
  color: #ffffff;
}

.limo-booking-plugin .react-datepicker__day--disabled {
  color: var(--limo-text-disabled);
}

/* Animation classes */
.limo-booking-plugin .limo-animate-glow {
  animation: limo-glow-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes limo-glow-pulse {
  0%, 100% {
    background-color: rgba(var(--limo-primary-rgb, 118, 90, 61), 0.1);
    box-shadow: 0 0 0 0 rgba(var(--limo-primary-rgb, 118, 90, 61), 0.4);
  }
  50% {
    background-color: rgba(var(--limo-primary-rgb, 118, 90, 61), 0.3);
    box-shadow: 0 0 20px 0 rgba(var(--limo-primary-rgb, 118, 90, 61), 0.8);
  }
}

.limo-booking-plugin .limo-animate-shimmer {
  position: relative;
  overflow: hidden;
}

.limo-booking-plugin .limo-animate-shimmer::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg,
      rgba(var(--limo-primary-rgb, 118, 90, 61), 0.1),
      rgba(var(--limo-primary-rgb, 118, 90, 61), 0.3),
      rgba(var(--limo-primary-rgb, 118, 90, 61), 0.1));
  animation: limo-shimmer 2s infinite;
}

@keyframes limo-shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Mantine Component Overrides for WordPress - High Specificity */
.limo-booking-plugin .mantine-TextInput-root,
.limo-booking-plugin .mantine-Textarea-root,
.limo-booking-plugin .mantine-Select-root,
.limo-booking-plugin .mantine-NumberInput-root {
  width: 100% !important;
}

/* Input field styling with maximum specificity */
.limo-booking-plugin .mantine-TextInput-input,
.limo-booking-plugin .mantine-Textarea-input,
.limo-booking-plugin .mantine-Select-input,
.limo-booking-plugin .mantine-NumberInput-input,
.limo-booking-plugin input[type="text"],
.limo-booking-plugin input[type="email"],
.limo-booking-plugin input[type="tel"],
.limo-booking-plugin input[type="number"],
.limo-booking-plugin textarea {
  background-color: var(--limo-surface) !important;
  border: 1px solid var(--limo-border) !important;
  color: var(--limo-text-primary) !important;
  border-radius: 0.375rem !important;
  padding: 0.75rem !important;
  font-size: 1rem !important;
  line-height: 1.5 !important;
  /* Hide browser autofill icons */
  background-image: none !important;
}

/* Specifically target email inputs to hide autofill icons */
.limo-booking-plugin input[type="email"]::-webkit-credentials-auto-fill-button,
.limo-booking-plugin input[type="email"]::-webkit-contacts-auto-fill-button {
  visibility: hidden !important;
  display: none !important;
  pointer-events: none !important;
  height: 0 !important;
  width: 0 !important;
  margin: 0 !important;
}

.limo-booking-plugin .mantine-TextInput-input:focus,
.limo-booking-plugin .mantine-Textarea-input:focus,
.limo-booking-plugin .mantine-Select-input:focus,
.limo-booking-plugin .mantine-NumberInput-input:focus,
.limo-booking-plugin input:focus,
.limo-booking-plugin textarea:focus {
  border-color: var(--limo-primary) !important;
  box-shadow: 0 0 0 2px var(--limo-primary-transparent) !important;
  outline: none !important;
}

.limo-booking-plugin .mantine-TextInput-input::placeholder,
.limo-booking-plugin .mantine-Textarea-input::placeholder,
.limo-booking-plugin .mantine-Select-input::placeholder,
.limo-booking-plugin .mantine-NumberInput-input::placeholder,
.limo-booking-plugin input::placeholder,
.limo-booking-plugin textarea::placeholder {
  color: var(--limo-text-muted) !important;
}

.limo-booking-plugin .mantine-TextInput-label,
.limo-booking-plugin .mantine-Textarea-label,
.limo-booking-plugin .mantine-Select-label,
.limo-booking-plugin .mantine-NumberInput-label,
.limo-booking-plugin label {
  color: var(--limo-text-primary) !important;
  font-weight: 500 !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
}

/* Button styling */
.limo-booking-plugin .mantine-Button-root,
.limo-booking-plugin button {
  background-color: var(--limo-primary) !important;
  color: #ffffff !important;
  border: none !important;
  border-radius: 0.375rem !important;
  padding: 0.75rem 1rem !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease-in-out !important;
}

.limo-booking-plugin .mantine-Button-root:hover,
.limo-booking-plugin button:hover {
  background-color: var(--limo-primary-light) !important;
}

.limo-booking-plugin .mantine-Button-root:active,
.limo-booking-plugin button:active {
  background-color: var(--limo-primary-dark) !important;
}

/* Number Input Controls - Fix the increment/decrement buttons */
.limo-booking-plugin .mantine-NumberInput-control,
.limo-booking-plugin .mantine-NumberInput-controls button {
  background-color: var(--limo-surface) !important;
  border: 1px solid var(--limo-border) !important;
  color: var(--limo-text-primary) !important;
}

.limo-booking-plugin .mantine-NumberInput-control:hover,
.limo-booking-plugin .mantine-NumberInput-controls button:hover {
  background-color: var(--limo-surface-light) !important;
  border-color: var(--limo-primary) !important;
}

.limo-booking-plugin .mantine-NumberInput-controls {
  border-left: 1px solid var(--limo-border) !important;
}

/* Switch/Toggle styling */
.limo-booking-plugin .mantine-Switch-track {
  background-color: var(--limo-text-disabled) !important;
  border: none !important;
}

.limo-booking-plugin .mantine-Switch-track[data-checked] {
  background-color: var(--limo-primary) !important;
}

.limo-booking-plugin .mantine-Switch-thumb {
  background-color: #ffffff !important;
  border: none !important;
}

.limo-booking-plugin .mantine-Tabs-tab {
  color: var(--limo-text-secondary) !important;
}

.limo-booking-plugin .mantine-Tabs-tab[data-active] {
  color: #ffffff !important;
  background-color: var(--limo-primary) !important;
}

.limo-booking-plugin .mantine-Paper-root,
.limo-booking-plugin .mantine-Card-root {
  background-color: var(--limo-surface) !important;
  color: var(--limo-text-primary) !important;
}

.limo-booking-plugin .mantine-Text-root,
.limo-booking-plugin .mantine-Title-root {
  color: var(--limo-text-primary) !important;
}

.limo-booking-plugin .mantine-Group-root {
  color: var(--limo-text-primary) !important;
}

/* Dropdown and Select overrides */
.limo-booking-plugin .mantine-Select-dropdown {
  background-color: var(--limo-surface) !important;
  border-color: var(--limo-border) !important;
}

.limo-booking-plugin .mantine-Select-option {
  color: var(--limo-text-primary) !important;
}

.limo-booking-plugin .mantine-Select-option[data-selected] {
  background-color: var(--limo-primary) !important;
  color: #ffffff !important;
}

.limo-booking-plugin .mantine-Select-option:hover {
  background-color: var(--limo-surface-light) !important;
}

/* Modal overrides */
.limo-booking-plugin .mantine-Modal-content {
  background-color: var(--limo-surface) !important;
  color: var(--limo-text-primary) !important;
}

.limo-booking-plugin .mantine-Modal-header {
  background-color: var(--limo-surface) !important;
  border-bottom-color: var(--limo-border) !important;
}

.limo-booking-plugin .mantine-Modal-title {
  color: var(--limo-text-primary) !important;
}

/* Checkbox and Radio overrides */
.limo-booking-plugin .mantine-Checkbox-label,
.limo-booking-plugin .mantine-Radio-label {
  color: var(--limo-text-primary) !important;
}

/* CRITICAL WORDPRESS FIXES - Maximum Specificity */

/* Date Picker Styling - Critical Fix for WordPress */
.limo-booking-plugin .mantine-DateTimePicker-input,
.limo-booking-plugin .mantine-DateInput-input,
.limo-booking-plugin .mantine-DatePicker-input,
.limo-booking-plugin input[type="datetime-local"],
.limo-booking-plugin input[type="date"],
.limo-booking-plugin input[type="time"] {
  background-color: var(--limo-surface) !important;
  border: 1px solid var(--limo-border) !important;
  color: var(--limo-text-primary) !important;
  border-radius: 0.375rem !important;
  padding: 0.75rem !important;
}

.limo-booking-plugin .mantine-DateTimePicker-input:focus,
.limo-booking-plugin .mantine-DateInput-input:focus,
.limo-booking-plugin .mantine-DatePicker-input:focus {
  border-color: var(--limo-primary) !important;
  box-shadow: 0 0 0 2px var(--limo-primary-transparent) !important;
}

/* Date picker dropdown */
.limo-booking-plugin .mantine-DateTimePicker-dropdown,
.limo-booking-plugin .mantine-DatePicker-dropdown {
  background-color: var(--limo-surface) !important;
  border: 1px solid var(--limo-border) !important;
  border-radius: 0.5rem !important;
}

/* AGGRESSIVE MANTINE OVERRIDES - Force our styling */
.limo-booking-plugin [class*="mantine-"] {
  font-family: 'Raleway', sans-serif !important;
}

/* Location Input Icons - Ensure proper positioning */
.limo-booking-plugin .absolute.left-3 {
  left: 0.75rem !important;
  z-index: 10 !important;
}

.limo-booking-plugin .absolute.right-3 {
  right: 0.75rem !important;
  z-index: 10 !important;
}

/* Map pin icons in location inputs */
.limo-booking-plugin .w-5.h-5.text-primary {
  color: var(--limo-primary) !important;
  width: 1.25rem !important;
  height: 1.25rem !important;
}

/* Ensure location input has proper padding for icons */
.limo-booking-plugin input[type="text"].pl-10 {
  padding-left: 2.5rem !important;
}

.limo-booking-plugin input[type="text"].pr-20 {
  padding-right: 5rem !important;
}

/* Button alignment and spacing fixes */
.limo-booking-plugin .flex.items-center.gap-2,
.limo-booking-plugin .flex.items-center.gap-3 {
  align-items: center !important;
}

.limo-booking-plugin .flex-shrink-0 {
  flex-shrink: 0 !important;
}

/* Stop action buttons */
.limo-booking-plugin button[title="Remove stop"] {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 2.5rem !important;
  min-height: 2.5rem !important;
}

/* Toggle button alignment */
.limo-booking-plugin .mantine-Switch-root {
  display: flex !important;
  align-items: center !important;
}

/* Force all Mantine inputs to use our styling */
.limo-booking-plugin [class*="mantine-"][class*="-input"],
.limo-booking-plugin [class*="mantine-TextInput"],
.limo-booking-plugin [class*="mantine-NumberInput"],
.limo-booking-plugin [class*="mantine-Select"],
.limo-booking-plugin [class*="mantine-Textarea"] {
  background-color: var(--limo-surface) !important;
  border: 1px solid var(--limo-border) !important;
  color: var(--limo-text-primary) !important;
  border-radius: 0.375rem !important;
}

/* Force all Mantine buttons to use our styling */
.limo-booking-plugin [class*="mantine-Button"],
.limo-booking-plugin [class*="mantine-"][class*="-control"] {
  background-color: var(--limo-primary) !important;
  color: #ffffff !important;
  border: none !important;
  border-radius: 0.375rem !important;
}

/* Force Mantine number input controls */
.limo-booking-plugin [class*="mantine-NumberInput"] [class*="-control"] {
  background-color: var(--limo-surface) !important;
  border: 1px solid var(--limo-border) !important;
  color: var(--limo-text-primary) !important;
}

/* Force Mantine switch styling */
.limo-booking-plugin [class*="mantine-Switch"] [class*="-track"] {
  background-color: var(--limo-text-disabled) !important;
}

.limo-booking-plugin [class*="mantine-Switch"] [class*="-track"][data-checked] {
  background-color: var(--limo-primary) !important;
}

/* Vehicle Selection Grid - Force proper layout */
.limo-booking-plugin .vehicle-grid,
.limo-booking-plugin .vehicles-container {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
  gap: 1rem !important;
  width: 100% !important;
}

.limo-booking-plugin .vehicle-card {
  background-color: var(--limo-surface) !important;
  border: 1px solid var(--limo-border) !important;
  border-radius: 0.5rem !important;
  padding: 1rem !important;
  cursor: pointer !important;
  transition: all 0.2s ease-in-out !important;
}

.limo-booking-plugin .vehicle-card:hover {
  border-color: var(--limo-primary) !important;
  box-shadow: 0 4px 12px var(--limo-shadow) !important;
}

.limo-booking-plugin .vehicle-card.selected {
  border-color: var(--limo-primary) !important;
  background-color: var(--limo-primary-transparent) !important;
}

/* FORCE OVERRIDE ALL MANTINE COMPONENT STYLES */
.limo-booking-plugin [data-mantine-color-scheme] {
  --mantine-color-body: var(--limo-background) !important;
  --mantine-color-text: var(--limo-text-primary) !important;
  --mantine-color-primary-filled: var(--limo-primary) !important;
  --mantine-color-primary-filled-hover: var(--limo-primary-light) !important;
  --mantine-color-default-border: var(--limo-border) !important;
  --mantine-color-default: var(--limo-surface) !important;
  --mantine-color-default-hover: var(--limo-surface-light) !important;
}

/* Integration guide */
/*
To use this CSS in your WordPress plugin:

1. Add the 'limo-booking-plugin' class to your root container
2. Use the provided namespaced classes for styling elements
3. For theme switching, add data-theme="light" or data-theme="dark" to the root container

Example:
<div class="limo-booking-plugin" data-theme="dark">
  <button class="limo-button limo-button-primary">Book Now</button>
</div>
*/