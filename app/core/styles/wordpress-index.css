/*
 * WORDPRESS PLUGIN CSS ENTRY POINT
 * Bulletproof CSS architecture for commercial WordPress plugin distribution
 */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Raleway:wght@300;400;500;600;700&display=swap');

/* Import Mantine Core CSS - ESSENTIAL for proper component styling */
@import '@mantine/core/styles.css';

/* Import ONLY essential Tailwind utilities - NO conflicting base styles */
@import 'tailwindcss/utilities';

/* =============================================================================
   WORDPRESS THEME INTEGRATION LAYER
   ============================================================================= */

/* Allow WordPress themes to override our colors through CSS custom properties */
.limo-booking-plugin {
  /* WordPress theme integration variables */
  --limo-primary: var(--wp-limo-primary, var(--wp-primary, #111827));
  --limo-primary-light: var(--wp-limo-primary-light, var(--wp-primary-light, #8b6d4c));
  --limo-primary-dark: var(--wp-limo-primary-dark, var(--wp-primary-dark, #5d472f));
  --limo-background: var(--wp-limo-background, var(--wp-background, #000000));
  --limo-surface: var(--wp-limo-surface, var(--wp-surface, #141414));
  --limo-text-primary: var(--wp-limo-text-primary, var(--wp-text-primary, #ffffff));
  --limo-text-secondary: var(--wp-limo-text-secondary, var(--wp-text-secondary, #e5e7eb));
  --limo-text-muted: var(--wp-limo-text-muted, var(--wp-text-muted, #9ca3af));
  --limo-border: var(--wp-limo-border, var(--wp-border, #333333));
}

/* =============================================================================
   MAPBOX INTEGRATION - SCOPED TO PLUGIN
   ============================================================================= */

.limo-booking-plugin .mapboxgl-map {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  background-color: var(--limo-background);
  min-height: 300px;
  display: block;
}

.limo-booking-plugin .mapboxgl-canvas-container {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  min-height: 300px;
  display: block;
}

.limo-booking-plugin .mapboxgl-ctrl-group {
  background-color: var(--limo-surface);
  border: 1px solid var(--limo-border);
}

.limo-booking-plugin .mapboxgl-ctrl-group button {
  background-color: var(--limo-surface);
  color: var(--limo-text-primary);
  border: none;
}

.limo-booking-plugin .mapboxgl-ctrl-group button:hover {
  background-color: var(--limo-surface-light);
}

/* =============================================================================
   UTILITY CLASSES - SCOPED TO PLUGIN
   ============================================================================= */

.limo-booking-plugin .limo-hidden {
  display: none;
}

.limo-booking-plugin .limo-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.limo-booking-plugin .limo-flex {
  display: flex;
}

.limo-booking-plugin .limo-flex-col {
  flex-direction: column;
}

.limo-booking-plugin .limo-items-center {
  align-items: center;
}

.limo-booking-plugin .limo-justify-center {
  justify-content: center;
}

.limo-booking-plugin .limo-justify-between {
  justify-content: space-between;
}

.limo-booking-plugin .limo-gap-2 {
  gap: 0.5rem;
}

.limo-booking-plugin .limo-gap-4 {
  gap: 1rem;
}

.limo-booking-plugin .limo-gap-6 {
  gap: 1.5rem;
}

.limo-booking-plugin .limo-p-2 {
  padding: 0.5rem;
}

.limo-booking-plugin .limo-p-4 {
  padding: 1rem;
}

.limo-booking-plugin .limo-p-6 {
  padding: 1.5rem;
}

.limo-booking-plugin .limo-mb-2 {
  margin-bottom: 0.5rem;
}

.limo-booking-plugin .limo-mb-4 {
  margin-bottom: 1rem;
}

.limo-booking-plugin .limo-mb-6 {
  margin-bottom: 1.5rem;
}

.limo-booking-plugin .limo-w-full {
  width: 100%;
}

.limo-booking-plugin .limo-h-full {
  height: 100%;
}

.limo-booking-plugin .limo-text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.limo-booking-plugin .limo-text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.limo-booking-plugin .limo-text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.limo-booking-plugin .limo-text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.limo-booking-plugin .limo-font-medium {
  font-weight: 500;
}

.limo-booking-plugin .limo-font-semibold {
  font-weight: 600;
}

.limo-booking-plugin .limo-font-bold {
  font-weight: 700;
}

.limo-booking-plugin .limo-rounded {
  border-radius: 0.25rem;
}

.limo-booking-plugin .limo-rounded-md {
  border-radius: 0.375rem;
}

.limo-booking-plugin .limo-rounded-lg {
  border-radius: 0.5rem;
}

.limo-booking-plugin .limo-shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.limo-booking-plugin .limo-shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.limo-booking-plugin .limo-shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* =============================================================================
   ANIMATION UTILITIES
   ============================================================================= */

.limo-booking-plugin .limo-transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.limo-booking-plugin .limo-transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.limo-booking-plugin .limo-duration-200 {
  transition-duration: 200ms;
}

.limo-booking-plugin .limo-duration-300 {
  transition-duration: 300ms;
}

.limo-booking-plugin .limo-ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* =============================================================================
   RESPONSIVE UTILITIES
   ============================================================================= */

@media (min-width: 640px) {
  .limo-booking-plugin .limo-sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 768px) {
  .limo-booking-plugin .limo-md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .limo-booking-plugin .limo-md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .limo-booking-plugin .limo-lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .limo-booking-plugin .limo-lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
