import { NextRequest, NextResponse } from 'next/server';
import { getAffiliatesForQuote } from '@/lib/api/affiliates';

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const city = url.searchParams.get('city');
    
    if (!city) {
      return NextResponse.json({
        success: false,
        error: 'City parameter is required'
      }, { status: 400 });
    }
    
    console.log(`[API] Getting affiliates for city: "${city}"`);
    
    // Call the function on the server side
    const affiliates = await getAffiliatesForQuote(city);
    
    console.log(`[API] Found ${affiliates.length} affiliates for city: "${city}"`);
    
    return NextResponse.json({
      success: true,
      data: affiliates,
      count: affiliates.length
    });
    
  } catch (error) {
    console.error('[API] Error getting affiliates:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
