// SERVER ONLY: This API route uses pg and must not be imported by any React component or shared code.
import { NextResponse } from 'next/server'
import { Pool } from 'pg'

// Create a connection pool
const pool = new Pool({
  user: process.env.POSTGRES_USER || process.env.USER,
  host: process.env.POSTGRES_HOST || 'localhost',
  database: process.env.POSTGRES_DATABASE || 'wwms',
  password: process.env.POSTGRES_PASSWORD || '',
  port: parseInt(process.env.POSTGRES_PORT || '5432'),
})

const TEST_USERS = [
  {
    email: "<EMAIL>",
    password: "password123",
    role: "ADMIN",
    firstName: "Admin",
    lastName: "User"
  },
  {
    email: "<EMAIL>",
    password: "password123",
    role: "EVENT_MANAGER",
    firstName: "Event",
    lastName: "Manager"
  },
  {
    email: "<EMAIL>",
    password: "password123",
    role: "CUSTOMER",
    firstName: "Customer",
    lastName: "User"
  },
  {
    email: "<EMAIL>",
    password: "password123",
    role: "AFFILIATE",
    firstName: "Affiliate",
    lastName: "Partner"
  }
]

export async function POST() {
  try {
    // Get a client from the pool
    const client = await pool.connect()

    try {
      // Start a transaction
      await client.query('BEGIN')

      // Create or update each test user
      for (const user of TEST_USERS) {
        const result = await client.query(`
          WITH existing_user AS (
            SELECT id FROM auth.users WHERE email = $1
          ),
          upserted_user AS (
            INSERT INTO auth.users (
              email,
              encrypted_password,
              raw_app_meta_data,
              raw_user_meta_data
            )
            SELECT
              $1,
              crypt($2, gen_salt('bf')),
              '{"provider":"email","providers":["email"]}'::jsonb,
              jsonb_build_object('role', $3::text)
            WHERE NOT EXISTS (SELECT 1 FROM existing_user)
            RETURNING id
          ),
          updated_user AS (
            UPDATE auth.users
            SET
              encrypted_password = crypt($2, gen_salt('bf')),
              raw_user_meta_data = jsonb_build_object('role', $3::text),
              updated_at = now()
            WHERE id = (SELECT id FROM existing_user)
            RETURNING id
          ),
          user_id AS (
            SELECT id FROM upserted_user
            UNION ALL
            SELECT id FROM updated_user
          )
          INSERT INTO public.profiles (
            id,
            email,
            first_name,
            last_name,
            role
          )
          SELECT
            id,
            $1,
            $4,
            $5,
            $3::text
          FROM user_id
          ON CONFLICT (id) DO UPDATE SET
            email = $1,
            first_name = $4,
            last_name = $5,
            role = $3::text,
            updated_at = now()
          RETURNING id;
        `, [user.email, user.password, user.role, user.firstName, user.lastName])

        console.log(`User ${user.email} (${user.role}) created/updated with ID:`, result.rows[0]?.id)
      }

      // Commit the transaction
      await client.query('COMMIT')

      console.log('All test users created/updated successfully')
      return NextResponse.json({
        message: 'All test users created/updated successfully'
      })

    } catch (error: any) {
      // Rollback the transaction on error
      await client.query('ROLLBACK')
      throw error
    } finally {
      // Release the client back to the pool
      client.release()
    }

  } catch (error: any) {
    console.error('Error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to setup test users',
        details: error.message 
      },
      { status: 500 }
    )
  }
} 