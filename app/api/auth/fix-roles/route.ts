import { createClient } from '@/lib/supabase/server';
import { NextResponse } from 'next/server';
import { hasRole } from '@/app/lib/auth';

// This is an admin-only endpoint to fix role mismatches
export async function POST(req: Request) {
  try {
    const supabase = createClient({});
    
    // Verify the user has admin access
    const { data: session, error: sessionError } = await supabase.auth.getSession();
    if (sessionError) {
      return NextResponse.json(
        { error: 'Authentication error', details: sessionError.message },
        { status: 401 }
      );
    }

    if (!session?.session?.user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    // Use the hasRole utility for standardized role checking
    const userRoles = session.session.user.user_metadata?.roles || session.session.user.app_metadata?.roles || [];
    if (!hasRole(userRoles, 'SUPER_ADMIN')) {
      // Log unauthorized attempt
      await supabase.from('logs').insert({
        level: 'warning',
        message: 'Unauthorized attempt to use fix-roles API',
        context: {
          userId: session.session.user.id,
          email: session.session.user.email,
          roles: userRoles,
          timestamp: new Date().toISOString(),
        },
      });
      
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Get the user's roles
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role, roles')
      .eq('id', session.session.user.id)
      .single();

    if (profileError) {
      return NextResponse.json(
        { error: 'Profile fetch error', details: profileError.message },
        { status: 500 }
      );
    }

    // Consolidate roles from profile.role (string) and profile.roles (array)
    const combinedProfileRoles = Array.isArray(profile.roles) ? profile.roles : [];
    if (profile.role && !combinedProfileRoles.includes(profile.role)) {
      combinedProfileRoles.push(profile.role);
    }

    // Check if the user has SUPER_ADMIN or ADMIN role using hasRole utility
    if (!hasRole(combinedProfileRoles, 'SUPER_ADMIN') && !hasRole(combinedProfileRoles, 'ADMIN')) {
      // Log unauthorized attempt
      await supabase.from('logs').insert({
        level: 'warning',
        message: 'Unauthorized attempt to use fix-roles API',
        context: {
          userId: session.session.user.id,
          email: session.session.user.email,
          roles: combinedProfileRoles,
          timestamp: new Date().toISOString(),
        },
      });
      
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Call the fix-roles function with service role client
    // We use the service role to create a new client to ensure we have admin access
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    if (!serviceRoleKey) {
      return NextResponse.json(
        { error: 'Service role key not configured' },
        { status: 500 }
      );
    }

    const adminClient = createClient({ 
      serviceRole: serviceRoleKey 
    });

    // Use custom RPC call to fix roles directly in the database
    const { data: result, error: fixError } = await adminClient.rpc('fix_role_mismatch');

    if (fixError) {
      // Log the error
      await supabase.from('logs').insert({
        level: 'error',
        message: 'Error in fix-roles API endpoint',
        context: {
          error: fixError.message,
          userId: session.session.user.id,
          email: session.session.user.email,
          timestamp: new Date().toISOString(),
        },
      });
      
      return NextResponse.json(
        { error: 'Role fix error', details: fixError.message },
        { status: 500 }
      );
    }

    // Run SQL cleanup directly
    const { data: sqlResult, error: sqlError } = await adminClient.rpc('execute_sql', {
      sql_string: `
        -- Fix specific affiliates that have 'CLIENT' role
        UPDATE auth.users AS u
        SET 
          raw_app_meta_data = jsonb_set(
            COALESCE(u.raw_app_meta_data, '{}'::jsonb),
            '{role}',
            '"AFFILIATE"'
          )
        FROM public.profiles AS p
        WHERE u.id = p.id
          AND u.raw_app_meta_data->>'role' = 'CLIENT'
          AND (
            -- Check if AFFILIATE is in the roles array in user_metadata
            u.raw_user_meta_data->'roles' @> '"AFFILIATE"'::jsonb
            OR
            -- Check if AFFILIATE is in the profile roles array
            'AFFILIATE' = ANY(p.roles)
          )
        RETURNING u.id, u.email, u.raw_app_meta_data->>'role' AS new_role;
      `
    });

    // Log success
    await supabase.from('logs').insert({
      level: 'info',
      message: 'Successfully executed fix-roles API endpoint',
      context: {
        userId: session.session.user.id,
        email: session.session.user.email,
        result: result,
        sqlResult: sqlResult,
        timestamp: new Date().toISOString(),
      },
    });

    return NextResponse.json({ 
      success: true, 
      roleFixes: result,
      sqlFixes: sqlResult,
      message: 'Role synchronization completed' 
    });
    
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Unexpected error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
} 