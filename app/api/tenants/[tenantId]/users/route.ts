import { createAuthenticatedSupabaseClient, requireRole } from '@/lib/auth/server';
import { NextResponse } from 'next/server';

/**
 * GET /api/tenants/[tenantId]/users
 * Get users for a specific tenant with pagination and search
 */
export async function GET(
  request: Request,
  { params }: { params: { tenantId: string } }
) {
  try {
    const supabase = await createAuthenticatedSupabaseClient();
    
    try {
      await requireRole(['SUPER_ADMIN', 'ADMIN']);
    } catch (roleError: any) {
      return NextResponse.json({ error: roleError.message || 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const pageSize = parseInt(searchParams.get('pageSize') || '10', 10);
    const search = searchParams.get('search') || null;

    const { data, error } = await supabase
      .rpc('get_tenant_users_rpc', {
        p_tenant_id: params.tenantId,
        p_page: page,
        p_page_size: pageSize,
        p_search: search
      });

    if (error) {
      console.error('Error fetching tenant users:', error);
      return NextResponse.json(
        { error: 'Failed to fetch tenant users', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json(data);

  } catch (error: any) {
    console.error('Error in GET /api/tenants/[tenantId]/users:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/tenants/[tenantId]/users
 * Add or update a user in a tenant
 */
export async function POST(
  request: Request,
  { params }: { params: { tenantId: string } }
) {
  try {
    const supabase = await createAuthenticatedSupabaseClient();
    
    try {
      await requireRole(['SUPER_ADMIN', 'ADMIN']);
    } catch (roleError: any) {
      return NextResponse.json({ error: roleError.message || 'Forbidden' }, { status: 403 });
    }

    const { userId, role } = await request.json();

    if (!userId || !role) {
      return NextResponse.json(
        { error: 'User ID and role are required' },
        { status: 400 }
      );
    }

    const { data, error } = await supabase
      .rpc('upsert_tenant_user_rpc', {
        p_tenant_id: params.tenantId,
        p_user_id: userId,
        p_role: role.toUpperCase()
      });

    if (error) {
      console.error('Error adding/updating tenant user:', error);
      return NextResponse.json(
        { error: 'Failed to add/update tenant user', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json(data);

  } catch (error: any) {
    console.error('Error in POST /api/tenants/[tenantId]/users:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/tenants/[tenantId]/users/[userId]
 * Remove a user from a tenant
 */
export async function DELETE(
  request: Request,
  { params }: { params: { tenantId: string; userId: string } }
) {
  try {
    const supabase = await createAuthenticatedSupabaseClient();
    
    try {
      await requireRole(['SUPER_ADMIN', 'ADMIN']);
    } catch (roleError: any) {
      return NextResponse.json({ error: roleError.message || 'Forbidden' }, { status: 403 });
    }

    const { data, error } = await supabase
      .rpc('remove_tenant_user_rpc', {
        p_tenant_id: params.tenantId,
        p_user_id: params.userId
      });

    if (error) {
      console.error('Error removing tenant user:', error);
      return NextResponse.json(
        { error: 'Failed to remove user from tenant', details: error.message },
        { status: 500 }
      );
    }

    return NextResponse.json(data);

  } catch (error: any) {
    console.error('Error in DELETE /api/tenants/[tenantId]/users/[userId]:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}