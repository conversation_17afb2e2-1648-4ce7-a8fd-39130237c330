import { NextRequest, NextResponse } from "next/server";
import { getAffiliatesForQuote } from "@/lib/api/affiliates";
import { getSupabaseClient } from "@/lib/supabase";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const city = url.searchParams.get("city") || "Miami";

    console.log(
      `[DEBUG TEST] Testing getAffiliatesForQuote for city: "${city}"`
    );

    // Test the function directly
    const affiliates = await getAffiliatesForQuote(city);

    console.log(`[DEBUG TEST] Result: ${affiliates.length} affiliates found`);

    // Also test direct database query
    const supabase = getSupabaseClient();
    if (supabase) {
      const { data: directQuery, error } = await supabase
        .from("affiliate_companies")
        .select("id, name, city, status")
        .eq("status", "active")
        .or(`city.eq.${city},city.ilike.%${city}%`);

      console.log(`[DEBUG TEST] Direct query result:`, directQuery);
      console.log(`[DEBUG TEST] Direct query error:`, error);

      return NextResponse.json({
        success: true,
        city: city,
        functionResult: {
          count: affiliates.length,
          affiliates: affiliates.map((a) => ({
            id: a.id,
            name: a.name,
            city: a.city,
          })),
        },
        directQuery: {
          count: directQuery?.length || 0,
          affiliates: directQuery || [],
          error: error,
        },
      });
    }

    return NextResponse.json({
      success: true,
      city: city,
      functionResult: {
        count: affiliates.length,
        affiliates: affiliates.map((a) => ({
          id: a.id,
          name: a.name,
          city: a.city,
        })),
      },
      directQuery: {
        error: "Supabase client not available",
      },
    });
  } catch (error) {
    console.error("[DEBUG TEST] Error:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
