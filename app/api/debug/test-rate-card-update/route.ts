import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function POST(request: NextRequest) {
  try {
    console.log('[DEBUG API] Testing single rate card update')

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get one Austin rate card first
    const { data: rateCards, error: fetchError } = await supabase
      .from('rate_cards')
      .select('id, company_id, vehicle_type, is_active, p2p_point_to_point_rate')
      .eq('company_id', '11111111-1111-1111-1111-111111111111')
      .limit(1)

    if (fetchError) {
      console.error('[DEBUG API] Error fetching rate card:', fetchError)
      return NextResponse.json({ error: 'Failed to fetch rate card' }, { status: 500 })
    }

    if (!rateCards || rateCards.length === 0) {
      return NextResponse.json({ error: 'No rate cards found for Austin Elite Transport' }, { status: 404 })
    }

    const rateCard = rateCards[0]
    console.log('[DEBUG API] Found rate card:', rateCard)

    // Try to update this specific rate card
    const { data: updatedRateCard, error: updateError } = await supabase
      .from('rate_cards')
      .update({
        is_active: true,
        p2p_point_to_point_rate: 120,
        airport_transfer_flat_rate: 85,
        charter_hourly_rate: 75,
        pricing_model_type: 'P2P'
      })
      .eq('id', rateCard.id)
      .select()

    if (updateError) {
      console.error('[DEBUG API] Error updating rate card:', updateError)
      return NextResponse.json({ error: 'Failed to update rate card', details: updateError }, { status: 500 })
    }

    console.log('[DEBUG API] Updated rate card:', updatedRateCard)

    // Verify the update
    const { data: verifyRateCard, error: verifyError } = await supabase
      .from('rate_cards')
      .select('id, company_id, vehicle_type, is_active, p2p_point_to_point_rate')
      .eq('id', rateCard.id)
      .single()

    if (verifyError) {
      console.error('[DEBUG API] Error verifying rate card:', verifyError)
      return NextResponse.json({ error: 'Failed to verify rate card' }, { status: 500 })
    }

    console.log('[DEBUG API] Verified rate card:', verifyRateCard)

    return NextResponse.json({
      success: true,
      message: 'Rate card update test completed',
      original: rateCard,
      updated: updatedRateCard,
      verified: verifyRateCard
    })

  } catch (error) {
    console.error('[DEBUG API] Error in test rate card update:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
