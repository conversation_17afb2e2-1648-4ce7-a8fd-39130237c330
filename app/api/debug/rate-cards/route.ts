import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: NextRequest) {
  try {
    console.log('[DEBUG API] Starting debug rate cards request')

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    console.log('[DEBUG API] Fetching all rate cards')

    // Get all rate cards
    const { data: rateCards, error } = await supabase
      .from('rate_cards')
      .select(`
        id, company_id, vehicle_type, pricing_model_type,
        p2p_point_to_point_rate, dt_per_mile_rate, dt_per_hour_rate,
        airport_transfer_flat_rate, charter_hourly_rate, status, is_active
      `)
      .order('company_id')

    if (error) {
      console.error('[DEBUG API] Error fetching rate cards:', error)
      return NextResponse.json({ error: 'Failed to fetch rate cards' }, { status: 500 })
    }

    console.log(`[DEBUG API] Found ${rateCards?.length || 0} total rate cards`)

    // Log each rate card
    rateCards?.forEach((rateCard, index) => {
      console.log(`[DEBUG API] Rate Card ${index + 1}:`, {
        id: rateCard.id,
        company_id: rateCard.company_id,
        vehicle_type: rateCard.vehicle_type,
        pricing_model_type: rateCard.pricing_model_type,
        p2p_rate: rateCard.p2p_point_to_point_rate,
        airport_rate: rateCard.airport_transfer_flat_rate,
        charter_rate: rateCard.charter_hourly_rate,
        is_active: rateCard.is_active,
        status: rateCard.status
      })
    })

    // Count active rate cards
    const activeRateCards = rateCards?.filter(rc => rc.is_active === true) || []
    console.log(`[DEBUG API] Active rate cards: ${activeRateCards.length}`)

    // Group by company
    const byCompany = rateCards?.reduce((acc: any, rc) => {
      if (!acc[rc.company_id]) {
        acc[rc.company_id] = []
      }
      acc[rc.company_id].push(rc)
      return acc
    }, {}) || {}

    console.log(`[DEBUG API] Rate cards by company:`, Object.keys(byCompany).map(companyId => ({
      company_id: companyId,
      count: byCompany[companyId].length,
      active_count: byCompany[companyId].filter((rc: any) => rc.is_active).length
    })))

    return NextResponse.json({
      success: true,
      total_rate_cards: rateCards?.length || 0,
      active_rate_cards: activeRateCards.length,
      rate_cards: rateCards,
      by_company: byCompany
    })

  } catch (error) {
    console.error('[DEBUG API] Error in rate cards debug:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
