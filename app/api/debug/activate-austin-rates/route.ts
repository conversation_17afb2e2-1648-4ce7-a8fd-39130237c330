import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function POST(request: NextRequest) {
  try {
    console.log('[DEBUG API] Activating Austin affiliate rate cards')

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Get Austin affiliate IDs
    const austinAffiliateIds = [
      '11111111-1111-1111-1111-111111111111', // Austin Elite Transport
      '22222222-2222-2222-2222-222222222222', // Texas Premium Rides
      '33333333-3333-3333-3333-333333333333', // Capital City Cars
      '44444444-4444-4444-4444-444444444444', // Lone Star Luxury
      '55555555-5555-5555-5555-555555555555'  // Hill Country Transport
    ]

    // Update rate cards for Austin affiliates to be active and add pricing
    const { data: updatedRateCards, error: updateError } = await supabase
      .from('rate_cards')
      .update({
        is_active: true,
        p2p_point_to_point_rate: 120,
        airport_transfer_flat_rate: 85,
        charter_hourly_rate: 75,
        pricing_model_type: 'P2P'
      })
      .in('company_id', austinAffiliateIds)
      .select()

    if (updateError) {
      console.error('[DEBUG API] Error updating rate cards:', updateError)
      return NextResponse.json({ error: 'Failed to update rate cards' }, { status: 500 })
    }

    console.log(`[DEBUG API] Updated ${updatedRateCards?.length || 0} rate cards`)

    // Get updated rate cards to verify
    const { data: verifyRateCards, error: verifyError } = await supabase
      .from('rate_cards')
      .select('id, company_id, vehicle_type, is_active, p2p_point_to_point_rate')
      .in('company_id', austinAffiliateIds)
      .eq('is_active', true)

    if (verifyError) {
      console.error('[DEBUG API] Error verifying rate cards:', verifyError)
      return NextResponse.json({ error: 'Failed to verify rate cards' }, { status: 500 })
    }

    console.log(`[DEBUG API] Verified ${verifyRateCards?.length || 0} active rate cards`)

    return NextResponse.json({
      success: true,
      message: 'Austin affiliate rate cards activated',
      updated_count: updatedRateCards?.length || 0,
      active_rate_cards: verifyRateCards
    })

  } catch (error) {
    console.error('[DEBUG API] Error in activate Austin rates:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
