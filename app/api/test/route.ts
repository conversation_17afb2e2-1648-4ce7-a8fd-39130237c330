import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/test
 * Simple test endpoint to verify API and Supabase connection
 */
export async function GET(request: NextRequest) {
  try {
    console.log('Test API - Starting request');

    // Test basic response first
    const basicResponse = {
      message: 'API is working',
      timestamp: new Date().toISOString(),
      environment: {
        NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
        hasServiceRoleKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY
      }
    };

    console.log('Test API - Basic response ready');

    // Test Supabase connection
    try {
      const supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          }
        }
      );

      console.log('Test API - Supabase client created');

      // Simple query to test connection
      const { data, error } = await supabase
        .from('profiles')
        .select('id, email')
        .limit(1);

      console.log('Test API - Supabase query completed');

      if (error) {
        console.error('Test API - Supabase error:', error);
        return NextResponse.json({
          ...basicResponse,
          supabase: {
            connected: false,
            error: error.message
          }
        });
      }

      console.log('Test API - Supabase query successful');

      return NextResponse.json({
        ...basicResponse,
        supabase: {
          connected: true,
          sampleData: data
        }
      });

    } catch (supabaseError) {
      console.error('Test API - Supabase connection error:', supabaseError);
      return NextResponse.json({
        ...basicResponse,
        supabase: {
          connected: false,
          error: supabaseError instanceof Error ? supabaseError.message : 'Unknown Supabase error'
        }
      });
    }

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('Test API - General error:', error);
    return NextResponse.json(
      { error: `Test API failed: ${errorMessage}` },
      { status: 500 }
    );
  }
}
