import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    // Create Supabase client with proper auth
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Verify user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Fetch passengers
    // First, try to create the passengers table if it doesn't exist
    try {
      await supabase.rpc('execute_sql', {
        sql: `
          CREATE TABLE IF NOT EXISTS public.passengers (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              first_name TEXT NOT NULL,
              last_name TEXT NOT NULL,
              email TEXT NOT NULL,
              phone_number TEXT,
              passenger_type TEXT DEFAULT 'guest',
              company TEXT,
              dietary_restrictions TEXT,
              special_requirements TEXT,
              created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
              tenant_id UUID,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
          ALTER TABLE public.passengers ENABLE ROW LEVEL SECURITY;
        `
      })
    } catch (tableError) {
      console.log('Table creation attempt (may already exist):', tableError)
    }

    const { data: passengers, error: fetchError } = await supabase
      .from('passengers')
      .select('*')
      .order('created_at', { ascending: false })

    if (fetchError) {
      console.error('Error fetching passengers:', fetchError)
      return NextResponse.json({ error: 'Failed to fetch passengers' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      passengers: passengers || []
    })

  } catch (error) {
    console.error('Error in GET /api/event-manager/passengers:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { first_name, last_name, email, phone_number, passenger_type, company, dietary_restrictions, special_requirements } = body

    // Create Supabase client with proper auth
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Verify user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Validate required fields
    if (!first_name || !last_name || !email) {
      return NextResponse.json({ error: 'First name, last name, and email are required' }, { status: 400 })
    }

    // Get the user's tenant_id (for now, use a default tenant)
    // TODO: Implement proper tenant context
    const defaultTenantId = '00000000-0000-0000-0000-000000000001' // Default tenant ID

    // Create the passenger
    const { data: passenger, error: createError } = await supabase
      .from('passengers')
      .insert({
        first_name,
        last_name,
        email,
        phone_number: phone_number || '',
        passenger_type: passenger_type || 'guest',
        company: company || '',
        dietary_restrictions: dietary_restrictions || '',
        special_requirements: special_requirements || '',
        tenant_id: defaultTenantId,
        created_by: user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (createError) {
      console.error('Error creating passenger:', createError)
      return NextResponse.json({ error: 'Failed to create passenger' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      passenger: passenger
    })

  } catch (error) {
    console.error('Error in POST /api/event-manager/passengers:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
