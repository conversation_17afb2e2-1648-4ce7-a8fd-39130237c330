import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  console.log('=== MANUAL QUOTE SUBMIT API CALLED ===')
  try {
    const body = await request.json()
    const { quoteRequest, reason } = body
    console.log('Manual quote request received:', { quoteRequest, reason })

    if (!quoteRequest) {
      return NextResponse.json({ error: 'Quote request is required' }, { status: 400 })
    }

    // Create Supabase client with proper auth for user verification
    const cookieStore = cookies()
    const authSupabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Get current user
    const { data: { user }, error: userError } = await authSupabase.auth.getUser()
    if (userError || !user) {
      console.error('Authentication error:', userError)
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Create service role client for database operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Generate quote reference
    const timestamp = Date.now().toString().slice(-6)
    const randomSuffix = Math.random().toString(36).substring(2, 5).toUpperCase()
    const quoteReference = `QT-${timestamp}-${randomSuffix}`

    // Create quote with manual processing flag
    const quoteData = {
      reference: quoteReference,
      user_id: user.id,
      status: 'manual_processing', // Special status for manual quotes
      service_type: quoteRequest.service_type || 'point_to_point',
      vehicle_type: quoteRequest.vehicle_type,
      passenger_count: parseInt(quoteRequest.passenger_count) || 1,
      pickup_address: quoteRequest.pickup_address,
      pickup_city: quoteRequest.city,
      pickup_state: quoteRequest.state,
      pickup_zip: quoteRequest.zip,
      pickup_country: quoteRequest.country || 'USA',
      dropoff_address: quoteRequest.dropoff_address,
      dropoff_city: quoteRequest.dropoff_city,
      dropoff_state: quoteRequest.dropoff_state,
      dropoff_zip: quoteRequest.dropoff_zip,
      dropoff_country: quoteRequest.dropoff_country || 'USA',
      date: quoteRequest.date,
      time: quoteRequest.time,
      special_requirements: quoteRequest.special_requirements,
      contact_name: quoteRequest.contact_name,
      contact_email: quoteRequest.contact_email,
      contact_phone: quoteRequest.contact_phone,
      manual_processing_reason: reason || 'no_affiliates_found',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    console.log('Creating manual quote with data:', quoteData)

    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .insert(quoteData)
      .select()
      .single()

    if (quoteError) {
      console.error('Error creating manual quote:', quoteError)
      return NextResponse.json({
        error: 'Failed to create quote',
        details: quoteError,
        errorCode: quoteError?.code,
        errorMessage: quoteError?.message,
        hint: quoteError?.hint
      }, { status: 500 })
    }

    console.log('Manual quote created:', quote)

    // Create a notification for super admin about manual quote
    try {
      await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/notifications/manual-quote-request`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          quoteId: quote.id,
          quoteReference: quoteReference,
          reason: reason,
          quoteDetails: quoteRequest,
          userId: user.id
        })
      })
    } catch (notificationError) {
      console.error('Error sending manual quote notification:', notificationError)
      // Don't fail the operation for notification errors
    }

    console.log(`Manual Quote Submit API - Quote ${quoteReference} submitted for manual processing`)

    return NextResponse.json({
      success: true,
      quoteId: quote.id,
      quoteReference: quoteReference,
      status: 'manual_processing',
      message: `Quote request ${quoteReference} submitted for manual processing. Our team will contact you shortly.`
    })

  } catch (error) {
    console.error('Error in manual quote submit API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
