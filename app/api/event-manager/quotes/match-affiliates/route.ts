import { NextRequest, NextResponse } from "next/server";
import { createServerClient } from "@supabase/ssr";
import { createClient } from "@supabase/supabase-js";
import { cookies } from "next/headers";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { city, vehicleType, serviceType, date, time, passengers } = body;

    if (!city) {
      return NextResponse.json(
        { error: "City is required for affiliate matching" },
        { status: 400 }
      );
    }

    // Create Supabase client with proper auth for user verification
    const cookieStore = cookies();
    const supabaseAuth = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Verify user is authenticated
    const {
      data: { user },
      error: authError,
    } = await supabaseAuth.auth.getUser();
    console.log("Affiliate Matching API - Auth check:", {
      hasUser: !!user,
      userId: user?.id,
      userEmail: user?.email,
      userRoles: user?.user_metadata?.roles,
      authError: authError?.message,
    });

    if (authError || !user) {
      console.log(
        "Affiliate Matching API - Authentication failed:",
        authError?.message || "No user"
      );
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Create service role client for affiliate queries (bypasses RLS)
    // This is needed because CLIENT users need to see active/approved affiliates for quote matching
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    console.log("Affiliate Matching API - Starting request");
    console.log("Search criteria:", {
      city,
      vehicleType,
      serviceType,
      date,
      time,
      passengers,
    });

    // Map service types to match database expectations
    let mappedServiceType = serviceType;
    if (serviceType === "point" || serviceType === "point-to-point") {
      mappedServiceType = "point_to_point";
    }
    console.log("Mapped service type:", serviceType, "->", mappedServiceType);

    // Clean city name to remove state suffix
    const cleanCity = city.split(",")[0].trim();
    console.log("Cleaned city:", city, "->", cleanCity);

    // Query to find matching affiliates - we'll join vehicles and rate cards separately
    // First, let's see all affiliates in the city regardless of status
    const { data: allAffiliates, error: allAffiliatesError } = await supabase
      .from("affiliate_companies")
      .select("id, name, city, status, application_status")
      .ilike("city", `%${cleanCity}%`);

    console.log(
      `All affiliates in ${cleanCity}:`,
      allAffiliates?.map((a) => ({
        id: a.id,
        name: a.name,
        city: a.city,
        status: a.status,
        application_status: a.application_status,
      }))
    );

    // Now filter for active ones
    const { data: affiliates, error: affiliatesError } = await supabase
      .from("affiliate_companies")
      .select("id, name, city, status, application_status")
      .eq("status", "active")
      .ilike("city", `%${cleanCity}%`);

    if (affiliatesError) {
      console.error("Error fetching affiliates:", affiliatesError);
      return NextResponse.json(
        { error: "Failed to fetch affiliates" },
        { status: 500 }
      );
    }

    console.log(
      `Found ${affiliates?.length || 0} active affiliates out of ${allAffiliates?.length || 0} total`
    );

    if (!affiliates || affiliates.length === 0) {
      return NextResponse.json({
        success: true,
        affiliates: [],
        allowManualSubmission: true, // Allow client to submit quote even without matches
        message:
          "No affiliates found in this area. You can still submit your quote and we will manually find suitable partners.",
        searchCriteria: {
          city: cleanCity,
          vehicleType,
          serviceType: mappedServiceType,
          originalCity: city,
          originalServiceType: serviceType,
          date,
          time,
          passengers,
        },
      });
    }

    // Now fetch vehicles and rate cards for these affiliates
    const affiliateIds = affiliates.map((a) => a.id);

    // Get vehicles for these affiliates
    let vehicleQuery = supabase
      .from("vehicles")
      .select("id, company_id, type, make, model, capacity, status")
      .in("company_id", affiliateIds)
      .eq("status", "active");

    // Note: We'll do vehicle type filtering after fetching to handle case-insensitive matching
    if (passengers) {
      vehicleQuery = vehicleQuery.gte("capacity", passengers);
    }

    const { data: vehicles, error: vehiclesError } = await vehicleQuery;

    if (vehiclesError) {
      console.error("Error fetching vehicles:", vehiclesError);
      return NextResponse.json(
        { error: "Failed to fetch vehicles" },
        { status: 500 }
      );
    }

    // Get rate cards for these affiliates - use safe columns and extract from special_rates
    let rateCardQuery = supabase
      .from("rate_cards")
      .select(
        `
        id, company_id, vehicle_type, status,
        base_rate, per_mile_rate, per_hour_rate, minimum_hours,
        special_rates
      `
      )
      .in("company_id", affiliateIds)
      .eq("status", "approved");

    // Note: We'll do vehicle type filtering after fetching to handle case-insensitive matching

    console.log(
      "Rate card filtering for service type:",
      mappedServiceType,
      "allows pricing models:",
      ["P2P", "AIRPORT_TRANSFER", "D+T"]
    );

    const { data: rawRateCards, error: rateCardsError } = await rateCardQuery;

    if (rateCardsError) {
      console.error("Error fetching rate cards:", rateCardsError);
      return NextResponse.json(
        { error: "Failed to fetch rate cards" },
        { status: 500 }
      );
    }

    // Process rate cards to extract data from special_rates JSONB
    const rateCards = (rawRateCards || [])
      .map((card) => {
        const specialRates = card.special_rates || {};
        return {
          ...card,
          // Extract fields from special_rates
          pricing_model_type: specialRates.pricing_model_type || "P2P",
          p2p_point_to_point_rate:
            specialRates.p2p_point_to_point_rate || card.base_rate,
          p2p_extra_hour_rate: specialRates.p2p_extra_hour_rate,
          dt_base_fee: specialRates.dt_base_fee,
          dt_per_mile_rate: specialRates.dt_per_mile_rate || card.per_mile_rate,
          dt_per_hour_rate: specialRates.dt_per_hour_rate || card.per_hour_rate,
          dt_min_miles: specialRates.dt_min_miles,
          dt_min_hours: specialRates.dt_min_hours,
          airport_transfer_flat_rate: specialRates.airport_transfer_flat_rate,
          charter_hourly_rate:
            specialRates.charter_hourly_rate || card.per_hour_rate,
          charter_min_hours:
            specialRates.charter_min_hours || card.minimum_hours,
        };
      })
      .filter((card) => {
        // Filter by service type compatibility
        const pricingModel = card.pricing_model_type;
        if (mappedServiceType === "point_to_point") {
          return (
            ["P2P", "AIRPORT_TRANSFER", "D+T"].includes(pricingModel) &&
            card.p2p_point_to_point_rate
          );
        } else if (mappedServiceType === "airport") {
          return (
            ["AIRPORT_TRANSFER", "P2P"].includes(pricingModel) &&
            (card.airport_transfer_flat_rate || card.p2p_point_to_point_rate)
          );
        } else if (mappedServiceType === "hourly") {
          return pricingModel === "CHARTER" && card.charter_hourly_rate;
        }
        return card.p2p_point_to_point_rate; // Default fallback
      });

    console.log(
      `Found ${vehicles?.length || 0} vehicles and ${rateCards?.length || 0} rate cards`
    );

    // Helper function to normalize vehicle types for matching
    const normalizeVehicleType = (type: string): string => {
      return type.toLowerCase().replace(/[^a-z]/g, "");
    };

    // Filter affiliates that have both vehicles and rate cards with proper vehicle type matching
    const validAffiliates = affiliates.filter((affiliate) => {
      const affiliateVehicles =
        vehicles?.filter((v) => v.company_id === affiliate.id) || [];
      const affiliateRateCards =
        rateCards?.filter((rc) => rc.company_id === affiliate.id) || [];

      // If no vehicle type specified, just check for any vehicles and rate cards
      if (!vehicleType) {
        return affiliateVehicles.length > 0 && affiliateRateCards.length > 0;
      }

      // Check for matching vehicle types (case-insensitive)
      const normalizedRequestedType = normalizeVehicleType(vehicleType);

      const matchingVehicles = affiliateVehicles.filter(
        (v) => normalizeVehicleType(v.type) === normalizedRequestedType
      );

      const matchingRateCards = affiliateRateCards.filter(
        (rc) =>
          normalizeVehicleType(rc.vehicle_type) === normalizedRequestedType
      );

      console.log(`Affiliate ${affiliate.name} vehicle type matching:`, {
        requested: vehicleType,
        normalizedRequested: normalizedRequestedType,
        vehicles: affiliateVehicles.map((v) => ({
          type: v.type,
          normalized: normalizeVehicleType(v.type),
        })),
        rateCards: affiliateRateCards.map((rc) => ({
          type: rc.vehicle_type,
          normalized: normalizeVehicleType(rc.vehicle_type),
        })),
        matchingVehicles: matchingVehicles.length,
        matchingRateCards: matchingRateCards.length,
      });

      // Must have at least one matching vehicle and one matching rate card
      return matchingVehicles.length > 0 && matchingRateCards.length > 0;
    });

    console.log(
      `${validAffiliates.length} affiliates have both vehicles and rate cards`
    );

    // If no valid affiliates found, return empty with manual submission option
    if (validAffiliates.length === 0) {
      return NextResponse.json({
        success: true,
        affiliates: [],
        allowManualSubmission: true,
        message:
          "No affiliates with available vehicles and rates found in this area. You can still submit your quote and we will manually find suitable partners.",
        searchCriteria: {
          city: cleanCity,
          vehicleType,
          serviceType: mappedServiceType,
          originalCity: city,
          originalServiceType: serviceType,
          date,
          time,
          passengers,
        },
      });
    }

    // Transform and enhance affiliate data
    const enhancedAffiliates = validAffiliates.map((affiliate: any) => {
      const affiliateVehicles =
        vehicles?.filter((v) => v.company_id === affiliate.id) || [];
      const affiliateRateCards =
        rateCards?.filter((rc) => rc.company_id === affiliate.id) || [];

      // Filter for matching vehicle types if specified
      let matchingVehicles = affiliateVehicles;
      let matchingRateCards = affiliateRateCards;

      if (vehicleType) {
        const normalizedRequestedType = normalizeVehicleType(vehicleType);
        matchingVehicles = affiliateVehicles.filter(
          (v) => normalizeVehicleType(v.type) === normalizedRequestedType
        );
        matchingRateCards = affiliateRateCards.filter(
          (rc) =>
            normalizeVehicleType(rc.vehicle_type) === normalizedRequestedType
        );
      }

      const vehicle = matchingVehicles[0]; // Take first matching vehicle
      const rateCard = matchingRateCards[0]; // Take first matching rate card

      // Calculate estimated pricing based on rate card pricing model and service type
      let estimatedPrice = 100; // Default fallback

      if (mappedServiceType === "airport") {
        // For airport service, prefer airport rates, then P2P rates
        estimatedPrice =
          rateCard.airport_transfer_flat_rate ||
          rateCard.p2p_point_to_point_rate ||
          100;
      } else if (mappedServiceType === "hourly") {
        // For hourly service, use charter rates
        estimatedPrice =
          rateCard.charter_hourly_rate || rateCard.dt_per_hour_rate || 80;
      } else if (mappedServiceType === "point_to_point") {
        // For point-to-point, use the best available rate based on pricing model
        if (rateCard.pricing_model_type === "P2P") {
          estimatedPrice = rateCard.p2p_point_to_point_rate || 100;
        } else if (rateCard.pricing_model_type === "AIRPORT_TRANSFER") {
          // Airport transfer rates can be used for point-to-point
          estimatedPrice =
            rateCard.airport_transfer_flat_rate ||
            rateCard.p2p_point_to_point_rate ||
            100;
        } else if (rateCard.pricing_model_type === "D+T") {
          // Distance + Time pricing (estimate 10 miles, 1 hour)
          estimatedPrice =
            (rateCard.dt_per_mile_rate || 2) * 10 +
            (rateCard.dt_per_hour_rate || 50);
        } else {
          // Fallback to any available rate
          estimatedPrice =
            rateCard.p2p_point_to_point_rate ||
            rateCard.airport_transfer_flat_rate ||
            100;
        }
      }

      console.log("Pricing calculation:", {
        serviceType: mappedServiceType,
        pricingModel: rateCard.pricing_model_type,
        estimatedPrice,
        availableRates: {
          p2p: rateCard.p2p_point_to_point_rate,
          airport: rateCard.airport_transfer_flat_rate,
          charter: rateCard.charter_hourly_rate,
        },
      });

      // No minimum fare check needed for new structure

      // Determine tier based on default logic (columns don't exist in DB)
      let tier = "Standard";
      const rating = 4.0; // Default rating since column doesn't exist
      if (rating >= 4.8) {
        tier = "Elite";
      } else if (rating >= 4.5) {
        tier = "Premium";
      }

      // Calculate response time priority based on tier
      let avgResponseTime = "< 15 min";
      if (tier === "Elite") {
        avgResponseTime = "< 5 min";
      } else if (tier === "Premium") {
        avgResponseTime = "< 10 min";
      }

      return {
        id: affiliate.id,
        company_name: affiliate.name,
        city: affiliate.city,
        tier: tier,
        rating: rating,
        avg_response_time: avgResponseTime,
        vehicle_type: vehicle.type,
        vehicle_model: `${vehicle.make} ${vehicle.model}`,
        capacity: vehicle.capacity,
        base_rate:
          rateCard.p2p_point_to_point_rate ||
          rateCard.airport_transfer_flat_rate ||
          rateCard.charter_hourly_rate,
        total_price: Math.round(estimatedPrice),
        features: getVehicleFeatures(vehicle.type, tier),
        availability: "confirmed",
        estimated_arrival: getEstimatedArrival(tier),
        notes:
          tier === "Elite"
            ? "Premium service with professional chauffeur"
            : null,
      };
    });

    // Sort by tier priority (Elite first, then Premium, then Standard)
    const sortedAffiliates = enhancedAffiliates.sort((a, b) => {
      const tierOrder = { Elite: 0, Premium: 1, Standard: 2 };
      const aTierOrder = tierOrder[a.tier as keyof typeof tierOrder] || 2;
      const bTierOrder = tierOrder[b.tier as keyof typeof tierOrder] || 2;

      if (aTierOrder !== bTierOrder) {
        return aTierOrder - bTierOrder;
      }

      // If same tier, sort by rating
      return (b.rating || 0) - (a.rating || 0);
    });

    console.log(
      `Affiliate Matching API - Returning ${sortedAffiliates.length} sorted affiliates`
    );

    return NextResponse.json({
      success: true,
      affiliates: sortedAffiliates,
      searchCriteria: {
        city: cleanCity,
        vehicleType,
        serviceType: mappedServiceType,
        originalCity: city,
        originalServiceType: serviceType,
        date,
        time,
        passengers,
      },
    });
  } catch (error) {
    console.error("Error in affiliate matching API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Helper function to get vehicle features based on type and tier
function getVehicleFeatures(vehicleType: string, tier: string): string[] {
  const baseFeatures = ["Professional Driver", "Clean Vehicle"];

  if (tier === "Elite") {
    return [
      ...baseFeatures,
      "WiFi",
      "Refreshments",
      "Premium Sound System",
      "Climate Control",
    ];
  } else if (tier === "Premium") {
    return [...baseFeatures, "WiFi", "Climate Control", "Phone Charging"];
  } else {
    return [...baseFeatures, "Phone Charging"];
  }
}

// Helper function to get estimated arrival based on tier
function getEstimatedArrival(tier: string): string {
  if (tier === "Elite") {
    return "10-15 minutes";
  } else if (tier === "Premium") {
    return "15-20 minutes";
  } else {
    return "20-25 minutes";
  }
}
