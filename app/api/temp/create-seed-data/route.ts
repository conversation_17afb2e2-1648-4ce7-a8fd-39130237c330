import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient()
    
    console.log('Creating comprehensive seed data...')
    
    // Step 1: Create Austin affiliate companies
    console.log('Creating affiliate companies for Austin...')
    const affiliateCompanies = [
      {
        id: '11111111-1111-1111-1111-111111111111',
        name: 'Austin Elite Transport',
        city: 'Austin',
        state: 'TX',
        status: 'active',
        email: '<EMAIL>',
        phone: '************',
        address: '123 Main St, Austin, TX 78701',
        website: 'https://austinelite.com',
        created_at: new Date().toISOString()
      },
      {
        id: '22222222-2222-2222-2222-222222222222',
        name: 'Texas Premium Rides',
        city: 'Austin',
        state: 'TX',
        status: 'active',
        email: '<EMAIL>',
        phone: '************',
        address: '456 Oak Ave, Austin, TX 78702',
        website: 'https://txpremium.com',
        created_at: new Date().toISOString()
      },
      {
        id: '33333333-3333-3333-3333-333333333333',
        name: 'Capital City Cars',
        city: 'Austin',
        state: 'TX',
        status: 'active',
        email: '<EMAIL>',
        phone: '************',
        address: '789 Cedar Ln, Austin, TX 78703',
        website: 'https://capitalcars.com',
        created_at: new Date().toISOString()
      },
      {
        id: '44444444-4444-4444-4444-444444444444',
        name: 'Lone Star Luxury',
        city: 'Austin',
        state: 'TX',
        status: 'active',
        email: '<EMAIL>',
        phone: '************',
        address: '321 Pine St, Austin, TX 78704',
        website: 'https://lonestarlux.com',
        created_at: new Date().toISOString()
      },
      {
        id: '55555555-5555-5555-5555-555555555555',
        name: 'Hill Country Transport',
        city: 'Austin',
        state: 'TX',
        status: 'active',
        email: '<EMAIL>',
        phone: '************',
        address: '654 Elm Dr, Austin, TX 78705',
        website: 'https://hillcountrytrans.com',
        created_at: new Date().toISOString()
      }
    ]

    const { data: createdAffiliates, error: affiliateError } = await supabase
      .from('affiliate_companies')
      .upsert(affiliateCompanies, { 
        onConflict: 'id',
        ignoreDuplicates: false 
      })
      .select()

    if (affiliateError) {
      console.error('Error creating affiliate companies:', affiliateError)
      return NextResponse.json({ error: 'Failed to create affiliate companies', details: affiliateError }, { status: 500 })
    }

    console.log(`Created ${createdAffiliates?.length || 0} affiliate companies`)

    // Step 2: Create vehicles for these affiliates
    console.log('Creating vehicles...')
    const vehicles = [
      // Austin Elite Transport vehicles
      {
        id: '66666666-6666-6666-6666-666666666666',
        affiliate_company_id: '11111111-1111-1111-1111-111111111111',
        vehicle_type: 'sedan',
        make: 'Mercedes',
        model: 'S-Class',
        year: 2023,
        capacity: 4,
        status: 'active',
        license_plate: 'AET001',
        color: 'Black',
        created_at: new Date().toISOString()
      },
      {
        id: '77777777-7777-7777-7777-777777777777',
        affiliate_company_id: '11111111-1111-1111-1111-111111111111',
        vehicle_type: 'suv',
        make: 'BMW',
        model: 'X7',
        year: 2023,
        capacity: 7,
        status: 'active',
        license_plate: 'AET002',
        color: 'White',
        created_at: new Date().toISOString()
      },
      // Texas Premium Rides vehicles
      {
        id: '88888888-8888-8888-8888-888888888888',
        affiliate_company_id: '22222222-2222-2222-2222-222222222222',
        vehicle_type: 'sedan',
        make: 'Audi',
        model: 'A8',
        year: 2023,
        capacity: 4,
        status: 'active',
        license_plate: 'TPR001',
        color: 'Silver',
        created_at: new Date().toISOString()
      },
      {
        id: '99999999-9999-9999-9999-999999999999',
        affiliate_company_id: '22222222-2222-2222-2222-222222222222',
        vehicle_type: 'suv',
        make: 'Cadillac',
        model: 'Escalade',
        year: 2023,
        capacity: 7,
        status: 'active',
        license_plate: 'TPR002',
        color: 'Black',
        created_at: new Date().toISOString()
      },
      // Capital City Cars vehicles
      {
        id: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
        affiliate_company_id: '33333333-3333-3333-3333-333333333333',
        vehicle_type: 'van',
        make: 'Mercedes',
        model: 'Sprinter',
        year: 2022,
        capacity: 12,
        status: 'active',
        license_plate: 'CCC001',
        color: 'White',
        created_at: new Date().toISOString()
      },
      {
        id: 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
        affiliate_company_id: '33333333-3333-3333-3333-333333333333',
        vehicle_type: 'sedan',
        make: 'Tesla',
        model: 'Model S',
        year: 2023,
        capacity: 5,
        status: 'active',
        license_plate: 'CCC002',
        color: 'Blue',
        created_at: new Date().toISOString()
      },
      // Lone Star Luxury vehicles
      {
        id: 'cccccccc-cccc-cccc-cccc-cccccccccccc',
        affiliate_company_id: '44444444-4444-4444-4444-444444444444',
        vehicle_type: 'luxury',
        make: 'Rolls-Royce',
        model: 'Ghost',
        year: 2023,
        capacity: 4,
        status: 'active',
        license_plate: 'LSL001',
        color: 'Black',
        created_at: new Date().toISOString()
      },
      {
        id: 'dddddddd-dddd-dddd-dddd-dddddddddddd',
        affiliate_company_id: '44444444-4444-4444-4444-444444444444',
        vehicle_type: 'suv',
        make: 'Range Rover',
        model: 'Autobiography',
        year: 2023,
        capacity: 7,
        status: 'active',
        license_plate: 'LSL002',
        color: 'White',
        created_at: new Date().toISOString()
      },
      // Hill Country Transport vehicles
      {
        id: 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee',
        affiliate_company_id: '55555555-5555-5555-5555-555555555555',
        vehicle_type: 'van',
        make: 'Ford',
        model: 'Transit',
        year: 2022,
        capacity: 15,
        status: 'active',
        license_plate: 'HCT001',
        color: 'White',
        created_at: new Date().toISOString()
      },
      {
        id: 'ffffffff-ffff-ffff-ffff-ffffffffffff',
        affiliate_company_id: '55555555-5555-5555-5555-555555555555',
        vehicle_type: 'sedan',
        make: 'Lexus',
        model: 'LS',
        year: 2023,
        capacity: 4,
        status: 'active',
        license_plate: 'HCT002',
        color: 'Gray',
        created_at: new Date().toISOString()
      }
    ]

    const { data: createdVehicles, error: vehicleError } = await supabase
      .from('vehicles')
      .upsert(vehicles, { 
        onConflict: 'id',
        ignoreDuplicates: false 
      })
      .select()

    if (vehicleError) {
      console.error('Error creating vehicles:', vehicleError)
      return NextResponse.json({ error: 'Failed to create vehicles', details: vehicleError }, { status: 500 })
    }

    console.log(`Created ${createdVehicles?.length || 0} vehicles`)

    // Step 3: Create rate cards for different service types and vehicle types
    console.log('Creating rate cards...')
    const rateCards = [
      // Austin Elite Transport rates
      {
        id: '10101010-1010-1010-1010-101010101010',
        affiliate_company_id: '11111111-1111-1111-1111-111111111111',
        vehicle_type: 'sedan',
        service_type: 'point_to_point',
        base_rate: 150.00,
        per_mile_rate: 2.50,
        per_hour_rate: 75.00,
        minimum_hours: 2,
        status: 'active',
        created_at: new Date().toISOString()
      },
      {
        id: '20202020-2020-2020-2020-202020202020',
        affiliate_company_id: '11111111-1111-1111-1111-111111111111',
        vehicle_type: 'suv',
        service_type: 'point_to_point',
        base_rate: 200.00,
        per_mile_rate: 3.00,
        per_hour_rate: 100.00,
        minimum_hours: 2,
        status: 'active',
        created_at: new Date().toISOString()
      },
      // Texas Premium Rides rates
      {
        id: '30303030-3030-3030-3030-303030303030',
        affiliate_company_id: '22222222-2222-2222-2222-222222222222',
        vehicle_type: 'sedan',
        service_type: 'point_to_point',
        base_rate: 140.00,
        per_mile_rate: 2.25,
        per_hour_rate: 70.00,
        minimum_hours: 2,
        status: 'active',
        created_at: new Date().toISOString()
      },
      {
        id: '40404040-4040-4040-4040-404040404040',
        affiliate_company_id: '22222222-2222-2222-2222-222222222222',
        vehicle_type: 'suv',
        service_type: 'point_to_point',
        base_rate: 190.00,
        per_mile_rate: 2.75,
        per_hour_rate: 95.00,
        minimum_hours: 2,
        status: 'active',
        created_at: new Date().toISOString()
      },
      // Capital City Cars rates
      {
        id: '50505050-5050-5050-5050-505050505050',
        affiliate_company_id: '33333333-3333-3333-3333-333333333333',
        vehicle_type: 'van',
        service_type: 'point_to_point',
        base_rate: 300.00,
        per_mile_rate: 4.00,
        per_hour_rate: 150.00,
        minimum_hours: 3,
        status: 'active',
        created_at: new Date().toISOString()
      },
      {
        id: '60606060-6060-6060-6060-606060606060',
        affiliate_company_id: '33333333-3333-3333-3333-333333333333',
        vehicle_type: 'sedan',
        service_type: 'point_to_point',
        base_rate: 130.00,
        per_mile_rate: 2.00,
        per_hour_rate: 65.00,
        minimum_hours: 2,
        status: 'active',
        created_at: new Date().toISOString()
      },
      // Lone Star Luxury rates
      {
        id: '70707070-7070-7070-7070-707070707070',
        affiliate_company_id: '44444444-4444-4444-4444-444444444444',
        vehicle_type: 'luxury',
        service_type: 'point_to_point',
        base_rate: 400.00,
        per_mile_rate: 5.00,
        per_hour_rate: 200.00,
        minimum_hours: 3,
        status: 'active',
        created_at: new Date().toISOString()
      },
      {
        id: '80808080-8080-8080-8080-808080808080',
        affiliate_company_id: '44444444-4444-4444-4444-444444444444',
        vehicle_type: 'suv',
        service_type: 'point_to_point',
        base_rate: 350.00,
        per_mile_rate: 4.50,
        per_hour_rate: 175.00,
        minimum_hours: 3,
        status: 'active',
        created_at: new Date().toISOString()
      },
      // Hill Country Transport rates
      {
        id: '90909090-9090-9090-9090-909090909090',
        affiliate_company_id: '55555555-5555-5555-5555-555555555555',
        vehicle_type: 'van',
        service_type: 'point_to_point',
        base_rate: 280.00,
        per_mile_rate: 3.75,
        per_hour_rate: 140.00,
        minimum_hours: 3,
        status: 'active',
        created_at: new Date().toISOString()
      },
      {
        id: 'a1a1a1a1-a1a1-a1a1-a1a1-a1a1a1a1a1a1',
        affiliate_company_id: '55555555-5555-5555-5555-555555555555',
        vehicle_type: 'sedan',
        service_type: 'point_to_point',
        base_rate: 135.00,
        per_mile_rate: 2.10,
        per_hour_rate: 68.00,
        minimum_hours: 2,
        status: 'active',
        created_at: new Date().toISOString()
      }
    ]

    const { data: createdRateCards, error: rateError } = await supabase
      .from('rate_cards')
      .upsert(rateCards, { 
        onConflict: 'id',
        ignoreDuplicates: false 
      })
      .select()

    if (rateError) {
      console.error('Error creating rate cards:', rateError)
      return NextResponse.json({ error: 'Failed to create rate cards', details: rateError }, { status: 500 })
    }

    console.log(`Created ${createdRateCards?.length || 0} rate cards`)

    return NextResponse.json({ 
      success: true, 
      message: 'Comprehensive seed data created successfully',
      data: {
        affiliates: createdAffiliates?.length || 0,
        vehicles: createdVehicles?.length || 0,
        rateCards: createdRateCards?.length || 0
      }
    })

  } catch (error) {
    console.error('Error in seed data creation:', error)
    return NextResponse.json({ error: 'Seed data creation failed', details: error }, { status: 500 })
  }
}
