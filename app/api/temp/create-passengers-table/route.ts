import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Create passengers table
    const { error } = await supabase.rpc('execute_sql', {
      sql: `
        -- Create passengers table
        CREATE TABLE IF NOT EXISTS public.passengers (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            first_name TEXT NOT NULL,
            last_name TEXT NOT NULL,
            email TEXT NOT NULL,
            phone_number TEXT,
            passenger_type TEXT DEFAULT 'guest' CHECK (passenger_type IN ('guest', 'vip', 'staff', 'adult', 'child')),
            company TEXT,
            dietary_restrictions TEXT,
            special_requirements TEXT,
            created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
            tenant_id UUID,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Add indexes for passengers
        CREATE INDEX IF NOT EXISTS idx_passengers_created_by ON public.passengers(created_by);
        CREATE INDEX IF NOT EXISTS idx_passengers_tenant_id ON public.passengers(tenant_id);
        CREATE INDEX IF NOT EXISTS idx_passengers_email ON public.passengers(email);
        CREATE INDEX IF NOT EXISTS idx_passengers_type ON public.passengers(passenger_type);

        -- Enable RLS on passengers
        ALTER TABLE public.passengers ENABLE ROW LEVEL SECURITY;

        -- Create RLS policies for passengers
        DROP POLICY IF EXISTS "Users can view passengers in their tenant" ON public.passengers;
        CREATE POLICY "Users can view passengers in their tenant"
            ON public.passengers
            FOR SELECT
            USING (
                created_by = auth.uid()
                OR EXISTS (
                    SELECT 1 FROM public.profiles 
                    WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
                )
            );

        DROP POLICY IF EXISTS "Users can create passengers in their tenant" ON public.passengers;
        CREATE POLICY "Users can create passengers in their tenant"
            ON public.passengers
            FOR INSERT
            WITH CHECK (
                created_by = auth.uid()
            );

        DROP POLICY IF EXISTS "Users can update passengers they created" ON public.passengers;
        CREATE POLICY "Users can update passengers they created"
            ON public.passengers
            FOR UPDATE
            USING (
                created_by = auth.uid()
                OR EXISTS (
                    SELECT 1 FROM public.profiles 
                    WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
                )
            );

        DROP POLICY IF EXISTS "Users can delete passengers they created" ON public.passengers;
        CREATE POLICY "Users can delete passengers they created"
            ON public.passengers
            FOR DELETE
            USING (
                created_by = auth.uid()
                OR EXISTS (
                    SELECT 1 FROM public.profiles 
                    WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
                )
            );
      `
    })

    if (error) {
      console.error('Error creating passengers table:', error)
      return NextResponse.json({ error: 'Failed to create passengers table' }, { status: 500 })
    }

    return NextResponse.json({ success: true, message: 'Passengers table created successfully' })

  } catch (error) {
    console.error('Error in create passengers table API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
