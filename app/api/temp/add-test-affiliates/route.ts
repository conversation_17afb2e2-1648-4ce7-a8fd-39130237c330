import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Add test affiliate companies for Austin
    const { data: affiliates, error: affiliateError } = await supabase
      .from('affiliate_companies')
      .upsert([
        {
          id: '11111111-1111-1111-1111-111111111111',
          name: 'Austin Elite Transport',
          city: 'Austin',
          state: 'TX',
          status: 'active',
          application_status: 'approved',
          contact_email: '<EMAIL>',
          contact_phone: '************',
          created_at: new Date().toISOString()
        },
        {
          id: '22222222-2222-2222-2222-222222222222',
          name: 'Texas Premium Rides',
          city: 'Austin',
          state: 'TX',
          status: 'active',
          application_status: 'approved',
          contact_email: '<EMAIL>',
          contact_phone: '************',
          created_at: new Date().toISOString()
        },
        {
          id: '33333333-3333-3333-3333-333333333333',
          name: 'Capital City Cars',
          city: 'Austin',
          state: 'TX',
          status: 'active',
          application_status: 'approved',
          contact_email: '<EMAIL>',
          contact_phone: '************',
          created_at: new Date().toISOString()
        }
      ], { 
        onConflict: 'id',
        ignoreDuplicates: false 
      })
      .select()

    if (affiliateError) {
      console.error('Error creating affiliate companies:', affiliateError)
      return NextResponse.json({ error: 'Failed to create affiliate companies' }, { status: 500 })
    }

    // Add test vehicles for these affiliates
    const { data: vehicles, error: vehicleError } = await supabase
      .from('vehicles')
      .upsert([
        {
          id: '44444444-4444-4444-4444-444444444444',
          affiliate_company_id: '11111111-1111-1111-1111-111111111111',
          vehicle_type: 'sedan',
          make: 'Mercedes',
          model: 'S-Class',
          year: 2023,
          capacity: 4,
          status: 'active',
          created_at: new Date().toISOString()
        },
        {
          id: '55555555-5555-5555-5555-555555555555',
          affiliate_company_id: '22222222-2222-2222-2222-222222222222',
          vehicle_type: 'suv',
          make: 'Cadillac',
          model: 'Escalade',
          year: 2023,
          capacity: 7,
          status: 'active',
          created_at: new Date().toISOString()
        },
        {
          id: '66666666-6666-6666-6666-666666666666',
          affiliate_company_id: '33333333-3333-3333-3333-333333333333',
          vehicle_type: 'van',
          make: 'Mercedes',
          model: 'Sprinter',
          year: 2022,
          capacity: 12,
          status: 'active',
          created_at: new Date().toISOString()
        }
      ], { 
        onConflict: 'id',
        ignoreDuplicates: false 
      })
      .select()

    if (vehicleError) {
      console.error('Error creating vehicles:', vehicleError)
      return NextResponse.json({ error: 'Failed to create vehicles' }, { status: 500 })
    }

    // Add test rate cards
    const { data: rateCards, error: rateError } = await supabase
      .from('rate_cards')
      .upsert([
        {
          id: '77777777-7777-7777-7777-777777777777',
          affiliate_company_id: '11111111-1111-1111-1111-111111111111',
          vehicle_type: 'sedan',
          service_type: 'point_to_point',
          base_rate: 150.00,
          per_mile_rate: 2.50,
          per_hour_rate: 75.00,
          minimum_hours: 2,
          status: 'active',
          created_at: new Date().toISOString()
        },
        {
          id: '88888888-8888-8888-8888-888888888888',
          affiliate_company_id: '22222222-2222-2222-2222-222222222222',
          vehicle_type: 'suv',
          service_type: 'point_to_point',
          base_rate: 200.00,
          per_mile_rate: 3.00,
          per_hour_rate: 100.00,
          minimum_hours: 2,
          status: 'active',
          created_at: new Date().toISOString()
        },
        {
          id: '99999999-9999-9999-9999-999999999999',
          affiliate_company_id: '33333333-3333-3333-3333-333333333333',
          vehicle_type: 'van',
          service_type: 'point_to_point',
          base_rate: 300.00,
          per_mile_rate: 4.00,
          per_hour_rate: 150.00,
          minimum_hours: 3,
          status: 'active',
          created_at: new Date().toISOString()
        }
      ], { 
        onConflict: 'id',
        ignoreDuplicates: false 
      })
      .select()

    if (rateError) {
      console.error('Error creating rate cards:', rateError)
      return NextResponse.json({ error: 'Failed to create rate cards' }, { status: 500 })
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Test affiliate data created successfully',
      data: {
        affiliates: affiliates?.length || 0,
        vehicles: vehicles?.length || 0,
        rateCards: rateCards?.length || 0
      }
    })

  } catch (error) {
    console.error('Error in add test affiliates API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
