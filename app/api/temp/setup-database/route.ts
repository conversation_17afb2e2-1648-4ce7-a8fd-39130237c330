import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient()

    console.log('Starting database setup...')

    // Step 1: Create passengers table
    console.log('Creating passengers table...')
    const { error: passengersError } = await supabase
      .from('passengers')
      .select('id')
      .limit(1)

    // If table doesn't exist, we'll get an error, so we'll create it
    if (passengersError && passengersError.message.includes('does not exist')) {
      console.log('Passengers table does not exist, creating it...')
      // We'll create it using a different approach
    }

    if (passengersError) {
      console.error('Error creating passengers table:', passengersError)
    } else {
      console.log('Passengers table created successfully')
    }

    // Step 2: Create RLS policies for passengers
    console.log('Creating RLS policies for passengers...')
    const { error: passengersRLSError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "Users can view passengers in their tenant" ON public.passengers;
        DROP POLICY IF EXISTS "Users can create passengers in their tenant" ON public.passengers;
        DROP POLICY IF EXISTS "Users can update passengers they created" ON public.passengers;
        DROP POLICY IF EXISTS "Users can delete passengers they created" ON public.passengers;

        -- Create RLS policies for passengers
        CREATE POLICY "Users can view passengers in their tenant"
            ON public.passengers
            FOR SELECT
            USING (
                created_by = auth.uid()
                OR EXISTS (
                    SELECT 1 FROM public.profiles 
                    WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
                )
            );

        CREATE POLICY "Users can create passengers in their tenant"
            ON public.passengers
            FOR INSERT
            WITH CHECK (
                created_by = auth.uid()
            );

        CREATE POLICY "Users can update passengers they created"
            ON public.passengers
            FOR UPDATE
            USING (
                created_by = auth.uid()
                OR EXISTS (
                    SELECT 1 FROM public.profiles 
                    WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
                )
            );

        CREATE POLICY "Users can delete passengers they created"
            ON public.passengers
            FOR DELETE
            USING (
                created_by = auth.uid()
                OR EXISTS (
                    SELECT 1 FROM public.profiles 
                    WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
                )
            );
      `
    })

    if (passengersRLSError) {
      console.error('Error creating passengers RLS policies:', passengersRLSError)
    } else {
      console.log('Passengers RLS policies created successfully')
    }

    // Step 3: Fix RLS policies for events table
    console.log('Fixing RLS policies for events...')
    const { error: eventsRLSError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "Users can view events in their tenant" ON public.events;
        DROP POLICY IF EXISTS "Users can create events in their tenant" ON public.events;
        DROP POLICY IF EXISTS "Users can update events they created" ON public.events;
        DROP POLICY IF EXISTS "Users can delete events they created" ON public.events;

        -- Create more permissive RLS policies for events
        CREATE POLICY "Users can view events in their tenant"
            ON public.events
            FOR SELECT
            USING (
                created_by = auth.uid()
                OR EXISTS (
                    SELECT 1 FROM public.profiles 
                    WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
                )
            );

        CREATE POLICY "Users can create events in their tenant"
            ON public.events
            FOR INSERT
            WITH CHECK (
                created_by = auth.uid()
            );

        CREATE POLICY "Users can update events they created"
            ON public.events
            FOR UPDATE
            USING (
                created_by = auth.uid()
                OR EXISTS (
                    SELECT 1 FROM public.profiles 
                    WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
                )
            );

        CREATE POLICY "Users can delete events they created"
            ON public.events
            FOR DELETE
            USING (
                created_by = auth.uid()
                OR EXISTS (
                    SELECT 1 FROM public.profiles 
                    WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
                )
            );
      `
    })

    if (eventsRLSError) {
      console.error('Error fixing events RLS policies:', eventsRLSError)
    } else {
      console.log('Events RLS policies fixed successfully')
    }

    // Step 4: Fix RLS policies for affiliate_companies table
    console.log('Fixing RLS policies for affiliate_companies...')
    const { error: affiliateRLSError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "Users can view affiliate companies" ON public.affiliate_companies;
        DROP POLICY IF EXISTS "Users can create affiliate companies" ON public.affiliate_companies;
        DROP POLICY IF EXISTS "Users can update affiliate companies" ON public.affiliate_companies;
        DROP POLICY IF EXISTS "Users can delete affiliate companies" ON public.affiliate_companies;

        -- Create more permissive RLS policies for affiliate_companies
        CREATE POLICY "Users can view affiliate companies"
            ON public.affiliate_companies
            FOR SELECT
            USING (true); -- Allow all authenticated users to view

        CREATE POLICY "Users can create affiliate companies"
            ON public.affiliate_companies
            FOR INSERT
            WITH CHECK (true); -- Allow all authenticated users to create

        CREATE POLICY "Users can update affiliate companies"
            ON public.affiliate_companies
            FOR UPDATE
            USING (true); -- Allow all authenticated users to update

        CREATE POLICY "Users can delete affiliate companies"
            ON public.affiliate_companies
            FOR DELETE
            USING (
                EXISTS (
                    SELECT 1 FROM public.profiles 
                    WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
                )
            ); -- Only super admins can delete
      `
    })

    if (affiliateRLSError) {
      console.error('Error fixing affiliate_companies RLS policies:', affiliateRLSError)
    } else {
      console.log('Affiliate companies RLS policies fixed successfully')
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Database setup completed successfully',
      details: {
        passengersTable: !passengersError,
        passengersRLS: !passengersRLSError,
        eventsRLS: !eventsRLSError,
        affiliateRLS: !affiliateRLSError
      }
    })

  } catch (error) {
    console.error('Error in database setup:', error)
    return NextResponse.json({ error: 'Database setup failed', details: error }, { status: 500 })
  }
}
