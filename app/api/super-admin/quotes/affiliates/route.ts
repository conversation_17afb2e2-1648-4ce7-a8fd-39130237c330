import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import { Database } from '@/lib/types/supabase'; // Use global Database type
import { hasRole } from '@/lib/auth';
import { UserRole } from '@/src/types/roles';

// API endpoint to fetch affiliates for quote assignment by Super Admin
// GET /api/super-admin/quotes/affiliates?city=Boston

export async function GET(req: Request) {
  console.log('[API SA] GET /api/super-admin/quotes/affiliates - Request received');
  try {
    const supabase = createRouteHandlerClient<Database>({ cookies });
    
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError) {
      console.error('[API SA] Session error:', sessionError.message);
      return NextResponse.json({ error: "Authentication error", code: "auth_error" }, { status: 500 });
    }
    if (!session) {
      console.log('[API SA] No session found, unauthorized.');
      return NextResponse.json({ error: "Authentication required", code: "auth_required" }, { status: 401 });
    }

    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('roles') // Only select roles
      .eq('id', session.user.id)
      .single();
    
    if (profileError) {
      console.error('[API SA] Error fetching super-admin profile:', profileError.message);
      return NextResponse.json({ error: "Failed to verify user role", code: "profile_fetch_error" }, { status: 500 });
    }
    
    if (!profile?.roles || !hasRole(profile.roles as UserRole[], 'SUPER_ADMIN' as UserRole)) {
      console.warn(`[API SA] User ${session.user.id} not SUPER_ADMIN. Roles: ${profile?.roles?.join(', ')}`);
      return NextResponse.json({ error: "Forbidden: SUPER_ADMIN access required", code: "access_denied" }, { status: 403 });
    }

    const url = new URL(req.url);
    const city = url.searchParams.get('city');
    
    let query = supabase
      .from('affiliate_companies')
      .select('*') // Consider selecting specific fields if not all are needed by client
      .eq('status', 'active'); // Always fetch active affiliates

    if (city) {
      const searchCity = city.trim();
      console.log(`[API SA] Filtering active affiliates by city (ILIKE): ${searchCity}`);
      // Using .or() for flexible city matching: exact, starts with, contains
      query = query.or(`city.ilike.${searchCity},city.ilike.${searchCity}%,city.ilike.%${searchCity}%`);
    } else {
      console.log('[API SA] No city filter, fetching all active affiliates.');
    }
    
    const { data: affiliates, error } = await query;

    if (error) {
      console.error('[API SA] Error fetching affiliate_companies:', error.message);
      return NextResponse.json({ error: "Failed to fetch affiliates", details: error.message, code: "fetch_error" }, { status: 500 });
    }

    let sortedAffiliates = affiliates || [];

    if (city && sortedAffiliates.length > 0) {
      const searchCityLower = city.trim().toLowerCase();
      const getMatchType = (affiliateCity: string | null) => {
        if (!affiliateCity) return 3; // No city ranks last
        const lowerAffiliateCity = affiliateCity.trim().toLowerCase();
        if (lowerAffiliateCity === searchCityLower) return 0; // Exact match
        if (lowerAffiliateCity.startsWith(searchCityLower)) return 1; // Starts with
        if (lowerAffiliateCity.includes(searchCityLower)) return 2; // Contains
        return 3; // No match or partial match not covered, ranks last
      };

      sortedAffiliates.sort((a, b) => {
        const matchA = getMatchType(a.city);
        const matchB = getMatchType(b.city);
        if (matchA !== matchB) {
          return matchA - matchB;
        }
        // Optional: Secondary sort by name if match type is the same
        return (a.name || '').localeCompare(b.name || '');
      });
      console.log(`[API SA] Fetched and sorted ${sortedAffiliates.length} affiliates for city: ${city}`);
    } else if (sortedAffiliates.length > 0) {
      console.log(`[API SA] Fetched ${sortedAffiliates.length} active affiliates (no city filter or no matches for city).`);
    }

    return NextResponse.json({ affiliates: sortedAffiliates });

  } catch (error: any) {
    console.error('[API SA] Unexpected error in /super-admin/quotes/affiliates route:', error.message);
    return NextResponse.json(
      { error: "Internal server error fetching affiliates", details: error.message, code: "unknown_error" },
      { status: 500 }
    );
  }
} 