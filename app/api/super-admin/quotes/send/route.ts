import { NextResponse } from 'next/server';
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from 'next/headers';
import { Database } from '@/lib/types/supabase'; // Assuming Database types are correctly defined
import { hasRole } from '@/lib/auth';
import { UserRole } from '@/src/types/roles';
// import { transitionQuoteStatus } from '@/lib/utils/quote-status-transitions'; // This import was in admin but not used

export async function POST(request: Request) {
  const supabase = createRouteHandlerClient<Database>({ cookies });
  
  const { data: { session }, error: sessionError } = await supabase.auth.getSession();
  if (sessionError) {
    console.error('Session error in super-admin/quotes/send:', sessionError);
    return NextResponse.json({ message: 'Authentication error' }, { status: 500 });
  }
  if (!session) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }
  
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('roles')
    .eq('id', session.user.id)
    .single();
  
  if (profileError) {
    console.error('Error fetching super-admin profile for send quote:', profileError);
    return NextResponse.json({ message: 'Failed to verify user role' }, { status: 500 });
  }
  
  if (!hasRole(profile?.roles as UserRole[], 'SUPER_ADMIN' as UserRole)) { // Corrected role check for SUPER_ADMIN
    return NextResponse.json(
      { message: 'Unauthorized - SUPER_ADMIN access required' }, 
      { status: 403 }
    );
  }
  
  try {
    const requestData = await request.json();
    const { quoteId, affiliateIds } = requestData;
    
    if (!quoteId || !affiliateIds || !Array.isArray(affiliateIds) || affiliateIds.length === 0) {
      return NextResponse.json(
        { message: 'Missing required fields: quoteId and affiliateIds (array)' }, 
        { status: 400 }
      );
    }
    
    const { data: quote, error: quoteError } = await supabase
      .from('quotes')
      .select('*')
      .eq('id', quoteId)
      .single();
    
    if (quoteError) {
      if (quoteError.code === 'PGRST116') {
        return NextResponse.json({ message: 'Quote not found' }, { status: 404 });
      }
      console.error('Error fetching quote for send by super-admin:', quoteError);
      return NextResponse.json({ message: 'Error fetching quote details' }, { status: 500 });
    }
    
    const { data: affiliates, error: affiliatesError } = await supabase
      .from('affiliate_companies') // Assuming this is the correct table for affiliates
      .select('id')
      .in('id', affiliateIds);
    
    if (affiliatesError) {
      console.error('Error verifying affiliates for send by super-admin:', affiliatesError);
      return NextResponse.json({ message: 'Failed to verify affiliates' }, { status: 500 });
    }
    
    if (!affiliates || affiliates.length !== affiliateIds.length) {
      const foundIds = affiliates?.map(a => a.id) || [];
      const missingIds = affiliateIds.filter(id => !foundIds.includes(id));
      return NextResponse.json(
        { message: 'One or more affiliates not found', missingAffiliateIds: missingIds }, 
        { status: 400 }
      );
    }
    
    // Ensure p_admin_id is correctly named p_super_admin_id or just p_user_id if the function is generic
    // For now, using p_user_id and assuming the function can handle it or will be updated.
    const { data: rpcResult, error: rpcError } = await supabase.rpc('send_quote_to_affiliates', {
      p_quote_id: quoteId,
      p_affiliate_ids: affiliateIds,
      p_user_id: session.user.id // Changed from p_admin_id to p_user_id for clarity
    });
    
    if (rpcError) {
      console.error('Error from RPC send_quote_to_affiliates by super-admin:', rpcError);
      // Fallback logic was present in admin, consider if it's still needed/appropriate for super-admin
      // For now, treating RPC error as a failure to send.
      return NextResponse.json(
        { message: 'Failed to send quote to affiliates via RPC', error: rpcError.message }, 
        { status: 500 }
      );
    }
    
    const timelineEntries = affiliateIds.map(affiliateId => ({
      quote_id: quoteId,
      action: 'QUOTE_SENT_BY_SUPER_ADMIN', // Action reflects super-admin
      user_id: session.user.id,
      affiliate_id: affiliateId,
      details: `Quote sent to affiliate by super-admin ${session.user.email}`
    }));
    
    const { error: timelineError } = await supabase
      .from('quote_timeline')
      .insert(timelineEntries);
    
    if (timelineError) {
      console.warn('Warning: Failed to create timeline entries for super-admin quote send:', timelineError);
    }
    
    const { data: updatedQuote, error: fetchUpdatedError } = await supabase
      .from('quotes')
      .select('*')
      .eq('id', quoteId)
      .single();
    
    if (fetchUpdatedError) {
      console.error('Error fetching updated quote after send by super-admin:', fetchUpdatedError);
      return NextResponse.json(
        { message: 'Quote sent, but failed to fetch updated data. Please refresh.' },
        { status: 200 } // Still a success for the send operation itself
      );
    }
    
    return NextResponse.json(updatedQuote);
    
  } catch (error: any) {
    console.error('Error processing super-admin quote send request:', error);
    const errorMessage = error.message || 'Internal server error';
    return NextResponse.json(
      { message: errorMessage, error: error.code || undefined }, 
      { status: 500 }
    );
  }
} 