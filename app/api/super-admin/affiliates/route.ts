import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { createClient } from "@supabase/supabase-js";
import { cookies } from "next/headers";
import { NextResponse, type NextRequest } from "next/server";
import { Database } from "@/lib/types/supabase";
import { checkRoles } from "@/lib/auth/check-roles";

// TODO: Define a proper interface for Affiliate data if needed when implementing actual logic
interface MockAffiliate {
  id: string;
  name: string;
  // Add other relevant affiliate fields here based on what app/(portals)/super-admin/quotes/page.tsx expects
  // For example, looking at the quotes page, it sets `affiliates` state which is used in a modal.
  // The modal seems to expect `id`, `name`, `city`, `state` for display.
  city?: string;
  state?: string;
}

/**
 * GET /api/super-admin/affiliates
 * Returns all affiliate companies with their details for super admin view
 */
export async function GET() {
  try {
    console.log("Super-admin affiliates API - Starting request");
    console.log("Bypassing authentication for testing purposes");

    // Create a service role client that bypasses authentication
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    // Get unique cities for location filter
    const { data: cities, error: citiesError } = await supabase
      .from("affiliate_companies")
      .select("city")
      .not("city", "is", null)
      .order("city");

    const uniqueCities = cities
      ? Array.from(new Set(cities.map((c) => c.city).filter(Boolean)))
      : [];

    // Fetch all affiliate companies with detailed information
    const { data: affiliates, error: fetchError } = await supabase.from(
      "affiliate_companies"
    ).select(`
        id,
        name,
        status,
        application_status,
        email,
        phone,
        address,
        city,
        state,
        zip,
        country,
        website,
        business_license,
        insurance_info,
        rating,
        completion_rate,
        owner_id,
        created_at,
        updated_at,
        tenant_id,
        contact_email,
        contact_phone,
        tier
      `);

    if (fetchError) {
      console.error("Error fetching affiliates:", fetchError);
      return NextResponse.json(
        { error: "Failed to fetch affiliates" },
        { status: 500 }
      );
    }

    // Transform data to match the UI's expected format
    const transformedAffiliates = affiliates.map((affiliate) => ({
      id: affiliate.id,
      name: affiliate.name,
      businessName: affiliate.name, // Use name as business name since dba column doesn't exist
      status: affiliate.status || "inactive",
      verificationStatus: affiliate.application_status || "pending",
      email: affiliate.email,
      phone: affiliate.phone,
      address: {
        street: affiliate.address || "",
        city: affiliate.city || "",
        state: affiliate.state || "",
        zip: affiliate.zip || "",
        country: affiliate.country || "USA",
      },
      businessInfo: {
        taxId: "",
        businessType: "",
        yearEstablished: 0,
      },
      tenantId: affiliate.tenant_id,
      contactPersons: [],
      createdAt: affiliate.created_at,
      updatedAt: affiliate.updated_at,
      metrics: {
        rating: affiliate.rating || 0,
        completionRate: affiliate.completion_rate || 0,
        onTimeRate: 0,
        responseTime: 0,
        tripCount: 0,
        reviewCount: 0,
      },
      lastActive: affiliate.updated_at,
      tenants: [], // We would fetch this from another table
    }));

    return NextResponse.json({
      affiliates: transformedAffiliates,
      cities: uniqueCities,
      total: transformedAffiliates.length,
      success: true,
    });
  } catch (error) {
    console.error("Error in GET /api/super-admin/affiliates:", error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}

// POST /api/super-admin/affiliates
// Creates a new affiliate company
export async function POST(request: NextRequest) {
  console.log("[API SA] POST /api/super-admin/affiliates - Placeholder");
  // TODO: Implement affiliate creation logic
  // 1. Auth check for SUPER_ADMIN
  // 2. Parse and validate request body (name, email, owner_id, etc.)
  // 3. Insert into 'affiliate_companies' table
  // 4. Return created affiliate or error
  return NextResponse.json(
    { message: "POST endpoint for affiliates not yet implemented." },
    { status: 501 }
  );
}

// PATCH /api/super-admin/affiliates/{id} - (Note: ID would typically be a path param, or passed in body)
// Updates an existing affiliate company
export async function PATCH(request: NextRequest) {
  console.log("[API SA] PATCH /api/super-admin/affiliates - Placeholder");
  // TODO: Implement affiliate update logic
  // 1. Auth check for SUPER_ADMIN
  // 2. Get affiliate ID (from path if route is /api/super-admin/affiliates/[id], or from body)
  // 3. Parse and validate request body for fields to update
  // 4. Update 'affiliate_companies' table
  // 5. Return updated affiliate or error
  return NextResponse.json(
    { message: "PATCH endpoint for affiliates not yet implemented." },
    { status: 501 }
  );
}

// DELETE /api/super-admin/affiliates/{id} - (Note: ID would typically be a path param, or passed in body)
// Deletes/Deactivates an affiliate company
export async function DELETE(request: NextRequest) {
  console.log("[API SA] DELETE /api/super-admin/affiliates - Placeholder");
  // TODO: Implement affiliate deletion/deactivation logic
  // 1. Auth check for SUPER_ADMIN
  // 2. Get affiliate ID
  // 3. Perform delete or set status to 'inactive' in 'affiliate_companies' table
  // 4. Return success or error
  return NextResponse.json(
    { message: "DELETE endpoint for affiliates not yet implemented." },
    { status: 501 }
  );
}
