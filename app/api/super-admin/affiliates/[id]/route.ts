import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { createClient } from "@supabase/supabase-js";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";
import { checkRoles } from "@/lib/auth/check-roles";

/**
 * PATCH /api/super-admin/affiliates/:id
 * Update an affiliate's information
 */
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log(
      `Super-admin affiliate update API - Starting request for ID: ${params.id}`
    );
    console.log("Bypassing authentication for testing purposes");

    // Create a service role client that bypasses authentication (same as GET method)
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    // Get the affiliate ID from params
    const { id } = params;
    if (!id) {
      return NextResponse.json(
        { error: "Affiliate ID is required" },
        { status: 400 }
      );
    }

    // Parse the request body
    const body = await request.json();

    // Create an object for the fields to update
    const updateData: Record<string, any> = {};

    // Handle status update
    if (body.status) {
      if (!["active", "pending", "inactive"].includes(body.status)) {
        return NextResponse.json(
          { error: "Invalid status value" },
          { status: 400 }
        );
      }
      updateData.status = body.status;
    }

    // Handle verification status update if provided
    if (body.verificationStatus) {
      if (
        !["verified", "in_progress", "pending", "rejected"].includes(
          body.verificationStatus
        )
      ) {
        return NextResponse.json(
          { error: "Invalid verification status value" },
          { status: 400 }
        );
      }
      updateData.verification_status = body.verificationStatus;
    }

    // If no valid fields to update, return error
    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { error: "No valid fields to update" },
        { status: 400 }
      );
    }

    // Add audit fields
    updateData.updated_at = new Date().toISOString();

    // Update the affiliate
    const { data, error: updateError } = await supabase
      .from("affiliate_companies")
      .update(updateData)
      .eq("id", id)
      .select()
      .single();

    if (updateError) {
      console.error("Error updating affiliate:", updateError);
      return NextResponse.json(
        { error: "Failed to update affiliate" },
        { status: 500 }
      );
    }

    // Log the update action to audit logs
    const auditLogEntry = {
      user_id: "7ab6f229-1250-485b-8a17-1947237b0ca3", // TODO: Get from session
      action: "affiliate_update",
      table_name: "affiliate_companies",
      record_id: id,
      details: JSON.stringify({
        affiliate_id: id,
        changes: updateData,
      }),
    };

    await supabase.from("audit_logs").insert(auditLogEntry);

    // Send notification email if status changed to active or inactive
    if (updateData.status) {
      try {
        let notificationType = "";
        if (updateData.status === "active") {
          notificationType = "approval";
        } else if (updateData.status === "inactive") {
          notificationType = "rejection";
        } else if (updateData.verification_status === "in_progress") {
          notificationType = "update_request";
        }

        if (notificationType) {
          const notificationResponse = await fetch(
            `${process.env.NEXT_PUBLIC_APP_URL}/api/super-admin/affiliates/${id}/notify`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Cookie: request.headers.get("Cookie") || "",
              },
              body: JSON.stringify({
                type: notificationType,
                status: updateData.status,
                message: body.message || "",
              }),
            }
          );

          if (!notificationResponse.ok) {
            console.error("Failed to send notification email");
          }
        }
      } catch (notificationError) {
        console.error("Error sending notification:", notificationError);
        // Don't fail the main request if notification fails
      }
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error("Error in PATCH /api/super-admin/affiliates/[id]:", error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/super-admin/affiliates/:id
 * Get details for a specific affiliate
 */
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    console.log(
      `Super-admin affiliate details API - Starting request for ID: ${params.id}`
    );
    console.log("Bypassing authentication for testing purposes");

    // Create a service role client that bypasses authentication
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );

    // Get the affiliate ID from params
    const { id } = params;
    if (!id) {
      return NextResponse.json(
        { error: "Affiliate ID is required" },
        { status: 400 }
      );
    }

    // Fetch affiliate company details
    const { data: affiliate, error: affiliateError } = await supabase
      .from("affiliate_companies")
      .select(
        `
        id,
        name,
        status,
        application_status,
        email,
        phone,
        address,
        city,
        state,
        zip,
        country,
        website,
        business_license,
        insurance_info,
        rating,
        completion_rate,
        owner_id,
        created_at,
        updated_at,
        tenant_id
      `
      )
      .eq("id", id)
      .single();

    if (affiliateError) {
      console.error("Error fetching affiliate:", affiliateError);
      return NextResponse.json(
        { error: "Affiliate not found" },
        { status: 404 }
      );
    }

    // Fetch fleet vehicles
    const { data: vehicles, error: vehiclesError } = await supabase
      .from("vehicles")
      .select(
        `
        id,
        type,
        make,
        model,
        year,
        license_plate,
        capacity,
        amenities,
        status,
        created_at,
        updated_at
      `
      )
      .eq("company_id", id);

    if (vehiclesError) {
      console.error("Error fetching vehicles:", vehiclesError);
    }

    // Fetch rate cards
    const { data: rateCards, error: rateCardsError } = await supabase
      .from("rate_cards")
      .select(
        `
        id,
        vehicle_type,
        pricing_model_type,
        p2p_point_to_point_rate,
        p2p_extra_hour_rate,
        dt_base_fee,
        dt_per_mile_rate,
        dt_per_hour_rate,
        dt_min_miles,
        per_hour_rate,
        minimum_hours,
        airport_transfer_flat_rate,
        status,
        created_at,
        updated_at
      `
      )
      .eq("company_id", id);

    if (rateCardsError) {
      console.error("Error fetching rate cards:", rateCardsError);
    }

    // Fetch drivers
    const { data: drivers, error: driversError } = await supabase
      .from("drivers")
      .select(
        `
        id,
        first_name,
        last_name,
        email,
        phone,
        license_number,
        license_expiry,
        status,
        created_at,
        updated_at
      `
      )
      .eq("company_id", id);

    if (driversError) {
      console.error("Error fetching drivers:", driversError);
    }

    // Transform data to match the UI's expected format
    const transformedAffiliate = {
      id: affiliate.id,
      name: affiliate.name,
      businessName: affiliate.name, // Use name as business name since dba doesn't exist
      status: affiliate.status || "inactive",
      verificationStatus: affiliate.application_status || "pending",
      email: affiliate.email,
      phone: affiliate.phone,
      address: {
        street: affiliate.address || "",
        city: affiliate.city || "",
        state: affiliate.state || "",
        zip: affiliate.zip || "",
        country: affiliate.country || "USA",
      },
      businessInfo: {
        taxId: affiliate.business_license || "",
        businessType: "",
        yearEstablished: 0,
      },
      tenantId: affiliate.tenant_id,
      contactPersons: [],
      createdAt: affiliate.created_at,
      updatedAt: affiliate.updated_at,
      metrics: {
        rating: affiliate.rating || 0,
        completionRate: affiliate.completion_rate || 0,
        onTimeRate: 0,
        responseTime: 0,
        tripCount: 0,
        reviewCount: 0,
      },
      lastActive: affiliate.updated_at,
      tenants: [],
    };

    return NextResponse.json({
      affiliate: transformedAffiliate,
      fleet: vehicles || [],
      rateCards: rateCards || [],
      drivers: drivers || [],
      success: true,
    });
  } catch (error) {
    console.error("Error in GET /api/super-admin/affiliates/[id]:", error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}
