import { NextResponse } from 'next/server';
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from 'next/headers';
import { checkRoles } from '@/lib/auth/check-roles';
import { getSession } from "@/app/lib/auth";
import { createServerClient } from "@supabase/ssr";
import { getTenantEmailTemplate } from "@/app/lib/tenant-email-templates";

/**
 * POST /api/super-admin/affiliates/:id/notify
 * Send email notification to affiliate about status change
 */
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    // Auth check
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized - please login' }, { status: 401 });
    }

    // Role check - must be super admin
    const { hasRole, error: roleError } = await checkRoles(session, ['SUPER_ADMIN']);
    if (roleError || !hasRole) {
      return NextResponse.json({ error: 'Forbidden - insufficient permissions' }, { status: 403 });
    }

    const { id } = params;
    const body = await request.json();
    const { type, message, rejection_reasons } = body;

    if (!type || !['approval', 'rejection', 'update_request'].includes(type)) {
      return NextResponse.json({ error: 'Invalid notification type' }, { status: 400 });
    }

    // Fetch affiliate details
    const { data: affiliate, error: fetchError } = await supabase
      .from('affiliate_companies')
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError || !affiliate) {
      return NextResponse.json({ error: 'Affiliate not found' }, { status: 404 });
    }

    let emailSubject: string = '';
    let emailContent: string = '';

    const template = await getTenantEmailTemplate(affiliate.tenant_id, type);

    if (template) {
      emailSubject = template.subject_template;
      emailContent = template.content_template;

      // Simple templating logic (can be replaced with a more robust engine if needed)
      emailContent = emailContent.replace(/\{\{affiliate_name\}\}/g, affiliate.name || 'Affiliate');
      if (type === 'approval') {
        emailContent = emailContent.replace(/\{\{approval_notes\}\}/g, message || '');
      } else if (type === 'rejection') {
        let reasonsHtml = '';
        if (rejection_reasons && Array.isArray(rejection_reasons) && rejection_reasons.length > 0) {
          reasonsHtml = `<ul>${rejection_reasons.map(reason => `<li>${reason}</li>`).join('')}</ul>`;
        }
        emailContent = emailContent.replace(/\{\{rejection_reasons\}\}/g, reasonsHtml);
        emailContent = emailContent.replace(/\{\{rejection_notes\}\}/g, message || '');
      } else if (type === 'update_request') {
        emailContent = emailContent.replace(/\{\{message\}\}/g, message || '');
      }
    } else {
      // Fallback for when no template is found (should not happen if default templates are seeded)
      console.warn(`No email template found for type: ${type}. Using generic content.`);
      switch (type) {
        case 'approval':
          emailSubject = '🎉 Your Affiliate Application Has Been Approved!';
          emailContent = `
            <h2>Congratulations ${affiliate.name || 'Affiliate'}!</h2>
            <p>We're excited to inform you that your affiliate application has been approved and your account is now active.</p>
            <p>Welcome to our affiliate network!</p>
          `;
          break;
        case 'rejection':
          emailSubject = 'Update Required: Your Affiliate Application';
          let reasonsHtml = '';
          if (rejection_reasons && Array.isArray(rejection_reasons) && rejection_reasons.length > 0) {
            reasonsHtml = `<ul>${rejection_reasons.map(reason => `<li>${reason}</li>`).join('')}</ul>`;
          }
          emailContent = `
            <h2>Application Status Update</h2>
            <p>Thank you for your interest in joining our affiliate network.</p>
            <p>After reviewing your application, we need some additional information or corrections before we can proceed with approval.</p>
            ${reasonsHtml}
            ${message ? `<p>Additional Details:</p><p>${message}</p>` : ''}
          `;
          break;
        case 'update_request':
          emailSubject = 'Action Required: Please Update Your Application';
          emailContent = `
            <h2>Application Update Required</h2>
            <p>We're reviewing your affiliate application and need you to provide some additional information.</p>
            ${message ? `<p>Required Updates:</p><p>${message}</p>` : ''}
          `;
          break;
        default:
          emailSubject = 'Notification from TransFlow';
          emailContent = '<p>A notification from TransFlow regarding your account.</p>';
          break;
      }
    }

    const fromEmail = process.env.SYSTEM_EMAIL_FROM_ADDRESS || "<EMAIL>";
    const fromName = affiliate.tenant_id ? `${affiliate.tenant_id} Team` : "TransFlow Team"; // Customize sender name based on tenant

    const emailPayload = {
      to: affiliate.email,
      from: `${fromName} <${fromEmail}>`,
      subject: emailSubject,
      html: emailContent,
      // ... other fields like dealId, themeColor etc. if applicable and needed for emails
    };

    const res = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/emails`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(emailPayload),
    });

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(`Failed to send notification email: ${JSON.stringify(errorData)}`);
    }

    return NextResponse.json({ success: true, message: "Notification sent successfully" });
  } catch (error) {
    console.error("Error sending affiliate notification:", error);
    return NextResponse.json({ success: false, error: (error as Error).message }, { status: 500 });
  }
}
