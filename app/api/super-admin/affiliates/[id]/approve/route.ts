import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(
      "Super-admin affiliate approval API - Starting request for ID:",
      params.id
    );

    // For testing purposes, bypass authentication
    console.log("Bypassing authentication for testing purposes");

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get request body for any additional data
    const body = await request.json().catch(() => ({}));
    const { notes, send_notification = true } = body;

    // Get current affiliate data for audit trail
    const { data: currentAffiliate, error: fetchError } = await supabase
      .from("affiliate_companies")
      .select("*")
      .eq("id", params.id)
      .single();

    if (fetchError) {
      console.error("Error fetching affiliate:", fetchError);
      return NextResponse.json(
        { error: "Affiliate not found", details: fetchError.message },
        { status: 404 }
      );
    }

    const previousStatus = currentAffiliate.application_status;

    // Update affiliate status to approved
    const { data: updatedAffiliate, error: updateError } = await supabase
      .from("affiliate_companies")
      .update({
        status: "active",
        application_status: "approved",
        updated_at: new Date().toISOString(),
      })
      .eq("id", params.id)
      .select()
      .single();

    if (updateError) {
      console.error("Error updating affiliate status:", updateError);
      return NextResponse.json(
        { error: "Failed to approve affiliate", details: updateError.message },
        { status: 500 }
      );
    }

    // Create audit log entry
    const { error: auditError } = await supabase.from("audit_logs").insert({
      user_id: "7ab6f229-1250-485b-8a17-1947237b0ca3", // TODO: Get from session
      action: "affiliate_approved",
      table_name: "affiliate_companies",
      record_id: params.id,
      details: JSON.stringify({
        previous_status: previousStatus,
        new_status: "approved",
        notes: notes,
        affiliate_name: currentAffiliate.name,
      }),
    });

    if (auditError) {
      console.error("Error creating audit log:", auditError);
      // Don't fail the request for audit log errors
    }

    // Auto-activate all rate cards for the approved affiliate
    console.log(
      "Auto-activating rate cards for approved affiliate:",
      params.id
    );
    const { data: activatedRateCards, error: rateCardError } = await supabase
      .from("rate_cards")
      .update({
        is_active: true,
        status: "approved",
        updated_at: new Date().toISOString(),
      })
      .eq("company_id", params.id)
      .select("id, vehicle_type, status, is_active");

    if (rateCardError) {
      console.error("Error activating rate cards:", rateCardError);
      // Log warning but don't fail the approval - rate cards can be activated manually later
      console.warn(
        "Rate card activation failed, but affiliate approval succeeded"
      );
    } else {
      console.log(
        `Successfully activated ${
          activatedRateCards?.length || 0
        } rate cards for affiliate ${params.id}:`,
        activatedRateCards
      );
    }

    // Note: affiliate_approval_history table doesn't exist yet
    // History is tracked via audit_logs table above
    console.log("Approval history tracked via audit_logs");

    // Send notification email if requested
    if (send_notification) {
      try {
        const notificationResponse = await fetch(
          `${request.nextUrl.origin}/api/super-admin/affiliates/${params.id}/notify`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              type: "approval",
              custom_message: notes,
            }),
          }
        );

        if (!notificationResponse.ok) {
          console.error("Failed to send notification email");
        }
      } catch (notificationError) {
        console.error("Error sending notification:", notificationError);
        // Don't fail the request for notification errors
      }
    }

    console.log(`Affiliate ${params.id} approved successfully`);

    return NextResponse.json({
      success: true,
      message: "Affiliate approved successfully",
      affiliate: updatedAffiliate,
      notification_sent: send_notification,
      rate_cards_activated: activatedRateCards?.length || 0,
      activated_rate_cards: activatedRateCards || [],
    });
  } catch (error) {
    console.error("Error in affiliate approval API:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
