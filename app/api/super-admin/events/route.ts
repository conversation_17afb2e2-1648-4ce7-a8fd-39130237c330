import { getSession, hasRole } from '@/app/lib/auth';
import { cookies } from 'next/headers';
import { createClient } from '@supabase/supabase-js';
import { toUserRoles } from '@/src/types/roles';
import { NextRequest, NextResponse } from 'next/server';

interface Event {
  id: string;
  name: string;
  description: string;
  start_date: string;
  end_date: string;
  location: string;
  total_passengers: number;
  status: string;
  customer_id: string;
  tenant_id: string;
  created_at: string;
  updated_at: string;
  customer?: {
    id: string;
    email: string;
    full_name: string;
  };
  organization?: {
    id: string;
    name: string;
    slug: string;
  };
}

export async function GET(request: NextRequest) {
  // Require session and SUPER_ADMIN
  const session = await getSession(request);
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  if (!hasRole(session.roles, 'SUPER_ADMIN')) {
    return NextResponse.json({ error: 'Forbidden: SUPER_ADMIN role required', requiredRole: 'SUPER_ADMIN', userRoles: session.roles }, { status: 403 });
  }
  // Use authenticated Supabase client
  const supabase = await getSupabaseClient(request);
  if (!supabase) {
    return NextResponse.json({ error: 'Failed to initialize Supabase client' }, { status: 500 });
  }
  // TODO: Implement actual event fetching logic
  return NextResponse.json({ message: 'Super-admin events endpoint secured. Implement logic as needed.' });
}

// Create a function to get an authenticated Supabase client
const getSupabaseClient = async (request: NextRequest) => {
  const session = await getSession(request);
  if (!session) return null;
  const authHeader = request.headers.get('authorization');
  let token = authHeader?.split(' ')[1];
  if (!token && 'access_token' in session && typeof (session as any).access_token === 'string') {
    token = (session as any).access_token;
  }
  if (!token) {
    console.error('No access token found in session or headers');
    return null;
  }
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
        storageKey: 'sb-auth-token',
        storage: {
          getItem: (key) => {
            const cookieStore = cookies();
            return cookieStore.get(key)?.value || null;
          },
          setItem: () => {},
          removeItem: () => {},
        },
      },
      global: {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    }
  );
};

export async function GET_OLD(request: NextRequest) {
  try {
    console.log('Super-admin events API - Starting request');

    // Create a service role client that bypasses authentication
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Get organization filter from query params
    const { searchParams } = new URL(request.url);
    const orgFilter = searchParams.get('org');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    console.log('Super-admin events API - Params:', { orgFilter, status, limit, offset });

    // Build the query - use auth.users instead of profiles for the foreign key relationship
    let query = supabase
      .from('events')
      .select(`
        id,
        name,
        description,
        start_date,
        end_date,
        location,
        total_passengers,
        status,
        customer_id,
        tenant_id,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Organization filtering is now done in the transformation loop

    // Apply status filter if specified
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    const { data: events, error } = await query;

    if (error) {
      console.error('Error fetching events:', error);
      return NextResponse.json({ error: 'Failed to fetch events' }, { status: 500 });
    }

    console.log(`Super-admin events API - Found ${events?.length || 0} events`);

    // Get customer and organization data for each event
    const transformedEvents = [];

    for (const event of events || []) {
      // Get customer profile data
      const { data: profile } = await supabase
        .from('profiles')
        .select(`
          id,
          email,
          full_name
        `)
        .eq('id', event.customer_id)
        .single();

      // Get user organizations separately
      let userOrganizations = [];
      if (profile) {
        const { data: orgData } = await supabase
          .from('user_organizations')
          .select(`
            organization_id,
            organizations(id, name, slug)
          `)
          .eq('user_id', profile.id);

        userOrganizations = orgData || [];
      }

      // Apply organization filter if specified
      if (orgFilter && orgFilter !== 'all') {
        const hasMatchingOrg = userOrganizations.some(
          uo => uo.organization_id === orgFilter
        );
        if (!hasMatchingOrg) {
          continue; // Skip this event if it doesn't match the organization filter
        }
      }

      transformedEvents.push({
        id: event.id,
        name: event.name,
        description: event.description,
        start_date: event.start_date,
        end_date: event.end_date,
        location: event.location,
        total_passengers: event.total_passengers,
        status: event.status,
        customer_id: event.customer_id,
        tenant_id: event.tenant_id,
        created_at: event.created_at,
        updated_at: event.updated_at,
        customer: {
          id: profile?.id,
          email: profile?.email,
          full_name: profile?.full_name
        },
        organization: {
          id: userOrganizations[0]?.organizations?.id,
          name: userOrganizations[0]?.organizations?.name,
          slug: userOrganizations[0]?.organizations?.slug
        }
      });
    }

    return NextResponse.json({
      events: transformedEvents,
      success: true
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('Error in super-admin events API:', error);
    return NextResponse.json(
      { error: `Failed to fetch events: ${errorMessage}` },
      { status: 500 }
    );
  }
} 