import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'
import { getSession, hasRole, Session } from '@/app/lib/auth'
import { cookies } from 'next/headers'
import { Database } from '@/lib/database.types'

// Create a server-side Supabase client
function createServerClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false
      }
    }
  )
}

// Import the UserProfile and UserRole type from roles to ensure consistency
import type {
  UserProfile as BaseUserProfile,
  UserRole
} from '@/src/types/roles';
import { isValidRole } from '@/src/types/roles'; // Import isValidRole

// No longer need to re-export UserRole here as it's imported directly where needed
// export type { UserRole };

// Define the user profile type that matches our database schema
// Extend the base UserProfile type to include our role mapping
interface UserProfile extends Omit<BaseUserProfile, 'roles'> {
  roles: UserRole[];
  [key: string]: any; // Allow additional properties
}

// Type for permission override
interface PermissionOverride {
  id: string;
  tenant_id: string | null;
  organization_id: string | null;
  feature_permissions: Record<string, boolean>;
  ui_customizations: Record<string, string>;
  access_controls: Record<string, any>;
  template_id: string | null;
  notes: string | null;
  permission_templates?: {
    id: string;
    name: string;
    description: string;
  };
  tenants?: {
    id: string;
    name: string;
    tenant_type: string;
  };
  organizations?: {
    id: string;
    name: string;
    slug: string;
  };
}

// Create a function to get an authenticated Supabase client
const getSupabaseClient = async (request: NextRequest) => {
  const session = await getSession(request) as Session | null;
  if (!session) return null;

  // Get the access token from the Authorization header if available
  const authHeader = request.headers.get('authorization');
  const token = authHeader?.split(' ')[1] || session.access_token;

  if (!token) {
    console.error('No access token found in session or headers');
    return null;
  }

  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
        storageKey: 'sb-auth-token',
        storage: {
          getItem: (key) => {
            const cookieStore = cookies();
            return cookieStore.get(key)?.value || null;
          },
          setItem: () => {},
          removeItem: () => {},
        },
      },
      global: {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    }
  );
};

/**
 * GET /api/super-admin/users/[userId]/permissions
 * Get user's granular permissions
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  // Debug: log all cookies and headers received
  console.log('[PERMISSIONS API] Request headers:', Object.fromEntries(request.headers.entries()));
  console.log('[PERMISSIONS API] Cookies received:', request.cookies.getAll());
  console.log('[PERMISSIONS API] Handler called for user:', params.userId);
  try {
    console.log('[PERMISSIONS API] About to call getSession');
    const session = await getSession(request);
    console.log('[PERMISSIONS API] getSession result:', session);
    if (!session) {
      console.error('No valid session found in cookies');
      return NextResponse.json({ 
        error: 'Unauthorized',
        details: 'No valid session found. Please sign in again.' 
      }, { status: 401 });
    }
    // Guard: session.id must be present and a valid UUID
    if (!session.id || typeof session.id !== 'string' || !/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/.test(session.id)) {
      console.error('[PERMISSIONS API] Invalid or missing session.id:', session.id, 'Full session:', session);
      return NextResponse.json({
        error: 'Invalid session',
        details: 'Session is missing a valid user id. Please sign in again.'
      }, { status: 401 });
    }
    // Use hasRole for SUPER_ADMIN check directly on session.roles
    if (!hasRole(session.roles, 'SUPER_ADMIN')) {
      console.error('Forbidden: User does not have required role. Has roles:', session.roles);
      return NextResponse.json({ 
        error: 'Forbidden', 
        message: 'You must be a SUPER_ADMIN to access this resource',
        requiredRole: 'SUPER_ADMIN',
        userRoles: session.roles 
      }, { status: 403 });
    }

    // Get authenticated Supabase client with the session token
    const supabase = await getSupabaseClient(request);
    if (!supabase) {
      console.error('Failed to initialize Supabase client');
      return NextResponse.json({ 
        error: 'Internal Server Error',
        details: 'Failed to initialize database client'
      }, { status: 500 });
    }
    
    console.log('Supabase client initialized successfully');

    // Get current user's profile with roles
    const { data: currentUserProfile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', session.id)
      .single();

    if (profileError || !currentUserProfile) {
      console.error('Error fetching current user profile:', profileError);
      return NextResponse.json({ 
        error: 'Failed to verify permissions',
        details: `Could not fetch profile: ${profileError?.message || 'Profile not found'}` 
      }, { status: 500 });
    }

    console.log('Current user profile:', JSON.stringify(currentUserProfile, null, 2));

    // Validate user has SUPER_ADMIN role
    const isSuperAdmin = hasRole(currentUserProfile.roles, 'SUPER_ADMIN'); // Now directly assigned boolean
    console.log('User has SUPER_ADMIN role:', isSuperAdmin);
    
    if (!isSuperAdmin) {
      console.error('User does not have SUPER_ADMIN role');
      return NextResponse.json({ 
        error: 'Forbidden',
        message: 'Insufficient permissions. SUPER_ADMIN role required.',
        requiredRole: 'SUPER_ADMIN',
        userRoles: currentUserProfile.roles
      }, { status: 403 });
    }

    // Get user's current permissions
    console.log('Fetching permissions for user ID:', params.userId);
    const { data: permissions, error: permissionsError } = await supabase
      .from('user_permission_overrides')
      .select('*')
      .eq('user_id', params.userId);

    if (permissionsError) {
      console.error('Database error fetching permissions:', permissionsError);
      return NextResponse.json({ 
        error: 'Failed to fetch user permissions',
        details: `Database error: ${permissionsError.message}`
      }, { status: 500 });
    }
    
    console.log('Found permissions:', JSON.stringify(permissions, null, 2));

    // Get target user's basic info
    console.log('Fetching target user profile for ID:', params.userId);
    const { data: userProfile, error: profileFetchError } = await supabase
      .from('profiles')
      .select(`
        id,
        email,
        full_name,
        roles,
        created_at
      `)
      .eq('id', params.userId)
      .single();

    if (profileFetchError) {
      console.error('Error fetching target user profile:', profileFetchError);
      return NextResponse.json({ 
        error: 'Failed to fetch user profile',
        details: `Database error: ${profileFetchError.message}`
      }, { status: 500 });
    }
    
    console.log('Target user profile:', JSON.stringify(userProfile, null, 2));

    // Get user's organization memberships
    console.log('Fetching organizations for user ID:', params.userId);
    const { data: userOrgs, error: orgsError } = await supabase
      .from('user_organizations')
      .select('*')
      .eq('user_id', params.userId)
      .not('tenant_id', 'is', null);

    if (orgsError) {
      console.error('Error fetching user organizations:', orgsError);
      // Don't fail the entire request if we can't get orgs
      console.log('Continuing without organization data');
    } else {
      console.log('Found organizations:', JSON.stringify(userOrgs, null, 2));
    }

    return NextResponse.json({
      user: userProfile,
      organizations: userOrgs || [],
      permissions: permissions ? permissions[0] || null : null, // Assuming one permission override per user for now
    });
  } catch (error: any) {
    console.error('[PERMISSIONS API GET] Catch-all error:', error);
    return NextResponse.json({ error: 'An unexpected error occurred.', details: error.message }, { status: 500 });
  }
}

/**
 * POST /api/super-admin/users/[userId]/permissions
 * Update user's granular permissions (create or update override)
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  console.log('[PERMISSIONS API POST] Handler called for user:', params.userId);
  try {
    const session = await getSession(request);
    if (!session) {
      console.error('No valid session found in cookies for POST request');
      return NextResponse.json({
        error: 'Unauthorized',
        details: 'No valid session found. Please sign in again.'
      }, { status: 401 });
    }

    if (!session.id || typeof session.id !== 'string' || !/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/.test(session.id)) {
      console.error('[PERMISSIONS API POST] Invalid or missing session.id');
      return NextResponse.json({
        error: 'Invalid session',
        details: 'Session is missing a valid user id. Please sign in again.'
      }, { status: 401 });
    }

    // Use hasRole for SUPER_ADMIN check directly on session.roles
    if (!hasRole(session.roles, 'SUPER_ADMIN')) {
      console.error('Forbidden: User does not have required role for POST. Has roles:', session.roles);
      return NextResponse.json({
        error: 'Forbidden',
        message: 'You must be a SUPER_ADMIN to perform this action',
        requiredRole: 'SUPER_ADMIN',
        userRoles: session.roles
      }, { status: 403 });
    }

    const supabase = await getSupabaseClient(request);
    if (!supabase) {
      console.error('Failed to initialize Supabase client for POST request');
      return NextResponse.json({
        error: 'Internal Server Error',
        details: 'Failed to initialize database client'
      }, { status: 500 });
    }

    const targetUserId = params.userId;
    if (!targetUserId || !/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/.test(targetUserId)) {
      console.error('Invalid targetUserId for POST:', targetUserId);
      return NextResponse.json({ error: 'Invalid target user ID' }, { status: 400 });
    }

    const body = await request.json();
    const { role: newRole, features, ui, access, templateId, notes, organizationId, tenantId } = body;

    // Validate incoming role if provided
    if (newRole && !isValidRole(newRole)) {
      console.error('Invalid new role provided:', newRole);
      return NextResponse.json({ error: 'Invalid role provided' }, { status: 400 });
    }

    // Business logic for role changes and permissions:
    // 1. Fetch the target user's existing profile and permissions
    const { data: targetUserProfile, error: fetchProfileError } = await supabase
      .from('profiles')
      .select('*, permission_overrides(*)')
      .eq('id', targetUserId)
      .single();

    if (fetchProfileError || !targetUserProfile) {
      console.error('Failed to fetch target user profile:', fetchProfileError);
      return NextResponse.json({ error: 'Target user not found or profile fetch failed' }, { status: 404 });
    }

    // Ensure targetUserProfile.roles is an array for consistent hasRole usage
    const targetUserRoles = targetUserProfile.roles || [];

    // Prevent non-SUPER_ADMIN from changing SUPER_ADMIN's role
    // This check is implicitly covered by the initial SUPER_ADMIN check for the acting user,
    // but explicitly adding for robustness and clarity, ensuring a SUPER_ADMIN can't demote themselves or another SUPER_ADMIN via this endpoint if not intended.
    if (newRole && hasRole(targetUserRoles, 'SUPER_ADMIN')) {
      if (!hasRole(session.roles, 'SUPER_ADMIN')) { // Only SUPER_ADMIN can change a SUPER_ADMIN's role
        return NextResponse.json({ error: 'You do not have permission to change the role of a SUPER_ADMIN' }, { status: 403 });
      }
    }

    // Update user role if newRole is provided
    if (newRole) {
      console.log(`Attempting to update user ${targetUserId} role to: ${newRole}`);
      const { data: authUserUpdate, error: authUpdateError } = await supabase.auth.admin.updateUserById(
        targetUserId,
        {
          user_metadata: {
            roles: [newRole],
            role: newRole, // For backward compatibility with legacy single role property
          },
        }
      );
      if (authUpdateError) {
        console.error('Error updating user auth metadata:', authUpdateError);
        return NextResponse.json({ error: 'Failed to update user role', details: authUpdateError.message }, { status: 500 });
      }
      console.log('User auth metadata updated successfully', authUserUpdate);

      // Also update the profile table directly for consistency
      const { error: profileRoleUpdateError } = await supabase
        .from('profiles')
        .update({ roles: [newRole], role: newRole }) // Update both for consistency
        .eq('id', targetUserId);

      if (profileRoleUpdateError) {
        console.error('Error updating profile role:', profileRoleUpdateError);
        return NextResponse.json({ error: 'Failed to update profile role', details: profileRoleUpdateError.message }, { status: 500 });
      }
      console.log('Profile role updated successfully');
    }

    let permissionOverrideId = targetUserProfile.permission_overrides?.[0]?.id;
    let updatePermissionsPayload: Partial<PermissionOverride> = {
      feature_permissions: features || {},
      ui_customizations: ui || {},
      access_controls: access || {},
      template_id: templateId,
      notes: notes,
      tenant_id: tenantId || null, // Allow setting/clearing tenant_id
      organization_id: organizationId || null, // Allow setting/clearing organization_id
    };

    if (permissionOverrideId) {
      // Update existing permission override
      console.log('Updating existing permission override:', permissionOverrideId);
      const { data: updatedOverride, error: updateOverrideError } = await supabase
        .from('permission_overrides')
        .update(updatePermissionsPayload)
        .eq('id', permissionOverrideId)
        .select()
        .single();

      if (updateOverrideError) {
        console.error('Error updating permission override:', updateOverrideError);
        return NextResponse.json({ error: 'Failed to update permissions', details: updateOverrideError.message }, { status: 500 });
      }
      console.log('Permission override updated successfully', updatedOverride);
      return NextResponse.json(updatedOverride);
    } else {
      // Create new permission override
      console.log('Creating new permission override for user:', targetUserId);
      const { data: newOverride, error: createOverrideError } = await supabase
        .from('permission_overrides')
        .insert({
          user_id: targetUserId,
          ...updatePermissionsPayload,
        })
        .select()
        .single();

      if (createOverrideError) {
        console.error('Error creating permission override:', createOverrideError);
        return NextResponse.json({ error: 'Failed to create permissions', details: createOverrideError.message }, { status: 500 });
      }
      console.log('New permission override created successfully', newOverride);
      return NextResponse.json(newOverride);
    }
  } catch (error: any) {
    console.error('[PERMISSIONS API POST] Catch-all error:', error);
    return NextResponse.json({ error: 'An unexpected error occurred.', details: error.message }, { status: 500 });
  }
}

/**
 * DELETE /api/super-admin/users/[userId]/permissions
 * Delete a user's granular permission override
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  console.log('[PERMISSIONS API DELETE] Handler called for user:', params.userId);
  try {
    const session = await getSession(request);
    if (!session) {
      console.error('No valid session found in cookies for DELETE request');
      return NextResponse.json({
        error: 'Unauthorized',
        details: 'No valid session found. Please sign in again.'
      }, { status: 401 });
    }

    if (!session.id || typeof session.id !== 'string' || !/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/.test(session.id)) {
      console.error('[PERMISSIONS API DELETE] Invalid or missing session.id');
      return NextResponse.json({
        error: 'Invalid session',
        details: 'Session is missing a valid user id. Please sign in again.'
      }, { status: 401 });
    }

    // Check for SUPER_ADMIN in normalized roles
    if (!hasRole(session.roles, 'SUPER_ADMIN')) { // This will be a boolean, not an array
      console.error('Forbidden: User does not have required role for DELETE. Has roles:', session.roles);
      return NextResponse.json({
        error: 'Forbidden',
        message: 'You must be a SUPER_ADMIN to perform this action',
        requiredRole: 'SUPER_ADMIN',
        userRoles: session.roles
      }, { status: 403 });
    }

    const supabase = await getSupabaseClient(request);
    if (!supabase) {
      console.error('Failed to initialize Supabase client for DELETE request');
      return NextResponse.json({
        error: 'Internal Server Error',
        details: 'Failed to initialize database client'
      }, { status: 500 });
    }

    const targetUserId = params.userId;
    if (!targetUserId || !/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/.test(targetUserId)) {
      console.error('Invalid targetUserId for DELETE:', targetUserId);
      return NextResponse.json({ error: 'Invalid target user ID' }, { status: 400 });
    }

    // Prevent a SUPER_ADMIN from deleting their own permission override if it's the last one for an admin account.
    // Or if this would leave no SUPER_ADMINS.
    // This logic might need to be more robust based on business rules.
    const { data: userProfile, error: profileFetchError } = await supabase
      .from('profiles')
      .select('id, roles, role, email')
      .eq('id', targetUserId)
      .single();

    if (profileFetchError || !userProfile) {
      console.warn('Profile not found for target user, perhaps no override exists.', targetUserId);
      return NextResponse.json({ message: 'No permission override found for this user, nothing to delete.' }, { status: 200 });
    }

    // If the target user is a SUPER_ADMIN and it's the last one, prevent deletion
    // This assumes SUPER_ADMIN role is critical for system operation.
    // Robust check for the last SUPER_ADMIN
    if (hasRole(userProfile.roles, 'SUPER_ADMIN')) {
      const { count: superAdminCount, error: countError } = await supabase
        .from('profiles')
        .select('id', { count: 'exact' })
        .filter('roles', 'cs', ['SUPER_ADMIN']); // Use 'cs' for contains (array contains element)

      if (countError) {
        console.error('Error counting SUPER_ADMINs:', countError);
        return NextResponse.json({ error: 'Failed to check SUPER_ADMIN count.' }, { status: 500 });
      }

      if (superAdminCount === 1 && userProfile.id === session.id) {
        return NextResponse.json({
          error: 'Forbidden',
          message: "Cannot delete the last SUPER_ADMIN's permission override."
        }, { status: 403 });
      }
    }

    // Delete the permission override
    const { error: deleteError } = await supabase
      .from('permission_overrides')
      .delete()
      .eq('user_id', targetUserId);

    if (deleteError) {
      console.error('Error deleting permission override:', deleteError);
      return NextResponse.json({ error: 'Failed to delete permissions', details: deleteError.message }, { status: 500 });
    }

    return NextResponse.json({ message: 'Permission override deleted successfully' });
  } catch (error: any) {
    console.error('[PERMISSIONS API DELETE] Catch-all error:', error);
    return NextResponse.json({ error: 'An unexpected error occurred.', details: error.message }, { status: 500 });
  }
}
