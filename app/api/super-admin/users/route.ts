import { getSession, hasRole } from '@/app/lib/auth';
import { toUserRoles } from '@/src/types/roles';
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

/**
 * GET /api/super-admin/users
 * Get all users for super admin management
 */
export async function GET(request: NextRequest) {
  try {
    // Require session
    const session = await getSession(request);
    console.log('[DEBUG] Session in users API:', session);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Robustly extract roles as an array
    let roles = [];
    if (Array.isArray(session.roles)) roles = session.roles;
    else if (session.roles) roles = [session.roles];
    else if (Array.isArray(session.role)) roles = session.role;
    else if (session.role) roles = [session.role];
    else if (session.user_metadata && Array.isArray(session.user_metadata.roles)) roles = session.user_metadata.roles;
    else if (session.user_metadata && session.user_metadata.role) roles = [session.user_metadata.role];
    else if (session.app_metadata && Array.isArray(session.app_metadata.roles)) roles = session.app_metadata.roles;
    else if (session.app_metadata && session.app_metadata.role) roles = [session.app_metadata.role];
    console.log('[DEBUG] Extracted roles:', roles);
    if (!roles.includes('SUPER_ADMIN')) {
      return NextResponse.json({ error: 'Forbidden: SUPER_ADMIN role required', requiredRole: 'SUPER_ADMIN', userRoles: roles }, { status: 403 });
    }
    // Use authenticated Supabase client
    const supabase = await getSupabaseClient(request);
    if (!supabase) {
      return NextResponse.json({ error: 'Failed to initialize Supabase client' }, { status: 500 });
    }

    console.log('Super-admin users API - Starting request');

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const search = searchParams.get('search');
    const role = searchParams.get('role');

    console.log('Super-admin users API - Params:', { limit, offset, search, role });

    // Build the query - simplified to avoid complex joins that might cause issues
    let query = supabase
      .from('profiles')
      .select(`
        id,
        email,
        full_name,
        role,
        roles,
        company_name,
        phone_number,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply search filter
    if (search) {
      query = query.or(`email.ilike.%${search}%,full_name.ilike.%${search}%,company_name.ilike.%${search}%`);
    }

    // Apply role filter
    if (role && role !== 'all') {
      query = query.eq('role', role);
    }

    const { data: users, error } = await query;

    if (error) {
      console.error('Error fetching users:', error);
      return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 });
    }

    console.log(`Super-admin users API - Found ${users?.length || 0} users`);
    console.log('Super-admin users API - Sample user IDs:', users?.slice(0, 3).map((u: any) => u.id));

    // Transform the data - simplified without organization joins for now
    const transformedUsers = users?.map((user: any) => ({
      id: user.id,
      email: user.email,
      full_name: user.full_name,
      role: user.role,
      roles: user.roles || [],
      company_name: user.company_name,
      phone_number: user.phone_number,
      created_at: user.created_at,
      updated_at: user.updated_at,
      organizations: [] // Will fetch separately if needed
    })) || [];

    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error('Error getting user count:', countError);
    }

    return NextResponse.json({
      users: transformedUsers,
      total: count || 0,
      success: true
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('Error in super-admin users API:', error);
    return NextResponse.json(
      { error: `Failed to fetch users: ${errorMessage}` },
      { status: 500 }
    );
  }
}

/**
 * POST /api/super-admin/users
 * Create a new user (for future implementation)
 */
export async function POST(request: NextRequest) {
  try {
    // Require session
    const session = await getSession(request);
    console.log('[DEBUG] Session in users API (POST):', session);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    if (!hasRole(session.roles, 'SUPER_ADMIN')) {
      return NextResponse.json({ error: 'Forbidden: SUPER_ADMIN role required', requiredRole: 'SUPER_ADMIN', userRoles: session.roles }, { status: 403 });
    }
    // Use authenticated Supabase client
    const supabase = await getSupabaseClient(request);
    if (!supabase) {
      return NextResponse.json({ error: 'Failed to initialize Supabase client' }, { status: 500 });
    }

    console.log('Super-admin users API - POST request');

    const body = await request.json();
    const { email, full_name, role, company_name } = body;

    // For now, just return a placeholder response
    // In the future, this would create a new user via Supabase Auth
    return NextResponse.json({
      message: 'User creation not yet implemented',
      success: false
    }, { status: 501 });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('Error in super-admin users POST API:', error);
    return NextResponse.json(
      { error: `Failed to create user: ${errorMessage}` },
      { status: 500 }
    );
  }
}

// Create a function to get an authenticated Supabase client
const getSupabaseClient = async (request: NextRequest) => {
  const session = await getSession(request);
  if (!session) return null;
  const authHeader = request.headers.get('authorization');
  let token = authHeader?.split(' ')[1];
  if (!token && 'access_token' in session && typeof (session as any).access_token === 'string') {
    token = (session as any).access_token;
  }
  if (!token) {
    console.error('No access token found in session or headers');
    return null;
  }
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
        storageKey: 'sb-auth-token',
        storage: {
          getItem: (key) => {
            const cookieStore = cookies();
            return cookieStore.get(key)?.value || null;
          },
          setItem: () => {},
          removeItem: () => {},
        },
      },
      global: {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    }
  );
};
