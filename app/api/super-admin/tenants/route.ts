import { cookies } from 'next/headers';
import { createClient } from '@supabase/supabase-js';
import { toUserRoles } from '@/src/types/roles';
import { NextRequest, NextResponse } from 'next/server';
import { getSession, hasRole } from '@/app/lib/auth';

/**
 * GET /api/super-admin/tenants
 * Get all available tenants for tenant switching
 */
export async function GET(request: NextRequest) {
  try {
    // Require session
    const session = await getSession(request);
    console.log('[DEBUG] Session in tenants API:', session);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    if (!hasRole(session.roles, 'SUPER_ADMIN')) {
      return NextResponse.json({ error: 'Forbidden: SUPER_ADMIN role required', requiredRole: 'SUPER_ADMIN', userRoles: session.roles }, { status: 403 });
    }
    // Use authenticated Supabase client
    const supabase = await getSupabaseClient(request);
    if (!supabase) {
      return NextResponse.json({ error: 'Failed to initialize Supabase client' }, { status: 500 });
    }

    console.log('Super-admin tenants API - Starting request');

    // Log the request for debugging
    const authHeader = request.headers.get('authorization');
    console.log('Super-admin tenants API - Auth header present:', !!authHeader);

    // Fetch all tenants
    const { data: tenants, error } = await supabase
      .from('tenants')
      .select(`
        id,
        name,
        slug,
        domain,
        tenant_type,
        status,
        branding,
        settings,
        created_at,
        updated_at
      `)
      .eq('status', 'active')
      .order('name');

    if (error) {
      console.error('Error fetching tenants:', error);
      return NextResponse.json({ error: 'Failed to fetch tenants' }, { status: 500 });
    }

    console.log(`Super-admin tenants API - Found ${tenants?.length || 0} tenants`);

    // For super admin, there's no "current tenant" by default
    // They can switch between any tenant
    return NextResponse.json({
      tenants: tenants || [],
      currentTenant: null,
      success: true
    });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('Error fetching tenants:', error);
    return NextResponse.json(
      { error: `Failed to fetch tenants: ${errorMessage}` },
      { status: 500 }
    );
  }
}

/**
 * POST /api/super-admin/tenants
 * Create a new tenant
 */
export async function POST(request: NextRequest) {
  try {
    // Require session
    const session = await getSession(request);
    console.log('[DEBUG] Session in tenants API (POST):', session);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    if (!hasRole(session.roles, 'SUPER_ADMIN')) {
      return NextResponse.json({ error: 'Forbidden: SUPER_ADMIN role required', requiredRole: 'SUPER_ADMIN', userRoles: session.roles }, { status: 403 });
    }
    // Use authenticated Supabase client
    const supabase = await getSupabaseClient(request);
    if (!supabase) {
      return NextResponse.json({ error: 'Failed to initialize Supabase client' }, { status: 500 });
    }

    console.log('Super-admin tenants API - Creating new tenant');

    const body = await request.json();
    const { name, slug, domain, tenant_type, branding, settings } = body;

    // Validate required fields
    if (!name || !slug || !tenant_type) {
      return NextResponse.json(
        { error: 'Name, slug, and tenant_type are required' },
        { status: 400 }
      );
    }

    // Create the tenant
    const { data: tenant, error } = await supabase
      .from('tenants')
      .insert({
        name,
        slug,
        domain,
        tenant_type,
        branding: branding || {},
        settings: settings || {},
        status: 'active'
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating tenant:', error);
      return NextResponse.json({ error: 'Failed to create tenant' }, { status: 500 });
    }

    console.log('Super-admin tenants API - Tenant created successfully:', tenant.id);

    return NextResponse.json({
      tenant,
      success: true
    });

  } catch (error) {
    console.error('Error in POST /api/super-admin/tenants:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}

// Create a function to get an authenticated Supabase client
const getSupabaseClient = async (request: NextRequest) => {
  const session = await getSession(request);
  if (!session) return null;
  const authHeader = request.headers.get('authorization');
  let token = authHeader?.split(' ')[1];
  if (!token && 'access_token' in session && typeof (session as any).access_token === 'string') {
    token = (session as any).access_token;
  }
  if (!token) {
    console.error('No access token found in session or headers');
    return null;
  }
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
        storageKey: 'sb-auth-token',
        storage: {
          getItem: (key) => {
            const cookieStore = cookies();
            return cookieStore.get(key)?.value || null;
          },
          setItem: () => {},
          removeItem: () => {},
        },
      },
      global: {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    }
  );
};
