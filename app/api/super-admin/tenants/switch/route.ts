import { getSession, hasRole } from '../../../../lib/auth';
import { toUserRoles, UserRole } from '@/src/types/roles';
import { cookies } from 'next/headers';
import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  // Require session and SUPER_ADMIN
  const session = await getSession(request);
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const sessionRoles = Array.isArray(session.role) ? session.role : [session.role];
  const sessionUserRoles = toUserRoles(sessionRoles);
  if (!hasRole(sessionUserRoles, 'SUPER_ADMIN' as UserRole)) {
    return NextResponse.json({ error: 'Forbidden: SUPER_ADMIN role required', requiredRole: 'SUPER_ADMIN', userRoles: sessionUserRoles }, { status: 403 });
  }
  // Use authenticated Supabase client
  const supabase = await getSupabaseClient(request);
  if (!supabase) {
    return NextResponse.json({ error: 'Failed to initialize Supabase client' }, { status: 500 });
  }
  try {
    const body = await request.json();
    const { tenant_id } = body;
    if (!tenant_id || typeof tenant_id !== 'string' || !isValidUUID(tenant_id)) {
      return NextResponse.json({ error: 'Invalid tenant_id format. Must be a UUID string.' }, { status: 400 });
    }
    // Call the switch_tenant RPC or update logic as needed
    const { data, error: rpcError } = await supabase.rpc('switch_tenant', {
      tenant_id_to_switch: tenant_id,
    });
    if (rpcError) {
      console.error('Error calling switch_tenant RPC:', rpcError);
      if (rpcError.message.includes('does not exist') || rpcError.message.includes('not authorized')) {
        return NextResponse.json({ error: rpcError.message }, { status: 400 });
      }
      return NextResponse.json({ error: 'Failed to switch tenant.', details: rpcError.message }, { status: 500 });
    }
    return NextResponse.json({ message: data || 'Tenant switch operation successful.' });
  } catch (e: any) {
    console.error('Error in switch-tenant API:', e);
    let errorMessage = 'An unexpected error occurred.';
    if (e.message) {
      errorMessage = e.message;
    }
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

function isValidUUID(uuid: string) {
  return /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(uuid);
}

// Create a function to get an authenticated Supabase client
const getSupabaseClient = async (request: NextRequest) => {
  const session = await getSession(request);
  if (!session) return null;
  const authHeader = request.headers.get('authorization');
  let token = authHeader?.split(' ')[1];
  if (!token && 'access_token' in session && typeof (session as any).access_token === 'string') {
    token = (session as any).access_token;
  }
  if (!token) {
    console.error('No access token found in session or headers');
    return null;
  }
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
        storageKey: 'sb-auth-token',
        storage: {
          getItem: (key) => {
            const cookieStore = cookies();
            return cookieStore.get(key)?.value || null;
          },
          setItem: () => {},
          removeItem: () => {},
        },
      },
      global: {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    }
  );
};
