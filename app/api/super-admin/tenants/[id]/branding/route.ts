import { NextResponse } from 'next/server';
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from 'next/headers';
import { getSession, hasRole } from "../../../../../lib/auth"; // CORRECTED RELATIVE PATH
import { Database } from "@supabase/supabase-js";

export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const { id: tenantId } = params; // Extract tenantId from params
    console.log('PATCH /api/super-admin/tenants/[id]/branding - tenantId:', tenantId);
    const session = await getSession(request as any); // Cast to any to satisfy NextRequest type for now

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    console.log('PATCH /api/super-admin/tenants/[id]/branding - session:', session);

    // Ensure the user has SUPER_ADMIN role
    if (!hasRole(session.roles, 'SUPER_ADMIN')) { // Use session.roles directly
      return NextResponse.json({ error: 'Forbidden - Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const {
      logo_url,
      favicon_url,
      primary_color,
      secondary_color,
      background_color,
      custom_css,
      email_templates,
    } = body; // Destructure branding fields from the request body

    const supabase = createRouteHandlerClient<Database>({ cookies });

    // Fetch the existing tenant to get its current branding data if any
    const { data: tenant, error: fetchError } = await supabase
      .from('tenants')
      .select('id, branding')
      .eq('id', tenantId)
      .single();

    if (fetchError) {
      console.error("Error fetching tenant (branding PATCH):", fetchError);
      return NextResponse.json({ error: 'Failed to fetch tenant' }, { status: 500 });
    }
    console.log('PATCH /api/super-admin/tenants/[id]/branding - fetched tenant:', tenant);

    const currentBranding = tenant?.branding || {};
    console.log('PATCH /api/super-admin/tenants/[id]/branding - currentBranding:', currentBranding);

    // Merge new branding data with existing data, overwriting common fields
    const updatedBranding = {
      ...currentBranding,
      logo_url,
      favicon_url,
      primary_color,
      secondary_color,
      background_color,
      custom_css,
      email_templates,
    };
    console.log('PATCH /api/super-admin/tenants/[id]/branding - updatedBranding:', updatedBranding);

    const { data, error: updateError } = await supabase
      .from('tenants')
      .update({ branding: updatedBranding })
      .eq('id', tenantId)
      .select();

    if (updateError) {
      console.error("Error updating tenant branding in DB:", updateError);
      return NextResponse.json({ error: updateError.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data: data[0] });

  } catch (error) {
    console.error("Unhandled error in branding PATCH handler:", error);
    return NextResponse.json(
      { error: (error as Error).message },
      { status: 500 }
    );
  }
}