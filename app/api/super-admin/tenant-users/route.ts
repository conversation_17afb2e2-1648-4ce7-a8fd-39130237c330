import { type NextRequest, NextResponse } from 'next/server';
import {
  createAuthenticatedSupabaseClient,
  requireRole,
} from "@/lib/auth/server";

// Schema for tenant_users table (for clarity)
// id (uuid, pk) - auto-generated
// tenant_id (uuid, fk to tenants.id)
// user_id (uuid, fk to auth.users.id or public.profiles.id - ensure consistency)
// role (text)
// created_at (timestamptz) - auto-generated
// updated_at (timestamptz) - auto-generated

const UUID_REGEX = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;

/**
 * @swagger
 * /api/super-admin/tenant-users:
 *   post:
 *     summary: Associate a user with a tenant and assign a role.
 *     tags:
 *       - Super Admin - Tenant Users
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - tenant_id
 *               - role
 *             properties:
 *               user_id:
 *                 type: string
 *                 format: uuid
 *                 description: The ID of the user to associate.
 *               tenant_id:
 *                 type: string
 *                 format: uuid
 *                 description: The ID of the tenant.
 *               role:
 *                 type: string
 *                 description: The role to assign to the user within the tenant.
 *     responses:
 *       201:
 *         description: User successfully associated with tenant.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/TenantUser'
 *       400:
 *         description: Bad request (e.g., missing fields, invalid UUIDs).
 *       409:
 *         description: Conflict (e.g., user already associated with tenant).
 *       500:
 *         description: Internal server error.
 */
export async function POST(request: NextRequest) {
  try {
    await requireRole(["SUPER_ADMIN"]);
    const supabase = await createAuthenticatedSupabaseClient();

    const body = await request.json();
    const { user_id, tenant_id, role } = body;

    if (!user_id || !tenant_id || !role) {
      return NextResponse.json(
        { error: 'user_id, tenant_id, and role are required fields.' },
        { status: 400 }
      );
    }
    if (!UUID_REGEX.test(user_id)) {
        return NextResponse.json({ error: 'Invalid user_id format.' }, { status: 400 });
    }
    if (!UUID_REGEX.test(tenant_id)) {
        return NextResponse.json({ error: 'Invalid tenant_id format.' }, { status: 400 });
    }
    if (typeof role !== 'string' || role.trim() === '') {
        return NextResponse.json({ error: 'Role must be a non-empty string.' }, { status: 400 });
    }

    const { data, error } = await supabase
      .from('tenant_users')
      .schema('saas_tenants')
      .insert([{ user_id, tenant_id, role }])
      .select()
      .single();

    if (error) {
      console.error('Supabase error associating user with tenant (POST):', error);
      if (error.code === '23505') { // unique_violation
        return NextResponse.json(
          { error: 'This user is already associated with this tenant.' },
          { status: 409 }
        );
      }
      if (error.code === '23503') { // foreign_key_violation
        return NextResponse.json(
          { error: 'Invalid user_id or tenant_id. Ensure they exist.' },
          { status: 400 }
        );
      }
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ message: 'User successfully associated with tenant.', data }, { status: 201 });
  } catch (e: any) {
    console.error('Unexpected error in POST /api/super-admin/tenant-users:', e);
    if (e.name === 'SyntaxError') { // JSON parsing error
        return NextResponse.json({ error: 'Invalid JSON payload.' }, { status: 400 });
    }
    return NextResponse.json({ error: e.message || 'An unexpected error occurred.' }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/super-admin/tenant-users:
 *   get:
 *     summary: List tenant-user associations.
 *     tags:
 *       - Super Admin - Tenant Users
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: tenant_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by tenant ID to list its users.
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by user ID to list their tenants.
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Number of records to return.
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Number of records to skip for pagination.
 *     responses:
 *       200:
 *         description: A list of tenant-user associations.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/TenantUser'
 *                 count:
 *                   type: integer
 *       400:
 *         description: Bad request (e.g., must provide tenant_id or user_id).
 *       500:
 *         description: Internal server error.
 */
export async function GET(request: NextRequest) {
  try {
    await requireRole(["SUPER_ADMIN"]);
    const supabase = await createAuthenticatedSupabaseClient();
    const { searchParams } = new URL(request.url);

    const tenantId = searchParams.get('tenant_id');
    const userId = searchParams.get('user_id');
    const limit = parseInt(searchParams.get('limit') || '20', 10);
    const offset = parseInt(searchParams.get('offset') || '0', 10);

    if (!tenantId && !userId) {
      return NextResponse.json(
        { error: 'Either tenant_id or user_id query parameter must be provided.' },
        { status: 400 }
      );
    }

    let query = supabase
      .from('tenant_users')
      .schema('saas_tenants')
      .select('*, tenants(name, slug), profiles(email, first_name, last_name)', { count: 'exact' });

    if (tenantId) {
      if (!UUID_REGEX.test(tenantId)) {
        return NextResponse.json({ error: 'Invalid tenant_id format.' }, { status: 400 });
      }
      query = query.eq('tenant_id', tenantId);
    }
    if (userId) {
      if (!UUID_REGEX.test(userId)) {
        return NextResponse.json({ error: 'Invalid user_id format.' }, { status: 400 });
      }
      query = query.eq('user_id', userId);
    }

    query = query.range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) {
      console.error('Supabase error fetching tenant-user associations (GET):', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ data, count });
  } catch (e: any) {
    console.error('Unexpected error in GET /api/super-admin/tenant-users:', e);
    return NextResponse.json({ error: e.message || 'An unexpected error occurred.' }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/super-admin/tenant-users:
 *   put:
 *     summary: Update a user's role within a tenant.
 *     tags:
 *       - Super Admin - Tenant Users
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - tenant_id
 *               - role
 *             properties:
 *               user_id:
 *                 type: string
 *                 format: uuid
 *               tenant_id:
 *                 type: string
 *                 format: uuid
 *               role:
 *                 type: string
 *     responses:
 *       200:
 *         description: User role updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/TenantUser'
 *       400:
 *         description: Bad request.
 *       404:
 *         description: Association not found.
 *       500:
 *         description: Internal server error.
 */
export async function PUT(request: NextRequest) {
  try {
    await requireRole(["SUPER_ADMIN"]);
    const supabase = await createAuthenticatedSupabaseClient();
    const body = await request.json();
    const { user_id, tenant_id, role } = body;

    if (!user_id || !tenant_id || !role) {
      return NextResponse.json(
        { error: 'user_id, tenant_id, and role are required.' },
        { status: 400 }
      );
    }
    if (!UUID_REGEX.test(user_id)) {
        return NextResponse.json({ error: 'Invalid user_id format.' }, { status: 400 });
    }
    if (!UUID_REGEX.test(tenant_id)) {
        return NextResponse.json({ error: 'Invalid tenant_id format.' }, { status: 400 });
    }
    if (typeof role !== 'string' || role.trim() === '') {
        return NextResponse.json({ error: 'Role must be a non-empty string.' }, { status: 400 });
    }

    const { data, error } = await supabase
      .from('tenant_users')
      .schema('saas_tenants')
      .update({ role, updated_at: new Date().toISOString() })
      .eq('user_id', user_id)
      .eq('tenant_id', tenant_id)
      .select()
      .single();

    if (error) {
      console.error('Supabase error updating tenant-user role (PUT):', error);
      if (error.code === 'PGRST116') { // row not found
          return NextResponse.json({ error: 'Tenant-user association not found.' }, { status: 404 });
      }
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    if (!data) {
        return NextResponse.json({ error: 'Tenant-user association not found.' }, { status: 404 });
    }

    return NextResponse.json(data);
  } catch (e: any) {
    console.error('Unexpected error in PUT /api/super-admin/tenant-users:', e);
    if (e.name === 'SyntaxError') { // JSON parsing error
        return NextResponse.json({ error: 'Invalid JSON payload.' }, { status: 400 });
    }
    return NextResponse.json({ error: e.message || 'An unexpected error occurred.' }, { status: 500 });
  }
}

/**
 * @swagger
 * /api/super-admin/tenant-users:
 *   delete:
 *     summary: Remove a user's association with a tenant.
 *     tags:
 *       - Super Admin - Tenant Users
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - tenant_id
 *             properties:
 *               user_id:
 *                 type: string
 *                 format: uuid
 *               tenant_id:
 *                 type: string
 *                 format: uuid
 *     responses:
 *       204:
 *         description: Association removed successfully.
 *       400:
 *         description: Bad request.
 *       404:
 *         description: Association not found.
 *       500:
 *         description: Internal server error.
 */
export async function DELETE(request: NextRequest) {
  try {
    await requireRole(["SUPER_ADMIN"]);
    const supabase = await createAuthenticatedSupabaseClient();
    const body = await request.json();
    const { user_id, tenant_id } = body;

    if (!user_id || !tenant_id) {
      return NextResponse.json(
        { error: 'user_id and tenant_id are required.' },
        { status: 400 }
      );
    }
    if (!UUID_REGEX.test(user_id)) {
        return NextResponse.json({ error: 'Invalid user_id format.' }, { status: 400 });
    }
    if (!UUID_REGEX.test(tenant_id)) {
        return NextResponse.json({ error: 'Invalid tenant_id format.' }, { status: 400 });
    }

    const { count: existingCount, error: checkError } = await supabase
      .from('tenant_users')
      .schema('saas_tenants')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user_id)
      .eq('tenant_id', tenant_id);

    if (checkError) {
        console.error('Supabase error checking tenant-user association (DELETE):', checkError);
        return NextResponse.json({ error: checkError.message }, { status: 500 });
    }

    if (existingCount === 0) {
        return NextResponse.json({ error: 'Tenant-user association not found.' }, { status: 404 });
    }

    const { error: deleteError } = await supabase
      .from('tenant_users')
      .schema('saas_tenants')
      .delete()
      .eq('user_id', user_id)
      .eq('tenant_id', tenant_id);

    if (deleteError) {
      console.error('Supabase error deleting tenant-user association (DELETE):', deleteError);
      return NextResponse.json({ error: deleteError.message }, { status: 500 });
    }

    return new NextResponse(null, { status: 204 }); // No content
  } catch (e: any) {
    console.error('Unexpected error in DELETE /api/super-admin/tenant-users:', e);
    if (e.name === 'SyntaxError') { // JSON parsing error
        return NextResponse.json({ error: 'Invalid JSON payload.' }, { status: 400 });
    }
    return NextResponse.json({ error: e.message || 'An unexpected error occurred.' }, { status: 500 });
  }
}

/**
 * @swagger
 * components:
 *   schemas:
 *     TenantUser:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         tenant_id:
 *           type: string
 *           format: uuid
 *         user_id:
 *           type: string
 *           format: uuid
 *         role:
 *           type: string
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *         tenants: # Optional, included in GET if joined
 *           type: object
 *           properties:
 *             name:
 *               type: string
 *             slug:
 *               type: string
 *         profiles: # Optional, included in GET if joined
 *           type: object
 *           properties:
 *             email:
 *               type: string
 *             first_name:
 *               type: string
 *             last_name:
 *               type: string
 */
