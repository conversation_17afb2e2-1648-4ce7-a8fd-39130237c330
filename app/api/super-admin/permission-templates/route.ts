import { getSession, hasRole } from '@/app/lib/auth';
import { cookies } from 'next/headers';
import { createClient } from '@supabase/supabase-js';
import { toUserRoles } from '@/src/types/roles';
import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/super-admin/permission-templates
 * Get all permission templates for quick assignment
 */
export async function GET(request: NextRequest) {
  try {
    // Require session
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Normalize roles (removed - now handled by getSession returning session.roles)
    // const sessionRoles = Array.isArray(session.role) ? session.role : [session.role];
    // const sessionUserRoles = toUserRoles(sessionRoles);
    if (!hasRole(session.roles, 'SUPER_ADMIN')) {
      return NextResponse.json({ error: 'Forbidden: SUPER_ADMIN role required', requiredRole: 'SUPER_ADMIN', userRoles: session.roles }, { status: 403 });
    }
    // Use authenticated Supabase client
    const supabase = await getSupabaseClient(request);
    if (!supabase) {
      return NextResponse.json({ error: 'Failed to initialize Supabase client' }, { status: 500 });
    }

    console.log('Super-admin permission templates API - Starting request');

    // Get all permission templates
    const { data: templates, error } = await supabase
      .from('permission_templates')
      .select('*')
      .order('template_type', { ascending: true })
      .order('name', { ascending: true });

    if (error) {
      console.error('Error fetching permission templates:', error);
      return NextResponse.json({ error: 'Failed to fetch permission templates' }, { status: 500 });
    }

    console.log(`Super-admin permission templates API - Found ${templates?.length || 0} templates`);

    return NextResponse.json({
      templates: templates || [],
      success: true
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('Error in super-admin permission templates API:', error);
    return NextResponse.json(
      { error: `Failed to fetch permission templates: ${errorMessage}` },
      { status: 500 }
    );
  }
}

/**
 * POST /api/super-admin/permission-templates
 * Create a new permission template
 */
export async function POST(request: NextRequest) {
  try {
    // Require session
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Normalize roles (removed - now handled by getSession returning session.roles)
    // const sessionRoles = Array.isArray(session.role) ? session.role : [session.role];
    // const sessionUserRoles = toUserRoles(sessionRoles);
    if (!hasRole(session.roles, 'SUPER_ADMIN')) {
      return NextResponse.json({ error: 'Forbidden: SUPER_ADMIN role required', requiredRole: 'SUPER_ADMIN', userRoles: session.roles }, { status: 403 });
    }
    // Use authenticated Supabase client
    const supabase = await getSupabaseClient(request);
    if (!supabase) {
      return NextResponse.json({ error: 'Failed to initialize Supabase client' }, { status: 500 });
    }

    console.log('Super-admin permission templates API - POST request');

    const body = await request.json();
    const {
      name,
      description,
      template_type,
      permissions,
      ui_customizations
    } = body;

    // Create new template
    const { data: template, error: templateError } = await supabase
      .from('permission_templates')
      .insert({
        name,
        description,
        template_type,
        permissions: permissions || {},
        ui_customizations: ui_customizations || {}
      })
      .select()
      .single();

    if (templateError) {
      console.error('Error creating permission template:', templateError);
      return NextResponse.json({ error: 'Failed to create permission template' }, { status: 500 });
    }

    console.log('Super-admin permission templates API - Successfully created template');

    return NextResponse.json({
      template,
      success: true
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('Error in super-admin permission templates API:', error);
    return NextResponse.json(
      { error: `Failed to create permission template: ${errorMessage}` },
      { status: 500 }
    );
  }
}

// Create a function to get an authenticated Supabase client
const getSupabaseClient = async (request: NextRequest) => {
  const session = await getSession(request);
  if (!session || !session.access_token) return null; // Ensure access_token is present in session
  
  // Directly use session.access_token, no need to re-extract from headers or cast
  const token = session.access_token; 
  if (!token) {
    console.error('No access token found in session after getSession');
    return null;
  }
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
        storageKey: 'sb-auth-token',
        storage: {
          getItem: (key) => {
            const cookieStore = cookies();
            return cookieStore.get(key)?.value || null;
          },
          setItem: () => {},
          removeItem: () => {},
        },
      },
      global: {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    }
  );
};
