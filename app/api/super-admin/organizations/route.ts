import { getSession } from '@/app/lib/auth';
import { cookies } from 'next/headers';
import { createClient } from '@supabase/supabase-js';
import { toUserRoles } from '@/src/types/roles';
import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/super-admin/organizations
 * Get all organizations for super admin management
 */
export async function GET(request: NextRequest) {
  try {
    // Require session
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Normalize roles
    const sessionRoles = Array.isArray(session.role) ? session.role : [session.role];
    const sessionUserRoles = toUserRoles(sessionRoles);
    if (!sessionUserRoles.includes('SUPER_ADMIN')) {
      return NextResponse.json({ error: 'Forbidden: SUPER_ADMIN role required', requiredRole: 'SUPER_ADMIN', userRoles: sessionUserRoles }, { status: 403 });
    }
    // Use authenticated Supabase client
    const supabase = await getSupabaseClient(request);
    if (!supabase) {
      return NextResponse.json({ error: 'Failed to initialize Supabase client' }, { status: 500 });
    }

    console.log('Super-admin organizations API - Starting request');

    // Fetch all organizations with tenant information
    const { data: organizations, error } = await supabase
      .from('organizations')
      .select(`
        id,
        name,
        slug,
        description,
        industry,
        logo_url,
        website,
        address,
        city,
        state,
        zip,
        country,
        phone,
        email,
        tenant_id,
        status,
        settings,
        created_at,
        updated_at
      `)
      .order('name');

    if (error) {
      console.error('Error fetching organizations:', error);
      return NextResponse.json({ error: 'Failed to fetch organizations' }, { status: 500 });
    }

    console.log(`Super-admin organizations API - Found ${organizations?.length || 0} organizations`);

    return NextResponse.json({
      organizations: organizations || [],
      success: true
    });

  } catch (error) {
    console.error('Error in GET /api/super-admin/organizations:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/super-admin/organizations
 * Create a new organization
 */
export async function POST(request: NextRequest) {
  try {
    // Require session
    const session = await getSession(request);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    // Normalize roles
    const sessionRoles = Array.isArray(session.role) ? session.role : [session.role];
    const sessionUserRoles = toUserRoles(sessionRoles);
    if (!sessionUserRoles.includes('SUPER_ADMIN')) {
      return NextResponse.json({ error: 'Forbidden: SUPER_ADMIN role required', requiredRole: 'SUPER_ADMIN', userRoles: sessionUserRoles }, { status: 403 });
    }
    // Use authenticated Supabase client
    const supabase = await getSupabaseClient(request);
    if (!supabase) {
      return NextResponse.json({ error: 'Failed to initialize Supabase client' }, { status: 500 });
    }

    console.log('Super-admin organizations API - Creating new organization');

    const body = await request.json();
    const { 
      name, 
      slug, 
      description, 
      industry, 
      tenant_id, 
      logo_url, 
      website, 
      address, 
      city, 
      state, 
      zip, 
      country, 
      phone, 
      email,
      settings 
    } = body;

    // Validate required fields
    if (!name || !slug || !tenant_id) {
      return NextResponse.json(
        { error: 'Name, slug, and tenant_id are required' },
        { status: 400 }
      );
    }

    // Create the organization
    const { data: organization, error } = await supabase
      .from('organizations')
      .insert({
        name,
        slug,
        description,
        industry,
        tenant_id,
        logo_url,
        website,
        address,
        city,
        state,
        zip,
        country,
        phone,
        email,
        settings: settings || {},
        status: 'active'
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating organization:', error);
      return NextResponse.json({ error: 'Failed to create organization' }, { status: 500 });
    }

    console.log('Super-admin organizations API - Organization created successfully:', organization.id);

    return NextResponse.json({
      organization,
      success: true
    });

  } catch (error) {
    console.error('Error in POST /api/super-admin/organizations:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}

// Create a function to get an authenticated Supabase client
const getSupabaseClient = async (request: NextRequest) => {
  const session = await getSession(request);
  if (!session) return null;
  const authHeader = request.headers.get('authorization');
  let token = authHeader?.split(' ')[1];
  if (!token && 'access_token' in session && typeof (session as any).access_token === 'string') {
    token = (session as any).access_token;
  }
  if (!token) {
    console.error('No access token found in session or headers');
    return null;
  }
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
        storageKey: 'sb-auth-token',
        storage: {
          getItem: (key) => {
            const cookieStore = cookies();
            return cookieStore.get(key)?.value || null;
          },
          setItem: () => {},
          removeItem: () => {},
        },
      },
      global: {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    }
  );
};
