import { getSession, hasRole } from '@/app/lib/auth';
import { toUserRoles } from '@/src/types/roles';
import { cookies } from 'next/headers';
import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  // Require session and SUPER_ADMIN
  const session = await getSession(request);
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  if (!hasRole(session.roles, 'SUPER_ADMIN')) {
    return NextResponse.json({ error: 'Forbidden: SUPER_ADMIN role required', requiredRole: 'SUPER_ADMIN', userRoles: session.roles }, { status: 403 });
  }
  // Use authenticated Supabase client
  const supabase = await getSupabaseClient(request);
  if (!supabase) {
    return NextResponse.json({ error: 'Failed to initialize Supabase client' }, { status: 500 });
  }
  try {
    const { searchParams } = new URL(request.url)
    const orgId = searchParams.get('orgId')
    const from = searchParams.get('from')
    const to = searchParams.get('to')

    // Get affiliate companies with their performance data
    const { data: affiliates, error: affiliatesError } = await supabase
      .from('affiliate_companies')
      .select(`
        id,
        name,
        application_status,
        created_at,
        affiliate_performance_metrics(
          compliance_score,
          customer_rating,
          quote_response_time_minutes,
          driver_arrival_on_time_percentage,
          total_trips,
          completed_trips,
          cancelled_trips,
          current_tier,
          tier_score,
          updated_at
        ),
        quote_affiliate_offers(
          id,
          status,
          rate_amount,
          created_at,
          quotes(
            id,
            created_at,
            total_amount
          )
        )
      `)
      .eq('application_status', 'approved')
      .gte('created_at', from || '2024-01-01')
      .lte('created_at', to || new Date().toISOString())

    if (affiliatesError) {
      console.error('Error fetching affiliates:', affiliatesError)
      return NextResponse.json({ error: 'Failed to fetch affiliate data' }, { status: 500 })
    }

    // Process affiliate performance data
    const affiliatePerformance = affiliates?.map(affiliate => {
      const metrics = affiliate.affiliate_performance_metrics?.[0]
      const offers = affiliate.quote_affiliate_offers || []
      
      // Calculate response times
      const responseTimes = offers.map(offer => {
        if (!offer.quotes) return 0
        const quoteTime = new Date((offer.quotes as any).created_at).getTime()
        const responseTime = new Date(offer.created_at).getTime()
        return (responseTime - quoteTime) / (1000 * 60 * 60) // Convert to hours
      }).filter(time => time > 0)

      const averageResponseTime = responseTimes.length > 0 
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
        : 0

      // Calculate conversion rates
      const totalQuotes = offers.length
      const acceptedQuotes = offers.filter(offer => offer.status === 'accepted').length
      const currentConversionRate = totalQuotes > 0 ? (acceptedQuotes / totalQuotes) * 100 : 0

      // Calculate revenue
      const revenue = offers
        .filter(offer => offer.status === 'accepted')
        .reduce((sum, offer) => sum + (offer.rate_amount || 0), 0)

      // Determine trend (mock calculation for now)
      const previousResponseTime = averageResponseTime * (1 + (Math.random() - 0.5) * 0.3)
      const responseTimeTrend = averageResponseTime < previousResponseTime ? 'down' : 
                               averageResponseTime > previousResponseTime ? 'up' : 'stable'
      
      const previousConversionRate = currentConversionRate * (1 + (Math.random() - 0.5) * 0.2)
      const conversionTrend = currentConversionRate > previousConversionRate ? 'up' :
                             currentConversionRate < previousConversionRate ? 'down' : 'stable'

      return {
        id: affiliate.id,
        name: affiliate.name,
        responseTime: {
          average: Math.round(averageResponseTime * 10) / 10,
          trend: responseTimeTrend,
          percentChange: Math.round(((averageResponseTime - previousResponseTime) / previousResponseTime) * 100) || 0
        },
        conversionRate: {
          current: Math.round(currentConversionRate * 10) / 10,
          previous: Math.round(previousConversionRate * 10) / 10,
          trend: conversionTrend
        },
        totalQuotes,
        acceptedQuotes,
        revenue,
        tier: metrics?.current_tier || 'Standard',
        slaCompliance: metrics?.compliance_score || 85,
        customerRating: metrics?.customer_rating || 4.5,
        lastActive: offers.length > 0 ? offers[0].created_at : affiliate.created_at
      }
    }) || []

    // Sort by performance (conversion rate * tier weight)
    const tierWeights: { [key: string]: number } = { Elite: 3, Premium: 2, Standard: 1 }
    affiliatePerformance.sort((a, b) => {
      const aScore = a.conversionRate.current * (tierWeights[a.tier] || 1)
      const bScore = b.conversionRate.current * (tierWeights[b.tier] || 1)
      return bScore - aScore
    })

    return NextResponse.json({
      affiliates: affiliatePerformance,
      summary: {
        totalAffiliates: affiliatePerformance.length,
        averageResponseTime: affiliatePerformance.length > 0 
          ? affiliatePerformance.reduce((sum, a) => sum + a.responseTime.average, 0) / affiliatePerformance.length 
          : 0,
        averageConversionRate: affiliatePerformance.length > 0 
          ? affiliatePerformance.reduce((sum, a) => sum + a.conversionRate.current, 0) / affiliatePerformance.length 
          : 0,
        totalRevenue: affiliatePerformance.reduce((sum, a) => sum + a.revenue, 0),
        tierDistribution: {
          Elite: affiliatePerformance.filter(a => a.tier === 'Elite').length,
          Premium: affiliatePerformance.filter(a => a.tier === 'Premium').length,
          Standard: affiliatePerformance.filter(a => a.tier === 'Standard').length
        }
      }
    })

  } catch (error) {
    console.error('Error in affiliate performance analytics API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Create a function to get an authenticated Supabase client
const getSupabaseClient = async (request: NextRequest) => {
  const session = await getSession(request);
  if (!session) return null;
  const authHeader = request.headers.get('authorization');
  let token = authHeader?.split(' ')[1];
  if (!token && 'access_token' in session && typeof (session as any).access_token === 'string') {
    token = (session as any).access_token;
  }
  if (!token) {
    console.error('No access token found in session or headers');
    return null;
  }
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
        storageKey: 'sb-auth-token',
        storage: {
          getItem: (key) => {
            const cookieStore = cookies();
            return cookieStore.get(key)?.value || null;
          },
          setItem: () => {},
          removeItem: () => {},
        },
      },
      global: {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    }
  );
};
