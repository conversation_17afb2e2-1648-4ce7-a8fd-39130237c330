import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST() {
  try {
    // Use service role key to create the function
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
    
    const supabase = createClient(supabaseUrl, serviceRoleKey);
    
    // Create the missing set_tenant_config function
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE OR REPLACE FUNCTION public.set_tenant_config(
            setting_name TEXT,
            setting_value TEXT,
            is_local BOOLEAN DEFAULT false
        )
        RETURNS TEXT
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
            -- Set the configuration parameter for the current session
            -- This is used by RLS policies to filter data by tenant
            PERFORM set_config(setting_name, setting_value, is_local);
            RETURN setting_value;
        END;
        $$;

        -- Grant execute permission to authenticated users
        GRANT EXECUTE ON FUNCTION public.set_tenant_config(TEXT, TEXT, BOOLEAN) TO authenticated;
      `
    });
    
    if (error) {
      console.error('Error creating function:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    
    return NextResponse.json({ success: true, message: 'set_tenant_config function created successfully' });
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Failed to create function' }, { status: 500 });
  }
}
