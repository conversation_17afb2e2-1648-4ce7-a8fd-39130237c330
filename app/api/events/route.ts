import { createClient } from '@/lib/supabase/server'
import { NextResponse } from 'next/server'
import { hasRole } from '@/app/lib/auth'

export async function GET() {
  try {
    const supabase = createClient()
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError) throw userError

    // Fetch events for the user's company
    const { data: events, error: eventsError } = await supabase
      .from('events')
      .select('*')
      .order('created_at', { ascending: false })

    if (eventsError) throw eventsError

    return NextResponse.json(events)
  } catch (error: any) {
    console.error('[Server] Error fetching events:', error.message)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}

export async function POST(request: Request) {
  try {
    const supabase = createClient()

    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      console.error('[Server] Auth error:', userError?.message || 'No user found')
      return NextResponse.json({ error: 'Auth session missing!' }, { status: 401 })
    }

    // Get user profile to check roles
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('roles')
      .eq('id', user?.id)
      .single()
    
    if (profileError) throw profileError

    // Check if user has EVENT_MANAGER role using the hasRole utility
    const isEventManager = hasRole(profile.roles, 'EVENT_MANAGER')
    
    // If not an event manager, convert the user
    if (!isEventManager) {
      // Call the database function to convert the user to an event manager
      await supabase.rpc('convert_to_event_manager', { user_id: user?.id })
      
      console.log(`[Server] User ${user?.id} converted to EVENT_MANAGER role`)
    }

    // Get current tenant_id
    const { data: tenant_id, error: tenantIdError } = await supabase.rpc('get_current_tenant_id');

    if (tenantIdError || !tenant_id) {
      console.error('[Server] Error fetching tenant_id:', tenantIdError?.message);
      return NextResponse.json({ error: 'Could not determine tenant context.' }, { status: 500 });
    }

    // Parse the request body
    const body = await request.json()
    const {
      name,
      description,
      start_date,
      end_date,
      location,
      total_passengers,
      status = 'draft',
      // Add any other fields needed
    } = body

    // Validate required fields
    if (!name || !start_date || !end_date || !location) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Create the event
    const { data: event, error: eventError } = await supabase
      .from('events')
      .insert({
        name,
        description,
        customer_id: user?.id, // This might be replaced by created_by_user_id later
        tenant_id: tenant_id, // Associate event with the current tenant
        start_date,
        end_date,
        location,
        total_passengers,
        status,
        // Add any other fields needed
      })
      .select()
      .single()

    if (eventError) throw eventError

    return NextResponse.json(event)
  } catch (error: any) {
    console.error('[Server] Error creating event:', error.message)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}
