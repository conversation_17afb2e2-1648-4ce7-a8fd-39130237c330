// SERVER ONLY: This API route uses pg (via db.ts) and must not be imported by any React component or shared code.
import { NextResponse } from "next/server"
import { query } from "@/lib/db"

export async function GET(
  request: Request,
  { params }: { params: { eventId: string } }
) {
  try {
    const { data: groups, error } = await query(
      `SELECT pg.*, 
        json_agg(json_build_object(
          'id', p.id,
          'first_name', p.first_name,
          'last_name', p.last_name
        )) as passengers
      FROM passenger_groups pg
      LEFT JOIN passenger_group_members pgm ON pg.id = pgm.group_id
      LEFT JOIN passengers p ON pgm.passenger_id = p.id
      WHERE pg.event_id = $1
      GROUP BY pg.id`,
      [params.eventId]
    )

    if (error) {
      return NextResponse.json({ error }, { status: 500 })
    }

    return NextResponse.json({ groups })
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}

export async function POST(
  request: Request,
  { params }: { params: { eventId: string } }
) {
  try {
    const body = await request.json()
    const { name, type, dietary_requirements, special_requirements, passengers } = body

    // Insert group
    const { data: group, error: groupError } = await query(
      'INSERT INTO passenger_groups (event_id, name, type, dietary_requirements, special_requirements) VALUES ($1, $2, $3, $4, $5) RETURNING *',
      [params.eventId, name, type, dietary_requirements, special_requirements]
    )

    if (groupError) {
      return NextResponse.json({ error: groupError }, { status: 500 })
    }

    // Create passenger group memberships
    if (passengers && passengers.length > 0) {
      const membershipValues = passengers
        .map((_: any, i: number) => `($1, $${i + 2})`)
        .join(', ')
      
      const membershipParams = [group[0].id, ...passengers]
      
      const { error: membershipError } = await query(
        `INSERT INTO passenger_group_members (group_id, passenger_id) VALUES ${membershipValues}`,
        membershipParams
      )

      if (membershipError) {
        return NextResponse.json({ error: membershipError }, { status: 500 })
      }
    }

    return NextResponse.json(group[0])
  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}