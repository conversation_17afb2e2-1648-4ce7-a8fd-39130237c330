import { NextRequest, NextResponse } from 'next/server'
import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from '@/lib/database.types'

const createSupabaseClient = () => {
  const cookieStore = cookies()
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options)
          } catch (error) {
            // Handle error
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, '', options)
          } catch (error) {
            // Handle error
          }
        },
      },
    }
  )
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseClient()
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Verify user has access to this company
    const { data: userCompany, error: accessError } = await supabase
      .from('affiliate_user_companies')
      .select('*')
      .eq('user_id', user.id)
      .eq('affiliate_id', companyId)
      .single()

    if (accessError || !userCompany) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      )
    }

    // Get compliance items
    const complianceItems = await getComplianceItems(supabase, companyId)

    return NextResponse.json({
      items: complianceItems
    })

  } catch (error) {
    console.error('Error in compliance items API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function getComplianceItems(supabase: any, companyId: string) {
  const items = []

  // Business documents
  const businessDocs = [
    {
      id: 'business-license',
      category: 'business',
      name: 'Business License',
      description: 'Valid business operating license',
      status: 'complete',
      actionUrl: '/affiliate/settings'
    },
    {
      id: 'general-liability',
      category: 'business',
      name: 'General Liability Insurance',
      description: 'Minimum $1M general liability coverage',
      status: 'complete',
      actionUrl: '/affiliate/settings'
    },
    {
      id: 'commercial-auto',
      category: 'business',
      name: 'Commercial Auto Insurance',
      description: 'Commercial vehicle insurance policy',
      status: 'expiring',
      expiryDate: '2024-04-15',
      actionUrl: '/affiliate/settings'
    },
    {
      id: 'workers-comp',
      category: 'business',
      name: 'Workers Compensation',
      description: 'Workers compensation insurance',
      status: 'missing',
      actionUrl: '/affiliate/settings'
    },
    {
      id: 'operating-permit',
      category: 'business',
      name: 'Operating Permit',
      description: 'Transportation operating permit',
      status: 'complete',
      actionUrl: '/affiliate/settings'
    }
  ]

  items.push(...businessDocs)

  // Get vehicles and their compliance status
  const { data: vehicles, error: vehiclesError } = await supabase
    .from('vehicles')
    .select('id, make, model, year, license_plate')
    .eq('company_id', companyId)

  if (!vehiclesError && vehicles) {
    vehicles.forEach((vehicle: any) => {
      const vehicleDocs = [
        {
          id: `vehicle-${vehicle.id}-registration`,
          category: 'vehicle',
          name: `${vehicle.year} ${vehicle.make} ${vehicle.model} - Registration`,
          description: `Vehicle registration for ${vehicle.license_plate}`,
          status: Math.random() > 0.8 ? 'missing' : 'complete',
          actionUrl: '/affiliate/fleet-rates'
        },
        {
          id: `vehicle-${vehicle.id}-insurance`,
          category: 'vehicle',
          name: `${vehicle.year} ${vehicle.make} ${vehicle.model} - Insurance`,
          description: `Vehicle insurance for ${vehicle.license_plate}`,
          status: Math.random() > 0.9 ? 'expiring' : 'complete',
          expiryDate: Math.random() > 0.5 ? '2024-05-20' : undefined,
          actionUrl: '/affiliate/fleet-rates'
        },
        {
          id: `vehicle-${vehicle.id}-inspection`,
          category: 'vehicle',
          name: `${vehicle.year} ${vehicle.make} ${vehicle.model} - Inspection`,
          description: `Safety inspection for ${vehicle.license_plate}`,
          status: Math.random() > 0.85 ? 'missing' : 'complete',
          actionUrl: '/affiliate/fleet-rates'
        }
      ]
      items.push(...vehicleDocs)
    })
  }

  // Get drivers and their compliance status
  const { data: drivers, error: driversError } = await supabase
    .from('drivers')
    .select('id, first_name, last_name, license_number')
    .eq('company_id', companyId)

  if (!driversError && drivers) {
    drivers.forEach((driver: any) => {
      const driverDocs = [
        {
          id: `driver-${driver.id}-license`,
          category: 'driver',
          name: `${driver.first_name} ${driver.last_name} - Driver License`,
          description: `Valid driver license for ${driver.license_number}`,
          status: Math.random() > 0.9 ? 'expiring' : 'complete',
          expiryDate: Math.random() > 0.5 ? '2024-06-30' : undefined,
          actionUrl: '/affiliate/drivers'
        },
        {
          id: `driver-${driver.id}-background`,
          category: 'driver',
          name: `${driver.first_name} ${driver.last_name} - Background Check`,
          description: `Background verification`,
          status: Math.random() > 0.85 ? 'missing' : 'complete',
          actionUrl: '/affiliate/drivers'
        },
        {
          id: `driver-${driver.id}-medical`,
          category: 'driver',
          name: `${driver.first_name} ${driver.last_name} - Medical Certificate`,
          description: `DOT medical certificate`,
          status: Math.random() > 0.8 ? 'missing' : 'complete',
          actionUrl: '/affiliate/drivers'
        }
      ]
      items.push(...driverDocs)
    })
  }

  return items
}
