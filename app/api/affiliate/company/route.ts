import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'
import { z } from 'zod'
import { NextRequest } from 'next/server'
import { hasRole } from '../../../lib/auth';
import { UserRole } from '@/src/types/roles';

const companyUpdateSchema = z.object({
  city: z.string().min(1, 'City is required'),
  name: z.string().optional(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  state: z.string().optional(),
  zip: z.string().optional(),
  country: z.string().optional(),
  service_areas: z.array(z.string()).optional(),
  vehicle_types: z.array(z.string()).optional(),
})

export async function GET(request: NextRequest) {
  const cookieStore = cookies()
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set({ name, value, ...options })
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set({ name, value: '', ...options })
        },
      },
    }
  )

  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }

    // First get the current user's company association
    const { data: userCompanyLinks, error: fetchError } = await supabase
      .from('affiliate_user_companies')
      .select(`
        affiliate_id,
        role,
        status,
        company:affiliate_companies (
          id,
          name,
          email,
          phone,
          address,
          city,
          state,
          zip,
          country,
          website,
          status,
          dba,
          owner_name,
          year_established,
          federal_tax_id,
          contact_email,
          contact_phone
        )
      `)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single()

    if (fetchError) {
      console.error('Error fetching company:', fetchError)
      return NextResponse.json({ error: 'Failed to fetch company details.' }, { status: 500 })
    }

    if (!userCompanyLinks) {
      return NextResponse.json({ error: 'No active company found for this user' }, { status: 404 })
    }

    return NextResponse.json(userCompanyLinks.company)
  } catch (error) {
    console.error('Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: Request) {
  const cookieStore = cookies()
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options)
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options)
        },
      },
    }
  )

  try {
    // Get the current user's session
    const { data: { session } } = await supabase.auth.getSession()
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify user has OWNER or SUPER_ADMIN role
    if (!hasRole(session.roles, ['OWNER' as UserRole, 'SUPER_ADMIN' as UserRole])) {
      return NextResponse.json({ error: 'Insufficient permissions to update company' }, { status: 403 });
    }

    // Parse and validate the request body
    const body = await request.json()
    console.log('Received update request:', body)

    const validatedData = companyUpdateSchema.parse(body)
    console.log('Validated data:', validatedData)

    // First, check if the company exists
    const { data: companies, error: fetchError } = await supabase
      .from('affiliate_companies')
      .select('*')
      .eq('owner_id', session.user.id)

    if (fetchError) {
      console.error('Error checking existing company:', fetchError)
      return NextResponse.json({ error: 'Failed to check company existence', details: fetchError.message }, { status: 500 })
    }

    console.log('Existing companies:', companies)

    const now = new Date().toISOString()
    const companyData = {
      ...validatedData,
      updated_at: now,
    }

    console.log('Prepared company data:', companyData)

    if (!companies || companies.length === 0) {
      // If company doesn't exist, create it
      const { data: newCompany, error: insertError } = await supabase
        .from('affiliate_companies')
        .insert({
          owner_id: session.user.id,
          ...companyData,
          created_at: now,
        })
        .select()
        .single()

      if (insertError) {
        console.error('Error creating company:', insertError)
        return NextResponse.json({ error: 'Failed to create company', details: insertError.message }, { status: 500 })
      }

      if (!newCompany) {
        return NextResponse.json({ error: 'Failed to create company', details: 'No data returned after insert' }, { status: 500 })
      }

      console.log('Created new company:', newCompany)
      return NextResponse.json(newCompany)
    }

    // If we have multiple companies, update the first one (allow multiple companies)
    if (companies.length > 1) {
      console.log('Found multiple companies, updating the first one')
      const companyToUpdate = companies[0]

      // Update the first company
      const { data: updatedCompany, error: updateError } = await supabase
        .from('affiliate_companies')
        .update(companyData)
        .eq('id', companyToUpdate.id)
        .select()
        .single()

      if (updateError) {
        console.error('Error updating company:', updateError)
        return NextResponse.json({ error: 'Failed to update company details', details: updateError.message }, { status: 500 })
      }

      if (!updatedCompany) {
        return NextResponse.json({ error: 'Failed to update company', details: 'No data returned after update' }, { status: 500 })
      }

      console.log('Updated company:', updatedCompany)
      return NextResponse.json(updatedCompany)
    }

    // Update the single existing company
    const { data: updatedCompany, error: updateError } = await supabase
      .from('affiliate_companies')
      .update(companyData)
      .eq('id', companies[0].id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating company:', updateError)
      return NextResponse.json({ error: 'Failed to update company details', details: updateError.message }, { status: 500 })
    }

    if (!updatedCompany) {
      return NextResponse.json({ error: 'Failed to update company', details: 'No data returned after update' }, { status: 500 })
    }

    console.log('Updated company:', updatedCompany)
    return NextResponse.json(updatedCompany)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid request data', details: error.errors }, { status: 400 })
    }
    console.error('Error in PUT /api/affiliate/company:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function PATCH(request: NextRequest) {
  const cookieStore = cookies()
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options)
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options)
        },
      },
    }
  )

  try {
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    if (authError || !session || !session.user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }

    // Verify user has OWNER or SUPER_ADMIN role
    if (!hasRole(session.roles, ['OWNER' as UserRole, 'SUPER_ADMIN' as UserRole])) {
      return NextResponse.json({ error: 'Insufficient permissions to update company' }, { status: 403 });
    }

    // Get the current user's company associations
    const { data: userCompanyLinks, error: fetchError } = await supabase
      .from('affiliate_user_companies')
      .select('affiliate_id, role')
      .eq('user_id', session.user.id)
      .eq('status', 'active')

    if (fetchError) {
      console.error('Error fetching user company links:', fetchError)
      return NextResponse.json({ error: 'Failed to fetch company details.' }, { status: 500 })
    }

    if (!userCompanyLinks || userCompanyLinks.length === 0) {
      return NextResponse.json({ error: 'No company associated with this user' }, { status: 404 })
    }

    // Get the company data from the request
    const companyData = await request.json()

    // Update the company data
    const { error: updateError } = await supabase
      .from('affiliate_companies')
      .update({
        name: companyData.name,
        email: companyData.email,
        phone: companyData.phone,
        address: companyData.address,
        city: companyData.city,
        state: companyData.state,
        zip: companyData.zip,
        country: companyData.country,
        website: companyData.website,
        dba: companyData.dba,
        owner_name: companyData.owner_name,
        year_established: companyData.year_established,
        federal_tax_id: companyData.federal_tax_id,
        contact_email: companyData.contact_email,
        contact_phone: companyData.contact_phone
      })
      .eq('id', userCompanyLinks[0].affiliate_id)

    if (updateError) {
      console.error('Error updating company:', updateError)
      return NextResponse.json({ error: 'Failed to update company details.' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}