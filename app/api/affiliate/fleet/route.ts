import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Define roles for operations in this specific route
const VALID_ROLES_GET = ['OWNER', 'ADMIN', 'DISPATCHER', 'STAFF', 'DRIVER'];
const VALID_ROLES_POST_PUT_DELETE = ['OWNER', 'ADMIN'];

// Zod schema for POST (adding a new vehicle to fleet_vehicles)
// Based on the existing POST handler logic
const fleetVehicleCreateSchema = z.object({
  vehicle_type: z.string().min(1),
  make: z.string().min(1),
  model: z.string().min(1),
  year: z.string().min(4).regex(/^\\d{4}$/),
  license_plate: z.string().min(1),
  capacity: z.number().int().positive(), // Assuming capacity should be a number
  amenities: z.array(z.string()).optional().default([]),
  // status is defaulted to 'ACTIVE' in the code, not part of body schema here
});

// Zod schema for PUT (updating a fleet_vehicle)
// Based on the existing PUT handler logic
const fleetVehicleUpdateSchema = z.object({
  id: z.string().uuid(), // Vehicle ID is required for update
  vehicle_type: z.string().min(1).optional(),
  make: z.string().min(1).optional(),
  model: z.string().min(1).optional(),
  year: z.string().min(4).regex(/^\\d{4}$/).optional(),
  license_plate: z.string().min(1).optional(),
  capacity: z.number().int().positive().optional(),
  amenities: z.array(z.string()).optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'MAINTENANCE']).optional(), // Example statuses
}).refine(data => Object.keys(data).length > 1, { // id + at least one other field
  message: "At least one field to update must be provided besides ID",
});

// Re-usable authorization function
async function authorizeAffiliateAndGetCompany(
  supabase: ReturnType<typeof createServerClient>,
  userId: string,
  companyIdFromHeader: string | null,
  allowedRoles: string[]
) {
  if (!companyIdFromHeader) {
    console.error('X-Affiliate-Company-ID header is missing in fleet route');
    return { error: NextResponse.json({ error: 'X-Affiliate-Company-ID header is required' }, { status: 400 }), companyId: null, userRole: null };
  }
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  if (!uuidRegex.test(companyIdFromHeader)) {
    console.error('Invalid X-Affiliate-Company-ID header format in fleet route');
    return { error: NextResponse.json({ error: 'Invalid X-Affiliate-Company-ID header format' }, { status: 400 }), companyId: null, userRole: null };
  }

  const { data: companyAccess, error: accessError } = await supabase
    .from('affiliate_user_companies')
    .select('role, status')
    .eq('user_id', userId)
    .eq('affiliate_id', companyIdFromHeader)
    .single();

  if (accessError || !companyAccess) {
    console.error('Error fetching affiliate company access or access denied in fleet route:', accessError);
    return { error: NextResponse.json({ error: 'Affiliate company access denied or not found.' }, { status: 403 }), companyId: null, userRole: null };
  }

  if (companyAccess.status !== 'ACTIVE') {
    console.error('User is not active in this company in fleet route:', companyAccess.status);
    return { error: NextResponse.json({ error: 'User is not active in this company.' }, { status: 403 }), companyId: null, userRole: null };
  }

  if (!allowedRoles.includes(companyAccess.role)) {
    console.error('User role not permitted for this operation in fleet route:', companyAccess.role);
    return { error: NextResponse.json({ error: 'User role not permitted for this operation.' }, { status: 403 }), companyId: null, userRole: null };
  }

  return { error: null, companyId: companyIdFromHeader, userRole: companyAccess.role };
}

export async function GET(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options);
        },
      },
    }
  );

  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      console.error('GET /api/affiliate/fleet: User not authenticated', userError);
      return NextResponse.json({ error: userError?.message || 'Not authenticated' }, { status: 401 });
    }

    const companyIdFromHeader = request.headers.get('X-Affiliate-Company-ID');
    const authResult = await authorizeAffiliateAndGetCompany(supabase, user.id, companyIdFromHeader, VALID_ROLES_GET);

    if (authResult.error) {
      return authResult.error;
    }
    const { companyId } = authResult; // This is the validated affiliate_id
    console.log(`GET /api/affiliate/fleet: Authorized for companyId: ${companyId} with role ${authResult.userRole}`);

    // Get the fleet vehicles with their current trips
    const { data: vehicles, error: vehiclesError } = await supabase
      .from('fleet_vehicles')
      .select(`
        *,
        trips!inner (
          id,
          status,
          actual_pickup_location,
          actual_dropoff_location
        )
      `)
      .eq('affiliate_id', companyId)
      .order('created_at', { ascending: false })
    if (vehiclesError) {
      console.error('GET /api/affiliate/fleet: Error fetching vehicles', { companyId, message: vehiclesError.message });
      throw vehiclesError;
    }

    // Get vehicle availability
    const { data: availability, error: availabilityError } = await supabase
      .from('affiliate_vehicle_availability')
      .select('*')
      .eq('affiliate_id', companyId)
    if (availabilityError) {
      console.error('GET /api/affiliate/fleet: Error fetching availability', { companyId, message: availabilityError.message });
      throw availabilityError;
    }

    return NextResponse.json({
      vehicles: vehicles || [],
      availability: availability || []
    });
  } catch (error: any) {
    console.error('Error fetching fleet data:', error.message || error);
    return NextResponse.json(
      { error: error.message || 'Error fetching fleet data' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options);
        },
      },
    }
  );

  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      console.error('POST /api/affiliate/fleet: User not authenticated', userError);
      return NextResponse.json({ error: userError?.message || 'Not authenticated' }, { status: 401 });
    }

    const companyIdFromHeader = request.headers.get('X-Affiliate-Company-ID');
    const authResult = await authorizeAffiliateAndGetCompany(supabase, user.id, companyIdFromHeader, VALID_ROLES_POST_PUT_DELETE);

    if (authResult.error) {
      return authResult.error;
    }
    const { companyId } = authResult; // This is the validated affiliate_id
    console.log(`POST /api/affiliate/fleet: Authorized for companyId: ${companyId} with role ${authResult.userRole}`);

    const body = await request.json();
    const parsedBody = fleetVehicleCreateSchema.safeParse(body);

    if (!parsedBody.success) {
      console.error("POST /api/affiliate/fleet: Validation failed.", { errors: parsedBody.error.format() });
      return NextResponse.json({ error: 'Invalid input', details: parsedBody.error.format() }, { status: 400 });
    }
    
    const { vehicle_type, make, model, year, license_plate, capacity, amenities } = parsedBody.data;

    // Insert new vehicle
    const { data: newVehicle, error: insertError } = await supabase
      .from('fleet_vehicles')
      .insert({
        affiliate_id: companyId,
        vehicle_type,
        make,
        model,
        year,
        license_plate,
        capacity,
        amenities,
        status: 'ACTIVE'
      })
      .select()
      .single();
    if (insertError) {
      console.error('POST /api/affiliate/fleet: Error inserting vehicle', { companyId, message: insertError.message });
      throw insertError;
    }

    // Update vehicle availability
    const { data: currentAvailability } = await supabase
      .from('affiliate_vehicle_availability')
      .select('*')
      .eq('affiliate_id', companyId)
      .eq('vehicle_type', vehicle_type)
      .single();

    if (currentAvailability) {
      const { error: updateAvailError } = await supabase
        .from('affiliate_vehicle_availability')
        .update({
          total_vehicles: currentAvailability.total_vehicles + 1,
          available_vehicles: currentAvailability.available_vehicles + 1
        })
        .eq('id', currentAvailability.id);
      if (updateAvailError) {
        console.warn('POST /api/affiliate/fleet: Failed to update existing availability record', { companyId, vehicle_type, message: updateAvailError.message });
        // Not throwing, as vehicle was created. Log and continue.
      }
    } else {
      const { error: insertAvailError } = await supabase
        .from('affiliate_vehicle_availability')
        .insert({
          affiliate_id: companyId,
          vehicle_type,
          total_vehicles: 1,
          available_vehicles: 1,
          availability_schedule: {
            peak_hours: {
              available: 1,
              start_time: '15:00',
              end_time: '19:00'
            }
          }
        });
      if (insertAvailError) {
        console.warn('POST /api/affiliate/fleet: Failed to insert new availability record', { companyId, vehicle_type, message: insertAvailError.message });
        // Not throwing, as vehicle was created. Log and continue.
      }
    }

    return NextResponse.json(newVehicle);
  } catch (error: any) {
    console.error('Error adding vehicle to fleet:', error.message || error);
    return NextResponse.json(
      { error: error.message || 'Error adding vehicle' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options);
        },
      },
    }
  );

  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      console.error('PUT /api/affiliate/fleet: User not authenticated', userError);
      return NextResponse.json({ error: userError?.message || 'Not authenticated' }, { status: 401 });
    }

    const companyIdFromHeader = request.headers.get('X-Affiliate-Company-ID');
    const authResult = await authorizeAffiliateAndGetCompany(supabase, user.id, companyIdFromHeader, VALID_ROLES_POST_PUT_DELETE);

    if (authResult.error) {
      return authResult.error;
    }
    const { companyId } = authResult; // This is the validated affiliate_id
    console.log(`PUT /api/affiliate/fleet: Authorized for companyId: ${companyId} with role ${authResult.userRole}`);

    const body = await request.json();
    const parsedBody = fleetVehicleUpdateSchema.safeParse(body);

    if (!parsedBody.success) {
      console.error("PUT /api/affiliate/fleet: Validation failed.", { errors: parsedBody.error.format() });
      return NextResponse.json({ error: 'Invalid input', details: parsedBody.error.format() }, { status: 400 });
    }
    
    const { id: vehicleId, ...updateData } = parsedBody.data;
    const { data: updatedVehicle, error: updateError } = await supabase
      .from('fleet_vehicles')
      .update(updateData)
      .eq('id', vehicleId)
      .eq('affiliate_id', companyId)
      .select()
      .single();

    if (updateError) {
      console.error('PUT /api/affiliate/fleet: Error updating vehicle', { vehicleId, companyId, message: updateError.message });
      if (updateError.code === 'PGRST116') { // Not found or no permission if RLS is strict
        return NextResponse.json({ error: 'Vehicle not found or not owned by affiliate.' }, { status: 404 });
      }
      throw updateError;
    }

    return NextResponse.json(updatedVehicle);
  } catch (error: any) {
    console.error('Error updating fleet vehicle:', error.message || error);
    return NextResponse.json(
      { error: error.message || 'Error updating vehicle' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options);
        },
      },
    }
  );

  try {
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      console.error('DELETE /api/affiliate/fleet: User not authenticated', userError);
      return NextResponse.json({ error: userError?.message || 'Not authenticated' }, { status: 401 });
    }

    const companyIdFromHeader = request.headers.get('X-Affiliate-Company-ID');
    const authResult = await authorizeAffiliateAndGetCompany(supabase, user.id, companyIdFromHeader, VALID_ROLES_POST_PUT_DELETE);

    if (authResult.error) {
      return authResult.error;
    }
    const { companyId } = authResult; // This is the validated affiliate_id
    console.log(`DELETE /api/affiliate/fleet: Authorized for companyId: ${companyId} with role ${authResult.userRole}`);
    
    // For DELETE, we expect the vehicle ID in the query parameters or body.
    // Assuming it's in query params like /api/affiliate/fleet?vehicleId=xxx
    const url = new URL(request.url);
    const vehicleId = url.searchParams.get('vehicleId');

    if (!vehicleId) {
      console.error('DELETE /api/affiliate/fleet: vehicleId query parameter is required.');
      return NextResponse.json({ error: 'vehicleId query parameter is required' }, { status: 400 });
    }
    
    // Validate if vehicleId is a UUID (basic validation)
    const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
    if (!uuidRegex.test(vehicleId)) {
        console.error('Invalid vehicleId format');
        return NextResponse.json({ error: 'Invalid vehicleId format' }, { status: 400 });
    }

    // Fetch vehicle_type before deleting to update availability
    const { data: vehicleToDelete, error: fetchError } = await supabase
      .from('fleet_vehicles')
        .select('vehicle_type')
        .eq('id', vehicleId)
        .eq('affiliate_id', companyId)
        .single();

    if (fetchError || !vehicleToDelete) {
        console.error('DELETE /api/affiliate/fleet: Vehicle not found or not owned by affiliate before delete', { vehicleId, companyId, message: fetchError?.message });
        return NextResponse.json({ error: 'Vehicle not found or not owned by affiliate.' }, { status: 404 });
    }

    const { error: deleteError, count } = await supabase
      .from('fleet_vehicles')
      .delete({ count: 'exact' })
      .eq('id', vehicleId)
      .eq('affiliate_id', companyId); // Ensure delete is for the correct company

    if (deleteError) {
      console.error('DELETE /api/affiliate/fleet: Error deleting vehicle', { vehicleId, companyId, message: deleteError.message });
      throw deleteError;
    }

    if (count === 0) { // Should have been caught by pre-fetch, but good for safety
        console.warn('DELETE /api/affiliate/fleet: Vehicle not found during delete operation though pre-fetch succeeded', { vehicleId, companyId });
        return NextResponse.json({ error: 'Vehicle not found or not owned by affiliate.' }, { status: 404 });
    }

    // Update vehicle availability (decrement)
    const { data: currentAvailability, error: availError } = await supabase
        .from('affiliate_vehicle_availability')
        .select('*')
      .eq('affiliate_id', companyId)
      .eq('vehicle_type', vehicleToDelete.vehicle_type)
      .single();

    if (availError) {
        console.warn('DELETE /api/affiliate/fleet: Could not find availability record to decrement', { companyId, vehicle_type: vehicleToDelete.vehicle_type, message: availError.message });
        // Log and continue, main entity deleted.
    } else if (currentAvailability) {
      const { error: updateAvailError } = await supabase
          .from('affiliate_vehicle_availability')
          .update({
          total_vehicles: Math.max(0, currentAvailability.total_vehicles - 1), // Ensure not negative
          available_vehicles: Math.max(0, currentAvailability.available_vehicles - 1) // Ensure not negative
        })
        .eq('id', currentAvailability.id);
      if (updateAvailError) {
         console.warn('DELETE /api/affiliate/fleet: Failed to update (decrement) availability record', { companyId, vehicle_type: vehicleToDelete.vehicle_type, message: updateAvailError.message });
      }
    }

    return NextResponse.json({ message: 'Vehicle deleted successfully from fleet' });
  } catch (error: any) {
    console.error('Error deleting fleet vehicle:', error.message || error);
    return NextResponse.json(
      { error: error.message || 'Error deleting vehicle' },
      { status: 500 }
    );
  }
} 