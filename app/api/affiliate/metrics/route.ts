import { NextRequest, NextResponse } from 'next/server'
import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from '@/lib/database.types'

const createSupabaseClient = () => {
  const cookieStore = cookies()
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options)
          } catch (error) {
            // Handle error
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, '', options)
          } catch (error) {
            // Handle error
          }
        },
      },
    }
  )
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseClient()

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 })
    }

    // Verify user has access to this company
    const { data: userCompany, error: accessError } = await supabase
      .from('affiliate_user_companies')
      .select('role')
      .eq('user_id', user.id)
      .eq('affiliate_id', companyId)
      .single()

    if (accessError || !userCompany) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Get company approval status
    const { data: company, error: companyError } = await supabase
      .from('affiliate_companies')
      .select('status')
      .eq('id', companyId)
      .single()

    if (companyError) {
      return NextResponse.json({ error: 'Company not found' }, { status: 404 })
    }

    const isApproved = company.status === 'active'

    // If not approved, return limited metrics
    if (!isApproved) {
      return NextResponse.json({
        isApproved: false,
        onboardingStatus: {
          companyProfile: true, // They have a company if we're here
          fleetVehicles: 0,
          rateCards: 0,
          documents: 0
        },
        message: 'Complete onboarding and get approved to see full metrics'
      })
    }

    // Get real metrics for approved companies
    const [
      { data: vehicles, error: vehiclesError },
      { data: rateCards, error: rateCardsError },
      { data: drivers, error: driversError }
    ] = await Promise.all([
      // Get vehicles
      supabase
        .from('vehicles')
        .select('id, type, status, created_at')
        .eq('company_id', companyId),

      // Get rate cards
      supabase
        .from('rate_cards')
        .select('id, vehicle_type, pricing_model_type, status, created_at')
        .eq('company_id', companyId),

      // Get drivers
      supabase
        .from('drivers')
        .select('id, status, created_at')
        .eq('company_id', companyId)
    ])

    // Mock offers data for now
    const offers: any[] = []

    if (vehiclesError || rateCardsError || driversError) {
      console.error('Error fetching metrics:', { vehiclesError, rateCardsError, driversError })
      return NextResponse.json({ error: 'Failed to fetch metrics' }, { status: 500 })
    }

    // Calculate metrics
    const totalOffers = offers?.length || 0
    const pendingOffers = offers?.filter(o => o.status.toLowerCase() === 'pending')?.length || 0
    const acceptedOffers = offers?.filter(o => o.status.toLowerCase() === 'accepted')?.length || 0
    const totalRevenue = offers?.filter(o => o.status.toLowerCase() === 'accepted')
      .reduce((sum, o) => sum + (o.rate_amount || 0), 0) || 0

    const activeVehicles = vehicles?.filter(v => v.status === 'active')?.length || 0
    const totalVehicles = vehicles?.length || 0

    const activeRateCards = rateCards?.length || 0
    const activeDrivers = drivers?.filter(d => d.status === 'active')?.length || 0

    // Recent activity (last 5 offers)
    const recentActivity = offers?.slice(0, 5).map(offer => ({
      id: offer.id,
      type: 'offer',
      customer: offer.quotes?.contact_name || 'Unknown',
      service: offer.quotes?.service_type || 'Unknown',
      amount: offer.rate_amount,
      status: offer.status,
      date: offer.created_at,
      reference: offer.quotes?.reference_number
    })) || []

    // Notifications (urgent items)
    const notifications = []

    if (pendingOffers > 0) {
      notifications.push({
        id: 'pending-offers',
        type: 'urgent',
        message: `You have ${pendingOffers} pending offer${pendingOffers > 1 ? 's' : ''} that need${pendingOffers === 1 ? 's' : ''} attention`,
        time: 'now',
        count: pendingOffers
      })
    }

    if (activeVehicles === 0 && totalVehicles > 0) {
      notifications.push({
        id: 'inactive-vehicles',
        type: 'warning',
        message: 'All your vehicles are inactive. Activate them to receive offers.',
        time: 'now'
      })
    }

    if (activeRateCards === 0) {
      notifications.push({
        id: 'no-rates',
        type: 'warning',
        message: 'No rate cards configured. Add rate cards to receive offers.',
        time: 'now'
      })
    }

    return NextResponse.json({
      isApproved: true,
      metrics: {
        totalOffers,
        pendingOffers,
        acceptedOffers,
        totalRevenue,
        activeVehicles,
        totalVehicles,
        activeRateCards,
        activeDrivers
      },
      recentActivity,
      notifications,
      summary: {
        thisMonth: {
          offers: offers?.filter(o => {
            const offerDate = new Date(o.created_at)
            const now = new Date()
            return offerDate.getMonth() === now.getMonth() && offerDate.getFullYear() === now.getFullYear()
          }).length || 0,
          revenue: offers?.filter(o => {
            const offerDate = new Date(o.created_at)
            const now = new Date()
            return offerDate.getMonth() === now.getMonth() &&
                   offerDate.getFullYear() === now.getFullYear() &&
                   o.status.toLowerCase() === 'accepted'
          }).reduce((sum, o) => sum + (o.rate_amount || 0), 0) || 0
        }
      }
    })

  } catch (error) {
    console.error('Error in affiliate metrics API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}