import { createServerClient, type CookieOptions } from "@supabase/ssr";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, "", options);
        },
      },
    }
  );

  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error(
        "GET /api/affiliate/user-companies: Not authenticated",
        authError
      );
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    console.log(
      "GET /api/affiliate/user-companies: Fetching companies for user",
      user.id
    );

    const { data: userCompanyLinks, error: fetchError } = await supabase
      .from("affiliate_user_companies")
      .select(
        `
        id,
        affiliate_id,
        role,
        status,
        company:affiliate_companies (
          id,
          name,
          email,
          phone,
          address,
          city,
          state,
          zip,
          country,
          website,
          status,
          application_status,
          rating,
          completion_rate,
          owner_id,
          created_at,
          updated_at,
          tenant_id
        )
      `
      )
      .eq("user_id", user.id);

    if (fetchError) {
      console.error(
        "GET /api/affiliate/user-companies: Failed to fetch companies",
        fetchError
      );
      return NextResponse.json(
        {
          error: "Failed to fetch affiliate company associations.",
          details: fetchError.message,
        },
        { status: 500 }
      );
    }

    // Transform the data to include company ID at the top level
    const responseData =
      userCompanyLinks?.map((link) => ({
        id: link.id,
        role: link.role,
        status: link.status,
        company_id: link.affiliate_id,
        company: link.company,
      })) || [];

    console.log(
      "GET /api/affiliate/user-companies: Successfully fetched companies",
      responseData.length
    );
    return NextResponse.json(responseData);
  } catch (error) {
    console.error("GET /api/affiliate/user-companies: Unhandled error", error);
    return NextResponse.json(
      {
        error: "Failed to fetch user companies.",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
