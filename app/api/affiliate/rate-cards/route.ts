import { type CookieOptions, createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { hasRole } from "@supabase/ssr";

// Zod schema for rate card creation/update
const rateCardSchema = z.object({
  id: z.union([
    z.string().uuid(),
    z.number().transform(String),
    z.literal('').transform(() => undefined)
  ]).optional(), // Handle UUID strings, numbers, or empty strings
  vehicle_type: z.string().min(1, "Vehicle type is required"),
  // Remove fields that shouldn't be in the input
  company_id: z.any().optional().transform(() => undefined), // Will be set by server
  created_at: z.any().optional().transform(() => undefined), // Will be set by server
  updated_at: z.any().optional().transform(() => undefined), // Will be set by server
  // company_id will be derived from the authenticated user session
  pricing_model_type: z.enum(['P2P', 'DT', 'HOURLY_CHARTER', 'AIRPORT_TRANSFER']),

  // P2P specific fields (optional based on pricing_model_type)
  p2p_point_to_point_rate: z.number().positive().optional().nullable(),
  p2p_extra_hour_rate: z.number().positive().optional().nullable(),

  // DT specific fields (optional based on pricing_model_type)
  dt_base_fee: z.number().nonnegative().optional().nullable(),
  dt_per_mile_rate: z.number().positive().optional().nullable(),
  dt_per_hour_rate: z.number().positive().optional().nullable(),
  dt_min_miles: z.number().nonnegative().optional().nullable(),
  dt_min_hours: z.number().nonnegative().optional().nullable(),

  // AIRPORT_TRANSFER specific fields (optional based on pricing_model_type)
  airport_transfer_flat_rate: z.number().positive().optional().nullable(),

  // HOURLY_CHARTER specific fields (optional based on pricing_model_type)
  charter_hourly_rate: z.number().positive().optional().nullable(),
  charter_min_hours: z.number().positive().optional().nullable(),

  status: z.enum(['draft', 'pending_approval', 'approved', 'rejected', 'archived']).default('draft'),
  // Deprecated generic fields - will be made nullable in DB and not primary for new entries
  base_rate: z.number().optional().nullable(),
  per_mile_rate: z.number().optional().nullable(),
  per_hour_rate: z.number().optional().nullable(),
  minimum_hours: z.number().optional().nullable(),
});

const rateCardArraySchema = z.array(rateCardSchema);

const VALID_ROLES_GET = ['OWNER', 'ADMIN', 'DISPATCHER', 'STAFF', 'DRIVER'];
const VALID_ROLES_POST = ['OWNER', 'ADMIN'];

// Re-usable authorization function
async function authorizeAffiliateAndGetCompany(
  supabase: ReturnType<typeof createServerClient>,
  userId: string,
  companyIdFromHeader: string | null,
  allowedRoles: string[]
) {
  if (!companyIdFromHeader) {
    console.error('X-Affiliate-Company-ID header is missing');
    return { error: NextResponse.json({ error: 'X-Affiliate-Company-ID header is required' }, { status: 400 }), companyId: null, userRole: null };
  }
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  if (!uuidRegex.test(companyIdFromHeader)) {
    console.error('Invalid X-Affiliate-Company-ID header format');
    return { error: NextResponse.json({ error: 'Invalid X-Affiliate-Company-ID header format' }, { status: 400 }), companyId: null, userRole: null };
  }

  const { data: companyAccess, error: accessError } = await supabase
    .from('affiliate_user_companies')
    .select('role, status')
    .eq('user_id', userId)
    .eq('affiliate_id', companyIdFromHeader)
    .single();

  if (accessError || !companyAccess) {
    console.error('Error fetching affiliate company access or access denied:', accessError);
    return { error: NextResponse.json({ error: 'Affiliate company access denied or not found.' }, { status: 403 }), companyId: null, userRole: null };
  }

  if (companyAccess.status !== 'ACTIVE') {
    console.error('User is not active in this company:', companyAccess.status);
    return { error: NextResponse.json({ error: 'User is not active in this company.' }, { status: 403 }), companyId: null, userRole: null };
  }

  // Use the hasRole utility for role checking
  if (!hasRole([companyAccess.role], allowedRoles)) {
    console.error('User role not permitted for this operation:', companyAccess.role);
    return { error: NextResponse.json({ error: 'User role not permitted for this operation.' }, { status: 403 }), companyId: null, userRole: null };
  }

  return { error: null, companyId: companyIdFromHeader, userRole: companyAccess.role };
}

export async function GET(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options);
        },
      },
    }
  );

  try {
    console.log("GET /api/affiliate/rate-cards: Attempting to get user");
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      console.error('User not authenticated:', userError);
      return NextResponse.json({ error: 'User not authenticated' }, { status: 401 });
    }

    const companyIdFromHeader = request.headers.get('X-Affiliate-Company-ID');
    const authResult = await authorizeAffiliateAndGetCompany(supabase, user.id, companyIdFromHeader, VALID_ROLES_GET);

    if (authResult.error) {
      return authResult.error;
    }
    const { companyId } = authResult;
    console.log(`Authorized for companyId: ${companyId} with role ${authResult.userRole} in GET rate-cards`);

    const { data: rateCards, error: rateCardsError } = await supabase
      .from('rate_cards')
      .select('*')
      .eq('company_id', companyId);

    if (rateCardsError) {
      console.error('GET /api/affiliate/rate-cards: Error fetching rate cards.', {
        companyId,
        rateCardsError: rateCardsError.message
      });
      return NextResponse.json({ error: rateCardsError.message }, { status: 500 });
    }

    // Transform rate cards to extract fields from special_rates JSONB
    const transformedRateCards = (rateCards || []).map(card => {
      const specialRates = card.special_rates || {};
      return {
        ...card,
        // Extract fields from special_rates and add them as direct properties
        p2p_point_to_point_rate: specialRates.p2p_point_to_point_rate || card.p2p_point_to_point_rate,
        p2p_extra_hour_rate: specialRates.p2p_extra_hour_rate || card.p2p_extra_hour_rate,
        dt_base_fee: specialRates.dt_base_fee || card.dt_base_fee,
        dt_per_mile_rate: specialRates.dt_per_mile_rate || card.dt_per_mile_rate,
        dt_per_hour_rate: specialRates.dt_per_hour_rate || card.dt_per_hour_rate,
        dt_min_miles: specialRates.dt_min_miles || card.dt_min_miles,
        dt_min_hours: specialRates.dt_min_hours || card.dt_min_hours,
        airport_transfer_flat_rate: specialRates.airport_transfer_flat_rate || card.airport_transfer_flat_rate,
        charter_hourly_rate: specialRates.charter_hourly_rate || card.charter_hourly_rate,
        charter_min_hours: specialRates.charter_min_hours || card.charter_min_hours,
        pricing_model_type: specialRates.pricing_model_type || card.pricing_model_type || 'P2P',
      };
    });

    console.log('GET /api/affiliate/rate-cards: Returning transformed rate cards:', transformedRateCards);
    return NextResponse.json(transformedRateCards);
  } catch (error: any) {
    console.error('GET /api/affiliate/rate-cards: Catch-all error.', { error: error.message, stack: error.stack });
    return NextResponse.json({ error: 'An unexpected error occurred.' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options);
        },
      },
    }
  );

  try {
    console.log("POST /api/affiliate/rate-cards: Attempting to get user");
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      console.error('User not authenticated:', userError);
      return NextResponse.json({ error: 'User not authenticated' }, { status: 401 });
    }

    const companyIdFromHeader = request.headers.get('X-Affiliate-Company-ID');
    const authResult = await authorizeAffiliateAndGetCompany(supabase, user.id, companyIdFromHeader, VALID_ROLES_POST);

    if (authResult.error) {
      return authResult.error;
    }
    const { companyId } = authResult;
    console.log(`Authorized for companyId: ${companyId} with role ${authResult.userRole} in POST rate-cards`);

    const body = await request.json();
    console.log("POST /api/affiliate/rate-cards: Request body:", JSON.stringify(body, null, 2));

    let rateCardsToProcess;
    if (Array.isArray(body)) {
      const parsedBody = rateCardArraySchema.safeParse(body);
      if (!parsedBody.success) {
        console.error("POST /api/affiliate/rate-cards: Validation failed for array.", {
          errors: parsedBody.error.format(),
          originalBody: body
        });
        return NextResponse.json({ error: 'Invalid input array', details: parsedBody.error.format() }, { status: 400 });
      }
      rateCardsToProcess = parsedBody.data;
    } else {
      const parsedBody = rateCardSchema.safeParse(body);
      if (!parsedBody.success) {
        console.error("POST /api/affiliate/rate-cards: Validation failed for single object.", {
          errors: parsedBody.error.format(),
          originalBody: body
        });
        return NextResponse.json({ error: 'Invalid input object', details: parsedBody.error.format() }, { status: 400 });
      }
      rateCardsToProcess = [parsedBody.data];
    }

    const validatedRateCards = rateCardsToProcess.map(card => {
      // Create the special_rates JSONB object with all extended fields
      const specialRates = {
        ...card.special_rates,
        // Distance + Time fields
        dt_base_fee: card.dt_base_fee === undefined ? null : Number(card.dt_base_fee),
        dt_per_mile_rate: card.dt_per_mile_rate === undefined ? null : Number(card.dt_per_mile_rate),
        dt_per_hour_rate: card.dt_per_hour_rate === undefined ? null : Number(card.dt_per_hour_rate),
        dt_min_miles: card.dt_min_miles === undefined ? null : Number(card.dt_min_miles),
        dt_min_hours: card.dt_min_hours === undefined ? null : Number(card.dt_min_hours),
        // Airport transfer fields
        airport_transfer_flat_rate: card.airport_transfer_flat_rate === undefined ? null : Number(card.airport_transfer_flat_rate),
        // Charter fields
        charter_hourly_rate: card.charter_hourly_rate === undefined ? null : Number(card.charter_hourly_rate),
        charter_min_hours: card.charter_min_hours === undefined ? null : Number(card.charter_min_hours),
        // Point-to-point fields
        p2p_point_to_point_rate: card.p2p_point_to_point_rate === undefined ? null : Number(card.p2p_point_to_point_rate),
        p2p_extra_hour_rate: card.p2p_extra_hour_rate === undefined ? null : Number(card.p2p_extra_hour_rate),
        // Pricing model
        pricing_model_type: card.pricing_model_type || 'P2P',
      };

      // Create the processed card using only safe columns and JSONB for the rest
      const processedCard = {
        company_id: companyId,
        vehicle_type: card.vehicle_type,
        status: card.status || 'draft',
        // Store all rate data in special_rates JSONB to avoid schema issues
        special_rates: specialRates,
        // Legacy fields that definitely exist
        base_rate: card.base_rate === undefined ? null : Number(card.base_rate),
        per_mile_rate: card.per_mile_rate === undefined ? null : Number(card.per_mile_rate),
        per_hour_rate: card.per_hour_rate === undefined ? null : Number(card.per_hour_rate),
        minimum_hours: card.minimum_hours === undefined ? null : Number(card.minimum_hours),
        gratuity_percentage: card.gratuity_percentage === undefined ? null : Number(card.gratuity_percentage),
        additional_fees: card.additional_fees || null,
        seasonal_multipliers: card.seasonal_multipliers || null,
      };

      // If ID is provided, include it for updates
      if (card.id) {
        processedCard.id = card.id;
      }

      return processedCard;
    });

    // Check if the affiliate company is approved to auto-activate rate cards
    const { data: companyData } = await supabase
      .from('affiliate_companies')
      .select('application_status')
      .eq('id', companyId)
      .single();

    const isCompanyApproved = companyData?.application_status === 'approved';
    console.log(`Company ${companyId} approval status: ${companyData?.application_status}, auto-activating rate cards: ${isCompanyApproved}`);

    // Handle upsert properly by checking for existing records
    const results = [];
    for (const card of validatedRateCards) {
      // Auto-activate rate cards for approved companies
      if (isCompanyApproved) {
        card.is_active = true;
        card.status = 'approved';
      }

      // Check if a rate card already exists for this company and vehicle type
      const { data: existingCard } = await supabase
        .from('rate_cards')
        .select('id')
        .eq('company_id', card.company_id)
        .eq('vehicle_type', card.vehicle_type)
        .single();

      if (existingCard) {
        // Update existing record - remove the id from the update data to avoid UUID conflicts
        const updateData = { ...card };
        delete updateData.id; // Remove the problematic ID field

        const { data: updatedCard, error: updateError } = await supabase
          .from('rate_cards')
          .update({
            ...updateData,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingCard.id)
          .select()
          .single();

        if (updateError) {
          console.error('Error updating rate card:', updateError);
          return NextResponse.json({ error: updateError.message }, { status: 500 });
        }
        results.push(updatedCard);
      } else {
        // Insert new record - remove the id field to let database generate it
        const insertData = { ...card };
        delete insertData.id; // Remove the ID field for new records

        const { data: newCard, error: insertError } = await supabase
          .from('rate_cards')
          .insert(insertData)
          .select()
          .single();

        if (insertError) {
          console.error('Error inserting rate card:', insertError);
          return NextResponse.json({ error: insertError.message }, { status: 500 });
        }
        results.push(newCard);
      }
    }

    // Transform results to extract fields from special_rates JSONB
    const transformedResults = results.map(card => {
      const specialRates = card.special_rates || {};
      return {
        ...card,
        // Extract fields from special_rates and add them as direct properties
        p2p_point_to_point_rate: specialRates.p2p_point_to_point_rate || card.p2p_point_to_point_rate,
        p2p_extra_hour_rate: specialRates.p2p_extra_hour_rate || card.p2p_extra_hour_rate,
        dt_base_fee: specialRates.dt_base_fee || card.dt_base_fee,
        dt_per_mile_rate: specialRates.dt_per_mile_rate || card.dt_per_mile_rate,
        dt_per_hour_rate: specialRates.dt_per_hour_rate || card.dt_per_hour_rate,
        dt_min_miles: specialRates.dt_min_miles || card.dt_min_miles,
        dt_min_hours: specialRates.dt_min_hours || card.dt_min_hours,
        airport_transfer_flat_rate: specialRates.airport_transfer_flat_rate || card.airport_transfer_flat_rate,
        charter_hourly_rate: specialRates.charter_hourly_rate || card.charter_hourly_rate,
        charter_min_hours: specialRates.charter_min_hours || card.charter_min_hours,
        pricing_model_type: specialRates.pricing_model_type || card.pricing_model_type || 'P2P',
      };
    });

    // Format response to match what the frontend expects
    const formattedResults = transformedResults.map(card => ({
      success: true,
      data: card
    }));

    console.log('POST /api/affiliate/rate-cards: Returning formatted results:', formattedResults);
    return NextResponse.json(formattedResults, { status: 200 });
  } catch (error: any) {
    console.error('POST /api/affiliate/rate-cards: Catch-all error.', { error: error.message, stack: error.stack });
    if (error instanceof SyntaxError) {
      return NextResponse.json({ error: 'Invalid JSON payload.' }, { status: 400 });
    }
    return NextResponse.json({ error: 'An unexpected error occurred.' }, { status: 500 });
  }
}