import { NextRequest, NextResponse } from "next/server";
import * as z from "zod";
import {
  createAuthenticatedSupabaseClient,
  requireAuth,
} from "@/lib/auth/server";

// Schema for company creation, mirroring the frontend form
const companyCreationSchema = z.object({
  companyName: z.string().min(2).max(100),
  contactEmail: z.string().email(),
  contactPhone: z.string().min(10).max(20),
  addressLine1: z.string().min(5).max(150),
  addressLine2: z.string().max(150).optional().or(z.literal("")),
  city: z.string().min(2).max(50),
  stateProvince: z.string().min(2).max(50),
  postalCode: z.string().min(3).max(20),
  country: z.string().min(2).max(50),
  website: z.string().url().optional().or(z.literal("")),
  dba: z.string().optional().or(z.literal("")),
  ownerName: z.string().optional().or(z.literal("")),
  yearEstablished: z.string().optional().or(z.literal("")),
  federalTaxId: z.string().optional().or(z.literal("")),
});

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth();
    if (!user) {
      console.error("POST /api/affiliate/companies: Not authenticated");
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    const data = await request.json();
    console.log(
      "POST /api/affiliate/companies: Received data",
      JSON.stringify(data)
    );

    // Validate input data
    const result = companyCreationSchema.safeParse(data);
    if (!result.success) {
      console.error(
        "POST /api/affiliate/companies: Validation failed",
        result.error
      );
      return NextResponse.json(
        { error: "Invalid input data", details: result.error.format() },
        { status: 400 }
      );
    }

    const companyData = result.data;

    // Create the company data object - map form fields to database fields
    const insertData = {
      name: companyData.companyName,
      email: companyData.contactEmail,
      phone: companyData.contactPhone,
      address: companyData.addressLine1,
      address_line_2: companyData.addressLine2 || null,
      city: companyData.city,
      state: companyData.stateProvince,
      zip: companyData.postalCode,
      country: companyData.country,
      website: companyData.website || null,
      owner_id: user.id,
      status: "active",
      // Additional contact fields
      contact_email: companyData.contactEmail,
      contact_phone: companyData.contactPhone,
      // These fields will be stored if they exist in the database schema
      dba: companyData.dba || null,
      owner_name: companyData.ownerName || null,
      year_established: companyData.yearEstablished || null,
      federal_tax_id: companyData.federalTaxId || null,
      // Set initial application status
      application_status: "pending",
    };

    console.log(
      "POST /api/affiliate/companies: Inserting company data",
      JSON.stringify(insertData, null, 2)
    );

    // Insert company into database
    const supabase = await createAuthenticatedSupabaseClient();
    const { data: company, error: insertError } = await supabase
      .from("affiliate_companies")
      .insert(insertData)
      .select()
      .single();

    if (insertError) {
      console.error("POST /api/affiliate/companies: Insert error", {
        error: insertError,
        code: insertError.code,
        message: insertError.message,
        details: insertError.details,
        hint: insertError.hint,
      });
      return NextResponse.json(
        {
          error: "Failed to create company",
          details: insertError.message,
          code: insertError.code,
        },
        { status: 500 }
      );
    }

    console.log(
      "POST /api/affiliate/companies: Company inserted successfully",
      company
    );

    // Create association between user and company
    console.log(
      "POST /api/affiliate/companies: Creating user-company association",
      {
        user_id: user.id,
        affiliate_id: company.id,
        role: "OWNER",
        status: "ACTIVE",
      }
    );

    // Create new association (allow multiple companies per user)
    const { data: linkData, error: linkError } = await supabase
      .from("affiliate_user_companies")
      .insert({
        user_id: user.id,
        affiliate_id: company.id,
        role: "OWNER", // Default role for company creator (must be uppercase)
        status: "ACTIVE", // Must be uppercase to match enum
      })
      .select()
      .single();

    if (linkError) {
      console.error("POST /api/affiliate/companies: Link error", {
        error: linkError,
        code: linkError.code,
        message: linkError.message,
        details: linkError.details,
        hint: linkError.hint,
      });

      // Handle duplicate association gracefully (user already associated with this company)
      if (
        linkError.code === "23505" &&
        linkError.message?.includes("unique_user_affiliate")
      ) {
        console.log(
          "POST /api/affiliate/companies: User already associated with company, continuing..."
        );
        // This is not an error - the association already exists
      } else {
        return NextResponse.json(
          {
            error: "Failed to link user to company",
            details: linkError.message,
            code: linkError.code,
          },
          { status: 500 }
        );
      }
    }

    console.log(
      "POST /api/affiliate/companies: User-company association created successfully",
      linkData
    );

    console.log("POST /api/affiliate/companies: Success", company.id);
    return NextResponse.json({
      message: "Company created successfully",
      company,
    });
  } catch (error) {
    console.error("POST /api/affiliate/companies: Unhandled error", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
