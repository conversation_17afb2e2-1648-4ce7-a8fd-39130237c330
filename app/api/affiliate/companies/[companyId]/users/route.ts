import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const VALID_ROLES_LIST_USERS = ['OWNER', 'ADMIN', 'DISPATCHER'];
const VALID_ROLES_INVITE_USER = ['OWNER', 'ADMIN'];

interface RouteContext {
  params: {
    companyId: string; // companyId from the path
  };
}

// Zod schema for inviting a user
const inviteUserSchema = z.object({
  email: z.string().email("Invalid email address"),
  role: z.enum(['DISPATCHER', 'DRIVER', 'STAFF', 'ADMIN']), // OWNER typically isn't assigned this way
});

// Re-usable authorization function (specific to validating against companyId in path and header)
async function authorizePathAndHeaderCompany(
  supabase: ReturnType<typeof createServerClient>,
  userId: string,
  companyIdFromPath: string,
  companyIdFromHeader: string | null,
  allowedRoles: string[]
) {
  if (!companyIdFromHeader) {
    console.error('authorizePathAndHeaderCompany: X-Affiliate-Company-ID header is missing');
    return { error: NextResponse.json({ error: 'X-Affiliate-Company-ID header is required' }, { status: 400 }), companyId: null, userRole: null };
  }
  if (companyIdFromPath !== companyIdFromHeader) {
    console.error('authorizePathAndHeaderCompany: Path companyId does not match X-Affiliate-Company-ID header');
    return { error: NextResponse.json({ error: 'Path companyId must match X-Affiliate-Company-ID header' }, { status: 400 }), companyId: null, userRole: null };
  }

  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  if (!uuidRegex.test(companyIdFromPath)) {
    console.error('authorizePathAndHeaderCompany: Invalid companyId format in path');
    return { error: NextResponse.json({ error: 'Invalid companyId format in path' }, { status: 400 }), companyId: null, userRole: null };
  }
  const { data: companyAccess, error: accessError } = await supabase
    .from('affiliate_user_companies')
    .select('role, status')
    .eq('user_id', userId)
    .eq('affiliate_id', companyIdFromPath) // Use companyIdFromPath as it's validated against header
    .single();

  if (accessError || !companyAccess) {
    console.error('authorizePathAndHeaderCompany: Error fetching company access or access denied:', accessError);
    return { error: NextResponse.json({ error: 'Affiliate company access denied or not found.' }, { status: 403 }), companyId: null, userRole: null };
  }

  if (companyAccess.status !== 'ACTIVE') {
    console.error('authorizePathAndHeaderCompany: User is not active in this company:', companyAccess.status);
    return { error: NextResponse.json({ error: 'User is not active in this company.' }, { status: 403 }), companyId: null, userRole: null };
  }

  if (!allowedRoles.includes(companyAccess.role)) {
    console.error('authorizePathAndHeaderCompany: User role not permitted for this operation:', companyAccess.role);
    return { error: NextResponse.json({ error: 'User role not permitted for this operation.' }, { status: 403 }), companyId: null, userRole: null };
  }

  return { error: null, companyId: companyIdFromPath, userRole: companyAccess.role };
}

// GET Handler: List users for a specific company
export async function GET(request: NextRequest, { params }: RouteContext) {
  const { companyId: companyIdFromPath } = params;
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: { /* ... cookie handlers ... */ get(name: string) { return cookieStore.get(name)?.value; }, set(name: string, value: string, options: CookieOptions) { cookieStore.set(name, value, options); }, remove(name: string, options: CookieOptions) { cookieStore.set(name, '', options); } },
    }
  );

  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'User not authenticated' }, { status: 401 });
    }

    const companyIdFromHeader = request.headers.get('X-Affiliate-Company-ID');
    const authResult = await authorizePathAndHeaderCompany(supabase, user.id, companyIdFromPath, companyIdFromHeader, VALID_ROLES_LIST_USERS);

    if (authResult.error) return authResult.error;
    const { companyId } = authResult;

    const { data: usersInCompany, error: fetchError } = await supabase
      .from('affiliate_user_companies')
      .select(`
        user_id,
        role,
        status,
        created_at,
        updated_at,
        users!inner ( email, raw_user_meta_data ) 
      `)
      .eq('affiliate_id', companyId);

    if (fetchError) {
      console.error(`Error fetching users for company ${companyId}:`, fetchError);
      return NextResponse.json({ error: 'Failed to fetch users for company.', details: fetchError.message }, { status: 500 });
    }
    
    const responseData = usersInCompany?.map(link => {
        // Handle the case where users might be an array (though !inner should make it an object)
        // This is a robust way to handle potential inconsistencies in type inference vs runtime reality.
        const userInfo = Array.isArray(link.users) ? link.users[0] : link.users;
        return {
            userId: link.user_id,
            email: userInfo?.email || 'N/A', 
            firstName: userInfo?.raw_user_meta_data?.firstName,
            lastName: userInfo?.raw_user_meta_data?.lastName,  
            roleInCompany: link.role,
            statusInCompany: link.status,
            joinedAt: link.created_at,
            lastUpdatedAt: link.updated_at
        };
    }) || [];

    return NextResponse.json(responseData);

  } catch (error: any) {
    console.error('GET /api/affiliate/companies/[companyId]/users: Catch-all error.', error);
    return NextResponse.json({ error: 'An unexpected error occurred.' }, { status: 500 });
  }
}

// POST Handler: Invite a user to a specific company
export async function POST(request: NextRequest, { params }: RouteContext) {
  const { companyId: companyIdFromPath } = params;
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: { /* ... cookie handlers ... */ get(name: string) { return cookieStore.get(name)?.value; }, set(name: string, value: string, options: CookieOptions) { cookieStore.set(name, value, options); }, remove(name: string, options: CookieOptions) { cookieStore.set(name, '', options); } },
    }
  );

  try {
    const { data: { user: invitingUser }, error: authError } = await supabase.auth.getUser();
    if (authError || !invitingUser) {
      return NextResponse.json({ error: 'User not authenticated' }, { status: 401 });
    }

    const companyIdFromHeader = request.headers.get('X-Affiliate-Company-ID');
    const authResult = await authorizePathAndHeaderCompany(supabase, invitingUser.id, companyIdFromPath, companyIdFromHeader, VALID_ROLES_INVITE_USER);

    if (authResult.error) return authResult.error;
    const { companyId } = authResult;

    const body = await request.json();
    const parsedInvite = inviteUserSchema.safeParse(body);

    if (!parsedInvite.success) {
      return NextResponse.json({ error: 'Invalid invitation data', details: parsedInvite.error.format() }, { status: 400 });
    }

    const { email: targetUserEmail, role: targetRole } = parsedInvite.data;

    // Step 1: We need to find the user_id of the targetUserEmail.
    // This requires admin privileges for the Supabase client, or a security definer function.
    // For now, let's assume the Supabase client used here has enough privilege OR
    // this part might need to be a call to a Supabase function `get_user_id_by_email(targetUserEmail)`
    // WARNING: Directly querying auth.users table for emails from a non-admin client is usually restricted.
    // This is a placeholder and might require a Supabase Function or elevated privileges.
    // A more secure way is to use Supabase admin client or specific RPC call.
    
    // --- Placeholder for fetching target user ID ---
    // This is a simplified approach. In production, you would use Supabase admin client or an RPC.
    // const { data: targetUserData, error: targetUserError } = await supabase.auth.admin.listUsers({ email: targetUserEmail });
    // if (targetUserError || !targetUserData || targetUserData.users.length === 0) {
    //   console.error('Target user not found by email or error occurred (requires admin for listUsers):', targetUserEmail, targetUserError);
    //   return NextResponse.json({ error: `User with email ${targetUserEmail} not found on the platform. Consider an invitation flow for new users.` }, { status: 404 });
    // }
    // const targetUserId = targetUserData.users[0].id;
    // --- End Placeholder ---

    // For now, we will proceed assuming targetUserId is known. This needs to be properly implemented.
    // Let's simulate an error if we cannot get user ID for now.
    // TODO: Implement a secure way to get targetUserId by email (e.g., Supabase Function)
    console.warn("Security Placeholder: The logic to fetch target user ID by email needs a secure implementation (e.g., Supabase Function or Admin API call).");
    // As a temporary measure, if you have a local setup and direct DB access, this could work with an admin client, but it's not for production client-facing APIs like this.
    // Example: (DO NOT USE IN PRODUCTION API ROUTES LIKE THIS WITHOUT ADMIN SDK & PROPER SECURITY)
    // const { data: foundUser, error: findUserError } = await supabase.from('users').select('id').eq('email', targetUserEmail).single(); // Assuming you have a public users table for lookup, which is also not ideal.
    
    // Since we cannot reliably get the target user ID here without admin rights or a dedicated function,
    // we will simulate that this is a flow for INVITING where the user might not exist yet.
    // The actual creation of the user in auth.users would happen when they accept the invitation.
    // Here, we just record the intent to invite in affiliate_user_companies, using the email as a placeholder
    // if the user_id is not yet known, OR we assume an admin has pre-created a shell user.

    // For the purpose of this exercise, we will assume the user must exist. If not, we error.
    // This check should be done via a secure mechanism (e.g. an RPC function)
    // For now, this part will likely fail or be insecure without admin client. 
    // Let's just create a placeholder for the invited user ID or make it fail if not found. This is a critical security/implementation detail.

    // A better approach: use Supabase Edge Functions for operations requiring admin privileges.
    // For now, let's assume you will handle user lookup securely. Here, we will try inserting into affiliate_user_companies.
    // If the user_id needs to be pre-fetched, this invite logic becomes more complex.
    // The current migration links user_id NOT NULL. So user must exist in auth.users.

    // Simulating failure to find user as direct lookup is problematic here.
    // A real implementation would need a Supabase Function `get_user_by_email` or use admin SDK in a secure context.
    return NextResponse.json({ error: "User lookup by email is not implemented securely in this context. This feature requires a Supabase Edge Function or Admin SDK usage for user lookup before inviting." }, { status: 501 }); // 501 Not Implemented

    /* --- Actual insert logic if targetUserId was found securely ---
    const { data: existingLink, error: linkCheckError } = await supabase
      .from('affiliate_user_companies')
      .select('id, status')
      .eq('user_id', targetUserId) // Assume targetUserId is fetched
      .eq('affiliate_id', companyId)
      .maybeSingle();

    if (linkCheckError && linkCheckError.code !== 'PGRST116') { // Ignore 'PGRST116' (0 rows)
        console.error('Error checking existing user link:', linkCheckError);
        return NextResponse.json({ error: 'Could not verify existing user link', details: linkCheckError.message }, { status: 500 });
    }

    if (existingLink) {
        if (existingLink.status === 'INVITED') {
            return NextResponse.json({ message: 'User has already been invited to this company.' }, { status: 200 });
        } else if (existingLink.status === 'ACTIVE') {
            return NextResponse.json({ error: 'User is already an active member of this company.' }, { status: 409 }); // 409 Conflict
        }
        // Potentially handle other statuses like DISABLED or REMOVED if re-inviting is allowed
    }

    const { data: newInvite, error: insertError } = await supabase
      .from('affiliate_user_companies')
      .insert({
        user_id: targetUserId, // Assume targetUserId is fetched
        affiliate_id: companyId,
        role: targetRole,
        status: 'INVITED' // Default status for new invite
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error inviting user to company:', insertError);
      if (insertError.code === '23505') { // unique_violation (user_id, affiliate_id)
          return NextResponse.json({ error: 'This user is already associated or invited to this company.' }, { status: 409 });
      }
      return NextResponse.json({ error: 'Failed to invite user to company.', details: insertError.message }, { status: 500 });
    }
    // TODO: Trigger invitation email to targetUserEmail with a link to accept.

    return NextResponse.json({ message: 'User invited successfully.', inviteDetails: newInvite }, { status: 201 });
    */

  } catch (error: any) {
    console.error('POST /api/affiliate/companies/[companyId]/users: Catch-all error.', error);
    if (error instanceof SyntaxError) {
      return NextResponse.json({ error: 'Invalid JSON payload.' }, { status: 400 });
    }
    return NextResponse.json({ error: 'An unexpected error occurred.' }, { status: 500 });
  }
} 