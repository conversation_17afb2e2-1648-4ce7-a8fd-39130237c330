import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const VALID_ROLES_MANAGE_USERS = ['OWNER', 'ADMIN']; // Roles that can update/delete other users

interface RouteContext {
  params: {
    companyId: string; // companyId from the path
    userId: string;    // userId (of the user being managed) from the path
  };
}

// Zod schema for updating a user's role/status in a company
const updateUserInCompanySchema = z.object({
  role: z.enum(['ADMIN', 'DISPATCHER', 'DRIVER', 'STAFF']).optional(), // Owner role cannot be assigned/changed here
  status: z.enum(['ACTIVE', 'DISABLED', 'PENDING_VERIFICATION', 'INVITED']).optional(), // REMOVED is handled by DELETE
}).refine(data => Object.keys(data).length > 0, {
  message: "At least one field (role or status) must be provided for update",
});

// Re-usable authorization function (validates acting user against company in path and header)
async function authorizePathAndHeaderCompany(
  supabase: ReturnType<typeof createServerClient>,
  actingUserId: string,
  companyIdFromPath: string,
  companyIdFromHeader: string | null,
  allowedRolesForAction: string[]
) {
  if (!companyIdFromHeader) {
    return { error: NextResponse.json({ error: 'X-Affiliate-Company-ID header is required' }, { status: 400 }), companyId: null, actingUserRole: null };
  }
  if (companyIdFromPath !== companyIdFromHeader) {
    return { error: NextResponse.json({ error: 'Path companyId must match X-Affiliate-Company-ID header' }, { status: 400 }), companyId: null, actingUserRole: null };
  }
  const uuidRegex = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  if (!uuidRegex.test(companyIdFromPath)) {
    return { error: NextResponse.json({ error: 'Invalid companyId format in path' }, { status: 400 }), companyId: null, actingUserRole: null };
  }
  const { data: actingUserAccess, error: accessError } = await supabase
    .from('affiliate_user_companies')
    .select('role, status')
    .eq('user_id', actingUserId)
    .eq('affiliate_id', companyIdFromPath)
    .single();

  if (accessError || !actingUserAccess) {
    return { error: NextResponse.json({ error: 'Affiliate company access denied or not found for acting user.' }, { status: 403 }), companyId: null, actingUserRole: null };
  }
  if (actingUserAccess.status !== 'ACTIVE') {
    return { error: NextResponse.json({ error: 'Acting user is not active in this company.' }, { status: 403 }), companyId: null, actingUserRole: null };
  }
  if (!allowedRolesForAction.includes(actingUserAccess.role)) {
    return { error: NextResponse.json({ error: 'Acting user role not permitted for this operation.' }, { status: 403 }), companyId: null, actingUserRole: null };
  }
  return { error: null, companyId: companyIdFromPath, actingUserRole: actingUserAccess.role };
}

// PUT Handler: Update a user's role or status in a company
export async function PUT(request: NextRequest, { params }: RouteContext) {
  const { companyId: companyIdFromPath, userId: targetUserIdFromPath } = params;
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) { // Shorthand method definition
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) { // Shorthand method definition
          try { cookieStore.set(name, value, options); } catch (error) { /* Readonly, ignore */ }
        },
        remove(name: string, options: CookieOptions) { // Shorthand method definition
          try { cookieStore.set(name, '', options); } catch (error) { /* Readonly, ignore */ }
        },
      },
    }
  );

  try {
    const { data: { user: actingUser }, error: authError } = await supabase.auth.getUser();
    if (authError || !actingUser) {
      return NextResponse.json({ error: 'User not authenticated' }, { status: 401 });
    }
    const uuidRegexValidation = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
    if (!uuidRegexValidation.test(targetUserIdFromPath)) {
        return NextResponse.json({ error: 'Invalid target userId format in path' }, { status: 400 });
    }

    const companyIdFromHeader = request.headers.get('X-Affiliate-Company-ID');
    const authResult = await authorizePathAndHeaderCompany(supabase, actingUser.id, companyIdFromPath, companyIdFromHeader, VALID_ROLES_MANAGE_USERS);
    if (authResult.error) return authResult.error;
    const { companyId, actingUserRole } = authResult;

    const body = await request.json();
    const parsedUpdate = updateUserInCompanySchema.safeParse(body);
    if (!parsedUpdate.success) {
      return NextResponse.json({ error: 'Invalid update data', details: parsedUpdate.error.format() }, { status: 400 });
    }
    const { role: newRole, status: newStatus } = parsedUpdate.data;

    // Business logic: Prevent self-update of role to OWNER or status if an Admin is trying to demote an Owner.
    // Fetch the target user's current role in the company.
    const { data: targetUserLink, error: targetLinkError } = await supabase
      .from('affiliate_user_companies')
      .select('role, user_id')
      .eq('affiliate_id', companyId)
      .eq('user_id', targetUserIdFromPath)
      .single();

    if (targetLinkError || !targetUserLink) {
      return NextResponse.json({ error: 'Target user not found in this company.' }, { status: 404 });
    }

    // Rule: An ADMIN cannot change an OWNER's role or status.
    if (targetUserLink.role === 'OWNER' && actingUserRole === 'ADMIN') {
        if (newRole) { 
             return NextResponse.json({ error: 'Admins cannot change the role of an Owner.' }, { status: 403 });
        }
        if (newStatus && newStatus !== 'ACTIVE') { // Trying to disable Owner by Admin
            // Potentially allow if newStatus is 'ACTIVE', but disallow changing from ACTIVE to something else
            // For simplicity: an Admin cannot change an Owner's status from ACTIVE.
            // This logic might need refinement based on exact business rules for owner management.
            const {data: currentOwner, error: ownerCheckErr} = await supabase.from('affiliate_user_companies').select('status').eq('user_id', targetUserIdFromPath).eq('affiliate_id', companyId).single();
            if(ownerCheckErr) return NextResponse.json({ error: 'Could not verify owner status for update.' }, { status: 500 });
            if(currentOwner.status === 'ACTIVE'){
                 return NextResponse.json({ error: 'Admins cannot change the status of an active Owner.' }, { status: 403 });
            }
        }
    }
    // Rule: A user (even Owner) cannot change their own role to OWNER using this endpoint (must be handled by a separate process or initial setup)
    // This is implicitly handled because 'OWNER' is not an option in updateUserInCompanySchema.role
    // if (actingUser.id === targetUserIdFromPath && newRole === 'OWNER') {
    //   return NextResponse.json({ error: 'Cannot change own role to OWNER via this endpoint.' }, { status: 403 });
    // }
    // Rule: Cannot change role of target user to OWNER if acting user is not OWNER (redundant if schema prevents role=OWNER)
    // This is implicitly handled because 'OWNER' is not an option in updateUserInCompanySchema.role
    // if (newRole === 'OWNER' && actingUserRole !== 'OWNER') {
    //      return NextResponse.json({ error: 'Only an Owner can assign another user as Owner (typically done via specific invite). Role "OWNER" not assignable here.' }, { status: 403 });
    // }
    
    const updatePayload: { role?: string; status?: string } = {};
    if (newRole) updatePayload.role = newRole;
    if (newStatus) updatePayload.status = newStatus;

    const { data: updatedLink, error: updateError } = await supabase
      .from('affiliate_user_companies')
      .update(updatePayload)
      .eq('affiliate_id', companyId)
      .eq('user_id', targetUserIdFromPath)
      .select()
      .single();

    if (updateError) {
      console.error(`Error updating user ${targetUserIdFromPath} in company ${companyId}:`, updateError);
      return NextResponse.json({ error: 'Failed to update user in company.', details: updateError.message }, { status: 500 });
    }

    return NextResponse.json(updatedLink);

  } catch (error: any) {
    console.error('PUT /api/affiliate/companies/[companyId]/users/[userId]: Catch-all error.', error);
    return NextResponse.json({ error: 'An unexpected error occurred.' }, { status: 500 });
  }
}

// DELETE Handler: Remove a user from a company
export async function DELETE(request: NextRequest, { params }: RouteContext) {
  const { companyId: companyIdFromPath, userId: targetUserIdFromPath } = params;
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) { // Shorthand method definition
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) { // Shorthand method definition
          try { cookieStore.set(name, value, options); } catch (error) { /* Readonly, ignore */ }
        },
        remove(name: string, options: CookieOptions) { // Shorthand method definition
          try { cookieStore.set(name, '', options); } catch (error) { /* Readonly, ignore */ }
        },
      },
    }
  );

  try {
    const { data: { user: actingUser }, error: authError } = await supabase.auth.getUser();
    if (authError || !actingUser) {
      return NextResponse.json({ error: 'User not authenticated' }, { status: 401 });
    }
    const uuidRegexValidationDel = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
    if (!uuidRegexValidationDel.test(targetUserIdFromPath)) {
        return NextResponse.json({ error: 'Invalid target userId format in path' }, { status: 400 });
    }

    const companyIdFromHeader = request.headers.get('X-Affiliate-Company-ID');
    const authResult = await authorizePathAndHeaderCompany(supabase, actingUser.id, companyIdFromPath, companyIdFromHeader, VALID_ROLES_MANAGE_USERS);
    if (authResult.error) return authResult.error;
    const { companyId, actingUserRole } = authResult;

    // Fetch the target user's current role to apply business logic.
    const { data: targetUserLink, error: targetLinkError } = await supabase
      .from('affiliate_user_companies')
      .select('role, user_id')
      .eq('affiliate_id', companyId)
      .eq('user_id', targetUserIdFromPath)
      .single();

    if (targetLinkError || !targetUserLink) {
      return NextResponse.json({ error: 'Target user not found in this company.' }, { status: 404 });
    }

    // Business Logic: Prevent an Owner from being removed by an Admin.
    if (targetUserLink.role === 'OWNER' && actingUserRole === 'ADMIN') {
      return NextResponse.json({ error: 'Admins cannot remove an Owner from the company.' }, { status: 403 });
    }
    // Business Logic: Prevent self-removal if user is the sole Owner.
    if (targetUserLink.role === 'OWNER' && actingUser.id === targetUserIdFromPath) {
      const { data: owners, error: ownerCountError } = await supabase
        .from('affiliate_user_companies')
        .select('user_id', { count: 'exact' })
        .eq('affiliate_id', companyId)
        .eq('role', 'OWNER');
      if (ownerCountError) {
        return NextResponse.json({ error: 'Could not verify owner count.' }, { status: 500 });
      }
      if (owners && owners.length === 1) {
        return NextResponse.json({ error: 'Cannot remove the sole Owner of the company. Transfer ownership first.' }, { status: 403 });
      }
    }

    // Perform deletion or set status to 'REMOVED'
    // For this example, we will delete the record.
    const { error: deleteError, count } = await supabase
      .from('affiliate_user_companies')
      .delete({ count: 'exact' })
      .eq('affiliate_id', companyId)
      .eq('user_id', targetUserIdFromPath);

    if (deleteError) {
      console.error(`Error deleting user ${targetUserIdFromPath} from company ${companyId}:`, deleteError);
      return NextResponse.json({ error: 'Failed to remove user from company.', details: deleteError.message }, { status: 500 });
    }
    if (count === 0) {
        return NextResponse.json({ error: 'User not found in company or already removed.' }, { status: 404 });
    }

    return NextResponse.json({ message: 'User removed from company successfully.' });

  } catch (error: any) {
    console.error('DELETE /api/affiliate/companies/[companyId]/users/[userId]: Catch-all error.', error);
    return NextResponse.json({ error: 'An unexpected error occurred.' }, { status: 500 });
  }
} 