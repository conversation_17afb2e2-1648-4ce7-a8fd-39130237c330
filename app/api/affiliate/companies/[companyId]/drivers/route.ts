import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/affiliate/companies/[companyId]/drivers
 * Fetch all drivers for a specific affiliate company
 */
export async function GET(
  request: NextRequest, 
  { params }: { params: { companyId: string } }
) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options);
        },
      },
    }
  );

  // Enforce multi-tenant security: require X-Affiliate-Company-ID header to match param
  const headerCompanyId = request.headers.get('x-affiliate-company-id');
  if (!headerCompanyId || headerCompanyId !== params.companyId) {
    return NextResponse.json({ error: 'Missing or mismatched X-Affiliate-Company-ID header.' }, { status: 403 });
  }

  // Auth check
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    return NextResponse.json({ error: 'User not authenticated' }, { status: 401 });
  }

  try {
    // Check if user has access to this company
    const { data: userCompany, error: userCompanyError } = await supabase
      .from('affiliate_user_companies')
      .select('*')
      .eq('user_id', user.id)
      .eq('affiliate_id', params.companyId)
      .maybeSingle();

    if (userCompanyError) {
      console.error('Error checking user company access:', userCompanyError);
      return NextResponse.json({ error: 'Error checking company access' }, { status: 500 });
    }

    if (!userCompany) {
      return NextResponse.json({ error: 'You do not have access to this company' }, { status: 403 });
    }

    // Fetch drivers for the company
    const { data: drivers, error: driversError } = await supabase
      .from('affiliate_drivers')
      .select('*')
      .eq('affiliate_id', params.companyId);

    if (driversError) {
      console.error('Error fetching drivers:', driversError);
      return NextResponse.json({ error: 'Failed to fetch drivers' }, { status: 500 });
    }

    // Transform the data to match the client-side interface
    const transformedDrivers = drivers.map(driver => ({
      id: driver.id,
      firstName: driver.first_name,
      lastName: driver.last_name,
      email: driver.email,
      phone: driver.phone,
      status: driver.status,
      licenseInfo: driver.license_info,
      metrics: driver.metrics,
      createdAt: driver.created_at,
      updatedAt: driver.updated_at,
    }));

    return NextResponse.json(transformedDrivers);
  } catch (error) {
    console.error('Error in GET /api/affiliate/companies/[companyId]/drivers:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/affiliate/companies/[companyId]/drivers
 * Create a new driver for a specific affiliate company
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { companyId: string } }
) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, '', options);
        },
      },
    }
  );

  // Enforce multi-tenant security: require X-Affiliate-Company-ID header to match param
  const headerCompanyId = request.headers.get('x-affiliate-company-id');
  if (!headerCompanyId || headerCompanyId !== params.companyId) {
    return NextResponse.json({ error: 'Missing or mismatched X-Affiliate-Company-ID header.' }, { status: 403 });
  }

  // Auth check
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    return NextResponse.json({ error: 'User not authenticated' }, { status: 401 });
  }

  try {
    // Check if user has access to this company with appropriate role
    const { data: userCompany, error: userCompanyError } = await supabase
      .from('affiliate_user_companies')
      .select('*')
      .eq('user_id', user.id)
      .eq('affiliate_id', params.companyId)
      .maybeSingle();

    if (userCompanyError) {
      console.error('Error checking user company access:', userCompanyError);
      return NextResponse.json({ error: 'Error checking company access' }, { status: 500 });
    }

    if (!userCompany) {
      return NextResponse.json({ error: 'You do not have access to this company' }, { status: 403 });
    }

    // Check if user has permission to add drivers (owner, admin, or manager)
    if (!['OWNER', 'ADMIN', 'MANAGER'].includes(userCompany.role)) {
      return NextResponse.json({ error: 'You do not have permission to add drivers' }, { status: 403 });
    }

    // Parse request body
    const body = await request.json();

    // Validate required fields
    if (!body.firstName || !body.lastName || !body.email || !body.phone) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Add the new driver
    const { data: newDriver, error: createError } = await supabase
      .from('affiliate_drivers')
      .insert({
        affiliate_id: params.companyId,
        first_name: body.firstName,
        last_name: body.lastName,
        email: body.email,
        phone: body.phone,
        status: 'pending',
        license_info: body.licenseInfo || {},
        created_by: user.id,
        updated_by: user.id,
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating driver:', createError);
      return NextResponse.json({ error: 'Failed to create driver' }, { status: 500 });
    }

    // Transform to client format
    const transformedDriver = {
      id: newDriver.id,
      firstName: newDriver.first_name,
      lastName: newDriver.last_name,
      email: newDriver.email,
      phone: newDriver.phone,
      status: newDriver.status,
      licenseInfo: newDriver.license_info,
      createdAt: newDriver.created_at,
      updatedAt: newDriver.updated_at,
    };

    return NextResponse.json(transformedDriver);
  } catch (error) {
    console.error('Error in POST /api/affiliate/companies/[companyId]/drivers:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
} 