import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

export async function GET(
  request: Request,
  { params }: { params: { companyId: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // Get user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get documents for the company
    const { data: documents, error: documentsError } = await supabase
      .from('company_documents')
      .select('*')
      .eq('company_id', params.companyId)
      .order('created_at', { ascending: false });

    if (documentsError) {
      console.error('Error fetching documents:', documentsError);
      return NextResponse.json({ error: 'Failed to fetch documents' }, { status: 500 });
    }

    return NextResponse.json(documents);
  } catch (error) {
    console.error('Error in GET /api/affiliate/companies/[companyId]/documents:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(
  request: Request,
  { params }: { params: { companyId: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // Get user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const documentType = formData.get('documentType') as string;
    const notes = formData.get('notes') as string;

    if (!file || !documentType) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Check if user has permission to upload documents
    const { data: userCompany, error: userCompanyError } = await supabase
      .from('affiliate_user_companies')
      .select('role')
      .eq('company_id', params.companyId)
      .eq('user_id', session.user.id)
      .single();

    if (userCompanyError || !userCompany || !['OWNER', 'ADMIN'].includes(userCompany.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Upload file to storage
    const fileExt = file.name.split('.').pop();
    const fileName = `${params.companyId}/${Date.now()}.${fileExt}`;
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('company_documents')
      .upload(fileName, file);

    if (uploadError) {
      console.error('Error uploading file:', uploadError);
      return NextResponse.json({ error: 'Failed to upload file' }, { status: 500 });
    }

    // Create document record
    const { data: document, error: documentError } = await supabase
      .from('company_documents')
      .insert({
        company_id: params.companyId,
        document_type: documentType,
        file_name: file.name,
        file_path: fileName,
        file_size: file.size,
        mime_type: file.type,
        notes,
        created_by: session.user.id,
        updated_by: session.user.id
      })
      .select()
      .single();

    if (documentError) {
      // Clean up uploaded file if document creation fails
      await supabase.storage
        .from('company_documents')
        .remove([fileName]);
      
      console.error('Error creating document record:', documentError);
      return NextResponse.json({ error: 'Failed to create document record' }, { status: 500 });
    }

    return NextResponse.json(document);
  } catch (error) {
    console.error('Error in POST /api/affiliate/companies/[companyId]/documents:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { companyId: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // Get user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get document ID from URL
    const url = new URL(request.url);
    const documentId = url.searchParams.get('documentId');
    if (!documentId) {
      return NextResponse.json({ error: 'Missing document ID' }, { status: 400 });
    }

    // Check if user has permission to delete documents
    const { data: userCompany, error: userCompanyError } = await supabase
      .from('affiliate_user_companies')
      .select('role')
      .eq('company_id', params.companyId)
      .eq('user_id', session.user.id)
      .single();

    if (userCompanyError || !userCompany || !['OWNER', 'ADMIN'].includes(userCompany.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Get document details
    const { data: document, error: documentError } = await supabase
      .from('company_documents')
      .select('file_path')
      .eq('id', documentId)
      .single();

    if (documentError) {
      console.error('Error fetching document:', documentError);
      return NextResponse.json({ error: 'Document not found' }, { status: 404 });
    }

    // Delete document record (this will trigger the storage cleanup)
    const { error: deleteError } = await supabase
      .from('company_documents')
      .delete()
      .eq('id', documentId);

    if (deleteError) {
      console.error('Error deleting document:', deleteError);
      return NextResponse.json({ error: 'Failed to delete document' }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE /api/affiliate/companies/[companyId]/documents:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: { companyId: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies });
    
    // Get user session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get document ID and update data from request body
    const { documentId, status, verificationNotes } = await request.json();
    if (!documentId) {
      return NextResponse.json({ error: 'Missing document ID' }, { status: 400 });
    }

    // Check if user has permission to update documents
    const { data: userCompany, error: userCompanyError } = await supabase
      .from('affiliate_user_companies')
      .select('role')
      .eq('company_id', params.companyId)
      .eq('user_id', session.user.id)
      .single();

    if (userCompanyError || !userCompany || !['OWNER', 'ADMIN'].includes(userCompany.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Update document
    const { data: document, error: updateError } = await supabase
      .from('company_documents')
      .update({
        status,
        verification_notes: verificationNotes,
        verified_by: session.user.id,
        verified_at: new Date().toISOString(),
        updated_by: session.user.id
      })
      .eq('id', documentId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating document:', updateError);
      return NextResponse.json({ error: 'Failed to update document' }, { status: 500 });
    }

    return NextResponse.json(document);
  } catch (error) {
    console.error('Error in PATCH /api/affiliate/companies/[companyId]/documents:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 