import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { z } from 'zod'

const createDispatcherSchema = z.object({
  email: z.string().email(),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  selectedCompanies: z.array(z.string()).optional()
})

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const body = await request.json()
    const validationResult = createDispatcherSchema.safeParse(body)
    
    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Invalid request data',
        details: validationResult.error.format()
      }, { status: 400 })
    }

    const { email, firstName, lastName, selectedCompanies } = validationResult.data

    // Check if user owns the companies they're trying to assign
    if (selectedCompanies && selectedCompanies.length > 0) {
      const { data: ownedCompanies, error: companiesError } = await supabase
        .from('affiliate_companies')
        .select('id')
        .eq('owner_id', user.id)
        .in('id', selectedCompanies)

      if (companiesError) {
        console.error('Error checking company ownership:', companiesError)
        return NextResponse.json({ error: 'Failed to verify company ownership' }, { status: 500 })
      }

      if (ownedCompanies.length !== selectedCompanies.length) {
        return NextResponse.json({ error: 'You can only assign dispatchers to companies you own' }, { status: 403 })
      }
    }

    // Generate a temporary password
    const tempPassword = Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8)

    // Create the dispatcher user account
    const { data: authData, error: createUserError } = await supabase.auth.admin.createUser({
      email,
      password: tempPassword,
      email_confirm: true,
      user_metadata: {
        first_name: firstName,
        last_name: lastName,
        full_name: `${firstName} ${lastName}`,
        user_type: 'dispatcher'
      }
    })

    if (createUserError) {
      console.error('Error creating dispatcher user:', createUserError)
      return NextResponse.json({ error: 'Failed to create dispatcher account' }, { status: 500 })
    }

    if (!authData.user) {
      return NextResponse.json({ error: 'Failed to create dispatcher account' }, { status: 500 })
    }

    // Create profile record
    const { error: profileError } = await supabase
      .from('profiles')
      .insert({
        id: authData.user.id,
        email,
        first_name: firstName,
        last_name: lastName,
        full_name: `${firstName} ${lastName}`,
        role: 'dispatcher'
      })

    if (profileError) {
      console.error('Error creating dispatcher profile:', profileError)
      // Don't fail the request, just log the error
    }

    // Get user's companies if no specific companies selected
    let companiesToAssign = selectedCompanies || []
    
    if (!selectedCompanies || selectedCompanies.length === 0) {
      const { data: userCompanies, error: userCompaniesError } = await supabase
        .from('affiliate_companies')
        .select('id')
        .eq('owner_id', user.id)

      if (userCompaniesError) {
        console.error('Error fetching user companies:', userCompaniesError)
        return NextResponse.json({ error: 'Failed to assign companies' }, { status: 500 })
      }

      companiesToAssign = userCompanies.map(c => c.id)
    }

    // Link dispatcher to companies
    if (companiesToAssign.length > 0) {
      const companyLinks = companiesToAssign.map(companyId => ({
        user_id: authData.user.id,
        affiliate_id: companyId,
        role: 'DISPATCHER',
        status: 'ACTIVE'
      }))

      const { error: linkError } = await supabase
        .from('affiliate_user_companies')
        .insert(companyLinks)

      if (linkError) {
        console.error('Error linking dispatcher to companies:', linkError)
        return NextResponse.json({ error: 'Failed to assign companies to dispatcher' }, { status: 500 })
      }
    }

    // TODO: Send email with login credentials
    // For now, we'll just return the temporary password in the response
    // In production, this should be sent via email

    return NextResponse.json({
      message: 'Dispatcher created successfully',
      dispatcher: {
        id: authData.user.id,
        email,
        firstName,
        lastName,
        companies: companiesToAssign,
        tempPassword // Remove this in production
      }
    })

  } catch (error) {
    console.error('Error creating dispatcher:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's companies
    const { data: userCompanies, error: companiesError } = await supabase
      .from('affiliate_companies')
      .select('id')
      .eq('owner_id', user.id)

    if (companiesError) {
      console.error('Error fetching user companies:', companiesError)
      return NextResponse.json({ error: 'Failed to fetch companies' }, { status: 500 })
    }

    const companyIds = userCompanies.map(c => c.id)

    if (companyIds.length === 0) {
      return NextResponse.json({ dispatchers: [] })
    }

    // Get dispatchers linked to user's companies
    const { data: dispatcherLinks, error: linksError } = await supabase
      .from('affiliate_user_companies')
      .select(`
        user_id,
        affiliate_id,
        role,
        status,
        profiles!inner(
          id,
          email,
          first_name,
          last_name,
          role,
          created_at
        )
      `)
      .in('affiliate_id', companyIds)
      .eq('role', 'DISPATCHER')

    if (linksError) {
      console.error('Error fetching dispatchers:', linksError)
      return NextResponse.json({ error: 'Failed to fetch dispatchers' }, { status: 500 })
    }

    // Group dispatchers by user and collect their companies
    const dispatchersMap = new Map()
    
    dispatcherLinks.forEach((link: any) => {
      const profile = link.profiles
      if (!dispatchersMap.has(profile.id)) {
        dispatchersMap.set(profile.id, {
          id: profile.id,
          email: profile.email,
          first_name: profile.first_name,
          last_name: profile.last_name,
          status: link.status.toLowerCase(),
          created_at: profile.created_at,
          companies: []
        })
      }
      dispatchersMap.get(profile.id).companies.push(link.affiliate_id)
    })

    const dispatchers = Array.from(dispatchersMap.values())

    return NextResponse.json({ dispatchers })

  } catch (error) {
    console.error('Error fetching dispatchers:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
