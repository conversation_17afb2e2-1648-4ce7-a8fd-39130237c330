import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get affiliate company ID for the user
    const { data: affiliateUser, error: affiliateError } = await supabase
      .from('affiliate_user_companies')
      .select('company_id')
      .eq('user_id', user.id)
      .single();

    if (affiliateError || !affiliateUser) {
      return NextResponse.json({ error: 'Affiliate not found' }, { status: 404 });
    }

    // Get vehicle exclusions for this affiliate
    const { data: exclusions, error: exclusionsError } = await supabase
      .from('affiliate_vehicle_exclusions')
      .select('*')
      .eq('affiliate_company_id', affiliateUser.company_id)
      .order('created_at', { ascending: false });

    if (exclusionsError) {
      console.error('Error fetching vehicle exclusions:', exclusionsError);
      return NextResponse.json({ error: 'Failed to fetch exclusions' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      exclusions: exclusions || []
    });

  } catch (error) {
    console.error('Error in vehicle exclusions GET:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get affiliate company ID for the user
    const { data: affiliateUser, error: affiliateError } = await supabase
      .from('affiliate_user_companies')
      .select('company_id')
      .eq('user_id', user.id)
      .single();

    if (affiliateError || !affiliateUser) {
      return NextResponse.json({ error: 'Affiliate not found' }, { status: 404 });
    }

    // Parse request body
    const { vehicle_type, reason } = await request.json();

    if (!vehicle_type) {
      return NextResponse.json({ error: 'Vehicle type is required' }, { status: 400 });
    }

    // Normalize vehicle type for consistent storage
    const normalizedVehicleType = vehicle_type.toLowerCase().trim();

    // Insert or update vehicle exclusion
    const { data: exclusion, error: insertError } = await supabase
      .from('affiliate_vehicle_exclusions')
      .upsert({
        affiliate_company_id: affiliateUser.company_id,
        vehicle_type: normalizedVehicleType,
        reason: reason || "We don't carry this vehicle in our fleet",
        excluded_at: new Date().toISOString()
      }, {
        onConflict: 'affiliate_company_id,vehicle_type'
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error creating vehicle exclusion:', insertError);
      return NextResponse.json({ error: 'Failed to create exclusion' }, { status: 500 });
    }

    console.log(`Vehicle exclusion created: ${affiliateUser.company_id} excluded ${normalizedVehicleType}`);

    return NextResponse.json({
      success: true,
      exclusion,
      message: `You will no longer receive offers for ${vehicle_type} vehicles`
    });

  } catch (error) {
    console.error('Error in vehicle exclusions POST:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const cookieStore = cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value;
          },
        },
      }
    );

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get affiliate company ID for the user
    const { data: affiliateUser, error: affiliateError } = await supabase
      .from('affiliate_user_companies')
      .select('company_id')
      .eq('user_id', user.id)
      .single();

    if (affiliateError || !affiliateUser) {
      return NextResponse.json({ error: 'Affiliate not found' }, { status: 404 });
    }

    // Get vehicle_type from query params
    const { searchParams } = new URL(request.url);
    const vehicleType = searchParams.get('vehicle_type');

    if (!vehicleType) {
      return NextResponse.json({ error: 'Vehicle type is required' }, { status: 400 });
    }

    const normalizedVehicleType = vehicleType.toLowerCase().trim();

    // Delete vehicle exclusion
    const { error: deleteError } = await supabase
      .from('affiliate_vehicle_exclusions')
      .delete()
      .eq('affiliate_company_id', affiliateUser.company_id)
      .eq('vehicle_type', normalizedVehicleType);

    if (deleteError) {
      console.error('Error deleting vehicle exclusion:', deleteError);
      return NextResponse.json({ error: 'Failed to delete exclusion' }, { status: 500 });
    }

    console.log(`Vehicle exclusion removed: ${affiliateUser.company_id} can now receive ${normalizedVehicleType} offers`);

    return NextResponse.json({
      success: true,
      message: `You will now receive offers for ${vehicleType} vehicles again`
    });

  } catch (error) {
    console.error('Error in vehicle exclusions DELETE:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
