import { createServerClient, type CookieOptions } from "@supabase/ssr";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

/**
 * POST /api/affiliate/offers/[offerId]/accept
 * Accept a quote offer for an affiliate company
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { offerId: string } }
) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, "", options);
        },
      },
    }
  );

  // Auth check
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();
  if (userError || !user) {
    return NextResponse.json(
      { error: "User not authenticated" },
      { status: 401 }
    );
  }

  try {
    const { offerId } = params;

    console.log(
      `[Accept Offer] Starting accept process for offer: ${offerId}, user: ${user.id}`
    );

    if (!offerId) {
      return NextResponse.json(
        { error: "Offer ID is required" },
        { status: 400 }
      );
    }

    // Get the offer details to check permissions
    const { data: offer, error: offerError } = await supabase
      .from("quote_affiliate_offers")
      .select("*")
      .eq("id", offerId)
      .single();

    if (offerError) {
      console.error("Error fetching offer:", offerError);
      return NextResponse.json(
        { error: "Failed to fetch offer details" },
        { status: 500 }
      );
    }

    if (!offer) {
      return NextResponse.json({ error: "Offer not found" }, { status: 404 });
    }

    console.log(`[Accept Offer] Found offer:`, {
      id: offer.id,
      status: offer.status,
      company_id: offer.company_id,
      expires_at: offer.expires_at,
      isExpired: new Date(offer.expires_at) < new Date(),
    });

    // Check if user has access to this affiliate company
    const { data: userCompany, error: userCompanyError } = await supabase
      .from("affiliate_user_companies")
      .select("*")
      .eq("user_id", user.id)
      .eq("affiliate_id", offer.company_id)
      .maybeSingle();

    if (userCompanyError) {
      console.error("Error checking user company access:", userCompanyError);
      return NextResponse.json(
        { error: "Error checking company access" },
        { status: 500 }
      );
    }

    if (!userCompany) {
      return NextResponse.json(
        { error: "You do not have access to this affiliate company" },
        { status: 403 }
      );
    }

    // Check if offer is in a valid state for acceptance
    const validStatuses = ["pending", "PENDING", "countered", "COUNTERED"];
    if (!validStatuses.includes(offer.status)) {
      return NextResponse.json(
        {
          error: `Cannot accept an offer with status "${offer.status}"`,
        },
        { status: 400 }
      );
    }

    // Check if the offer is expired
    const now = new Date();
    const expiresAt = new Date(offer.expires_at);
    if (expiresAt < now) {
      return NextResponse.json(
        { error: "This offer has expired" },
        { status: 400 }
      );
    }

    // Start a transaction to update the offer and quote
    const { data: updatedOffer, error: updateError } = await supabase
      .from("quote_affiliate_offers")
      .update({
        status: "ACCEPTED",
        updated_at: new Date().toISOString(),
        updated_by: user.id,
      })
      .eq("id", offerId)
      .select()
      .single();

    if (updateError) {
      console.error("Error updating offer:", updateError);
      console.error("Update details:", {
        offerId,
        userId: user.id,
        offerStatus: offer.status,
        updateData: {
          status: "accepted",
          updated_at: new Date().toISOString(),
          updated_by: user.id,
        },
      });
      return NextResponse.json(
        {
          error: "Failed to update offer",
          details: updateError.message,
          code: updateError.code,
        },
        { status: 500 }
      );
    }

    // Update the quote status to indicate affiliate acceptance
    const { error: quoteUpdateError } = await supabase
      .from("quotes")
      .update({
        status: "affiliate_accepted",
        selected_affiliate_id: offer.company_id,
        updated_at: new Date().toISOString(),
      })
      .eq("id", offer.quote_id);

    if (quoteUpdateError) {
      console.error("Error updating quote:", quoteUpdateError);
      // Even though the offer update succeeded, we should indicate the error
      return NextResponse.json(
        {
          error: "Offer accepted but quote update failed",
          offerId: offerId,
        },
        { status: 500 }
      );
    }

    // Log the activity in quotes timeline
    await supabase.from("quote_timeline").insert({
      quote_id: offer.quote_id,
      action: "OFFER_ACCEPTED",
      status: "affiliate_accepted",
      created_by: user.id,
      details: JSON.stringify({
        offerId: offer.id,
        affiliateId: offer.company_id,
        acceptedRate: offer.counter_offer_amount || offer.rate_amount,
      }),
    });

    // Transform for client
    const transformedOffer = {
      id: updatedOffer.id,
      quoteId: updatedOffer.quote_id,
      affiliateId: updatedOffer.company_id,
      status: updatedOffer.status,
      proposedRate: updatedOffer.rate_amount,
      counterOffer: updatedOffer.counter_offer_amount,
      notes: updatedOffer.notes,
      expiresAt: updatedOffer.expires_at,
      createdAt: updatedOffer.created_at,
      updatedAt: updatedOffer.updated_at,
    };

    return NextResponse.json({
      message: "Offer accepted successfully",
      offer: transformedOffer,
    });
  } catch (error) {
    console.error(
      "Error in POST /api/affiliate/offers/[offerId]/accept:",
      error
    );
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}
