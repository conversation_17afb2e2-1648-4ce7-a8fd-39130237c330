import { createServerClient, type CookieOptions } from "@supabase/ssr";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

/**
 * POST /api/affiliate/offers/[offerId]/reject
 * Reject a quote offer for an affiliate company
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { offerId: string } }
) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, "", options);
        },
      },
    }
  );

  // Auth check
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();
  if (userError || !user) {
    return NextResponse.json(
      { error: "User not authenticated" },
      { status: 401 }
    );
  }

  try {
    const { offerId } = params;

    if (!offerId) {
      return NextResponse.json(
        { error: "Offer ID is required" },
        { status: 400 }
      );
    }

    // Parse request body for rejection reason
    const body = await request.json();
    const { reason } = body || {};

    // Get the offer details to check permissions
    const { data: offer, error: offerError } = await supabase
      .from("quote_affiliate_offers")
      .select("*")
      .eq("id", offerId)
      .single();

    if (offerError) {
      console.error("Error fetching offer:", offerError);
      return NextResponse.json(
        { error: "Failed to fetch offer details" },
        { status: 500 }
      );
    }

    if (!offer) {
      return NextResponse.json({ error: "Offer not found" }, { status: 404 });
    }

    // Check if user has access to this affiliate
    const { data: userCompany, error: userCompanyError } = await supabase
      .from("affiliate_user_companies")
      .select("*")
      .eq("user_id", user.id)
      .eq("affiliate_id", offer.company_id)
      .maybeSingle();

    if (userCompanyError) {
      console.error("Error checking user company access:", userCompanyError);
      return NextResponse.json(
        { error: "Error checking company access" },
        { status: 500 }
      );
    }

    if (!userCompany) {
      return NextResponse.json(
        { error: "You do not have access to this affiliate company" },
        { status: 403 }
      );
    }

    // Check if offer is in a valid state for rejection
    const validStatuses = ["pending", "PENDING", "countered", "COUNTERED"];
    if (!validStatuses.includes(offer.status)) {
      return NextResponse.json(
        {
          error: `Cannot reject an offer with status "${offer.status}"`,
        },
        { status: 400 }
      );
    }

    // Update the offer status to rejected
    const { data: updatedOffer, error: updateError } = await supabase
      .from("quote_affiliate_offers")
      .update({
        status: "REJECTED",
        notes: reason || offer.notes,
        updated_at: new Date().toISOString(),
        updated_by: user.id,
      })
      .eq("id", offerId)
      .select()
      .single();

    if (updateError) {
      console.error("Error updating offer:", updateError);
      return NextResponse.json(
        { error: "Failed to update offer" },
        { status: 500 }
      );
    }

    // Log the activity in quotes timeline
    await supabase.from("quote_timeline").insert({
      quote_id: offer.quote_id,
      action: "OFFER_REJECTED",
      status: "affiliate_rejected",
      created_by: user.id,
      details: JSON.stringify({
        offerId: offer.id,
        affiliateId: offer.company_id,
        reason: reason,
      }),
    });

    // Transform for client
    const transformedOffer = {
      id: updatedOffer.id,
      quoteId: updatedOffer.quote_id,
      affiliateId: updatedOffer.company_id,
      status: updatedOffer.status,
      proposedRate: updatedOffer.rate_amount,
      counterOffer: updatedOffer.counter_offer_amount,
      notes: updatedOffer.notes,
      expiresAt: updatedOffer.expires_at,
      createdAt: updatedOffer.created_at,
      updatedAt: updatedOffer.updated_at,
    };

    return NextResponse.json({
      message: "Offer rejected successfully",
      offer: transformedOffer,
    });
  } catch (error) {
    console.error(
      "Error in POST /api/affiliate/offers/[offerId]/reject:",
      error
    );
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}
