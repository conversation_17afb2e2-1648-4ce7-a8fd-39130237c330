import { createRouteH<PERSON>lerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import { v4 as uuidv4 } from "uuid";

export async function POST(
  request: NextRequest,
  { params }: { params: { offerId: string } }
) {
  console.log(`[API] Starting accept offer request for offer ID: ${params.offerId}`);
  
  // Basic validation - check for offer ID
  if (!params.offerId) {
    console.error("[API] No offer ID provided");
    return NextResponse.json(
      { error: "Offer ID is required" },
      { status: 400 }
    );
  }

  try {
    // Create Supabase client
    const supabase = createRouteHandlerClient({ cookies });
    
    console.log(`[API] Successfully created Supabase client, updating offer status`);
    
    // Update the offer status to "accepted"
    const { data: updatedOffer, error: updateError } = await supabase
      .from("quote_offers")
      .update({ status: "accepted" })
      .eq("id", params.offerId)
      .select("*")
      .single();
      
    if (updateError) {
      console.error(`[API] Error updating offer status: ${updateError.message}`);
      return NextResponse.json(
        { error: `Failed to update offer: ${updateError.message}` },
        { status: 500 }
      );
    }
    
    if (!updatedOffer) {
      console.error(`[API] Offer not found with ID: ${params.offerId}`);
      return NextResponse.json(
        { error: "Offer not found" },
        { status: 404 }
      );
    }
    
    console.log(`[API] Successfully updated offer status to accepted for ID: ${params.offerId}`);
    
    // Get the associated quote
    const { data: quoteData } = await supabase
      .from("quotes")
      .select("*")
      .eq("id", updatedOffer.quote_id)
      .single();
    
    // Add a timeline entry
    if (quoteData) {
      await supabase
        .from("quote_timeline")
        .insert({
          id: uuidv4(),
          quote_id: updatedOffer.quote_id,
          action: "offer_accepted",
          details: `Offer accepted by affiliate`,
          created_at: new Date().toISOString(),
        });
      
      // Update quote status to indicate offer acceptance
      await supabase
        .from("quotes")
        .update({ status: "offer_accepted" })
        .eq("id", updatedOffer.quote_id);
    }
    
    return NextResponse.json({ 
      success: true, 
      message: "Offer accepted successfully",
      data: updatedOffer
    });
    
  } catch (error) {
    console.error(`[API] Unexpected error in accept offer: ${error instanceof Error ? error.message : String(error)}`);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
} 