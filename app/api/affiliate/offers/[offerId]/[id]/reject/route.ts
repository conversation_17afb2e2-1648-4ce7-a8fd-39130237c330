import { createServerClient, type CookieOptions } from "@supabase/ssr";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import { v4 as uuidv4 } from "uuid";
import type { Database } from "@/lib/database.types";

const createSupabaseClient = () => {
  const cookieStore = cookies();
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set(name, value, options);
          } catch (error) {
            // Handle error
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            cookieStore.set(name, '', options);
          } catch (error) {
            // Handle error
          }
        },
      },
    }
  );
};

export async function POST(
  request: NextRequest,
  { params }: { params: { offerId: string } }
) {
  console.log(`[API] Starting reject offer request for offer ID: ${params.offerId}`);
  
  // Basic validation - check for offer ID
  if (!params.offerId) {
    console.error("[API] No offer ID provided");
    return NextResponse.json(
      { error: "Offer ID is required" },
      { status: 400 }
    );
  }

  try {
    // Create Supabase client
    const supabase = createSupabaseClient();
    
    console.log(`[API] Successfully created Supabase client, updating offer status`);
    
    // Fetch user BEFORE any logging that requires user_id
    const { data: { user }, error: userAuthError } = await supabase.auth.getUser();
    if (userAuthError || !user) {
      console.error("[API] User not authenticated prior to logging history:", userAuthError);
      // Decide if this is a critical error. If logging user_id is mandatory, 
      // you might return an error or proceed with a default/null user_id if allowed by DB.
      // For this fix, we'll assume the operation can proceed but logging might be affected.
    }

    // Update the offer status to "rejected"
    const { data: updatedOffer, error: updateError } = await supabase
      .from("quote_offers")
      .update({ status: "rejected" })
      .eq("id", params.offerId)
      .select("*")
      .single();
      
    if (updateError) {
      console.error(`[API] Error updating offer status: ${updateError.message}`);
      return NextResponse.json(
        { error: `Failed to update offer: ${updateError.message}` },
        { status: 500 }
      );
    }
    
    if (!updatedOffer) {
      console.error(`[API] Offer not found with ID: ${params.offerId}`);
      return NextResponse.json(
        { error: "Offer not found" },
        { status: 404 }
      );
    }
    
    console.log(`[API] Successfully updated offer status to rejected for ID: ${params.offerId}`);
    
    // Add a timeline entry
    if (!updatedOffer.quote_id) {
      console.error("[API] Cannot create timeline entry: updated offer is missing quote_id", updatedOffer);
    } else {
      await supabase
        .from("quote_timeline")
        .insert({
          quote_id: updatedOffer.quote_id,
          action: "offer_rejected",
          details: `Offer rejected by affiliate`,
          user_id: user?.id || 'system'
        });
    }
    
    // IF THERE IS AN INSERT TO quote_status_history, IT SHOULD LOOK LIKE THIS:
    // This is a placeholder. The actual insert needs to be located and modified.
    // Example: 
    // if (updatedOffer.quote_id && user) { // Ensure user is available for logging
    //   const { error: historyError } = await supabase
    //     .from('quote_status_history') 
    //     .insert({
    //       quote_id: updatedOffer.quote_id,
    //       action: 'Affiliate Rejected Offer', // Or a more specific action
    //       details: `Offer ID: ${updatedOffer.id} rejected by affiliate ${user.id}`,
    //       user_id: user.id, // <-- ENSURE THIS IS PRESENT
    //     });
    //   if (historyError) {
    //     console.error("[API] Error logging to quote_status_history:", historyError.message);
    //     // Non-critical error, so don't return 500, but log it.
    //   }
    // }
    
    return NextResponse.json({ 
      success: true, 
      message: "Offer rejected successfully",
      data: updatedOffer
    });
    
  } catch (error) {
    console.error(`[API] Unexpected error in reject offer: ${error instanceof Error ? error.message : String(error)}`);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
} 