import { createServerClient, type CookieOptions } from "@supabase/ssr";
import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import { Database } from "@/lib/types/supabase";
import { getSession, hasRole } from "../../../lib/auth";
import { UserRole } from "@/src/types/roles";

/**
 * GET /api/affiliate/offers
 * List all quote offers for the authenticated affiliate user's company
 */
export async function GET(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, "", options);
        },
      },
    }
  );

  // Get request parameters
  const searchParams = request.nextUrl.searchParams;
  const status = searchParams.get("status");
  const limit = parseInt(searchParams.get("limit") || "50");
  const offset = parseInt(searchParams.get("offset") || "0");
  const affiliateCompanyId = searchParams.get("companyId");

  // Auth check
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();
  if (userError || !user) {
    return NextResponse.json(
      { error: "User not authenticated" },
      { status: 401 }
    );
  }

  try {
    // If no specific affiliate company ID is provided, get all companies the user has access to
    let affiliateIds: string[] = [];

    if (affiliateCompanyId) {
      // Check if user has access to the specified company
      const { data: userCompany, error: userCompanyError } = await supabase
        .from("affiliate_user_companies")
        .select("*")
        .eq("user_id", user.id)
        .eq("affiliate_id", affiliateCompanyId)
        .maybeSingle();

      if (userCompanyError) {
        console.error("Error checking user company access:", userCompanyError);
        return NextResponse.json(
          { error: "Error checking company access" },
          { status: 500 }
        );
      }

      if (!userCompany) {
        return NextResponse.json(
          { error: "You do not have access to this company" },
          { status: 403 }
        );
      }

      affiliateIds = [affiliateCompanyId];
    } else {
      // Get all companies the user has access to
      const { data: userCompanies, error: userCompaniesError } = await supabase
        .from("affiliate_user_companies")
        .select("affiliate_id")
        .eq("user_id", user.id);

      if (userCompaniesError) {
        console.error("Error fetching user companies:", userCompaniesError);
        return NextResponse.json(
          { error: "Error fetching user companies" },
          { status: 500 }
        );
      }

      if (!userCompanies || userCompanies.length === 0) {
        return NextResponse.json({ offers: [] });
      }

      affiliateIds = userCompanies.map((uc) => uc.affiliate_id);
    }

    // Build query for quote offers - use the correct table name
    let query = supabase
      .from("quote_affiliate_offers")
      .select(
        `
        *,
        quotes:quotes(
          id,
          reference_number,
          pickup_location,
          dropoff_location,
          date,
          time,
          passenger_count,
          luggage_count,
          service_type,
          vehicle_type,
          status,
          city,
          total_amount,
          contact_name,
          contact_email,
          contact_phone,
          duration,
          distance,
          customer_id,
          special_requests,
          priority,
          created_at,
          updated_at,
          is_multi_day,
          flight_number,
          is_return_trip,
          return_date,
          return_time,
          return_flight_number,
          car_seats_needed,
          infant_seats,
          toddler_seats,
          booster_seats,
          intermediate_stops,
          duration_hours
        )
      `
      )
      .in("company_id", affiliateIds)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply status filter if provided
    if (status) {
      query = query.eq("status", status);
    }

    // Execute query
    const { data: offers, error: offersError, count } = await query;

    console.log(
      `[OFFERS DEBUG] Raw query results for affiliateIds ${affiliateIds}:`,
      {
        offersError,
        offerCount: offers?.length || 0,
        offers: offers?.map((o) => ({
          id: o.id,
          quote_id: o.quote_id,
          company_id: o.company_id,
          status: o.status,
        })),
      }
    );

    if (offersError) {
      console.error("Error fetching quote offers:", offersError);
      return NextResponse.json(
        { error: "Failed to fetch quote offers" },
        { status: 500 }
      );
    }

    // PERFORMANCE OPTIMIZATION: Batch queries to avoid N+1 problem
    // Get all unique company IDs and vehicle types from offers
    const companyIds = [...new Set(offers?.map((o) => o.company_id) || [])];
    const vehicleTypes = [
      ...new Set(
        offers?.map((o) => o.quotes?.vehicle_type).filter(Boolean) || []
      ),
    ];

    // Batch query for all deactivated vehicles
    const { data: allDeactivatedVehicles, error: vehicleError } = await supabase
      .from("vehicles")
      .select("id, company_id, type")
      .in("company_id", companyIds)
      .in("type", vehicleTypes)
      .eq("status", "deactivated");

    // Batch query for all rate cards
    const { data: allRateCards, error: rateError } = await supabase
      .from("rate_cards")
      .select("id, company_id, vehicle_type, special_rates")
      .in("company_id", companyIds)
      .in("vehicle_type", vehicleTypes);

    // Create lookup maps for fast access
    const deactivatedVehicleMap = new Map<string, Set<string>>();
    allDeactivatedVehicles?.forEach((v) => {
      const key = `${v.company_id}-${v.type}`;
      if (!deactivatedVehicleMap.has(key)) {
        deactivatedVehicleMap.set(key, new Set());
      }
      deactivatedVehicleMap.get(key)!.add(v.id);
    });

    const rateCardMap = new Map<string, any[]>();
    allRateCards?.forEach((rc) => {
      const key = `${rc.company_id}-${rc.vehicle_type}`;
      if (!rateCardMap.has(key)) {
        rateCardMap.set(key, []);
      }
      rateCardMap.get(key)!.push(rc);
    });

    // Filter offers based on vehicle and rate card conditions
    const filteredOffers = [];

    for (const offer of offers || []) {
      if (!offer.quotes) continue;

      const quote = offer.quotes;
      const vehicleType = quote.vehicle_type;
      const serviceType = quote.service_type;

      console.log(
        `[OFFERS DEBUG] Processing offer ${offer.id} for company ${offer.company_id}:`,
        {
          vehicleType,
          serviceType,
          pickup: quote.pickup_location,
          dropoff: quote.dropoff_location,
        }
      );

      // Check for deactivated vehicles using lookup map
      const deactivatedKey = `${offer.company_id}-${vehicleType}`;
      const deactivatedVehicles =
        deactivatedVehicleMap.get(deactivatedKey) || new Set();

      console.log(
        `[OFFERS DEBUG] Minimal friction check for ${offer.company_id}:`,
        {
          vehicleError,
          deactivatedCount: deactivatedVehicles.size,
          vehicleType,
          approach: "minimal_friction_onboarding",
        }
      );

      // Skip only if affiliate has EXPLICITLY deactivated this vehicle type
      if (vehicleError) {
        console.log(
          `[OFFERS DEBUG] Database error checking vehicles for ${offer.company_id}:`,
          vehicleError
        );
        continue;
      }

      if (deactivatedVehicles.size > 0) {
        console.log(
          `[OFFERS DEBUG] Skipping offer ${offer.id} - affiliate explicitly excluded ${vehicleType} vehicles`
        );
        continue; // Skip this offer - affiliate explicitly doesn't carry this vehicle type
      }

      console.log(
        `[OFFERS DEBUG] Including offer ${offer.id} - minimal friction onboarding (no explicit exclusion)`
      );

      // Check if we have approved rate cards for this vehicle type and service type
      // Use flexible matching - affiliates can serve requests with compatible pricing models
      let compatiblePricingModels: string[] = [];

      // Check if this is actually an airport-related trip based on locations
      const isAirportTrip =
        quote.pickup_location?.toLowerCase().includes("airport") ||
        quote.dropoff_location?.toLowerCase().includes("airport");

      switch (serviceType) {
        case "point":
          if (isAirportTrip) {
            // Point-to-point trips involving airports can be served by multiple pricing models
            // This gives affiliates maximum flexibility in their business model
            compatiblePricingModels = ["P2P", "AIRPORT_TRANSFER", "DT"];
          } else {
            // Regular point-to-point trips can use P2P, D+T, or even Airport Transfer pricing
            // Many affiliates use Airport Transfer pricing for all their services
            compatiblePricingModels = ["P2P", "DT", "AIRPORT_TRANSFER"];
          }
          break;
        case "hourly":
          // Hourly trips require hourly charter pricing
          compatiblePricingModels = ["HOURLY_CHARTER"];
          break;
        case "airport":
          // Airport trips can be served by Airport Transfer, P2P, or D+T pricing
          // This respects affiliate business choices - some use one model for everything
          compatiblePricingModels = ["AIRPORT_TRANSFER", "P2P", "DT"];
          break;
        default:
          console.log(
            `[OFFERS DEBUG] Skipping offer ${offer.id} - unknown service type: ${serviceType}`
          );
          continue; // Skip unknown service types
      }

      console.log(
        `[OFFERS DEBUG] Compatible pricing models for ${serviceType} (updated):`,
        compatiblePricingModels
      );

      // Check if affiliate has ANY compatible rate card for this vehicle type using lookup map
      // Note: Rate cards don't need approval - affiliates can change rates freely
      const rateCardKey = `${offer.company_id}-${vehicleType}`;
      const rateCards = rateCardMap.get(rateCardKey) || [];

      // Filter rate cards by pricing model type from special_rates JSONB field
      const compatibleRateCards = rateCards.filter((rc) => {
        const pricingModelType = rc.special_rates?.pricing_model_type;
        return (
          pricingModelType && compatiblePricingModels.includes(pricingModelType)
        );
      });

      console.log(`[OFFERS DEBUG] Rate cards check for ${offer.company_id}:`, {
        rateError,
        totalRateCardCount: rateCards.length,
        compatibleRateCardCount: compatibleRateCards.length,
        compatibleRateCards: compatibleRateCards.map((rc) => ({
          id: rc.id,
          pricing_model_type: rc.special_rates?.pricing_model_type,
        })),
        searchCriteria: {
          company_id: offer.company_id,
          vehicleType,
          compatiblePricingModels,
        },
      });

      // MINIMAL FRICTION ONBOARDING: Don't require rate cards for new affiliates
      // They can set up pricing later when they respond to offers
      if (rateError) {
        console.log(
          `[OFFERS DEBUG] Database error checking rate cards for ${offer.company_id}:`,
          rateError
        );
        // Continue anyway - minimal friction approach
      }

      if (compatibleRateCards.length === 0) {
        console.log(
          `[OFFERS DEBUG] No rate cards found for ${offer.id}, but including anyway (minimal friction onboarding)`
        );
        // Continue anyway - affiliate can set pricing when responding
      } else {
        console.log(
          `[OFFERS DEBUG] Found ${compatibleRateCards.length} compatible rate cards for ${offer.id}`
        );
      }

      // If we get here, the offer meets all conditions
      console.log(
        `[OFFERS DEBUG] Offer ${offer.id} passed all filters - adding to results`
      );
      filteredOffers.push(offer);
    }

    // Transform data for client
    const transformedOffers = filteredOffers.map((offer) => ({
      id: offer.id,
      quote_id: offer.quote_id,
      company_id: offer.company_id,
      rate_amount: offer.rate_amount,
      status: offer.status.toLowerCase(),
      notes: offer.notes,
      expires_at: offer.expires_at,
      created_at: offer.created_at,
      updated_at: offer.updated_at,
      quote: offer.quotes || null,
    }));

    return NextResponse.json({
      offers: transformedOffers,
      count: count || transformedOffers.length,
      hasMore: (count || transformedOffers.length) > offset + limit,
    });
  } catch (error) {
    console.error("Error in GET /api/affiliate/offers:", error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/affiliate/offers
 * Submit a counter-offer for a quote
 */
export async function POST(request: NextRequest) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string, options: CookieOptions) {
          cookieStore.set(name, "", options);
        },
      },
    }
  );

  try {
    // Get the current user's session
    const session = await getSession(request);

    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Verify user has OWNER or SUPER_ADMIN role for creating offers
    if (
      !hasRole(session.roles, ["OWNER" as UserRole, "SUPER_ADMIN" as UserRole])
    ) {
      return NextResponse.json(
        { error: "Insufficient permissions to create offers" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      quote_id,
      company_id,
      price_per_mile,
      flat_rate,
      notes,
      vehicle_id,
    } = body;

    if (!quote_id || !company_id) {
      return NextResponse.json(
        { error: "Missing quote_id or company_id" },
        { status: 400 }
      );
    }

    if (price_per_mile === undefined && flat_rate === undefined) {
      return NextResponse.json(
        { error: "Either price_per_mile or flat_rate is required" },
        { status: 400 }
      );
    }

    // Validate that the company_id belongs to an affiliate company accessible by the user
    const { data: userCompany, error: userCompanyError } = await supabase
      .from("affiliate_user_companies")
      .select("*")
      .eq("user_id", session.user.id)
      .eq("affiliate_id", company_id)
      .maybeSingle();

    if (userCompanyError || !userCompany) {
      return NextResponse.json(
        { error: "You do not have access to this company" },
        { status: 403 }
      );
    }

    // Check if an offer already exists for this quote_id and company_id
    const { data: existingOffer, error: existingOfferError } = await supabase
      .from("quote_affiliate_offers")
      .select("id")
      .eq("quote_id", quote_id)
      .eq("company_id", company_id)
      .maybeSingle();

    if (existingOfferError) {
      console.error("Error checking existing offer:", existingOfferError);
      return NextResponse.json(
        { error: "Failed to check for existing offer" },
        { status: 500 }
      );
    }

    if (existingOffer) {
      return NextResponse.json(
        { error: "An offer for this quote from your company already exists" },
        { status: 409 }
      );
    }

    // Insert new quote offer
    const { data: newOffer, error } = await supabase
      .from("quote_affiliate_offers")
      .insert({
        quote_id,
        company_id,
        price_per_mile,
        flat_rate,
        notes,
        vehicle_id,
        status: "pending", // Default status
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating quote offer:", error);
      return NextResponse.json(
        { error: "Failed to create quote offer" },
        { status: 500 }
      );
    }

    return NextResponse.json(newOffer, { status: 201 });
  } catch (error: any) {
    console.error("Error creating quote offer:", error);
    return NextResponse.json(
      { error: "Internal server error", details: error.message },
      { status: 500 }
    );
  }
}

// Main API handler function
async function handleApiRequest() {
  try {
    // Get cookie store
    // const cookieStore = cookies(); // Already available if this function was used
    // Directly check if authentication cookie exists
    // const hasAuthCookie = cookieStore.has('sb-access-token') ||
    //                      cookieStore.has('sb-refresh-token');
    // Create a Supabase client with server-side cookies
    // const supabase = createRouteHandlerClient({ cookies: () => cookieStore }); // Commenting out old client if this function were active
    // Get and validate the user session
    // const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    // Get the affiliate company ID for the current user
    // const { data: affiliateCompanies, error: affiliateError } = await supabase
    //   .from('affiliate_companies')
    //   .select('id, name, status')
    //   .eq('owner_id', session.user.id)
    //   .eq('status', 'active');
    // if (affiliateError) {
    //   return NextResponse.json({
    //     error: "Database error",
    //     message: "Error fetching affiliate companies",
    //     details: affiliateError.message
    //   }, {
    //     status: 500
    //   });
    // }
    // if (!affiliateCompanies || affiliateCompanies.length === 0) {
    //   return NextResponse.json({
    //     error: "Not found",
    //     message: "No active affiliate companies found for this user",
    //     userId: session.user.id
    //   }, {
    //     status: 404
    //   });
    // Use the first active company
    // const affiliateCompany = affiliateCompanies[0];
    // Step 1: Get all the offers for this affiliate
    // const { data: offers, error: offersError } = await supabase
    //   .from('quote_offers')
    //   .select('*')
    //   .eq('company_id', affiliateCompany.id);
    // if (offersError) {
    //   return NextResponse.json({
    //     error: "Database error",
    //     message: "Error fetching offers",
    //     details: offersError.message
    //   }, {
    //     status: 500
    //   });
    // If no offers, return empty array
    // if (!offers || offers.length === 0) {
    //   return NextResponse.json([], {
    //     headers: {
    //       'Cache-Control': 'no-store, max-age=0',
    //       'Content-Type': 'application/json'
    //     }
    //   });
    // }
    // Step 2: Get all the quote IDs from the offers
    // const quoteIds = offers.map(offer => offer.quote_id);
    // Step 3: Fetch the quotes with customer data in separate queries
    // const { data: quotes, error: quotesError } = await supabase
    //   .from('quotes')
    //   .select(`
    //     id,
    //     reference_number,
    //     customer_id,
    //     service_type,
    //     vehicle_type,
    //     pickup_location,
    //     dropoff_location,
    //     date,
    //     time,
    //     duration,
    //     distance,
    //     status,
    //     passenger_count,
    //     luggage_count,
    //     priority,
    //     total_amount,
    //     special_requests,
    //     base_rate,
    //     service_fee,
    //     expiry_time,
    //     contact_name,
    //     contact_email,
    //     contact_phone,
    //     city,
    //     duration_hours,
    //     is_multi_day,
    //     flight_number,
    //     is_return_trip,
    //     return_date,
    //     return_time,
    //     return_flight_number,
    //     car_seats_needed,
    //     infant_seats,
    //     toddler_seats,
    //     booster_seats,
    //     intermediate_stops
    //   `)
    //   .in('id', quoteIds);
    // if (quotesError) {
    //   return NextResponse.json({
    //     error: "Database error",
    //     message: "Error fetching quotes",
    //     details: quotesError.message
    //   }, {
    //     status: 500
    //   });
    // }
    // Step 4: Fetch all customers associated with these quotes
    // const customerIds = quotes.map(quote => quote.customer_id).filter(Boolean);
    // let customers: any[] = [];
    // if (customerIds.length > 0) {
    //   const { data: customersData, error: customersError } = await supabase
    //     .from('customers')
    //     .select('id, full_name, email, phone, company_name')
    //     .in('id', customerIds);
    //   if (customersError) {
    //     console.error("Error fetching customers:", customersError);
    //     // Continue without customer data rather than failing
    //   } else {
    //     customers = customersData || [];
    //   }
    // }
    // Step 5: Create a map of customer data by ID for quick lookups
    // const customerMap: Record<string, any> = {};
    // customers.forEach(customer => {
    //   customerMap[customer.id] = customer;
    // });
    // Step 6: Create a map of quote data by ID for quick lookups
    // const quoteMap: Record<string, any> = {};
    // quotes.forEach(quote => {
    //   // Add the customer data to each quote
    //   const customerData = quote.customer_id ? customerMap[quote.customer_id] : null;
    //   quoteMap[quote.id] = {
    //     ...quote,
    //     customer: customerData || null
    //   };
    // });
    // Step 7: Merge the data to create the final response
    // const result = offers.map(offer => {
    //   const quoteData = quoteMap[offer.quote_id] || null;
    //   return {
    //     ...offer,
    //     quote: quoteData
    //   };
    // });
    // Return a successful response with the data
    // return NextResponse.json(result, {
    //   headers: {
    //     'Cache-Control': 'no-store, max-age=0',
    //     'Content-Type': 'application/json'
    //   }
    // });
  } catch (error: any) {
    // Provide detailed error for unexpected issues
    console.error("API Handler Error:", error);
    throw error;
  }
}
