import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  console.log('=== MANUAL QUOTE NOTIFICATION API CALLED ===')
  try {
    const body = await request.json()
    const { quoteId, quoteReference, reason, quoteDetails, userId } = body
    console.log('Manual quote notification request:', { quoteId, quoteReference, reason })

    // Create service role client for database operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Get user details for the notification
    const { data: userProfile, error: userError } = await supabase
      .from('profiles')
      .select('full_name, email')
      .eq('id', userId)
      .single()

    if (userError) {
      console.error('Error fetching user profile:', userError)
    }

    // Create notification record for super admin
    const notificationData = {
      type: 'manual_quote_request',
      title: `Manual Quote Processing Required - ${quoteReference}`,
      message: `A quote request requires manual processing due to: ${reason}. Client: ${userProfile?.full_name || 'Unknown'} (${userProfile?.email || 'No email'})`,
      data: {
        quoteId,
        quoteReference,
        reason,
        quoteDetails,
        userId,
        userProfile
      },
      priority: 'high',
      created_at: new Date().toISOString()
    }

    console.log('Creating manual quote notification:', notificationData)

    const { data: notification, error: notificationError } = await supabase
      .from('notifications')
      .insert(notificationData)
      .select()
      .single()

    if (notificationError) {
      console.error('Error creating manual quote notification:', notificationError)
      return NextResponse.json({
        error: 'Failed to create notification',
        details: notificationError
      }, { status: 500 })
    }

    console.log('Manual quote notification created:', notification)

    // TODO: Send email notification to super admin
    // TODO: Send SMS notification if configured
    // TODO: Send Slack/Teams notification if configured

    return NextResponse.json({
      success: true,
      notificationId: notification.id,
      message: 'Manual quote notification sent successfully'
    })

  } catch (error) {
    console.error('Error in manual quote notification API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
