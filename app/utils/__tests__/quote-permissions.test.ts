import { getQuoteActionPermissions, getTenantButtonStyle, getActionLabels } from '../quote-permissions';

describe('Quote Permissions', () => {
  describe('getQuoteActionPermissions', () => {
    it('should grant full permissions to super admin', () => {
      const permissions = getQuoteActionPermissions({
        userRoles: ['SUPER_ADMIN'],
        tenantType: 'shared',
        quoteStatus: 'pending',
        userType: 'admin'
      });

      expect(permissions.canSendToAffiliates).toBe(true);
      expect(permissions.canFixedRate).toBe(true);
      expect(permissions.canViewDetails).toBe(true);
      expect(permissions.canEdit).toBe(true);
      expect(permissions.canDelete).toBe(true);
      expect(permissions.canManageAffiliates).toBe(true);
      expect(permissions.canViewAnalytics).toBe(true);
      expect(permissions.canImpersonate).toBe(true);
    });

    it('should limit client admin permissions in shared tenant', () => {
      const permissions = getQuoteActionPermissions({
        userRoles: ['CLIENT'],
        tenantType: 'shared',
        quoteStatus: 'pending',
        userType: 'admin'
      });

      expect(permissions.canSendToAffiliates).toBe(true);
      expect(permissions.canFixedRate).toBe(true);
      expect(permissions.canAccept).toBe(false); // Clients don't accept quotes directly in shared
      expect(permissions.canReject).toBe(false);
      expect(permissions.canManageAffiliates).toBe(false);
      expect(permissions.canImpersonate).toBe(false);
    });

    it('should grant more control to white-label client admin', () => {
      const permissions = getQuoteActionPermissions({
        userRoles: ['CLIENT'],
        tenantType: 'white_label',
        quoteStatus: 'fixed_offer',
        userType: 'admin'
      });

      expect(permissions.canAccept).toBe(true); // White-label clients can accept
      expect(permissions.canReject).toBe(true);
      expect(permissions.canRequestChanges).toBe(true);
      expect(permissions.canManageAffiliates).toBe(true); // Can manage exclusive affiliates
    });

    it('should limit affiliate permissions appropriately', () => {
      const permissions = getQuoteActionPermissions({
        userRoles: ['AFFILIATE'],
        tenantType: 'shared',
        quoteStatus: 'rate_requested',
        userType: 'affiliate'
      });

      expect(permissions.canSendToAffiliates).toBe(false);
      expect(permissions.canFixedRate).toBe(false);
      expect(permissions.canAccept).toBe(true); // Can accept rate requests
      expect(permissions.canSubmitRate).toBe(true);
      expect(permissions.canEdit).toBe(false);
      expect(permissions.canDelete).toBe(false);
      expect(permissions.canManageAffiliates).toBe(false);
    });

    it('should limit customer permissions to viewing and accepting', () => {
      const permissions = getQuoteActionPermissions({
        userRoles: ['PASSENGER'],
        tenantType: 'shared',
        quoteStatus: 'fixed_offer',
        userType: 'customer'
      });

      expect(permissions.canSendToAffiliates).toBe(false);
      expect(permissions.canFixedRate).toBe(false);
      expect(permissions.canAccept).toBe(true); // Can accept fixed offers
      expect(permissions.canReject).toBe(true);
      expect(permissions.canRequestChanges).toBe(true);
      expect(permissions.canEdit).toBe(false);
      expect(permissions.canDelete).toBe(false);
      expect(permissions.canManageAffiliates).toBe(false);
    });

    it('should respect quote status for action availability', () => {
      const baseContext = {
        userRoles: ['SUPER_ADMIN'],
        tenantType: 'shared' as const,
        userType: 'admin' as const
      };

      // Pending quotes
      const pendingPermissions = getQuoteActionPermissions({
        ...baseContext,
        quoteStatus: 'pending'
      });
      expect(pendingPermissions.canSendToAffiliates).toBe(true);
      expect(pendingPermissions.canFixedRate).toBe(true);

      // Accepted quotes
      const acceptedPermissions = getQuoteActionPermissions({
        ...baseContext,
        quoteStatus: 'accepted'
      });
      expect(acceptedPermissions.canSendToAffiliates).toBe(false);
      expect(acceptedPermissions.canFixedRate).toBe(false);
      expect(acceptedPermissions.canEdit).toBe(false); // Can't edit accepted quotes
    });
  });

  describe('getTenantButtonStyle', () => {
    it('should return custom styling for white-label tenant', () => {
      const style = getTenantButtonStyle(
        'white_label',
        { primary_color: '#ff6b35' },
        'primary'
      );

      expect(style.style?.backgroundColor).toBe('#ff6b35');
      expect(style.style?.borderColor).toBe('#ff6b35');
      expect(style.className).toContain('text-white');
    });

    it('should return outline styling for white-label tenant', () => {
      const style = getTenantButtonStyle(
        'white_label',
        { primary_color: '#ff6b35' },
        'outline'
      );

      expect(style.style?.borderColor).toBe('#ff6b35');
      expect(style.style?.color).toBe('#ff6b35');
      expect(style.className).toContain('bg-transparent');
    });

    it('should return empty styling for shared tenant', () => {
      const style = getTenantButtonStyle('shared', {}, 'primary');
      expect(style).toEqual({});
    });
  });

  describe('getActionLabels', () => {
    it('should return white-label specific labels', () => {
      const labels = getActionLabels('white_label');
      expect(labels.sendToAffiliates).toBe('Send to Partners');
      expect(labels.requestRates).toBe('Request Quotes');
    });

    it('should return segregated tenant labels', () => {
      const labels = getActionLabels('segregated');
      expect(labels.sendToAffiliates).toBe('Send to Network');
      expect(labels.requestRates).toBe('Request Network Rates');
    });

    it('should return default labels for shared tenant', () => {
      const labels = getActionLabels('shared');
      expect(labels.sendToAffiliates).toBe('Send to Affiliates');
      expect(labels.requestRates).toBe('Request Rates');
    });
  });
});
