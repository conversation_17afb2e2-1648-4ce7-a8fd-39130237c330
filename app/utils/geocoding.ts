/**
 * Server-side geocoding utilities for extracting city names from coordinates
 * Uses HERE API and Mapbox API as fallbacks
 */

interface HereReverseGeocodingResponse {
  items: Array<{
    title: string;
    address: {
      label: string;
      countryCode: string;
      countryName: string;
      stateCode: string;
      state: string;
      county: string;
      city: string;
      district: string;
      street: string;
      postalCode: string;
    };
    position: {
      lat: number;
      lng: number;
    };
  }>;
}

interface MapboxReverseGeocodingResponse {
  type: string;
  query: number[];
  features: Array<{
    id: string;
    type: string;
    place_type: string[];
    relevance: number;
    text: string;
    place_name: string;
    center: number[];
    context: Array<{
      id: string;
      text: string;
      short_code?: string;
    }>;
  }>;
}

/**
 * Get city name from coordinates using HERE API
 * @param latitude Latitude coordinate
 * @param longitude Longitude coordinate
 * @returns City name or null if not found
 */
async function getCityFromHereAPI(latitude: number, longitude: number): Promise<string | null> {
  try {
    const hereApiKey = process.env.NEXT_PUBLIC_HERE_API_KEY;
    
    if (!hereApiKey) {
      console.error('[Geocoding] HERE API key not found');
      return null;
    }

    // HERE API expects coordinates in the format "latitude,longitude"
    const url = `https://revgeocode.search.hereapi.com/v1/revgeocode?at=${latitude},${longitude}&lang=en-US&apiKey=${hereApiKey}`;
    
    console.log('[Geocoding] Making HERE API request for coordinates:', { latitude, longitude });
    
    const response = await fetch(url);
    
    if (!response.ok) {
      console.error('[Geocoding] HERE API request failed:', { status: response.status, statusText: response.statusText });
      return null;
    }
    
    const data: HereReverseGeocodingResponse = await response.json();
    
    if (!data.items || data.items.length === 0) {
      console.log('[Geocoding] No items found in HERE API response');
      return null;
    }
    
    // Get the first item and extract city
    const firstItem = data.items[0];
    const city = firstItem.address?.city;
    
    if (city) {
      console.log('[Geocoding] City found from HERE API:', city);
      return city;
    }
    
    console.log('[Geocoding] No city found in HERE API response');
    return null;
    
  } catch (error) {
    console.error('[Geocoding] Error with HERE API:', error);
    return null;
  }
}

/**
 * Get city name from coordinates using Mapbox API
 * @param latitude Latitude coordinate
 * @param longitude Longitude coordinate
 * @returns City name or null if not found
 */
async function getCityFromMapboxAPI(latitude: number, longitude: number): Promise<string | null> {
  try {
    const mapboxApiKey = process.env.NEXT_PUBLIC_MAPBOX_TOKEN;
    
    if (!mapboxApiKey) {
      console.error('[Geocoding] Mapbox API key not found');
      return null;
    }

    // Mapbox API expects coordinates in the format "longitude,latitude"
    const url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${mapboxApiKey}&types=place`;
    
    console.log('[Geocoding] Making Mapbox API request for coordinates:', { latitude, longitude });
    
    const response = await fetch(url);
    
    if (!response.ok) {
      console.error('[Geocoding] Mapbox API request failed:', { status: response.status, statusText: response.statusText });
      return null;
    }
    
    const data: MapboxReverseGeocodingResponse = await response.json();
    
    if (!data.features || data.features.length === 0) {
      console.log('[Geocoding] No features found in Mapbox API response');
      return null;
    }
    
    // Find the place feature (city)
    const placeFeature = data.features.find(feature => 
      feature.place_type && feature.place_type.includes('place')
    );
    
    if (placeFeature) {
      const cityName = placeFeature.text;
      console.log('[Geocoding] City found from Mapbox API:', cityName);
      return cityName;
    }
    
    // If no place feature found, try to extract from context
    const firstFeature = data.features[0];
    if (firstFeature.context) {
      const placeContext = firstFeature.context.find(ctx => 
        ctx.id && ctx.id.startsWith('place.')
      );
      
      if (placeContext) {
        const cityName = placeContext.text;
        console.log('[Geocoding] City found from Mapbox API context:', cityName);
        return cityName;
      }
    }
    
    console.log('[Geocoding] No city found in Mapbox API response');
    return null;
    
  } catch (error) {
    console.error('[Geocoding] Error with Mapbox API:', error);
    return null;
  }
}

/**
 * Extract city name from coordinates using multiple geocoding services
 * Tries HERE API first, then falls back to Mapbox API
 * @param latitude Latitude coordinate
 * @param longitude Longitude coordinate
 * @returns City name or "Unknown" if not found
 */
export async function getCityFromCoordinates(latitude: number, longitude: number): Promise<string> {
  console.log('[Geocoding] Extracting city from coordinates:', { latitude, longitude });
  
  // Validate coordinates
  if (typeof latitude !== 'number' || typeof longitude !== 'number') {
    console.error('[Geocoding] Invalid coordinate types:', { latitude: typeof latitude, longitude: typeof longitude });
    return 'Unknown';
  }
  
  if (Math.abs(latitude) > 90 || Math.abs(longitude) > 180) {
    console.error('[Geocoding] Coordinates out of valid range:', { latitude, longitude });
    return 'Unknown';
  }
  
  // Try HERE API first
  const hereCity = await getCityFromHereAPI(latitude, longitude);
  if (hereCity) {
    return hereCity;
  }
  
  // Fall back to Mapbox API
  const mapboxCity = await getCityFromMapboxAPI(latitude, longitude);
  if (mapboxCity) {
    return mapboxCity;
  }
  
  console.log('[Geocoding] No city found from any geocoding service');
  return 'Unknown';
}

/**
 * Extract city from quote request data using coordinates if available, fallback to address parsing
 * @param quoteRequest Quote request data
 * @returns City name
 */
export async function extractCityFromQuoteRequest(quoteRequest: any): Promise<string> {
  console.log('[Geocoding] Extracting city from quote request');
  
  // First, try to use coordinates if available
  const latitude = quoteRequest.pickup_latitude || quoteRequest.pickupLatitude;
  const longitude = quoteRequest.pickup_longitude || quoteRequest.pickupLongitude;
  
  if (latitude && longitude) {
    console.log('[Geocoding] Using coordinates for city extraction:', { latitude, longitude });
    const city = await getCityFromCoordinates(latitude, longitude);
    if (city !== 'Unknown') {
      return city;
    }
  }
  
  // If coordinates not available or geocoding failed, try the city field
  if (quoteRequest.city && quoteRequest.city.trim() !== '') {
    console.log('[Geocoding] Using provided city field:', quoteRequest.city);
    return quoteRequest.city.trim();
  }
  
  // Last resort: try to extract from pickup location address
  const pickup_location = quoteRequest.pickup_location || quoteRequest.pickupLocation;
  if (pickup_location) {
    console.log('[Geocoding] Extracting city from pickup location address:', pickup_location);
    const city = pickup_location.split(",")[0]?.trim();
    if (city && city !== '') {
      return city;
    }
  }
  
  console.log('[Geocoding] No city could be extracted, using default');
  return 'Unknown';
}
