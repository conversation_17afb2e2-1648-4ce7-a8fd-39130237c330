import { hasRole } from "@/app/lib/auth";
import type { UserRole } from "@/src/types/roles";

/**
 * Helper function to check if user has a specific granular permission
 */
export function hasGranularPermission(
  granularPermissions: QuotePermissionContext["granularPermissions"],
  permission: string
): boolean {
  return granularPermissions?.feature_permissions?.[permission] === true;
}

/**
 * Helper function to check UI customization visibility
 */
export function getUIVisibility(
  granularPermissions: QuotePermissionContext["granularPermissions"],
  uiElement: string
): "visible" | "hidden" | "restricted" {
  return granularPermissions?.ui_customizations?.[uiElement] || "visible";
}

export interface QuoteActionPermissions {
  canSendToAffiliates: boolean;
  canFixedRate: boolean;
  canAccept: boolean;
  canReject: boolean;
  canRequestChanges: boolean;
  canArchive: boolean;
  canViewDetails: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canAssign: boolean;
  canSubmitRate: boolean;
  canContact: boolean;
  canManageAffiliates: boolean;
  canViewAnalytics: boolean;
  canImpersonate: boolean;
}

export interface QuotePermissionContext {
  userRoles: string[] | undefined;
  tenantType: "shared" | "segregated" | "white_label" | undefined;
  quoteStatus: string;
  userType: "admin" | "customer" | "affiliate" | "driver";
  isQuoteOwner?: boolean;
  organizationId?: string;
  granularPermissions?: {
    feature_permissions: Record<string, boolean>;
    ui_customizations: Record<string, "visible" | "hidden" | "restricted">;
    access_controls: Record<string, any>;
  };
  networkId?: string; // For TNC network scope
  workflowType?: "pure_saas" | "tnc"; // NEW: Determines workflow behavior
  clientSelectedAffiliates?: boolean; // NEW: Whether client already picked affiliates in booking form
}

/**
 * Determine workflow type based on context
 */
export function getWorkflowType(
  context: QuotePermissionContext
): "pure_saas" | "tnc" {
  // Explicit workflow type takes precedence
  if (context.workflowType) {
    return context.workflowType;
  }

  // If client selected affiliates in booking form, it's Pure SaaS workflow
  if (context.clientSelectedAffiliates) {
    return "pure_saas";
  }

  // TNC tenants use TNC workflow
  if (context.tenantType === "segregated") {
    return "tnc";
  }

  // Default to Pure SaaS for shared/white-label tenants
  return "pure_saas";
}

/**
 * Get comprehensive quote action permissions based on user role, tenant type, and quote status
 */
export function getQuoteActionPermissions(
  context: QuotePermissionContext
): QuoteActionPermissions {
  const {
    userRoles,
    tenantType,
    quoteStatus,
    userType,
    isQuoteOwner = false,
    granularPermissions,
    networkId,
    workflowType,
    clientSelectedAffiliates,
  } = context;
  const roles = userRoles || [];

  // Determine the workflow type for this context
  const currentWorkflowType = getWorkflowType(context);

  // Role checks
  const isSuperAdmin = hasRole(roles, ["SUPER_ADMIN"]);
  const isTNCAdmin = hasRole(roles, ["TNC_ADMIN"]);
  const isClient = hasRole(roles, ["CLIENT"]);
  const isAffiliate = hasRole(roles, ["AFFILIATE", "AFFILIATE_DISPATCH"]);
  const isCustomer = hasRole(roles, ["PASSENGER"]);

  // Super Client check (Client with granular permissions)
  const isSuperClient =
    isClient &&
    granularPermissions &&
    Object.keys(granularPermissions.feature_permissions).length > 0;

  // Status checks
  const status = quoteStatus.toLowerCase();
  const isPending = ["pending", "pending_quote", "new"].includes(status);
  const isRateRequested = ["rate_requested", "sent_to_affiliates"].includes(
    status
  );
  const isFixedOffer = [
    "fixed_offer",
    "quote_ready",
    "quote_assigned",
  ].includes(status);
  const isAccepted = status === "accepted";
  const isRejected = status === "rejected";
  const isActive = !isAccepted && !isRejected;

  // Super Admin permissions - WORKFLOW DEPENDENT
  if (isSuperAdmin) {
    // Pure SaaS: MONITORING ONLY (client already picked affiliates in booking form)
    if (currentWorkflowType === "pure_saas") {
      return {
        // MONITORING ONLY - No orchestration in Pure SaaS
        canSendToAffiliates: false, // N/A - Client already chose affiliates
        canFixedRate: false, // View only - no rate setting
        canAccept: false, // View only - client decides
        canReject: false, // View only - client decides
        canRequestChanges: false, // View only - no intervention
        canArchive: true, // Platform cleanup
        canViewDetails: true, // Full monitoring access
        canEdit: false, // View only - no quote editing
        canDelete: true, // Platform cleanup
        canAssign: false, // N/A - client already assigned
        canSubmitRate: false, // Super admin doesn't submit rates
        canContact: true, // Escalation support
        canManageAffiliates: true, // Platform management
        canViewAnalytics: true, // Cross-tenant visibility
        canImpersonate: true, // Support capability
      };
    }

    // TNC or Emergency: FULL ORCHESTRATION CAPABILITIES
    return {
      // Full intervention capabilities for TNC context or emergencies
      canSendToAffiliates: true, // TNC orchestration or emergency
      canFixedRate: true, // Emergency override
      canAccept: true, // Emergency override
      canReject: true, // Emergency override
      canRequestChanges: isActive, // Emergency intervention
      canArchive: true, // Cleanup
      canViewDetails: true,
      canEdit: true, // Correction capability
      canDelete: true, // Cleanup
      canAssign: true, // Manual assignment
      canSubmitRate: false, // Super admin doesn't submit rates
      canContact: true,
      canManageAffiliates: true, // Platform management
      canViewAnalytics: true, // Cross-tenant visibility
      canImpersonate: true, // Act as any user
    };
  }

  // TNC Admin permissions (Network Operator - Full Operational Control)
  if (isTNCAdmin) {
    return {
      // Full operational control within network
      canSendToAffiliates: isPending, // Network affiliates
      canFixedRate: isPending, // Network pricing
      canAccept: isFixedOffer || isRateRequested, // Customer decisions
      canReject: isActive, // Customer decisions
      canRequestChanges: isActive, // Quote modifications
      canArchive: isActive, // Network quotes
      canViewDetails: true,
      canEdit: isActive, // Network quotes
      canDelete: false, // No permanent deletion
      canAssign: isRateRequested || isFixedOffer, // Network assignment
      canSubmitRate: false, // TNC doesn't submit rates
      canContact: true,
      canManageAffiliates: true, // Network management
      canViewAnalytics: true, // Network metrics
      canImpersonate: false, // No impersonation
    };
  }

  // Super Client permissions (Empowered Client with granular permissions)
  if (isSuperClient && granularPermissions) {
    const fp = granularPermissions.feature_permissions;
    return {
      canSendToAffiliates: fp["quotes.send_to_affiliates"] && isPending,
      canFixedRate: fp["quotes.fixed_rate"] && isPending,
      canAccept: fp["quotes.accept"] && (isFixedOffer || isRateRequested),
      canReject: fp["quotes.reject"] && isActive,
      canRequestChanges: fp["quotes.request_changes"] && isActive,
      canArchive: fp["quotes.archive"] && isActive,
      canViewDetails: true,
      canEdit: fp["quotes.edit"] && isActive,
      canDelete: false, // Super Clients cannot delete
      canAssign: fp["quotes.assign"] && (isRateRequested || isFixedOffer),
      canSubmitRate: false,
      canContact: fp["affiliates.contact"] && isActive,
      canManageAffiliates: fp["affiliates.manage"] || false,
      canViewAnalytics:
        fp["analytics.advanced"] || fp["analytics.basic"] || false,
      canImpersonate: false, // No impersonation for Super Clients
    };
  }

  // Regular Client permissions (Standard User - Event Manager Layout)
  if (isClient || userType === "admin") {
    const basePermissions = {
      // Simplified capabilities for regular clients
      canSendToAffiliates: false, // Platform handles this
      canFixedRate: false, // Cannot set rates directly
      canAccept: isFixedOffer, // Final decision on quotes
      canReject: isFixedOffer, // Final decision on quotes
      canRequestChanges: false, // Via platform only
      canArchive: isActive, // Organization cleanup
      canViewDetails: true,
      canEdit: isPending, // Basic quote editing
      canDelete: false, // No deletion rights
      canAssign: false, // No direct assignment
      canSubmitRate: false,
      canContact: false, // No direct affiliate contact
      canManageAffiliates: false, // View only
      canViewAnalytics: true, // Basic reporting only
      canImpersonate: false,
    };

    // Tenant-specific adjustments
    switch (tenantType) {
      case "white_label":
        // White-label clients have more control over their quotes
        return {
          ...basePermissions,
          canAccept: isFixedOffer || isRateRequested,
          canReject: isActive,
          canRequestChanges: isActive,
          canManageAffiliates: true, // Can manage their exclusive affiliates
        };

      case "segregated":
        // TNC admins have operational control but limited cross-network access
        return {
          ...basePermissions,
          canAccept: isFixedOffer || isRateRequested,
          canReject: isActive,
          canRequestChanges: isActive,
          canManageAffiliates: true, // Can manage network participation
        };

      case "shared":
      default:
        // Shared tenant clients have standard permissions
        return basePermissions;
    }
  }

  // Affiliate permissions
  if (isAffiliate || userType === "affiliate") {
    return {
      canSendToAffiliates: false,
      canFixedRate: false,
      canAccept: isRateRequested, // Can accept rate requests
      canReject: isRateRequested,
      canRequestChanges: false,
      canArchive: false,
      canViewDetails: true,
      canEdit: false,
      canDelete: false,
      canAssign: false,
      canSubmitRate: isRateRequested,
      canContact: isActive,
      canManageAffiliates: false,
      canViewAnalytics: false,
      canImpersonate: false,
    };
  }

  // Customer/Passenger permissions
  if (isCustomer || userType === "customer") {
    return {
      canSendToAffiliates: false,
      canFixedRate: false,
      canAccept: isFixedOffer,
      canReject: isFixedOffer,
      canRequestChanges: isFixedOffer,
      canArchive: false,
      canViewDetails: true,
      canEdit: false,
      canDelete: false,
      canAssign: false,
      canSubmitRate: false,
      canContact: isActive,
      canManageAffiliates: false,
      canViewAnalytics: false,
      canImpersonate: false,
    };
  }

  // Default: no permissions
  return {
    canSendToAffiliates: false,
    canFixedRate: false,
    canAccept: false,
    canReject: false,
    canRequestChanges: false,
    canArchive: false,
    canViewDetails: true,
    canEdit: false,
    canDelete: false,
    canAssign: false,
    canSubmitRate: false,
    canContact: false,
    canManageAffiliates: false,
    canViewAnalytics: false,
    canImpersonate: false,
  };
}

/**
 * Get tenant-specific button styling for UI customization
 */
export function getTenantButtonStyle(
  tenantType: "shared" | "segregated" | "white_label" | undefined,
  branding: { primary_color?: string; secondary_color?: string } | undefined,
  variant: "primary" | "secondary" | "outline" = "primary"
) {
  if (tenantType === "white_label" && branding?.primary_color) {
    const primaryColor = branding.primary_color;
    switch (variant) {
      case "primary":
        return {
          style: { backgroundColor: primaryColor, borderColor: primaryColor },
          className: "text-white hover:opacity-90",
        };
      case "outline":
        return {
          style: { borderColor: primaryColor, color: primaryColor },
          className: "bg-transparent hover:bg-opacity-10",
        };
      case "secondary":
        return {
          style: {
            backgroundColor: branding.secondary_color || primaryColor,
            borderColor: branding.secondary_color || primaryColor,
          },
          className: "text-white hover:opacity-90",
        };
      default:
        return {};
    }
  }

  // Default styling for shared/segregated tenants
  return {};
}

/**
 * Get user-friendly action labels based on tenant type and context
 */
export function getActionLabels(
  tenantType: "shared" | "segregated" | "white_label" | undefined
) {
  switch (tenantType) {
    case "white_label":
      return {
        sendToAffiliates: "Send to Partners",
        requestRates: "Request Quotes",
        fixedRate: "Set Fixed Price",
        submitRate: "Submit Quote",
      };
    case "segregated":
      return {
        sendToAffiliates: "Send to Network",
        requestRates: "Request Network Rates",
        fixedRate: "Fixed Network Rate",
        submitRate: "Submit Network Rate",
      };
    case "shared":
    default:
      return {
        sendToAffiliates: "Send to Affiliates",
        requestRates: "Request Rates",
        fixedRate: "Fixed Rate",
        submitRate: "Submit Rate",
      };
  }
}
