import { hasRole } from '@/app/lib/auth';
import type { UserRole } from '@/src/types/roles';

export interface QuoteActionPermissions {
  canSendToAffiliates: boolean;
  canFixedRate: boolean;
  canAccept: boolean;
  canReject: boolean;
  canRequestChanges: boolean;
  canArchive: boolean;
  canViewDetails: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canAssign: boolean;
  canSubmitRate: boolean;
  canContact: boolean;
  canManageAffiliates: boolean;
  canViewAnalytics: boolean;
  canImpersonate: boolean;
}

export interface QuotePermissionContext {
  userRoles: string[] | undefined;
  tenantType: 'shared' | 'segregated' | 'white_label' | undefined;
  quoteStatus: string;
  userType: 'admin' | 'customer' | 'affiliate' | 'driver';
  isQuoteOwner?: boolean;
  organizationId?: string;
}

/**
 * Get comprehensive quote action permissions based on user role, tenant type, and quote status
 */
export function getQuoteActionPermissions(context: QuotePermissionContext): QuoteActionPermissions {
  const { userRoles, tenantType, quoteStatus, userType, isQuoteOwner = false } = context;
  const roles = userRoles || [];
  
  // Role checks
  const isSuperAdmin = hasRole(roles, ['SUPER_ADMIN']);
  const isClient = hasRole(roles, ['CLIENT']);
  const isAffiliate = hasRole(roles, ['AFFILIATE', 'AFFILIATE_DISPATCH']);
  const isCustomer = hasRole(roles, ['PASSENGER']);
  
  // Status checks
  const status = quoteStatus.toLowerCase();
  const isPending = ['pending', 'pending_quote', 'new'].includes(status);
  const isRateRequested = ['rate_requested', 'sent_to_affiliates'].includes(status);
  const isFixedOffer = ['fixed_offer', 'quote_ready', 'quote_assigned'].includes(status);
  const isAccepted = status === 'accepted';
  const isRejected = status === 'rejected';
  const isActive = !isAccepted && !isRejected;

  // Super Admin permissions (full access across all tenants)
  if (isSuperAdmin) {
    return {
      canSendToAffiliates: isPending,
      canFixedRate: isPending,
      canAccept: isFixedOffer || isRateRequested,
      canReject: isActive,
      canRequestChanges: isActive,
      canArchive: isActive,
      canViewDetails: true,
      canEdit: isActive,
      canDelete: true,
      canAssign: isRateRequested || isFixedOffer,
      canSubmitRate: false, // Super admin doesn't submit rates
      canContact: true,
      canManageAffiliates: true,
      canViewAnalytics: true,
      canImpersonate: true
    };
  }

  // Client Admin permissions (organization level)
  if (isClient || userType === 'admin') {
    const basePermissions = {
      canSendToAffiliates: isPending,
      canFixedRate: isPending,
      canAccept: false, // Clients don't accept quotes directly in most cases
      canReject: false,
      canRequestChanges: false,
      canArchive: isActive,
      canViewDetails: true,
      canEdit: isPending,
      canDelete: isPending,
      canAssign: isRateRequested || isFixedOffer,
      canSubmitRate: false,
      canContact: isActive,
      canManageAffiliates: false,
      canViewAnalytics: true,
      canImpersonate: false
    };

    // Tenant-specific adjustments
    switch (tenantType) {
      case 'white_label':
        // White-label clients have more control over their quotes
        return {
          ...basePermissions,
          canAccept: isFixedOffer || isRateRequested,
          canReject: isActive,
          canRequestChanges: isActive,
          canManageAffiliates: true // Can manage their exclusive affiliates
        };
      
      case 'segregated':
        // TNC admins have operational control but limited cross-network access
        return {
          ...basePermissions,
          canAccept: isFixedOffer || isRateRequested,
          canReject: isActive,
          canRequestChanges: isActive,
          canManageAffiliates: true // Can manage network participation
        };
      
      case 'shared':
      default:
        // Shared tenant clients have standard permissions
        return basePermissions;
    }
  }

  // Affiliate permissions
  if (isAffiliate || userType === 'affiliate') {
    return {
      canSendToAffiliates: false,
      canFixedRate: false,
      canAccept: isRateRequested, // Can accept rate requests
      canReject: isRateRequested,
      canRequestChanges: false,
      canArchive: false,
      canViewDetails: true,
      canEdit: false,
      canDelete: false,
      canAssign: false,
      canSubmitRate: isRateRequested,
      canContact: isActive,
      canManageAffiliates: false,
      canViewAnalytics: false,
      canImpersonate: false
    };
  }

  // Customer/Passenger permissions
  if (isCustomer || userType === 'customer') {
    return {
      canSendToAffiliates: false,
      canFixedRate: false,
      canAccept: isFixedOffer,
      canReject: isFixedOffer,
      canRequestChanges: isFixedOffer,
      canArchive: false,
      canViewDetails: true,
      canEdit: false,
      canDelete: false,
      canAssign: false,
      canSubmitRate: false,
      canContact: isActive,
      canManageAffiliates: false,
      canViewAnalytics: false,
      canImpersonate: false
    };
  }

  // Default: no permissions
  return {
    canSendToAffiliates: false,
    canFixedRate: false,
    canAccept: false,
    canReject: false,
    canRequestChanges: false,
    canArchive: false,
    canViewDetails: true,
    canEdit: false,
    canDelete: false,
    canAssign: false,
    canSubmitRate: false,
    canContact: false,
    canManageAffiliates: false,
    canViewAnalytics: false,
    canImpersonate: false
  };
}

/**
 * Get tenant-specific button styling for UI customization
 */
export function getTenantButtonStyle(
  tenantType: 'shared' | 'segregated' | 'white_label' | undefined,
  branding: { primary_color?: string; secondary_color?: string } | undefined,
  variant: 'primary' | 'secondary' | 'outline' = 'primary'
) {
  if (tenantType === 'white_label' && branding?.primary_color) {
    const primaryColor = branding.primary_color;
    switch (variant) {
      case 'primary':
        return { 
          style: { backgroundColor: primaryColor, borderColor: primaryColor },
          className: "text-white hover:opacity-90"
        };
      case 'outline':
        return { 
          style: { borderColor: primaryColor, color: primaryColor },
          className: "bg-transparent hover:bg-opacity-10"
        };
      case 'secondary':
        return { 
          style: { backgroundColor: branding.secondary_color || primaryColor, borderColor: branding.secondary_color || primaryColor },
          className: "text-white hover:opacity-90"
        };
      default:
        return {};
    }
  }
  
  // Default styling for shared/segregated tenants
  return {};
}

/**
 * Get user-friendly action labels based on tenant type and context
 */
export function getActionLabels(tenantType: 'shared' | 'segregated' | 'white_label' | undefined) {
  switch (tenantType) {
    case 'white_label':
      return {
        sendToAffiliates: 'Send to Partners',
        requestRates: 'Request Quotes',
        fixedRate: 'Set Fixed Price',
        submitRate: 'Submit Quote'
      };
    case 'segregated':
      return {
        sendToAffiliates: 'Send to Network',
        requestRates: 'Request Network Rates',
        fixedRate: 'Fixed Network Rate',
        submitRate: 'Submit Network Rate'
      };
    case 'shared':
    default:
      return {
        sendToAffiliates: 'Send to Affiliates',
        requestRates: 'Request Rates',
        fixedRate: 'Fixed Rate',
        submitRate: 'Submit Rate'
      };
  }
}
