"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { useAuth } from "@/lib/auth/context";
import { getSupabaseClient } from "@/lib/supabase";

export interface Tenant {
  id: string;
  name: string;
  slug: string;
  domain?: string;
  parent_tenant_id?: string;
  tenant_type: "shared" | "segregated" | "white_label";
  status: "active" | "inactive" | "suspended";
  branding: {
    logo_url?: string;
    primary_color?: string;
    secondary_color?: string;
    background_color?: string;
    custom_css?: string;
  };
  settings: {
    allowGlobalNetwork?: boolean;
    upsell_threshold?: number;
    [key: string]: any;
  };
  created_at: string;
  updated_at: string;
}

export interface TenantBranding {
  id: string;
  tenant_id: string;
  logo_url?: string;
  favicon_url?: string;
  primary_color?: string;
  secondary_color?: string;
  background_color?: string;
  custom_css?: string;
  email_templates: Record<string, string>;
}

interface TenantContextType {
  currentTenant: Tenant | null;
  availableTenants: Tenant[];
  isLoading: boolean;
  error: string | null;
  switchTenant: (tenantId: string) => Promise<void>;
  refreshTenants: () => Promise<void>;
  getTenantBranding: (tenantId: string) => Promise<TenantBranding | null>;
  isSuperAdmin: boolean;
}

const TenantContext = createContext<TenantContextType | undefined>(undefined);

interface TenantProviderProps {
  children: ReactNode;
}

export function TenantProvider({ children }: TenantProviderProps) {
  const { user } = useAuth();
  const [currentTenant, setCurrentTenant] = useState<Tenant | null>(null);
  const [availableTenants, setAvailableTenants] = useState<Tenant[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);
  const supabase = getSupabaseClient();

  // Check if user is super admin
  useEffect(() => {
    const checkSuperAdmin = async () => {
      if (!user || !supabase) return;

      try {
        const { data: profile } = await supabase
          .from("profiles")
          .select("roles")
          .eq("id", user.id)
          .single();

        setIsSuperAdmin(profile?.roles?.includes("SUPER_ADMIN") || false);
      } catch (error) {
        console.error("Error checking super admin status:", error);
      }
    };

    checkSuperAdmin();
  }, [user, supabase]);

  // Load available tenants
  const refreshTenants = async () => {
    if (!user || !supabase) return;

    try {
      setIsLoading(true);
      setError(null);

      // Super admins can see all tenants, others see only their assigned tenants
      let query = supabase.from("tenants").select("*");

      if (!isSuperAdmin) {
        // For non-super admins, we'll need to implement tenant assignment logic
        // For now, just show shared tenant
        query = query.eq("tenant_type", "shared");
      }

      const { data: tenants, error: tenantsError } = await query.order("name");

      if (tenantsError) throw tenantsError;

      setAvailableTenants(tenants || []);

      // Set default tenant if none selected
      if (!currentTenant && tenants && tenants.length > 0) {
        // Default to shared tenant or first available
        const defaultTenant =
          tenants.find((t) => t.tenant_type === "shared") || tenants[0];
        setCurrentTenant(defaultTenant);

        // Set tenant context in Supabase
        if (defaultTenant) {
          await supabase.rpc("set_config", {
            setting_name: "app.current_tenant_id",
            setting_value: defaultTenant.id,
            is_local: true,
          });
        }
      }
    } catch (err) {
      console.error("Error loading tenants:", err);
      setError(err instanceof Error ? err.message : "Failed to load tenants");
    } finally {
      setIsLoading(false);
    }
  };

  // Switch tenant context
  const switchTenant = async (tenantId: string) => {
    if (!supabase) return;

    try {
      const tenant = availableTenants.find((t) => t.id === tenantId);
      if (!tenant) throw new Error("Tenant not found");

      setCurrentTenant(tenant);

      // Set tenant context in Supabase for RLS
      await supabase.rpc("set_config", {
        setting_name: "app.current_tenant_id",
        setting_value: tenantId,
        is_local: true,
      });

      // Store in localStorage for persistence
      localStorage.setItem("currentTenantId", tenantId);
    } catch (err) {
      console.error("Error switching tenant:", err);
      setError(err instanceof Error ? err.message : "Failed to switch tenant");
    }
  };

  // Get tenant branding
  const getTenantBranding = async (
    tenantId: string
  ): Promise<TenantBranding | null> => {
    if (!supabase) return null;

    try {
      const { data, error } = await supabase
        .from("tenant_branding")
        .select("*")
        .eq("tenant_id", tenantId)
        .single();

      if (error && error.code !== "PGRST116") throw error; // PGRST116 = no rows returned
      return data;
    } catch (err) {
      console.error("Error loading tenant branding:", err);
      return null;
    }
  };

  // Initialize tenants on mount
  useEffect(() => {
    if (user) {
      refreshTenants();
    }
  }, [user, isSuperAdmin]);

  // Restore tenant from localStorage
  useEffect(() => {
    const savedTenantId = localStorage.getItem("currentTenantId");
    if (savedTenantId && availableTenants.length > 0) {
      const savedTenant = availableTenants.find((t) => t.id === savedTenantId);
      if (savedTenant) {
        switchTenant(savedTenantId);
      }
    }
  }, [availableTenants]);

  const value: TenantContextType = {
    currentTenant,
    availableTenants,
    isLoading,
    error,
    switchTenant,
    refreshTenants,
    getTenantBranding,
    isSuperAdmin,
  };

  return (
    <TenantContext.Provider value={value}>{children}</TenantContext.Provider>
  );
}

export function useTenant() {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error("useTenant must be used within a TenantProvider");
  }
  return context;
}

// Helper hook for tenant-aware queries
export function useTenantQuery() {
  const { currentTenant } = useTenant();
  const supabase = getSupabaseClient();

  const getTenantAwareQuery = (tableName: string) => {
    if (!supabase || !currentTenant) return null;

    return supabase
      .from(tableName)
      .select("*")
      .eq("tenant_id", currentTenant.id);
  };

  return { getTenantAwareQuery, currentTenant };
}
