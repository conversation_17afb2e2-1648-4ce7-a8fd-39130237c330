"use client"

import { useState } from "react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card"
import { But<PERSON> } from "@/app/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/app/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { Input } from "@/app/components/ui/input"
import { Label } from "@/app/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import { Badge } from "@/app/components/ui/badge"
import { Plus, Search, MoreVertical, Phone, Mail, Car, AlertCircle, Loader2 } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/app/components/ui/avatar"
import { useAffiliateAccess } from '@/app/lib/hooks/useAffiliateAccess'
import { useAffiliateCompany } from '@/app/contexts/AffiliateCompanyContext'
import { AffiliateOnboardingBanner } from '@/app/components/ui/AffiliateOnboardingBanner'
import { useToast } from "@/app/components/ui/use-toast"
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"

// Define driver type
interface Driver {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  profileImage?: string;
  status: 'active' | 'inactive' | 'pending';
  licenseInfo: {
    number: string;
    state: string;
    expirationDate: string;
    class: string;
  };
  metrics?: {
    rating: number;
    completedTrips: number;
    completionRate: number;
    onTimeRate: number;
  };
  createdAt: string;
  updatedAt: string;
}

// Define driver form type
interface DriverFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  profileImage?: string;
  licenseNumber: string;
  licenseState: string;
  licenseExpiration: string;
  licenseClass: string;
}

export default function DriversPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [isAddingDriver, setIsAddingDriver] = useState(false)
  const [editingDriver, setEditingDriver] = useState<Driver | null>(null)
  const [formData, setFormData] = useState<DriverFormData>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    profileImage: "",
    licenseNumber: "",
    licenseState: "",
    licenseExpiration: "",
    licenseClass: "",
  })

  const { showOnboardingBanner } = useAffiliateAccess()
  const { selectedCompany } = useAffiliateCompany()
  const { toast } = useToast()
  const queryClient = useQueryClient()

  // Use React Query to fetch drivers
  const {
    data: drivers,
    isLoading,
    error
  } = useQuery({
    queryKey: ['drivers', selectedCompany?.id],
    queryFn: async () => {
      if (!selectedCompany?.id) {
        return [];
      }

      const response = await fetch(`/api/affiliate/companies/${selectedCompany.id}/drivers`, {
        headers: {
          'X-Affiliate-Company-ID': selectedCompany.id
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch drivers');
      }

      return response.json();
    },
    enabled: !!selectedCompany?.id
  });

  // Mutation for adding a driver
  const addDriverMutation = useMutation({
    mutationFn: async (driverData: DriverFormData) => {
      if (!selectedCompany?.id) {
        throw new Error('No company selected');
      }

      const response = await fetch(`/api/affiliate/companies/${selectedCompany.id}/drivers`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Affiliate-Company-ID': selectedCompany.id
        },
        body: JSON.stringify({
          firstName: driverData.firstName,
          lastName: driverData.lastName,
          email: driverData.email,
          phone: driverData.phone,
          profileImage: driverData.profileImage,
          licenseInfo: {
            number: driverData.licenseNumber,
            state: driverData.licenseState,
            expirationDate: driverData.licenseExpiration,
            class: driverData.licenseClass,
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to add driver');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['drivers', selectedCompany?.id] });
      toast({
        title: "Driver added",
        description: "The driver has been added successfully",
      });
      setIsAddingDriver(false);
      resetForm();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      });
    }
  });

  // Mutation for updating a driver
  const updateDriverMutation = useMutation({
    mutationFn: async ({ id, driverData }: { id: string, driverData: DriverFormData }) => {
      if (!selectedCompany?.id) {
        throw new Error('No company selected');
      }

      const response = await fetch(`/api/affiliate/companies/${selectedCompany.id}/drivers/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-Affiliate-Company-ID': selectedCompany.id
        },
        body: JSON.stringify({
          firstName: driverData.firstName,
          lastName: driverData.lastName,
          email: driverData.email,
          phone: driverData.phone,
          profileImage: driverData.profileImage,
          licenseInfo: {
            number: driverData.licenseNumber,
            state: driverData.licenseState,
            expirationDate: driverData.licenseExpiration,
            class: driverData.licenseClass,
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update driver');
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['drivers', selectedCompany?.id] });
      toast({
        title: "Driver updated",
        description: "The driver has been updated successfully",
      });
      setEditingDriver(null);
      resetForm();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      });
    }
  });

  // Filter drivers based on search term
  const filteredDrivers = drivers?.filter((driver: Driver) => {
    const fullName = `${driver.firstName} ${driver.lastName}`.toLowerCase();
    return fullName.includes(searchTerm.toLowerCase()) ||
           driver.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
           driver.phone.includes(searchTerm);
  }) || [];

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = () => {
    if (editingDriver) {
      updateDriverMutation.mutate({ id: editingDriver.id, driverData: formData });
    } else {
      addDriverMutation.mutate(formData);
    }
  };

  const openEditModal = (driver: Driver) => {
    setEditingDriver(driver);
    setFormData({
      firstName: driver.firstName,
      lastName: driver.lastName,
      email: driver.email,
      phone: driver.phone,
      profileImage: driver.profileImage || "",
      licenseNumber: driver.licenseInfo.number,
      licenseState: driver.licenseInfo.state,
      licenseExpiration: driver.licenseInfo.expirationDate,
      licenseClass: driver.licenseInfo.class,
    });
  };

  const resetForm = () => {
    setFormData({
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      profileImage: "",
      licenseNumber: "",
      licenseState: "",
      licenseExpiration: "",
      licenseClass: "",
    });
  };

  const cancelEdit = () => {
    setEditingDriver(null);
    resetForm();
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, string> = {
      active: "bg-green-100 text-green-800 border-green-200",
      inactive: "bg-gray-100 text-gray-800 border-gray-200",
      suspended: "bg-red-100 text-red-800 border-red-200",
      pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
    }

    return (
      <Badge variant="outline" className={variants[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  // If selected company has no id, show a warning
  if (!selectedCompany?.id) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center justify-center p-6 bg-yellow-50 rounded-lg">
          <AlertCircle className="h-5 w-5 text-yellow-500 mr-2" />
          <p>Please select a company to manage drivers.</p>
        </div>
      </div>
    );
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center justify-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="ml-2">Loading drivers...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center justify-center p-6 bg-red-50 rounded-lg">
          <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
          <p>Error loading drivers: {error instanceof Error ? error.message : 'Unknown error'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {showOnboardingBanner && <AffiliateOnboardingBanner />}
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-2xl font-semibold">Drivers</h3>
          <p className="text-sm text-muted-foreground">
            Manage your fleet drivers and their assignments
          </p>
        </div>
        <Button onClick={() => { setIsAddingDriver(true); setEditingDriver(null); }}>
          <Plus className="mr-2 h-4 w-4" /> Add Driver
        </Button>
      </div>

      {/* Search and filters */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search drivers..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Select defaultValue="all">
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Drivers table */}
      <Card>
        <CardHeader>
          <CardTitle>Drivers</CardTitle>
          <CardDescription>
            Manage your driver information and documentation
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredDrivers.length === 0 ? (
            <div className="py-12 text-center text-muted-foreground">
              {searchTerm ? 'No drivers match your search criteria.' : 'No drivers found. Add your first driver to get started.'}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Driver</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>License</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Vehicle Preference</TableHead>
                  <TableHead>Rating</TableHead>
                  <TableHead>Trips</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredDrivers.map((driver: Driver) => (
                  <TableRow key={driver.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          {driver.profileImage ? (
                            <AvatarImage src={driver.profileImage} alt={`${driver.firstName} ${driver.lastName}`} />
                          ) : null}
                          <AvatarFallback>
                            {driver.firstName[0]}{driver.lastName[0]}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{driver.firstName} {driver.lastName}</div>
                          <div className="text-xs text-muted-foreground">ID: {driver.id}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-1 text-sm">
                          <Phone className="h-3 w-3" />
                          {driver.phone}
                        </div>
                        <div className="flex items-center gap-1 text-sm">
                          <Mail className="h-3 w-3" />
                          {driver.email}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{driver.licenseInfo.number}</TableCell>
                    <TableCell>{getStatusBadge(driver.status)}</TableCell>
                    <TableCell>{"Vehicle preference"}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <span className="font-medium">{driver.metrics?.rating || "N/A"}</span>
                        {driver.metrics?.rating && <span className="text-yellow-500">★</span>}
                      </div>
                    </TableCell>
                    <TableCell>{driver.metrics?.completedTrips || 0}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => openEditModal(driver)}>
                            Edit Driver
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            View Documents
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Driver Dialog */}
      <Dialog open={isAddingDriver || editingDriver !== null} onOpenChange={(open) => {
        if (!open) {
          setIsAddingDriver(false);
          setEditingDriver(null);
          resetForm();
        }
      }}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{editingDriver ? "Edit Driver" : "Add New Driver"}</DialogTitle>
            <DialogDescription>
              {editingDriver
                ? "Update driver information and documentation."
                : "Enter the driver details to add them to your fleet."}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  name="firstName"
                  value={formData.firstName}
                  onChange={handleFormChange}
                  placeholder="John"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  name="lastName"
                  value={formData.lastName}
                  onChange={handleFormChange}
                  placeholder="Doe"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleFormChange}
                placeholder="<EMAIL>"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleFormChange}
                placeholder="(*************"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="profileImage">Profile Image URL</Label>
              <Input
                id="profileImage"
                name="profileImage"
                value={formData.profileImage}
                onChange={handleFormChange}
                placeholder="https://example.com/profile.jpg"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="licenseNumber">License Number</Label>
                <Input
                  id="licenseNumber"
                  name="licenseNumber"
                  value={formData.licenseNumber}
                  onChange={handleFormChange}
                  placeholder="**********"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="licenseState">State</Label>
                <Input
                  id="licenseState"
                  name="licenseState"
                  value={formData.licenseState}
                  onChange={handleFormChange}
                  placeholder="NY"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="licenseExpiration">Expiration Date</Label>
                <Input
                  id="licenseExpiration"
                  name="licenseExpiration"
                  type="date"
                  value={formData.licenseExpiration}
                  onChange={handleFormChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="licenseClass">License Class</Label>
                <Input
                  id="licenseClass"
                  name="licenseClass"
                  value={formData.licenseClass}
                  onChange={handleFormChange}
                  placeholder="C"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={editingDriver ? cancelEdit : () => setIsAddingDriver(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={addDriverMutation.isLoading || updateDriverMutation.isLoading}
            >
              {(addDriverMutation.isLoading || updateDriverMutation.isLoading) && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              {editingDriver ? "Update Driver" : "Add Driver"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}