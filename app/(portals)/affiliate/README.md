# Affiliate Portal

## Overview
The Affiliate Portal provides transportation providers with tools to manage their fleet, drivers, rates, and trips. This portal is designed for users with the `AFFILIATE` role.

## Features
- **Dashboard**: Overview of trips, fleet, and performance
- **Trips**: Manage assigned transportation trips
- **Live Trips**: Real-time tracking of active trips
- **Fleet**: Manage vehicles in the fleet
- **Fleet Rates**: Set and manage pricing for different vehicle types
- **Rates**: Manage service rates
- **Drivers**: Manage driver information
- **Offers**: View and respond to transportation offers
- **Company**: Manage company information
- **Settings**: User and company settings

## Directory Structure
- `dashboard/` - Affiliate dashboard
- `trips/` - Trip management
- `liveTrips/` - Real-time trip tracking
- `fleet/` - Fleet management
- `fleetRates/` - Fleet pricing management
- `rates/` - Service rate management
- `drivers/` - Driver management
- `offers/` - Transportation offer management
- `company/` - Company information management
- `settings/` - Settings management
- `components/` - Affiliate-specific components

## Naming Conventions
All directories now use camelCase for consistency with the rest of the application.

## Key Components
- `layout.tsx` - Defines the layout for the affiliate portal
- `dashboard/page.tsx` - Dashboard page
- `trips/page.tsx` - Trips management page
- `fleet/page.tsx` - Fleet management page

## API Integration
The affiliate portal interacts with several API endpoints:
- `/api/trips` - Trip management
- `/api/fleet` - Fleet management
- `/api/drivers` - Driver management
- `/api/rates` - Rate management
- `/api/offers` - Offer management

## Testing
Tests for the affiliate portal are located in `/tests/affiliate/` and should mirror this directory structure. 