'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/app/components/ui/tabs"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import { Search, FilterX, Clock, Calendar, BellRing } from "lucide-react"
import { Badge } from "@/app/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { Card, CardHeader, CardTitle, CardContent } from "@/app/components/ui/card"
import { QuoteRow, QuoteRowData } from "@/app/components/shared/rows"
import { useToast } from "@/app/components/ui/use-toast"
import { useRouter } from "next/navigation"
import { useAffiliateAccess } from '@/app/lib/hooks/useAffiliateAccess'
import { AffiliateOnboardingBanner } from '@/app/components/ui/AffiliateOnboardingBanner'

// Create a custom interface that extends QuoteRowData for our affiliate rate requests
interface AffiliateRateRequestData extends QuoteRowData {
  has_responded?: boolean;
  affiliate_response?: {
    rate: number;
    status: string;
    submitted_at?: string;
  }
}

export default function AffiliateRateRequestsPage() {
  const { showOnboardingBanner } = useAffiliateAccess()
  const [rateRequests, setRateRequests] = useState<AffiliateRateRequestData[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedRequest, setSelectedRequest] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredRequests, setFilteredRequests] = useState<{
    all: AffiliateRateRequestData[];
    new: AffiliateRateRequestData[];
    responded: AffiliateRateRequestData[];
    expired: AffiliateRateRequestData[];
  }>({
    all: [],
    new: [],
    responded: [],
    expired: []
  })
  
  const { toast } = useToast()
  const router = useRouter()
  
  // Fetch rate requests data
  useEffect(() => {
    const fetchRateRequests = async () => {
      try {
        setLoading(true)
        
        const response = await fetch('/api/affiliate/rate-requests')
        
        if (!response.ok) {
          throw new Error(`Error: ${response.statusText}`)
        }
        
        const data = await response.json()
        
        if (data && Array.isArray(data.requests)) {
          // Map the API data to QuoteRowData format
          const mappedRequests: AffiliateRateRequestData[] = data.requests.map((req: any) => ({
            id: req.id,
            reference_number: req.reference_number,
            pickup_location: req.pickup_location || 'N/A',
            dropoff_location: req.dropoff_location || 'N/A',
            date: req.date,
            time: req.time,
            vehicle_type: req.vehicle_type || 'Standard',
            status: 'rate_requested', // All items in this view are rate requests
            passenger_count: req.passenger_count,
            special_requirements: req.special_requirements,
            priority: req.priority || 'medium',
            city: req.city,
            created_at: req.created_at,
            last_updated: req.updated_at,
            expiry_time: req.expiry_time,
            has_responded: req.has_responded || false,
            // Add this as a custom property for the affiliate view
            affiliate_response: req.has_responded ? {
              rate: req.rate || 0,
              status: 'submitted',
              submitted_at: req.response_date
            } : undefined
          })) as AffiliateRateRequestData[]
          
          setRateRequests(mappedRequests)
        } else {
          setRateRequests([])
        }
      } catch (err) {
        console.error('Error fetching rate requests:', err)
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setLoading(false)
      }
    }
    
    fetchRateRequests()
  }, [])
  
  // Filter rate requests
  useEffect(() => {
    const now = new Date()
    
    const filtered = {
      all: rateRequests.filter(req => filterBySearch(req)),
      new: rateRequests.filter(req => 
        filterBySearch(req) && 
        !req.has_responded && 
        new Date(req.expiry_time || '') > now
      ),
      responded: rateRequests.filter(req => 
        filterBySearch(req) && 
        req.has_responded
      ),
      expired: rateRequests.filter(req => 
        filterBySearch(req) && 
        !req.has_responded && 
        new Date(req.expiry_time || '') <= now
      )
    }
    
    setFilteredRequests(filtered)
  }, [rateRequests, searchTerm])
  
  // Search filter function
  const filterBySearch = (request: AffiliateRateRequestData) => {
    if (!searchTerm) return true
    
    const search = searchTerm.toLowerCase()
    return (
      (request.pickup_location?.toLowerCase().includes(search)) ||
      (request.dropoff_location?.toLowerCase().includes(search)) ||
      (request.vehicle_type?.toLowerCase().includes(search)) ||
      (request.reference_number && request.reference_number.toLowerCase().includes(search))
    )
  }
  
  // Handle actions
  const handleRequestSelection = (request: AffiliateRateRequestData) => {
    setSelectedRequest(request.id === selectedRequest ? null : request.id)
    router.push(`/affiliate/rate-requests/${request.id}`)
  }
  
  const handleSubmitRate = (requestId: string) => {
    // Find the request
    const request = rateRequests.find(r => r.id === requestId)
    if (!request) return
    
    // Navigate to the rate submission page
    router.push(`/affiliate/rate-requests/${requestId}/submit`)
  }
  
  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }
  
  // Error state
  if (error) {
    return (
      <div className="p-8">
        <div className="bg-destructive/10 p-4 rounded-md text-destructive">
          <p>Error loading rate requests: {error}</p>
          <Button 
            variant="outline" 
            onClick={() => window.location.reload()} 
            className="mt-2"
          >
            Retry
          </Button>
        </div>
      </div>
    )
  }
  
  if (showOnboardingBanner) {
    return (
      <div className="p-8">
        <AffiliateOnboardingBanner>
          <div className="mt-2 text-yellow-800">You must complete onboarding to access rate requests.</div>
        </AffiliateOnboardingBanner>
      </div>
    )
  }
  
  return (
    <div className="p-8 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">Rate Requests</h1>
          <p className="text-muted-foreground">
            View and respond to incoming rate requests from customers
          </p>
        </div>
      </div>
      
      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">New Requests</CardTitle>
            <BellRing className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredRequests.new.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Responded</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredRequests.responded.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Expired</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredRequests.expired.length}</div>
          </CardContent>
        </Card>
      </div>
      
      {/* Search & Filters */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search rate requests..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <Select defaultValue="all">
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Date Filter" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Dates</SelectItem>
            <SelectItem value="today">Today</SelectItem>
            <SelectItem value="expires-today">Expires Today</SelectItem>
            <SelectItem value="this-week">This Week</SelectItem>
          </SelectContent>
        </Select>
        
        {searchTerm && (
          <Button variant="outline" onClick={() => setSearchTerm('')}>
            <FilterX className="mr-2 h-4 w-4" />
            Clear
          </Button>
        )}
      </div>
      
      {/* Rate Requests List */}
      <Tabs defaultValue="all">
        <TabsList>
          <TabsTrigger value="all">
            All <Badge variant="secondary" className="ml-2">{filteredRequests.all.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="new">
            New <Badge variant="secondary" className="ml-2">{filteredRequests.new.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="responded">
            Responded <Badge variant="secondary" className="ml-2">{filteredRequests.responded.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="expired">
            Expired <Badge variant="secondary" className="ml-2">{filteredRequests.expired.length}</Badge>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="all" className="space-y-2 mt-6">
          {filteredRequests.all.length === 0 ? (
            <div className="text-center p-4 border rounded-md">
              <p className="text-muted-foreground">No rate requests found</p>
            </div>
          ) : (
            filteredRequests.all.map((request) => (
              <QuoteRow
                key={request.id}
                quote={request}
                isSelected={selectedRequest === request.id}
                onClick={() => handleRequestSelection(request)}
                onSubmitRate={() => handleSubmitRate(request.id)}
                userType="affiliate"
                expandable={true}
              />
            ))
          )}
        </TabsContent>
        
        <TabsContent value="new" className="space-y-2 mt-6">
          {filteredRequests.new.length === 0 ? (
            <div className="text-center p-4 border rounded-md">
              <p className="text-muted-foreground">No new rate requests found</p>
            </div>
          ) : (
            filteredRequests.new.map((request) => (
              <QuoteRow
                key={request.id}
                quote={request}
                isSelected={selectedRequest === request.id}
                onClick={() => handleRequestSelection(request)}
                onSubmitRate={() => handleSubmitRate(request.id)}
                userType="affiliate"
                expandable={true}
              />
            ))
          )}
        </TabsContent>
        
        <TabsContent value="responded" className="space-y-2 mt-6">
          {filteredRequests.responded.length === 0 ? (
            <div className="text-center p-4 border rounded-md">
              <p className="text-muted-foreground">No responded requests found</p>
            </div>
          ) : (
            filteredRequests.responded.map((request) => (
              <QuoteRow
                key={request.id}
                quote={request}
                isSelected={selectedRequest === request.id}
                onClick={() => handleRequestSelection(request)}
                userType="affiliate"
                expandable={true}
              />
            ))
          )}
        </TabsContent>
        
        <TabsContent value="expired" className="space-y-2 mt-6">
          {filteredRequests.expired.length === 0 ? (
            <div className="text-center p-4 border rounded-md">
              <p className="text-muted-foreground">No expired requests found</p>
            </div>
          ) : (
            filteredRequests.expired.map((request) => (
              <QuoteRow
                key={request.id}
                quote={request}
                isSelected={selectedRequest === request.id}
                onClick={() => handleRequestSelection(request)}
                userType="affiliate"
                expandable={true}
              />
            ))
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
} 