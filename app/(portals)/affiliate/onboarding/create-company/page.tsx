"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useAuth } from "@/lib/auth/context";
import { useAffiliateCompany } from "@/app/contexts/AffiliateCompanyContext";

import { Button } from "@/app/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/app/components/ui/form";
import { Input } from "@/app/components/ui/input";
import { Badge } from "@/app/components/ui/badge";
import { Separator } from "@/app/components/ui/separator";
import { Al<PERSON>, AlertDescription } from "@/app/components/ui/alert";
import { toast } from "@/app/components/ui/use-toast";
import { Building2, MapPin, Phone, Mail, Globe, Sparkles, ArrowRight, CheckCircle, Loader2 } from "lucide-react";

const companyFormSchema = z.object({
  companyName: z.string().min(2, { message: "Company name must be at least 2 characters." }).max(100),
  contactEmail: z.string().email({ message: "Please enter a valid email address." }),
  contactPhone: z.string().min(10, { message: "Phone number seems too short." }).max(20),
  addressLine1: z.string().min(5, { message: "Street address seems too short." }).max(150),
  addressLine2: z.string().max(150).optional().or(z.literal('')), // Allow empty string
  city: z.string().min(2, { message: "City name seems too short." }).max(50),
  stateProvince: z.string().min(2, { message: "State/Province seems too short." }).max(50),
  postalCode: z.string().min(3, { message: "Postal code seems too short." }).max(20),
  country: z.string().min(2, { message: "Country seems too short." }).max(50),
});

type CompanyFormValues = z.infer<typeof companyFormSchema>;

const CreateAffiliateCompanyPage = () => {
  const router = useRouter();
  const { user } = useAuth();
  const { fetchUserCompanies, selectCompany, userCompanies, isLoadingCompanies } = useAffiliateCompany();
  const [isLoading, setIsLoading] = React.useState(false);

  const form = useForm<CompanyFormValues>({
    resolver: zodResolver(companyFormSchema),
    defaultValues: {
      companyName: "",
      contactEmail: "", // Will be set by useEffect
      contactPhone: "",
      addressLine1: "",
      addressLine2: "",
      city: "",
      stateProvince: "",
      postalCode: "",
      country: "",
    },
  });

  // Redirect existing users away from create-company page
  useEffect(() => {
    // Only redirect if we're sure the user has companies and we're not loading
    if (!isLoadingCompanies && userCompanies && userCompanies.length > 0) {
      console.log('User already has companies, redirecting to dashboard');
      toast({
        title: "Company Already Exists",
        description: "You already have a company profile. Redirecting to dashboard.",
      });
      // Use a small delay to ensure the toast is shown
      setTimeout(() => {
        router.push('/affiliate/dashboard');
      }, 1000);
      return;
    }
  }, [isLoadingCompanies, userCompanies, router, toast]);

  useEffect(() => {
    if (user?.email && form.getValues("contactEmail") === "") {
      form.setValue("contactEmail", user.email);
    }
  }, [user, form]);

  async function onSubmit(values: CompanyFormValues) {
    console.log('CreateCompanyPage onSubmit called with values:', values);
    setIsLoading(true);
    try {
      const response = await fetch('/api/affiliate/companies', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(values),
      });

      console.log('CreateCompanyPage fetch response:', response);

      if (!response.ok) {
        let errorData = { message: "Failed to create company. Please try again." };
        try {
            errorData = await response.json();
        } catch (e) {
            errorData.message = response.statusText || errorData.message;
        }
        console.error('CreateCompanyPage API error:', errorData);
        toast({
          title: "Error Creating Company",
          description: errorData.message || "An unexpected error occurred. Please try again.",
          variant: "destructive",
        });
        throw new Error(errorData.message);
      }

      const responseData = await response.json();
      const newCompanyId = responseData.company?.id;

      console.log('Company created successfully:', responseData);
      console.log('New company ID:', newCompanyId);

      // Refetch company list to update context
      console.log('Refetching user companies...');
      await fetchUserCompanies();

      // Force selection of the newly created company after a brief delay to ensure context is updated
      if (newCompanyId) {
        console.log('Attempting to select new company:', newCompanyId);
        setTimeout(() => {
          selectCompany(newCompanyId);
        }, 500); // Increased delay to ensure context is fully updated
      }

      // Show success message
      toast({
        title: "Company Created Successfully!",
        description: "Now let's complete your service profile to start receiving quotes.",
      });

      // Redirect to enhanced form at step 3 (Operations & Services)
      router.push("/affiliate/company/enhanced");

    } catch (error: any) {
      console.error("Failed to create company (catch block):", error);
      toast({
        title: "Error Creating Company",
        description: error.message || "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5">
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        {/* Welcome Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-primary/10 rounded-full">
              <Building2 className="h-8 w-8 text-primary" />
            </div>
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            Welcome to Our Network!
          </h1>
          <p className="text-lg text-muted-foreground mt-2">
            Let's create your company profile to get started
          </p>
          <div className="flex justify-center mt-4">
            <Badge variant="secondary" className="flex items-center gap-1">
              <Sparkles className="h-3 w-3" />
              Step 1 of 2
            </Badge>
          </div>
        </div>

        {/* Main Form Card */}
        <Card className="shadow-xl border-0 bg-card/50 backdrop-blur-sm">
          <CardHeader className="text-center pb-6">
            <CardTitle className="flex items-center justify-center gap-2 text-xl">
              <Building2 className="h-5 w-5 text-primary" />
              Company Information
            </CardTitle>
            <CardDescription>
              Tell us about your transportation company. This information will help us match you with the right opportunities.
            </CardDescription>
          </CardHeader>

          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Company Details Section */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2 text-sm font-medium text-primary">
                    <Building2 className="h-4 w-4" />
                    Company Details
                  </div>

                  <FormField
                    control={form.control}
                    name="companyName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Company Name *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Your Transportation Company"
                            className="h-11"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          The legal name of your transportation business
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <Separator />

                {/* Contact Information Section */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2 text-sm font-medium text-primary">
                    <Phone className="h-4 w-4" />
                    Contact Information
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="contactEmail"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email Address *</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Mail className="absolute left-3 top-3.5 h-4 w-4 text-muted-foreground" />
                              <Input
                                placeholder="<EMAIL>"
                                className="h-11 pl-10"
                                {...field}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="contactPhone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Phone Number *</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Phone className="absolute left-3 top-3.5 h-4 w-4 text-muted-foreground" />
                              <Input
                                placeholder="+****************"
                                className="h-11 pl-10"
                                {...field}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <Separator />

                {/* Address Section */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2 text-sm font-medium text-primary">
                    <MapPin className="h-4 w-4" />
                    Business Address
                  </div>

                  <FormField
                    control={form.control}
                    name="addressLine1"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Street Address *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="123 Main Street"
                            className="h-11"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="addressLine2"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Address Line 2 (Optional)</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Suite 100, Floor 2, etc."
                            className="h-11"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="city"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>City *</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="New York"
                              className="h-11"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="stateProvince"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>State/Province *</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="NY"
                              className="h-11"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="postalCode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Postal Code *</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="10001"
                              className="h-11"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="United States"
                            className="h-11"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Next Steps Info */}
                <Alert className="border-primary/20 bg-primary/5">
                  <CheckCircle className="h-4 w-4 text-primary" />
                  <AlertDescription className="text-primary">
                    <strong>What's next?</strong> After creating your company profile, you'll be guided through completing your business details, fleet information, and rate cards.
                  </AlertDescription>
                </Alert>

                {/* Submit Button */}
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full h-12 text-lg font-medium bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary shadow-lg"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      Creating Your Company...
                    </>
                  ) : (
                    <>
                      Create Company Profile
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </>
                  )}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-8 text-sm text-muted-foreground">
          <p>Need help? Contact our support <NAME_EMAIL></p>
        </div>
      </div>
    </div>
  );
};

export default CreateAffiliateCompanyPage;