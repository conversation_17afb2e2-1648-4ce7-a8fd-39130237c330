"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/app/components/ui/tabs"
import { But<PERSON> } from "@/app/components/ui/button"
import { Badge } from "@/app/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table"
import { Progress } from "@/app/components/ui/progress"
import { Separator } from "@/app/components/ui/separator"
import { 
  DollarSign, 
  TrendingUp, 
  Calendar, 
  Download,
  CreditCard,
  AlertCircle,
  CheckCircle,
  Clock,
  FileText
} from "lucide-react"
import { useAffiliateAccess } from '@/app/lib/hooks/useAffiliateAccess'
import { AffiliateOnboardingBanner } from '@/app/components/ui/AffiliateOnboardingBanner'

// Mock data - replace with real API calls
const mockCommissionData = {
  currentMonth: {
    totalRevenue: 12450.00,
    commissionRate: 3.5,
    commissionOwed: 435.75,
    completedTrips: 28,
    pendingTrips: 3
  },
  yearToDate: {
    totalRevenue: 89650.00,
    commissionOwed: 3137.75,
    completedTrips: 187,
    averageCommissionRate: 3.2
  },
  recentTransactions: [
    {
      id: "TXN-001",
      date: "2024-01-15",
      tripId: "TRIP-4521",
      customerName: "John Smith",
      tripAmount: 450.00,
      commissionRate: 3.5,
      commissionAmount: 15.75,
      status: "completed",
      paidDate: "2024-01-16"
    },
    {
      id: "TXN-002", 
      date: "2024-01-14",
      tripId: "TRIP-4520",
      customerName: "Sarah Johnson",
      tripAmount: 320.00,
      commissionRate: 3.0,
      commissionAmount: 9.60,
      status: "completed",
      paidDate: "2024-01-15"
    },
    {
      id: "TXN-003",
      date: "2024-01-13",
      tripId: "TRIP-4519", 
      customerName: "Mike Davis",
      tripAmount: 680.00,
      commissionRate: 4.0,
      commissionAmount: 27.20,
      status: "pending",
      paidDate: null
    }
  ],
  commissionTiers: [
    { minRevenue: 0, maxRevenue: 10000, rate: 5.0, label: "Starter" },
    { minRevenue: 10000, maxRevenue: 25000, rate: 4.0, label: "Growth" },
    { minRevenue: 25000, maxRevenue: 50000, rate: 3.5, label: "Professional" },
    { minRevenue: 50000, maxRevenue: null, rate: 2.5, label: "Enterprise" }
  ]
}

export default function BillingPage() {
  const { showOnboardingBanner } = useAffiliateAccess()
  const [selectedPeriod, setSelectedPeriod] = useState("current-month")

  if (showOnboardingBanner) {
    return (
      <div className="p-8">
        <AffiliateOnboardingBanner>
          <div className="mt-2 text-yellow-800">Complete onboarding to access billing information.</div>
        </AffiliateOnboardingBanner>
      </div>
    )
  }

  const getCurrentTier = (revenue: number) => {
    return mockCommissionData.commissionTiers.find(tier => 
      revenue >= tier.minRevenue && (tier.maxRevenue === null || revenue < tier.maxRevenue)
    ) || mockCommissionData.commissionTiers[0]
  }

  const currentTier = getCurrentTier(mockCommissionData.yearToDate.totalRevenue)
  const nextTier = mockCommissionData.commissionTiers.find(tier => 
    tier.minRevenue > mockCommissionData.yearToDate.totalRevenue
  )

  return (
    <div className="space-y-6 p-6">
      <div>
        <h3 className="text-2xl font-bold">Billing & Commissions</h3>
        <p className="text-muted-foreground">
          Track your earnings, commission rates, and payment history
        </p>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${mockCommissionData.currentMonth.totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {mockCommissionData.currentMonth.completedTrips} completed trips
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Commission Owed</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${mockCommissionData.currentMonth.commissionOwed.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              {mockCommissionData.currentMonth.commissionRate}% average rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">YTD Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${mockCommissionData.yearToDate.totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {mockCommissionData.yearToDate.completedTrips} total trips
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Tier</CardTitle>
            <Badge variant="secondary">{currentTier.label}</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{currentTier.rate}%</div>
            <p className="text-xs text-muted-foreground">
              Commission rate
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="commission-tiers">Commission Tiers</TabsTrigger>
          <TabsTrigger value="payment-methods">Payment Methods</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Commission Progress */}
            <Card>
              <CardHeader>
                <CardTitle>Commission Tier Progress</CardTitle>
                <CardDescription>
                  Your progress towards the next commission tier
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Current: {currentTier.label} ({currentTier.rate}%)</span>
                    {nextTier && <span>Next: {nextTier.label} ({nextTier.rate}%)</span>}
                  </div>
                  {nextTier && (
                    <>
                      <Progress 
                        value={(mockCommissionData.yearToDate.totalRevenue / nextTier.minRevenue) * 100} 
                        className="w-full" 
                      />
                      <p className="text-xs text-muted-foreground">
                        ${(nextTier.minRevenue - mockCommissionData.yearToDate.totalRevenue).toLocaleString()} more to reach {nextTier.label} tier
                      </p>
                    </>
                  )}
                  {!nextTier && (
                    <p className="text-sm text-green-600 font-medium">
                      🎉 You've reached the highest tier!
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Payment Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Payment Summary</CardTitle>
                <CardDescription>
                  Commission payment status and schedule
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Pending Commission</span>
                    <Badge variant="outline">
                      <Clock className="w-3 h-3 mr-1" />
                      ${mockCommissionData.currentMonth.commissionOwed.toFixed(2)}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Next Payment Date</span>
                    <span className="text-sm font-medium">Jan 31, 2024</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Payment Method</span>
                    <span className="text-sm">Bank Transfer</span>
                  </div>
                </div>
                <Separator />
                <Button className="w-full" variant="outline">
                  <Download className="w-4 h-4 mr-2" />
                  Download Invoice
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Transactions</CardTitle>
              <CardDescription>
                Commission details for your completed trips
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Trip ID</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Trip Amount</TableHead>
                    <TableHead>Rate</TableHead>
                    <TableHead>Commission</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {mockCommissionData.recentTransactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>{new Date(transaction.date).toLocaleDateString()}</TableCell>
                      <TableCell className="font-mono text-sm">{transaction.tripId}</TableCell>
                      <TableCell>{transaction.customerName}</TableCell>
                      <TableCell>${transaction.tripAmount.toFixed(2)}</TableCell>
                      <TableCell>{transaction.commissionRate}%</TableCell>
                      <TableCell className="font-medium">${transaction.commissionAmount.toFixed(2)}</TableCell>
                      <TableCell>
                        <Badge variant={transaction.status === 'completed' ? 'default' : 'secondary'}>
                          {transaction.status === 'completed' ? (
                            <CheckCircle className="w-3 h-3 mr-1" />
                          ) : (
                            <Clock className="w-3 h-3 mr-1" />
                          )}
                          {transaction.status}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="commission-tiers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Commission Tier Structure</CardTitle>
              <CardDescription>
                Lower commission rates as your volume increases
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockCommissionData.commissionTiers.map((tier, index) => (
                  <div 
                    key={index}
                    className={`p-4 border rounded-lg ${
                      tier === currentTier ? 'border-primary bg-primary/5' : 'border-border'
                    }`}
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className="font-medium">{tier.label} Tier</h4>
                        <p className="text-sm text-muted-foreground">
                          {tier.maxRevenue 
                            ? `$${tier.minRevenue.toLocaleString()} - $${tier.maxRevenue.toLocaleString()}`
                            : `$${tier.minRevenue.toLocaleString()}+`
                          } annual revenue
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold">{tier.rate}%</div>
                        <div className="text-sm text-muted-foreground">commission</div>
                      </div>
                    </div>
                    {tier === currentTier && (
                      <Badge className="mt-2">Current Tier</Badge>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payment-methods" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payment Methods</CardTitle>
              <CardDescription>
                Manage how you receive commission payments
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 border rounded-lg">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-medium">Bank Transfer</h4>
                    <p className="text-sm text-muted-foreground">
                      Primary payment method
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Account ending in ****1234
                    </p>
                  </div>
                  <Badge>Primary</Badge>
                </div>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium">Payment Schedule</h4>
                <p className="text-sm text-muted-foreground">
                  Commissions are calculated and paid monthly on the last business day of each month.
                </p>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="w-4 h-4 text-blue-600 mt-0.5" />
                    <div className="text-sm text-blue-800">
                      <p className="font-medium">Payment Terms</p>
                      <p>Minimum payout threshold: $50.00</p>
                      <p>Processing time: 3-5 business days</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
