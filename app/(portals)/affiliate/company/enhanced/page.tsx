"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useAffiliateCompany } from '@/app/contexts/AffiliateCompanyContext'
import { ComprehensiveCompanyForm } from "@/app/components/affiliate/forms/ComprehensiveCompanyForm"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Button } from "@/app/components/ui/button"
import { Badge } from "@/app/components/ui/badge"
import { Progress } from "@/app/components/ui/progress"
import { Alert, AlertDescription } from "@/app/components/ui/alert"
import { Skeleton } from "@/app/components/ui/skeleton"
import { Building2, CheckCircle, Clock, AlertCircle, ArrowLeft } from "lucide-react"
import { toast } from "@/app/components/ui/use-toast"

interface CompanyFormValues {
  companyName: string
  dba?: string
  ownerName: string
  yearEstablished: string
  federalTaxId: string
  contactEmail: string
  contactPhone: string
  website?: string
  addressLine1: string
  addressLine2?: string
  city: string
  stateProvince: string
  postalCode: string
  country: string
  chargeForDelays: boolean
  chargeForStops: boolean
  offerMeetGreet: boolean
  roadShows: boolean
  griddGnetMember: boolean
  addonsLa: boolean
  directBilling: boolean
  livePhoneSupport: boolean
  airportsServed?: string
  languagesSpoken?: string
  dispatchSoftware?: string
  tripStatusUpdates?: string
  citiesCovered?: string[]
  dressCode?: string
  cancellationPolicy?: string
  meetGreet?: string
  businessLicense?: any
  insuranceCertificate?: any
}

export default function EnhancedCompanyProfilePage() {
  const router = useRouter()
  const { selectedCompany, isLoadingCompanies, companyError, fetchUserCompanies } = useAffiliateCompany()

  // Redirect to basic form since enhanced form is not ready
  useEffect(() => {
    router.replace('/affiliate/company')
  }, [router])
  const [companyDetails, setCompanyDetails] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  // Check if user is coming from onboarding (company has basic info but needs operations)
  const isFromOnboarding = companyDetails &&
    companyDetails.name &&
    companyDetails.contact_email &&
    companyDetails.address &&
    (!companyDetails.airports_served || companyDetails.airports_served.length === 0)

  // Calculate completion progress
  const calculateProgress = (data: any) => {
    if (!data) return 0

    // Step 1: Basic Information (25%)
    const basicFields = ['name', 'owner_name', 'year_established', 'federal_tax_id']
    const basicCompleted = basicFields.filter(field =>
      data[field] && data[field].toString().trim() !== ''
    ).length
    const basicProgress = (basicCompleted / basicFields.length) * 25

    // Step 2: Contact & Location (25%)
    const contactFields = ['contact_email', 'contact_phone', 'address', 'city', 'state', 'zip', 'country']
    const contactCompleted = contactFields.filter(field =>
      data[field] && data[field].toString().trim() !== ''
    ).length
    const contactProgress = (contactCompleted / contactFields.length) * 25

    // Step 3: Operations & Services (30%)
    const operationsFields = ['airports_served', 'languages_spoken', 'dispatch_software', 'cities_covered']
    const operationsCompleted = operationsFields.filter(field =>
      data[field] && (
        (Array.isArray(data[field]) && data[field].length > 0) ||
        (typeof data[field] === 'string' && data[field].trim() !== '')
      )
    ).length
    const operationsProgress = (operationsCompleted / operationsFields.length) * 30

    // Step 4: Policies & Preferences (20%)
    const policiesFields = ['dress_code', 'cancellation_policy', 'meet_greet']
    const policiesCompleted = policiesFields.filter(field =>
      data[field] && data[field].toString().trim() !== ''
    ).length
    const policiesProgress = (policiesCompleted / policiesFields.length) * 20

    return Math.round(basicProgress + contactProgress + operationsProgress + policiesProgress)
  }

  // Load company details
  useEffect(() => {
    if (!selectedCompany?.id) {
      setCompanyDetails(null)
      return
    }

    setIsLoading(true)
    fetch(`/api/affiliate/companies/${selectedCompany.id}`, {
      headers: { 'X-Affiliate-Company-ID': selectedCompany.id }
    })
      .then(res => res.json())
      .then(data => {
        setCompanyDetails(data)
      })
      .catch(error => {
        console.error('Error loading company details:', error)
        toast({
          title: "Error",
          description: "Failed to load company details",
          variant: "destructive",
        })
      })
      .finally(() => {
        setIsLoading(false)
      })
  }, [selectedCompany?.id])

  // Transform data for the form
  const getFormData = (data: any): Partial<CompanyFormValues> => {
    if (!data) return {}

    return {
      companyName: data.name || "",
      dba: data.dba || "",
      ownerName: data.owner_name || "",
      yearEstablished: data.year_established || "",
      federalTaxId: data.federal_tax_id || "",
      contactEmail: data.contact_email || data.email || "",
      contactPhone: data.contact_phone || data.phone || "",
      website: data.website || "",
      addressLine1: data.address || "",
      addressLine2: data.address_line_2 || "",
      city: data.city || "",
      stateProvince: data.state || "",
      postalCode: data.zip || "",
      country: data.country || "USA",
      chargeForDelays: !!data.charge_for_delays,
      chargeForStops: !!data.charge_for_stops,
      offerMeetGreet: !!data.offer_meet_greet,
      roadShows: !!data.road_shows,
      griddGnetMember: !!data.gridd_gnet_member,
      addonsLa: !!data.addons_la,
      directBilling: !!data.direct_billing,
      livePhoneSupport: !!data.live_phone_support,
      airportsServed: Array.isArray(data.airports_served) ? data.airports_served.join(', ') : data.airports_served || "",
      languagesSpoken: Array.isArray(data.languages_spoken) ? data.languages_spoken.join(', ') : data.languages_spoken || "",
      dispatchSoftware: data.dispatch_software || "",
      tripStatusUpdates: data.trip_status_updates || "",
      citiesCovered: Array.isArray(data.cities_covered) ? data.cities_covered : (data.cities_covered ? [data.cities_covered] : []),
      dressCode: data.dress_code || "",
      cancellationPolicy: data.cancellation_policy || "",
      meetGreet: data.meet_greet || "",
    }
  }

  // Handle form submission
  const handleSubmit = async (formData: CompanyFormValues) => {
    if (!selectedCompany?.id) return

    setIsSaving(true)
    try {
      // Transform form data back to API format
      const apiData = {
        name: formData.companyName,
        dba: formData.dba,
        owner_name: formData.ownerName,
        year_established: formData.yearEstablished,
        federal_tax_id: formData.federalTaxId,
        contact_email: formData.contactEmail,
        contact_phone: formData.contactPhone,
        email: formData.contactEmail, // Also update the main email field
        phone: formData.contactPhone, // Also update the main phone field
        website: formData.website,
        address: formData.addressLine1,
        address_line_2: formData.addressLine2,
        city: formData.city,
        state: formData.stateProvince,
        zip: formData.postalCode,
        country: formData.country,
        charge_for_delays: formData.chargeForDelays,
        charge_for_stops: formData.chargeForStops,
        offer_meet_greet: formData.offerMeetGreet,
        road_shows: formData.roadShows,
        gridd_gnet_member: formData.griddGnetMember,
        addons_la: formData.addonsLa,
        direct_billing: formData.directBilling,
        live_phone_support: formData.livePhoneSupport,
        airports_served: formData.airportsServed ?
          (typeof formData.airportsServed === 'string' ? formData.airportsServed.split(',').map(s => s.trim()).filter(Boolean) : formData.airportsServed) : [],
        languages_spoken: formData.languagesSpoken ?
          (typeof formData.languagesSpoken === 'string' ? formData.languagesSpoken.split(',').map(s => s.trim()).filter(Boolean) : formData.languagesSpoken) : [],
        dispatch_software: formData.dispatchSoftware ?
          (typeof formData.dispatchSoftware === 'string' ? formData.dispatchSoftware.split(',').map(s => s.trim()).filter(Boolean) : formData.dispatchSoftware) : [],
        trip_status_updates: formData.tripStatusUpdates ?
          (typeof formData.tripStatusUpdates === 'string' ? formData.tripStatusUpdates.split(',').map(s => s.trim()).filter(Boolean) : formData.tripStatusUpdates) : [],
        cities_covered: formData.citiesCovered || [],
        dress_code: formData.dressCode,
        cancellation_policy: formData.cancellationPolicy,
        meet_greet: formData.meetGreet,
        business_license: formData.businessLicense instanceof File ? `Uploaded: ${formData.businessLicense.name}` : (formData.businessLicense || ''),
        insurance_certificate: formData.insuranceCertificate instanceof File ? `Uploaded: ${formData.insuranceCertificate.name}` : (formData.insuranceCertificate || ''),
      }

      console.log('Enhanced form submitting data:', apiData);

      const response = await fetch(`/api/affiliate/companies/${selectedCompany.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-Affiliate-Company-ID': selectedCompany.id
        },
        body: JSON.stringify(apiData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error('API Error Response:', errorData)
        throw new Error(errorData.error || `Failed to update company profile (${response.status})`)
      }

      const updatedData = await response.json()
      setCompanyDetails(updatedData)

      // Refresh the company list to update progress
      if (fetchUserCompanies) {
        await fetchUserCompanies()
      }

      // Trigger a refresh of progress data by dispatching a custom event
      window.dispatchEvent(new CustomEvent('progressUpdated', {
        detail: { companyId: selectedCompany.id }
      }));

      toast({
        title: "Success",
        description: "Company profile updated successfully!",
      })
    } catch (error) {
      console.error('Error updating company:', error)
      toast({
        title: "Error",
        description: "Failed to update company profile. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoadingCompanies) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-64 w-full" />
      </div>
    )
  }

  if (companyError) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{companyError}</AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!selectedCompany) {
    return (
      <div className="container mx-auto py-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            No company selected. Please select a company from your dashboard.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  const progress = calculateProgress(companyDetails)

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Enhanced Company Profile</h1>
            <p className="text-muted-foreground">
              Complete your company profile to start receiving quote requests
            </p>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <div className="text-right">
            <div className="text-sm font-medium">Profile Completion</div>
            <div className="flex items-center gap-2">
              <Progress value={progress} className="w-24" />
              <span className="text-sm text-muted-foreground">{progress}%</span>
            </div>
          </div>

          <Badge variant={progress >= 80 ? "default" : progress >= 50 ? "secondary" : "destructive"}>
            {progress >= 80 ? (
              <>
                <CheckCircle className="h-3 w-3 mr-1" />
                Complete
              </>
            ) : (
              <>
                <Clock className="h-3 w-3 mr-1" />
                In Progress
              </>
            )}
          </Badge>
        </div>
      </div>

      {/* Form */}
      {isLoading ? (
        <div className="space-y-6">
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-96 w-full" />
        </div>
      ) : (
        <ComprehensiveCompanyForm
          initialData={getFormData(companyDetails)}
          onSubmit={handleSubmit}
          onAutoSave={handleSubmit} // Use the same submit function for auto-save
          isLoading={isSaving}
          initialStep={isFromOnboarding ? 2 : 0}
        />
      )}
    </div>
  )
}
