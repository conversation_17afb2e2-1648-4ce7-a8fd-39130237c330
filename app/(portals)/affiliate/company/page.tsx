"use client"

import { useEffect, useState, use<PERSON>emo, use<PERSON>allback } from "react"
import { useAffiliateCompany } from '@/app/contexts/AffiliateCompanyContext'
import { Card, CardContent, CardDescription, CardHeader, Card<PERSON>ooter, CardTitle } from "@/app/components/ui/card"
import { Separator } from "@/app/components/ui/separator"
import { Badge } from "@/app/components/ui/badge"
import { Progress } from "@/app/components/ui/progress"
import { Button } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/app/components/ui/tabs"
import {
  Building2, Mail, Phone, MapPin, FileText, CheckCircle2, XCircle, Shield, DollarSign, ClipboardCheck, AlertTriangle, Globe, Clock, Calendar, Edit3, Save, X, Info, Users, Briefcase, Settings, ShieldCheck, FileUp, CheckSquare, Square, UploadCloud, Loader2
} from "lucide-react"
import { Form, FormField, FormItem, FormLabel, FormControl, FormDescription as RHFormDescription, FormMessage } from "@/app/components/ui/form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm, Controller } from "react-hook-form"
import * as z from "zod"
import { useToast } from '@/app/components/ui/use-toast'
import FormSelect from '@/app/components/ui/form-select'
import { Command, CommandInput, CommandList, CommandItem, CommandEmpty, CommandGroup } from '@/app/components/ui/command'
import { Textarea } from "@/app/components/ui/textarea"
import { Checkbox } from "@/app/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from '@/app/components/ui/radio-group'
import { cn } from "@/lib/utils"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"

// Placeholder for actual API calls for document uploads/retrieval
const mockDocuments = [
  { id: "doc1", name: "Business License", status: "verified", uploaded: "Mar 15, 2024", url: "#" },
  { id: "doc2", name: "Insurance Certificate", status: "in_review", uploaded: "Mar 16, 2024", url: "#" },
  { id: "doc3", name: "DOT Number", status: "verified", uploaded: "Mar 16, 2024", url: "#" }, // Assuming DOT Number is a general document
];

const COUNTRIES = [
  { value: 'USA', label: 'United States' },
  { value: 'CAN', label: 'Canada' },
  { value: 'GBR', label: 'United Kingdom' },
  { value: 'AUS', label: 'Australia' },
  { value: 'DEU', label: 'Germany' },
  { value: 'FRA', label: 'France' },
  { value: 'JPN', label: 'Japan' },
  { value: 'ITA', label: 'Italy' },
  { value: 'ESP', label: 'Spain' },
  { value: 'NLD', label: 'Netherlands' },
];

const CITY_OPTIONS_BY_COUNTRY: Record<string, Array<{ value: string; label: string }>> = {
  'USA': [
    { value: 'NYC', label: 'New York' },
    { value: 'LAX', label: 'Los Angeles' },
    { value: 'CHI', label: 'Chicago' },
    { value: 'MIA', label: 'Miami' },
    { value: 'BOS', label: 'Boston' },
    { value: 'DAL', label: 'Dallas' },
    { value: 'HOU', label: 'Houston' },
    { value: 'PHI', label: 'Philadelphia' },
    { value: 'PHX', label: 'Phoenix' },
    { value: 'SFO', label: 'San Francisco' },
  ],
  'CAN': [
    { value: 'TOR', label: 'Toronto' },
    { value: 'MTL', label: 'Montreal' },
    { value: 'VAN', label: 'Vancouver' },
    { value: 'CAL', label: 'Calgary' },
    { value: 'OTT', label: 'Ottawa' },
  ],
  'GBR': [
    { value: 'LON', label: 'London' },
    { value: 'MAN', label: 'Manchester' },
    { value: 'BIR', label: 'Birmingham' },
    { value: 'GLA', label: 'Glasgow' },
    { value: 'LIV', label: 'Liverpool' },
  ],
};

const companySchema = z.object({
  // Only essential fields are required
  name: z.string().min(2, "Company name is required"),
  email: z.string().email("Invalid email").min(1, "Email is required"),
  phone: z.string().min(10, "Phone number is required").max(20),

  // Address fields - make only some required
  address: z.string().optional().or(z.literal('')),
  city: z.string().optional().or(z.literal('')),
  state: z.string().optional().or(z.literal('')),
  zip: z.string().optional().or(z.literal('')),
  country: z.string().optional().or(z.literal('')),

  // Optional fields
  website: z.string().optional().or(z.literal('')),
  dba: z.string().optional().or(z.literal('')),
  owner_name: z.string().optional().or(z.literal('')),
  year_established: z.string().refine(val => !val || /^(19\d{2}|20\d{2})$/.test(val), {
    message: "Invalid year format (YYYY)"
  }).optional().or(z.literal('')),
  federal_tax_id: z.string().optional().or(z.literal('')),
  contact_email: z.string().email("Invalid email").optional().or(z.literal('')),
  contact_phone: z.string().optional().or(z.literal('')),
  address_line_2: z.string().optional().or(z.literal('')),

  // Boolean fields (some are actually yes/no strings)
  charge_for_delays: z.string().optional(),
  charge_for_stops: z.boolean().optional(),
  offer_meet_greet: z.boolean().optional(),
  road_shows: z.string().optional(),
  gridd_gnet_member: z.boolean().optional(),
  addons_la: z.boolean().optional(),
  direct_billing: z.boolean().optional(),
  live_phone_support: z.boolean().optional(),

  // Array fields
  airports_served: z.array(z.string()).optional(),
  languages_spoken: z.array(z.string()).optional(),
  dispatch_software: z.array(z.string()).optional(),
  trip_status_updates: z.array(z.string()).optional(),
  cities_covered: z.array(z.string()).optional(),

  // Document fields
  business_license: z.any().optional(),
  insurance_certificate: z.any().optional(),

  // Status fields
  business_registration_status: z.string().optional(),
  insurance_status: z.string().optional(),
  fleet_inspection_status: z.string().optional(),
  rate_agreement_status: z.string().optional(),

  // Additional fields from the form
  application_status: z.string().optional(),
  progress: z.number().optional(),
  application_submitted_at: z.string().optional(),
  document_status: z.string().optional(),

  // Service standards and policies
  dress_code: z.string().optional(),
  cancellation_policy: z.string().optional(),
  meet_greet: z.string().optional(),
  events_seasonal: z.string().optional(),

  // Yes/No fields
  vanity_plates: z.string().optional(),
  bottled_water: z.string().optional(),
  track_flights: z.string().optional(),
  provide_chauffeur_info: z.string().optional(),
  wall_street_journal: z.string().optional(),
  farm_out_local: z.string().optional(),
  charge_amount: z.string().optional(),
  direct_billing_schedule: z.string().optional(),
});

type CompanyFormValues = z.infer<typeof companySchema>;
interface Option { value: string; label: string; }

const US_STATES: Option[] = [
  { value: 'AL', label: 'Alabama' }, { value: 'AK', label: 'Alaska' }, { value: 'AZ', label: 'Arizona' }, { value: 'AR', label: 'Arkansas' },
  { value: 'CA', label: 'California' }, { value: 'CO', label: 'Colorado' }, { value: 'CT', label: 'Connecticut' }, { value: 'DE', label: 'Delaware' },
  { value: 'FL', label: 'Florida' }, { value: 'GA', label: 'Georgia' }, { value: 'HI', label: 'Hawaii' }, { value: 'ID', label: 'Idaho' },
  { value: 'IL', label: 'Illinois' }, { value: 'IN', label: 'Indiana' }, { value: 'IA', label: 'Iowa' }, { value: 'KS', label: 'Kansas' },
  { value: 'KY', label: 'Kentucky' }, { value: 'LA', label: 'Louisiana' }, { value: 'ME', label: 'Maine' }, { value: 'MD', label: 'Maryland' },
  { value: 'MA', label: 'Massachusetts' }, { value: 'MI', label: 'Michigan' }, { value: 'MN', label: 'Minnesota' }, { value: 'MS', label: 'Mississippi' },
  { value: 'MO', label: 'Missouri' }, { value: 'MT', label: 'Montana' }, { value: 'NE', label: 'Nebraska' }, { value: 'NV', label: 'Nevada' },
  { value: 'NH', label: 'New Hampshire' }, { value: 'NJ', label: 'New Jersey' }, { value: 'NM', label: 'New Mexico' }, { value: 'NY', label: 'New York' },
  { value: 'NC', label: 'North Carolina' }, { value: 'ND', label: 'North Dakota' }, { value: 'OH', label: 'Ohio' }, { value: 'OK', label: 'Oklahoma' },
  { value: 'OR', label: 'Oregon' }, { value: 'PA', label: 'Pennsylvania' }, { value: 'RI', label: 'Rhode Island' }, { value: 'SC', label: 'South Carolina' },
  { value: 'SD', label: 'South Dakota' }, { value: 'TN', label: 'Tennessee' }, { value: 'TX', label: 'Texas' }, { value: 'UT', label: 'Utah' },
  { value: 'VT', label: 'Vermont' }, { value: 'VA', label: 'Virginia' }, { value: 'WA', label: 'Washington' }, { value: 'WV', label: 'West Virginia' },
  { value: 'WI', label: 'Wisconsin' }, { value: 'WY', label: 'Wyoming' }
];
const YEARS: Option[] = Array.from({ length: 100 }, (_, i) => {
  const year = new Date().getFullYear() - i;
  return { value: String(year), label: String(year) };
});
const LANGUAGES_OPTIONS: Option[] = [
  { value: 'english', label: 'English' }, { value: 'spanish', label: 'Spanish' }, { value: 'french', label: 'French' },
  { value: 'german', label: 'German' }, { value: 'italian', label: 'Italian' }, { value: 'portuguese', label: 'Portuguese' },
  { value: 'russian', label: 'Russian' }, { value: 'chinese', label: 'Chinese' }, { value: 'japanese', label: 'Japanese' },
  { value: 'korean', label: 'Korean' }, { value: 'arabic', label: 'Arabic' }, { value: 'hindi', label: 'Hindi' }, { value: 'other', label: 'Other' }
];
const DISPATCH_SOFTWARE_OPTIONS: Option[] = [
  { value: 'limoanywhere', label: 'LimoAnywhere' }, { value: 'livery_coach', label: 'Livery Coach' },
  { value: 'fasttrack', label: 'FastTrack' }, { value: 'hudson', label: 'Hudson' },
  { value: 'ground_alliance', label: 'Ground Alliance' }, { value: 'other', label: 'Other (Please specify)' },
];
const TRIP_STATUS_UPDATE_CHOICES: Option[] = [
  { value: 'driveranywhere', label: 'DriverAnywhere App' }, { value: 'limoanywhere_api', label: 'LimoAnywhere API/Network' },
  { value: 'automated_email', label: 'Automated Email Notifications' }, { value: 'text_messages_wwlimo', label: 'Text Messages to WWLIMO' },
  { value: 'manual_phone_call', label: 'Manual Phone Call' }, { value: 'other', label: 'Other Method' }
];
const YES_NO_OPTIONS: Option[] = [{ value: 'yes', label: 'Yes' }, { value: 'no', label: 'No' }];
const BILLING_SCHEDULE_OPTIONS: Option[] = [
  { value: 'biweekly', label: 'Bi-weekly Invoicing' }, { value: 'monthly', label: 'Monthly Invoicing' }, { value: 'per_trip', label: 'Per Trip Billing' }
];
const AIRPORT_OPTIONS: Option[] = [
    { value: 'JFK', label: 'JFK - John F. Kennedy Intl.' }, { value: 'LAX', label: 'LAX - Los Angeles Intl.' },
    { value: 'ORD', label: 'ORD - O\'Hare Intl.' }, { value: 'DFW', label: 'DFW - Dallas/Fort Worth Intl.' },
    { value: 'ATL', label: 'ATL - Hartsfield-Jackson Atlanta Intl.' }, { value: 'LHR', label: 'LHR - London Heathrow' },
];

interface TagInputProps {
  value: string[];
  onChange: (values: string[]) => void;
  options: Option[];
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

function TagInput({ value = [], onChange, options, placeholder, disabled, className }: TagInputProps) {
  const [inputValue, setInputValue] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  const selectedOptionsToDisplay = useMemo(() =>
    value.map(val => options.find(opt => opt.value === val) || { value: val, label: val }),
    [value, options]
  );

  const filteredOptions = useMemo(() => {
    if (!inputValue) return options.filter(opt => !value.includes(opt.value));
    return options.filter(opt =>
      !value.includes(opt.value) &&
      opt.label.toLowerCase().includes(inputValue.toLowerCase())
    );
  }, [inputValue, value, options]);

  const handleSelect = (selectedValue: string) => {
    if (!value.includes(selectedValue)) {
      onChange([...value, selectedValue]);
    }
    setInputValue('');
    setIsOpen(false);
  };

  const handleRemove = (removedValue: string) => {
    onChange(value.filter(v => v !== removedValue));
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter' && inputValue && filteredOptions.length > 0) {
      handleSelect(filteredOptions[0].value);
      event.preventDefault();
    } else if (event.key === 'Backspace' && !inputValue && value.length > 0) {
      handleRemove(value[value.length - 1]);
    }
  };

  return (
    <Command className={cn("relative", className)}>
      <div className={cn("flex flex-wrap gap-2 border rounded-md p-2 min-h-[40px] items-center bg-background focus-within:ring-2 focus-within:ring-ring", disabled && "bg-muted cursor-not-allowed")}>
        {selectedOptionsToDisplay.map(opt => (
          <Badge key={opt.value} variant="secondary" className="flex items-center gap-1 text-xs">
            {opt.label}
            {!disabled && (
              <button type="button" className="ml-1 rounded-full hover:bg-destructive/80 p-0.5" onClick={() => handleRemove(opt.value)}>
                <XCircle className="h-3 w-3 text-destructive-foreground" />
              </button>
            )}
          </Badge>
        ))}
        <CommandInput
          value={inputValue}
          onValueChange={text => { setInputValue(text); if (text) setIsOpen(true); else setIsOpen(false);}}
          onKeyDown={handleKeyDown}
          onBlur={() => setTimeout(() => setIsOpen(false), 150)}
          onFocus={() => setIsOpen(true)}
          placeholder={value.length === 0 ? placeholder : undefined}
          className="flex-1 h-auto p-0 bg-transparent border-0 shadow-none focus-visible:ring-0 disabled:cursor-not-allowed"
          disabled={disabled}
        />
      </div>
      {isOpen && !disabled && filteredOptions.length > 0 && (
        <CommandList className="absolute z-50 w-full mt-1 bg-popover border rounded-md shadow-lg max-h-60 overflow-y-auto">
          <CommandEmpty>{inputValue && "No results found."}</CommandEmpty>
          <CommandGroup>
            {filteredOptions.map(opt => (
              <CommandItem key={opt.value} onSelect={() => handleSelect(opt.value)} value={opt.value}>
                {opt.label}
              </CommandItem>
            ))}
          </CommandGroup>
        </CommandList>
      )}
    </Command>
  );
}

function CitiesTagInput({ selectedCountry, value, onChange, disabled, className }:
  { selectedCountry: string | undefined; value: string[]; onChange: (values: string[]) => void; disabled?: boolean; className?: string; }
) {
  const cityOptionsForCountry = useMemo(() =>
    selectedCountry && CITY_OPTIONS_BY_COUNTRY[selectedCountry] ? CITY_OPTIONS_BY_COUNTRY[selectedCountry] : [],
    [selectedCountry]
  );

  return (
    <TagInput
      value={value}
      onChange={onChange}
      options={cityOptionsForCountry}
      placeholder={selectedCountry ? 'Select or type cities...' : 'Select country first'}
      disabled={!selectedCountry || disabled}
      className={className}
    />
  );
}

const InfoRow = ({ label, value, icon, className }: { label: string; value: React.ReactNode; icon?: React.ReactNode; className?: string }) => (
  <div className={cn("mb-3", className)}>
    <div className="text-xs text-muted-foreground font-medium flex items-center">
      {icon && <span className="mr-2 text-primary">{icon}</span>}
      {label}
    </div>
    <div className="font-medium text-sm break-words">
      {value === undefined || value === null || (typeof value === 'string' && value.trim() === '') || (Array.isArray(value) && value.length === 0) ?
      <span className="text-muted-foreground italic">- Not Provided -</span> : value}
    </div>
  </div>
);

const FileDisplay = ({ file, name, onRemove, editMode }: { file: File | string | { name: string; url: string } | null | undefined; name: string; onRemove?: () => void; editMode?: boolean }) => {
  if (!file) return <span className="text-xs text-muted-foreground">No file selected</span>;

  let fileName = "Uploaded File";
  let fileUrl: string | undefined = undefined;
  let fileSize: number | undefined = undefined;

  if (typeof file === 'string') {
    if (file.startsWith("Uploaded: ") || file.startsWith("/uploads/")) { // Handle simulated upload strings or mock URLs
        fileName = file.startsWith("Uploaded: ") ? file.substring(10) : file.substring(file.lastIndexOf('/') + 1);
        fileUrl = undefined; // No actual URL for simulated uploads displayed this way
    } else { // Assume it's a real URL
        fileName = name;
        fileUrl = file;
    }
  } else if (file instanceof File) {
    fileName = file.name;
    fileSize = file.size;
  } else if (typeof file === 'object' && 'name' in file && 'url' in file) {
    fileName = file.name;
    fileUrl = file.url;
  }


  return (
    <div className="flex items-center justify-between p-2 border rounded-md bg-muted/50 text-sm">
      <div className="flex items-center gap-2">
        <FileText className="h-4 w-4 text-muted-foreground" />
        {fileUrl ?
          <a href={fileUrl} target="_blank" rel="noopener noreferrer" className="hover:underline text-primary">{fileName}</a> :
          <span>{fileName}</span>
        }
        {fileSize && <span className="text-xs text-muted-foreground">({(fileSize / 1024).toFixed(1)} KB)</span>}
      </div>
      {editMode && onRemove && (
        <Button type="button" variant="ghost" size="sm" onClick={onRemove} className="text-destructive hover:text-destructive">
          <X className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
};

export default function CompanyProfilePage() {
  const { selectedCompany, isLoadingCompanies, companyError, fetchUserCompanies } = useAffiliateCompany();
  const [companyDetails, setCompanyDetails] = useState<CompanyFormValues | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const { toast } = useToast();
  const [cityFilterCountry, setCityFilterCountry] = useState<string | undefined>();
  const [activeTab, setActiveTab] = useState('coreDetails');
  const [cities, setCities] = useState<Array<{ value: string; label: string }>>([]);

  const form = useForm<CompanyFormValues>({
    resolver: zodResolver(companySchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      address: '',
      city: '',
      state: '',
      zip: '',
      country: '',
      website: '',
      dba: '',
      owner_name: '',
      year_established: '',
      federal_tax_id: '',
      contact_email: '',
      contact_phone: '',
      address_line_2: '',

      // Boolean fields with proper boolean values (some are yes/no strings)
      charge_for_delays: '',
      charge_for_stops: false,
      offer_meet_greet: false,
      road_shows: '',
      gridd_gnet_member: false,
      addons_la: false,
      direct_billing: false,
      live_phone_support: false,

      // Array fields
      airports_served: [],
      languages_spoken: [],
      dispatch_software: [],
      trip_status_updates: [],
      cities_covered: [],

      // Document fields
      business_license: null,
      insurance_certificate: null,

      // Status fields
      business_registration_status: 'Not Started',
      insurance_status: 'Not Started',
      fleet_inspection_status: 'Not Started',
      rate_agreement_status: 'Not Started',
    },
    mode: 'onBlur',
  });

  const safeSplit = (val: any): string[] => {
    if (Array.isArray(val)) return val.filter(item => typeof item === 'string');
    if (typeof val === 'string' && val.trim() !== '') return val.split(',').map(s => s.trim()).filter(Boolean);
    return [];
  };

  useEffect(() => {
    if (!selectedCompany?.id) {
      setCompanyDetails(null);
      form.reset();
      setCityFilterCountry(undefined); // Reset filter country
      return;
    }
    setIsLoading(true);
    fetch(`/api/affiliate/companies/${selectedCompany.id}`, {
      headers: { 'X-Affiliate-Company-ID': selectedCompany.id }
    })
      .then(res => {
        if (!res.ok) throw new Error('Failed to fetch company data');
        return res.json();
      })
      .then(data => {
        const transformedData: CompanyFormValues = {
          ...form.getValues(),
          ...data,
          airports_served: safeSplit(data.airports_served),
          languages_spoken: safeSplit(data.languages_spoken),
          dispatch_software: safeSplit(data.dispatch_software),
          trip_status_updates: safeSplit(data.trip_status_updates),
          cities_covered: safeSplit(data.cities_covered),
          year_established: data.year_established ? String(data.year_established) : '',
          live_phone_support: !!data.live_phone_support,
          gridd_gnet_member: !!data.gridd_gnet_member,
          addons_la: !!data.addons_la,
          direct_billing: !!data.direct_billing,
        };
        setCompanyDetails(transformedData);
        form.reset(transformedData);
        setCityFilterCountry(transformedData.country); // Initialize city filter country from address country
      })
      .catch(err => {
        setCompanyDetails(null);
        form.reset();
        setCityFilterCountry(undefined);
        toast({ title: "Error", description: err.message || "Failed to fetch company details.", variant: "destructive" });
      })
      .finally(() => setIsLoading(false));
  }, [selectedCompany?.id, form, toast]);

  const addressCountry = form.watch("country");
  const selectedCountry = form.watch('country');

  useEffect(() => {
    // If the main address country changes, update the city filter country as a default.
    // This provides a better UX by keeping the filter in sync if the user changes the address country.
    if (isEditing && addressCountry !== cityFilterCountry) {
        setCityFilterCountry(addressCountry);
    }
  }, [addressCountry, isEditing, cityFilterCountry]);

  // Update cities when country changes
  useEffect(() => {
    if (selectedCountry && CITY_OPTIONS_BY_COUNTRY[selectedCountry]) {
      setCities(CITY_OPTIONS_BY_COUNTRY[selectedCountry]);
    } else {
      setCities([]);
    }
  }, [selectedCountry]);

  const onSubmit = async (values: CompanyFormValues) => {
    if (!selectedCompany?.id) return;
    setIsLoading(true);

    const jsonData = {...values};

    // Simulate file upload by replacing File objects with their names for submission
    if (jsonData.business_license instanceof File) {
      jsonData.business_license = `Uploaded: ${jsonData.business_license.name}`;
    }
    if (jsonData.insurance_certificate instanceof File) {
      jsonData.insurance_certificate = `Uploaded: ${jsonData.insurance_certificate.name}`;
    }

    try {
      const response = await fetch(`/api/affiliate/companies/${selectedCompany.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-Affiliate-Company-ID': selectedCompany.id
        },
        body: JSON.stringify(jsonData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Update failed with no details.' }));
        throw new Error(errorData.error || 'Failed to update company profile.');
      }
      const updatedData = await response.json();
       const transformedData: CompanyFormValues = {
        ...form.getValues(),
        ...updatedData,
        airports_served: safeSplit(updatedData.airports_served),
        languages_spoken: safeSplit(updatedData.languages_spoken),
        dispatch_software: safeSplit(updatedData.dispatch_software),
        trip_status_updates: safeSplit(updatedData.trip_status_updates),
        cities_covered: safeSplit(updatedData.cities_covered),
        year_established: updatedData.year_established ? String(updatedData.year_established) : '',
        live_phone_support: !!updatedData.live_phone_support,
        gridd_gnet_member: !!updatedData.gridd_gnet_member,
        addons_la: !!updatedData.addons_la,
        direct_billing: !!updatedData.direct_billing,
      };
      setCompanyDetails(transformedData);
      form.reset(transformedData);
      toast({ title: 'Success', description: 'Company profile updated successfully.' });
      setIsEditing(false);
      if (fetchUserCompanies) fetchUserCompanies();
    } catch (error: any) {
      toast({ title: 'Update Error', description: error.message, variant: 'destructive' });
    } finally {
      setIsLoading(false);
    }
  };

  const onCancel = () => {
    if (companyDetails) form.reset(companyDetails);
    else form.reset();
    setIsEditing(false);
  };

  if (isLoadingCompanies || (isLoading && !companyDetails && selectedCompany?.id)) {
    return <div className="container py-10 text-center"><Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />Loading company profile...</div>;
  }
  if (companyError) {
    return <div className="container py-10 text-red-600 text-center">Error loading company: {companyError}</div>;
  }
  if (!selectedCompany?.id) {
    return <div className="container py-10 text-muted-foreground text-center">Please select a company from the list to view its profile.</div>;
  }
  if (!companyDetails && !isLoading) {
    return <div className="container py-10 text-muted-foreground text-center">Company details could not be loaded. Try selecting again or contact support.</div>;
  }

  const currentCompanyName = form.watch("name") || companyDetails?.name || selectedCompany?.name || "Company Profile";
  const watchedRoadShows = form.watch("road_shows");
  const watchedChargeForDelays = form.watch("charge_for_delays");
  const watchedDirectBilling = form.watch("direct_billing");

  const renderFileInput = (fieldName: keyof CompanyFormValues, label: string) => (
    <FormField
      control={form.control}
      name={fieldName}
      disabled={isLoading || !isEditing}
      render={({ field: { onChange, value, ...restField }}) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <div>
              <Input
                type="file"
                className="block w-full text-sm text-slate-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary/10 file:text-primary hover:file:bg-primary/20 disabled:opacity-50 disabled:cursor-not-allowed"
                onChange={(e) => onChange(e.target.files?.[0] || null)}
                disabled={isLoading || !isEditing}
                {...restField}
              />
              {(value || (companyDetails && companyDetails[fieldName])) && (
                 <FileDisplay
                    file={value || (companyDetails && companyDetails[fieldName])}
                    name={label}
                    editMode={isEditing && !!value}
                    onRemove={value ? () => onChange(null) : undefined}
                  />
              )}
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );



  return (
    <div className="container mx-auto py-6 px-2 sm:px-4 space-y-6">
      {/* Enhanced Experience Banner */}
      <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-primary/10">
        <CardContent className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Building2 className="h-5 w-5 text-primary" />
            </div>
            <div>
              <h3 className="font-semibold text-primary">Try Our Enhanced Company Profile</h3>
              <p className="text-sm text-muted-foreground">
                Step-by-step guided form with progress tracking and better organization
              </p>
            </div>
          </div>
          <Button
            onClick={() => window.open('/affiliate/company/enhanced', '_blank')}
            className="bg-primary hover:bg-primary/90"
          >
            Try Enhanced Version
          </Button>
        </CardContent>
      </Card>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card className="shadow-md">
            <CardHeader className="pb-4">
              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-muted rounded-lg flex items-center justify-center ring-1 ring-border shadow-sm">
                    <Building2 className="h-8 w-8 text-primary" />
                  </div>
                  <div>
                    {isEditing ? (
                      <FormField control={form.control} name="name" disabled={isLoading} render={({ field }) => (
                        <FormItem className="w-full sm:w-auto">
                          <FormLabel className="sr-only">Company Name</FormLabel>
                          <FormControl><Input {...field} placeholder="Company Name" className="text-xl sm:text-2xl font-bold tracking-tight h-10 p-2 border-gray-300 focus:ring-primary focus:border-primary" /></FormControl>
                          <FormMessage />
                        </FormItem>
                      )} />
                    ) : (
                      <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-gray-800">{currentCompanyName}</h1>
                    )}
                    <div className="flex items-center gap-2 mt-1.5">
                      {companyDetails?.application_status && <Badge variant="outline" className="text-xs">{companyDetails.application_status}</Badge>}
                      {companyDetails?.progress !== undefined && companyDetails.progress !== null && (
                         <div className="flex items-center gap-1.5 w-32">
                            <Progress value={companyDetails.progress} className="h-1.5 rounded-full" />
                            <span className="text-xs text-muted-foreground">{companyDetails.progress}%</span>
                         </div>
                      )}
                    </div>
                     <p className="text-xs text-muted-foreground mt-1">
                       Application Submitted: {companyDetails?.application_submitted_at ? new Date(companyDetails.application_submitted_at).toLocaleDateString() : "N/A"}
                     </p>
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>

          <div className="mb-6">
            <Card>
              <CardHeader><CardTitle className="flex items-center text-lg"><ClipboardCheck className="mr-2 h-5 w-5 text-primary"/>Company Verification Status</CardTitle></CardHeader>
              <CardContent>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                      <Card className="shadow-sm"><CardHeader className="pb-1"><CardTitle className="text-sm font-medium flex items-center gap-1.5"><FileText className="h-4 w-4 text-muted-foreground" />Document Status</CardTitle></CardHeader><CardContent><div className="text-xl font-semibold">{companyDetails?.document_status || "-"}</div></CardContent></Card>
                      <Card className="shadow-sm"><CardHeader className="pb-1"><CardTitle className="text-sm font-medium flex items-center gap-1.5"><Shield className="h-4 w-4 text-muted-foreground" />Insurance Status</CardTitle></CardHeader><CardContent><div className={cn("text-xl font-semibold", companyDetails?.insurance_status === 'Verified' ? 'text-green-600' : companyDetails?.insurance_status === 'Pending' || companyDetails?.insurance_status === 'In Review' ? 'text-yellow-600' : 'text-red-600')}>{companyDetails?.insurance_status || "-"}</div></CardContent></Card>
                      <Card className="shadow-sm"><CardHeader className="pb-1"><CardTitle className="text-sm font-medium flex items-center gap-1.5"><Calendar className="h-4 w-4 text-muted-foreground" />Year Established</CardTitle></CardHeader><CardContent><div className="text-xl font-semibold">{companyDetails?.year_established || "-"}</div></CardContent></Card>
                      <Card className="shadow-sm"><CardHeader className="pb-1"><CardTitle className="text-sm font-medium flex items-center gap-1.5"><CheckSquare className="h-4 w-4 text-muted-foreground" />Federal Tax ID</CardTitle></CardHeader><CardContent><div className="text-xl font-semibold">{companyDetails?.federal_tax_id ? "Verified" : "Missing"}</div></CardContent></Card>
                  </div>
                  <h4 className="text-md font-semibold mb-3 mt-6">Verification Checklist</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3">
                      {[
                          { label: "Business Registration", statusKey: "business_registration_status", iconDefault: <CheckCircle2/> },
                          { label: "Insurance Documentation", statusKey: "insurance_status", iconDefault: <Shield/> },
                          { label: "Fleet Inspection", statusKey: "fleet_inspection_status", iconDefault: <MapPin/> },
                          { label: "Rate Agreement", statusKey: "rate_agreement_status", iconDefault: <DollarSign/> },
                      ].map(item => {
                          const status = companyDetails?.[item.statusKey as keyof CompanyFormValues] as string || "Not Started";
                          let icon = item.iconDefault;
                          let badgeVariant: "default" | "destructive" | "secondary" | "success" | "outline" | null | undefined = "outline";
                          if (status === "Verified" || status === "Completed") { icon = <CheckCircle2 className="h-4 w-4 text-green-500" />; badgeVariant = "success";}
                          else if (status === "In Review" || status === "Pending") { icon = <Clock className="h-4 w-4 text-yellow-500" />; badgeVariant = "outline"; }
                          else if (status === "Rejected" || status === "Expired") { icon = <XCircle className="h-4 w-4 text-red-500" />; badgeVariant = "destructive";}
                          return (
                              <div key={item.label} className="flex items-center justify-between p-3 bg-muted/40 rounded-lg border">
                                  <div className="flex items-center gap-2 text-sm">{icon}<span>{item.label}</span></div>
                                  <Badge variant={badgeVariant} className="text-xs">{status}</Badge>
                              </div>
                          );
                      })}
                  </div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="coreDetails" className="w-full" onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-1 sm:grid-cols-3 mb-4 bg-muted/50 p-1 rounded-lg">
              <TabsTrigger value="coreDetails" className="data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow-sm"><Briefcase className="mr-2 h-4 w-4" />Core Details</TabsTrigger>
              <TabsTrigger value="serviceProfile" className="data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow-sm"><Globe className="mr-2 h-4 w-4" />Service Profile</TabsTrigger>
              <TabsTrigger value="complianceDocs" className="data-[state=active]:bg-background data-[state=active]:text-primary data-[state=active]:shadow-sm"><ShieldCheck className="mr-2 h-4 w-4" />Compliance</TabsTrigger>
            </TabsList>

            <TabsContent value="coreDetails" className="space-y-6">
              <div className="flex justify-end mb-2">
                {isEditing ? (
                  <>
                    <Button size="sm" type="submit" disabled={isLoading || !form.formState.isDirty} className="bg-green-600 hover:bg-green-700 text-white">
                      <Save className="mr-1.5 h-4 w-4" /> {isLoading ? "Saving..." : "Save Changes"}
                    </Button>
                    <Button size="sm" variant="outline" onClick={onCancel} disabled={isLoading}>
                      <X className="mr-1.5 h-4 w-4" /> Cancel
                    </Button>
                  </>
                ) : (
                  <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
                    <Edit3 className="mr-1.5 h-4 w-4" /> Edit Profile
                  </Button>
                )}
              </div>
              <Card>
                <CardHeader><CardTitle className="flex items-center text-lg"><Info className="mr-2 h-5 w-5 text-primary"/>Basic Information</CardTitle></CardHeader>
                <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                  {isEditing ? <FormField control={form.control} name="dba" disabled={isLoading} render={({ field }) => (<FormItem><FormLabel>DBA (Doing Business As)</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>)} /> : <InfoRow label="DBA" value={companyDetails?.dba} />}
                  {isEditing ? <FormField control={form.control} name="owner_name" disabled={isLoading} render={({ field }) => (<FormItem><FormLabel>Owner Name</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>)} /> : <InfoRow label="Owner Name" value={companyDetails?.owner_name} />}
                  {isEditing ? <FormField control={form.control} name="year_established" disabled={isLoading} render={({ field }) => (<FormItem><FormLabel className="sr-only">Year Established</FormLabel><FormControl><FormSelect label="Year Established" options={YEARS} value={field.value} onChange={field.onChange} placeholder="Select Year" /></FormControl><FormMessage /></FormItem>)} /> : <InfoRow label="Year Established" value={companyDetails?.year_established} />}
                  {isEditing ? <FormField control={form.control} name="federal_tax_id" disabled={isLoading} render={({ field }) => (<FormItem><FormLabel>Federal Tax ID (EIN)</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>)} /> : <InfoRow label="Federal Tax ID (EIN)" value={companyDetails?.federal_tax_id ? `***-**-${companyDetails.federal_tax_id.slice(-4)}` : "-"} />}
                </CardContent>
              </Card>

              <Card>
                <CardHeader><CardTitle className="flex items-center text-lg"><Mail className="mr-2 h-5 w-5 text-primary"/>Contact & Address</CardTitle></CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                    {isEditing ? <FormField control={form.control} name="email" disabled={isLoading} render={({ field }) => (<FormItem><FormLabel>Email *</FormLabel><FormControl><Input type="email" {...field} /></FormControl><FormMessage /></FormItem>)} /> : <InfoRow label="Email" value={companyDetails?.email} />}
                    {isEditing ? <FormField control={form.control} name="contact_email" disabled={isLoading} render={({ field }) => (<FormItem><FormLabel>Contact Email</FormLabel><FormControl><Input type="email" {...field} /></FormControl><FormMessage /></FormItem>)} /> : <InfoRow label="Contact Email" value={companyDetails?.contact_email} />}
                    {isEditing ? <FormField control={form.control} name="phone" disabled={isLoading} render={({ field }) => (<FormItem><FormLabel>Phone *</FormLabel><FormControl><Input type="tel" {...field} /></FormControl><FormMessage /></FormItem>)} /> : <InfoRow label="Phone" value={companyDetails?.phone} />}
                    {isEditing ? <FormField control={form.control} name="contact_phone" disabled={isLoading} render={({ field }) => (<FormItem><FormLabel>Contact Phone</FormLabel><FormControl><Input type="tel" {...field} /></FormControl><FormMessage /></FormItem>)} /> : <InfoRow label="Contact Phone" value={companyDetails?.contact_phone} />}
                    {isEditing ? <FormField control={form.control} name="website" disabled={isLoading} render={({ field }) => (<FormItem><FormLabel>Company Website</FormLabel><FormControl><Input type="url" placeholder="https://example.com" {...field} /></FormControl><FormMessage /></FormItem>)} /> : <InfoRow label="Company Website" value={companyDetails?.website ? <a href={companyDetails.website} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">{companyDetails.website}</a> : "-"} />}
                  </div>
                  <Separator className="my-6" />
                  <h4 className="text-md font-semibold mb-3">Business Address</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-4">
                    {isEditing ? <FormField control={form.control} name="country" disabled={isLoading} render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                          disabled={isLoading || !isEditing}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a country" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {COUNTRIES.map((country) => (
                              <SelectItem key={country.value} value={country.value}>
                                {country.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )} /> : <InfoRow label="Country" value={COUNTRIES.find(c=>c.value === companyDetails?.country)?.label || companyDetails?.country} />}
                    {isEditing ? <FormField control={form.control} name="state" disabled={isLoading} render={({ field }) => (<FormItem><FormLabel className="sr-only">State/Province</FormLabel><FormControl><FormSelect label="State/Province" options={US_STATES} value={field.value} onChange={field.onChange} placeholder="Select State"/></FormControl><FormMessage /></FormItem>)} /> : <InfoRow label="State/Province" value={US_STATES.find(s=>s.value === companyDetails?.state)?.label || companyDetails?.state} />}
                    {isEditing ? (
                      <FormField
                        control={form.control}
                        name="city"
                        disabled={isLoading || !isEditing}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>City</FormLabel>
                            {cities.length > 0 ? (
                              <Select
                                onValueChange={field.onChange}
                                value={field.value}
                                disabled={isLoading || !isEditing}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select a city" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {cities.map((city) => (
                                    <SelectItem key={city.value} value={city.value}>
                                      {city.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            ) : (
                              <FormControl>
                                <Input placeholder="Enter city name" {...field} disabled={isLoading || !isEditing} />
                              </FormControl>
                            )}
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <InfoRow label="City" value={companyDetails?.city} />
                    )}
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 mt-4">
                     {isEditing ? <FormField control={form.control} name="address" disabled={isLoading} render={({ field }) => (<FormItem className="md:col-span-1"><FormLabel>Street Address</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>)} /> : <InfoRow label="Street Address" value={companyDetails?.address} className="md:col-span-1"/>}
                     {isEditing ? <FormField control={form.control} name="zip" disabled={isLoading} render={({ field }) => (<FormItem><FormLabel>ZIP/Postal Code</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>)} /> : <InfoRow label="ZIP/Postal Code" value={companyDetails?.zip} />}
                  </div>
                </CardContent>
              </Card>

              {isEditing && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center text-lg"><UploadCloud className="mr-2 h-5 w-5 text-primary"/>Upload Required Documents</CardTitle>
                    <CardDescription>Upload or replace new versions of your compliance documents. Ensure files are in PDF, JPG, or PNG format.</CardDescription>
                  </CardHeader>
                  <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                    {renderFileInput("business_license", "Business License")}
                    {renderFileInput("insurance_certificate", "Certificate of Insurance (COI)")}
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="serviceProfile" className="space-y-6">
              <div className="flex justify-end mb-2">
                {isEditing ? (
                  <>
                    <Button size="sm" type="submit" disabled={isLoading || !form.formState.isDirty} className="bg-green-600 hover:bg-green-700 text-white">
                      <Save className="mr-1.5 h-4 w-4" /> {isLoading ? "Saving..." : "Save Changes"}
                    </Button>
                    <Button size="sm" variant="outline" onClick={onCancel} disabled={isLoading}>
                      <X className="mr-1.5 h-4 w-4" /> Cancel
                    </Button>
                  </>
                ) : (
                  <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
                    <Edit3 className="mr-1.5 h-4 w-4" /> Edit Profile
                  </Button>
                )}
              </div>
              <Card>
                <CardHeader><CardTitle className="flex items-center text-lg"><MapPin className="mr-2 h-5 w-5 text-primary"/>Service Scope & Specialization</CardTitle></CardHeader>
                <CardContent className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-x-6 gap-y-4">
                  {isEditing && (
                    <FormItem>
                      <FormLabel className="sr-only">Country for Cities Below</FormLabel>
                      <FormControl>
                        <FormSelect
                          label="Country to filter cities by"
                          options={COUNTRIES}
                          value={cityFilterCountry}
                          onChange={setCityFilterCountry}
                          placeholder="Select Country to Filter Cities"
                        />
                      </FormControl>
                    </FormItem>
                  )}

                  {isEditing ? (
                <FormField
                  control={form.control}
                      name="cities_covered"
                      disabled={isLoading || !cityFilterCountry}
                  render={({ field }) => (
                    <FormItem>
                          <FormLabel>Cities Covered {cityFilterCountry ? `in ${COUNTRIES.find(c => c.value === cityFilterCountry)?.label}` : '(select country first)'}</FormLabel>
                          <FormControl><CitiesTagInput selectedCountry={cityFilterCountry} value={field.value || []} onChange={field.onChange} disabled={isLoading || !cityFilterCountry} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                  ) : (
                    <InfoRow label="Cities Covered" value={companyDetails?.cities_covered?.map(cc => CITY_OPTIONS_BY_COUNTRY[companyDetails?.country || '']?.find(opt => opt.value === cc)?.label || cc).join(', ') || "-"} />
                  )}

                  {isEditing ? (
                <FormField
                  control={form.control}
                      name="airports_served"
                      disabled={isLoading}
                  render={({ field }) => (
                    <FormItem>
                          <FormLabel>Airports Served</FormLabel>
                          <FormControl><TagInput options={AIRPORT_OPTIONS} placeholder="Tag airports..." value={field.value || []} onChange={field.onChange} disabled={isLoading} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                  ) : (
                    <InfoRow label="Airports Served" value={companyDetails?.airports_served?.map(as => AIRPORT_OPTIONS.find(opt => opt.value === as)?.label || as).join(', ') || "-"} />
                  )}

                  {isEditing ? (
                <FormField
                  control={form.control}
                      name="languages_spoken"
                      disabled={isLoading}
                  render={({ field }) => (
                    <FormItem>
                          <FormLabel>Languages Spoken by Staff/Chauffeurs</FormLabel>
                          <FormControl><TagInput options={LANGUAGES_OPTIONS} placeholder="Tag languages..." value={field.value || []} onChange={field.onChange} disabled={isLoading} /></FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  ) : (
                    <InfoRow label="Languages Spoken" value={companyDetails?.languages_spoken?.map(ls => LANGUAGES_OPTIONS.find(opt => opt.value === ls)?.label || ls).join(', ') || "-"} />
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader><CardTitle className="flex items-center text-lg"><Settings className="mr-2 h-5 w-5 text-primary"/>Operational Details</CardTitle></CardHeader>
                <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                  <FormField control={form.control} name="dispatch_software" disabled={isLoading || !isEditing} render={({ field }) => (
                    <FormItem className="md:col-span-1">
                      <FormLabel>Dispatch Software Used</FormLabel>
                      {isEditing ? (
                        <div className="space-y-2 p-3 border rounded-md bg-slate-50/50">
                          {DISPATCH_SOFTWARE_OPTIONS.map(opt => (
                            <FormField key={opt.value} control={form.control} name="dispatch_software" render={({ field: itemField }) => (
                              <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                                <FormControl><Checkbox checked={itemField.value?.includes(opt.value)} onCheckedChange={(checked) => {
                                  const currentVal = itemField.value || [];
                                  itemField.onChange(checked ? [...currentVal, opt.value] : currentVal.filter((v: string) => v !== opt.value));
                                }} disabled={isLoading} /></FormControl>
                                <FormLabel className="font-normal text-sm">{opt.label}</FormLabel>
                              </FormItem>
                            )} />
                          ))}
                        </div>
                      ) : <InfoRow label="" value={companyDetails?.dispatch_software?.map(ds => DISPATCH_SOFTWARE_OPTIONS.find(opt=>opt.value===ds)?.label || ds).join(', ') || "-"} />}
                      <FormMessage />
                    </FormItem>
                  )} />

                  <FormField
                    control={form.control}
                    name="trip_status_updates"
                    disabled={isLoading || !isEditing}
                    render={({ field }) => (
                      <FormItem className="md:col-span-1">
                        <FormLabel>Primary Status Update Method</FormLabel>
                        {isEditing ? (
                          <div className="space-y-2 p-3 border rounded-md bg-slate-50/50">
                            {TRIP_STATUS_UPDATE_CHOICES.map(opt => (
                              <FormField
                                key={opt.value}
                                control={form.control}
                                name="trip_status_updates"
                                render={({ field: itemField }) => (
                                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                      <FormControl>
                                      <Checkbox
                                        checked={itemField.value?.includes(opt.value)}
                                        onCheckedChange={checked => {
                                          const currentVal = itemField.value || [];
                                          itemField.onChange(
                                            checked
                                              ? [...currentVal, opt.value]
                                              : currentVal.filter((v: string) => v !== opt.value)
                                          );
                                        }}
                                        disabled={isLoading}
                                      />
                      </FormControl>
                                    <FormLabel className="font-normal text-sm">{opt.label}</FormLabel>
                                  </FormItem>
                                )}
                              />
                            ))}
                          </div>
                        ) : (
                          <InfoRow
                            label=""
                            value={companyDetails?.trip_status_updates
                              ?.map(tsu => TRIP_STATUS_UPDATE_CHOICES.find(opt => opt.value === tsu)?.label || tsu)
                              .join(', ') || "-"}
                          />
                        )}
                      <FormMessage />
                    </FormItem>
                  )}
                />

                  <div className="md:col-span-2 grid grid-cols-1 sm:grid-cols-3 gap-3 mt-2">
                    {isEditing ? <FormField control={form.control} name="live_phone_support" disabled={isLoading} render={({ field }) => (<FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm"><FormLabel className="text-sm">24/7 Live Phone Support?</FormLabel><FormControl><Checkbox checked={field.value} onCheckedChange={field.onChange} /></FormControl></FormItem>)} /> : <InfoRow label="24/7 Live Phone Support" value={companyDetails?.live_phone_support ? "Yes" : "No"} />}
                    {isEditing ? <FormField control={form.control} name="gridd_gnet_member" disabled={isLoading} render={({ field }) => (<FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm"><FormLabel className="text-sm">Gridd Gnet Member?</FormLabel><FormControl><Checkbox checked={field.value} onCheckedChange={field.onChange} /></FormControl></FormItem>)} /> : <InfoRow label="Gridd Gnet Member" value={companyDetails?.gridd_gnet_member ? "Yes" : "No"} />}
                    {isEditing ? <FormField control={form.control} name="addons_la" disabled={isLoading} render={({ field }) => (<FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm"><FormLabel className="text-sm">Using Addons.LA?</FormLabel><FormControl><Checkbox checked={field.value} onCheckedChange={field.onChange} /></FormControl></FormItem>)} /> : <InfoRow label="Using Addons.LA" value={companyDetails?.addons_la ? "Yes" : "No"} />}
              </div>
            </CardContent>
          </Card>

              <Card>
                <CardHeader><CardTitle className="flex items-center text-lg"><Users className="mr-2 h-5 w-5 text-primary"/>Service Standards & Policies</CardTitle></CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                        {isEditing ? <FormField control={form.control} name="dress_code" disabled={isLoading} render={({ field }) => (<FormItem><FormLabel>Chauffeur Dress Code</FormLabel><FormControl><Textarea placeholder="e.g., Dark suit, white shirt, tie" {...field} rows={3}/></FormControl><FormMessage /></FormItem>)} /> : <InfoRow label="Chauffeur Dress Code" value={companyDetails?.dress_code} />}
                        {isEditing ? <FormField control={form.control} name="cancellation_policy" disabled={isLoading} render={({ field }) => (<FormItem><FormLabel>Cancellation Policy</FormLabel><FormControl><Textarea placeholder="Detail your cancellation terms for different vehicle types/services..." {...field} rows={4}/></FormControl><FormMessage /></FormItem>)} /> : <InfoRow label="Cancellation Policy" value={companyDetails?.cancellation_policy} />}
                        {isEditing ? <FormField control={form.control} name="meet_greet" disabled={isLoading} render={({ field }) => (<FormItem><FormLabel>Meet & Greet Procedures / Airport Meeting Points</FormLabel><FormControl><Textarea placeholder="Describe standard airport meeting points and meet & greet services..." {...field} rows={4}/></FormControl><FormMessage /></FormItem>)} /> : <InfoRow label="Meet & Greet Procedures" value={companyDetails?.meet_greet} />}
                        {isEditing ? <FormField control={form.control} name="events_seasonal" disabled={isLoading} render={({ field }) => (<FormItem><FormLabel>Notes on Special Events / Seasonal Demands</FormLabel><FormControl><Textarea placeholder="Any specific protocols or notes for handling major local events or peak seasons?" {...field} rows={3}/></FormControl><FormMessage /></FormItem>)} /> : <InfoRow label="Special Events / Seasonal Notes" value={companyDetails?.events_seasonal} />}
                    </div>
                     <Separator className="my-6" />
                     <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {[
                            { name: 'vanity_plates', label: 'Do vehicles have vanity license plates?' },
                            { name: 'bottled_water', label: 'Is bottled water provided in vehicles?' },
                            { name: 'track_flights', label: 'Do you track all arriving flights?' },
                            { name: 'road_shows', label: 'Does your company handle Road Shows?' },
                            { name: 'provide_chauffeur_info', label: 'For Roadshows, can chauffeur info be provided by 12 noon day prior?', dependsOn: 'road_shows', watchValue: watchedRoadShows },
                            { name: 'wall_street_journal', label: 'For Roadshows, can Wall Street Journal be provided?', dependsOn: 'road_shows', watchValue: watchedRoadShows },
                            { name: 'farm_out_local', label: 'Does your company farm out local work?' },
                            { name: 'charge_for_delays', label: 'Do you charge for canceled flights or significant flight delays?' },
                        ].map(q => {
                            const TypedQName = q.name as keyof CompanyFormValues;
                            if (q.dependsOn && q.watchValue !== 'yes' && isEditing) return null;
                            if (q.dependsOn && companyDetails?.[q.dependsOn as keyof CompanyFormValues] !== 'yes' && !isEditing) return null;

                            return (
                              <Controller
                                key={q.name}
                                name={TypedQName}
                                control={form.control}
                                disabled={isLoading || !isEditing}
                                render={({ field }) => (
                                  <FormItem className="flex flex-col sm:flex-row sm:items-center sm:justify-between rounded-lg border p-3 shadow-sm gap-2">
                                    <FormLabel className="text-sm mb-2 sm:mb-0 flex-1">{q.label}</FormLabel>
                                    {isEditing ? (
                                      <RadioGroup onValueChange={field.onChange} defaultValue={field.value} value={field.value} className="flex gap-x-4 flex-shrink-0">
                                          {YES_NO_OPTIONS.map(opt => (
                                          <FormItem key={opt.value} className="flex items-center space-x-2 space-y-0">
                                              <FormControl><RadioGroupItem value={opt.value} id={`${TypedQName}-${opt.value}`} /></FormControl>
                                              <FormLabel htmlFor={`${TypedQName}-${opt.value}`} className="font-normal text-sm cursor-pointer">{opt.label}</FormLabel>
                                          </FormItem>
                                          ))}
                                      </RadioGroup>
                                    ) : <InfoRow label="" value={companyDetails?.[TypedQName] ? (companyDetails[TypedQName] === 'yes' ? 'Yes' : 'No') : "-"} className="mb-0 flex-shrink-0"/>}
                                    <FormMessage className="sm:ml-auto w-full sm:w-auto text-right" />
                                  </FormItem>
                                )}
                              />
                            );
                        })}
                        {((isEditing && watchedChargeForDelays === 'yes') || (!isEditing && companyDetails?.charge_for_delays === 'yes')) && (
                            isEditing ? <FormField control={form.control} name="charge_amount" disabled={isLoading} render={({ field }) => (<FormItem className="p-3 border rounded-lg shadow-sm md:col-span-2"><FormLabel>If yes, how much is charged for delays/cancellations?</FormLabel><FormControl><Input {...field} placeholder="$ Amount or description" /></FormControl><FormMessage /></FormItem>)} /> : <InfoRow label="Amount charged for delays/cancellations" value={companyDetails?.charge_amount} className="p-3 border rounded-lg shadow-sm md:col-span-2"/>
                        )}
                     </div>
                     <Separator className="my-6" />
                     <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-start">
                        {isEditing ? <FormField control={form.control} name="direct_billing" disabled={isLoading} render={({ field }) => (<FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm"><FormLabel className="text-sm">Offer Direct Billing?</FormLabel><FormControl><Checkbox checked={field.value} onCheckedChange={field.onChange} /></FormControl></FormItem>)} /> : <InfoRow label="Offer Direct Billing?" value={companyDetails?.direct_billing ? "Yes" : "No"}/>}
                        {((isEditing && watchedDirectBilling) || (!isEditing && companyDetails?.direct_billing)) && (
                            <Controller name="direct_billing_schedule" control={form.control} disabled={isLoading || !isEditing} render={({ field }) => (
                                <FormItem className="rounded-lg border p-3 shadow-sm">
                                <FormLabel className="text-sm mb-2 block">Direct Billing Schedule</FormLabel>
                                {isEditing ? (
                                <RadioGroup onValueChange={field.onChange} value={field.value} className="flex flex-col sm:flex-row gap-4">
                                    {BILLING_SCHEDULE_OPTIONS.map(opt => (
                                    <FormItem key={opt.value} className="flex items-center space-x-2 space-y-0">
                                        <FormControl><RadioGroupItem value={opt.value} id={`direct_billing_schedule-${opt.value}`} /></FormControl>
                                        <FormLabel htmlFor={`direct_billing_schedule-${opt.value}`} className="font-normal text-sm cursor-pointer">{opt.label}</FormLabel>
                                    </FormItem>
                                    ))}
                                </RadioGroup>
                                ) : <InfoRow label="" value={BILLING_SCHEDULE_OPTIONS.find(opt=>opt.value === companyDetails?.direct_billing_schedule)?.label || companyDetails?.direct_billing_schedule} className="mb-0"/>}
                                <FormMessage />
                                </FormItem>
                            )} />
                        )}
                     </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="complianceDocs" className="space-y-6">
              <Card>
                <CardHeader><CardTitle className="flex items-center text-lg"><FileText className="mr-2 h-5 w-5 text-primary"/>Submitted Documents Overview</CardTitle></CardHeader>
                <CardContent className="space-y-3">
                  {mockDocuments.filter(doc => doc.name === "Business License" || doc.name === "Insurance Certificate" || doc.name === "DOT Number").map(doc => (
                    <div key={doc.id} className="flex items-center justify-between p-3 bg-muted/40 rounded-lg border text-sm">
                      <div className="flex items-center gap-3">
                        {doc.status === "verified" ? <CheckCircle2 className="h-5 w-5 text-green-500" /> : doc.status === "in_review" ? <Clock className="h-5 w-5 text-yellow-500" /> : <XCircle className="h-5 w-5 text-gray-400" />}
                        <div><p className="font-medium">{doc.name}</p><p className="text-xs text-muted-foreground">Last Upload: {doc.uploaded}</p></div>
                      </div>
                      <Button variant="outline" size="sm" asChild><a href={doc.url} target="_blank" rel="noopener noreferrer"><Info className="mr-1.5 h-3.5 w-3.5"/>View Document</a></Button>
                    </div>
                  ))}
                  {!isEditing && companyDetails && (
                    <div className="mt-4 space-y-2">
                      {companyDetails.business_license && typeof companyDetails.business_license === 'string' &&
                        <FileDisplay file={companyDetails.business_license} name="Business License" editMode={false}/>}
                      {companyDetails.insurance_certificate && typeof companyDetails.insurance_certificate === 'string' &&
                        <FileDisplay file={companyDetails.insurance_certificate} name="Insurance Certificate" editMode={false}/>}
          </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </form>
      </Form>
    </div>
  );
}