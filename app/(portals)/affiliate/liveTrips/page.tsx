"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/app/components/ui/tabs"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import { Badge } from "@/app/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table"
import { MapPin, Phone, MessageSquare, Car, Clock, Calendar, User } from "lucide-react"
import GodsView, { Trip } from "@/app/components/shared/trips/GodsView"
import { useAffiliateAccess } from '@/app/lib/hooks/useAffiliateAccess';
import { AffiliateOnboardingBanner } from '@/app/components/ui/AffiliateOnboardingBanner';

// Sample data for the live trips
const sampleTrips: Trip[] = [
  {
    id: "trip-1",
    status: "in_progress",
    driver: {
      name: "<PERSON>",
      phone: "************",
    },
    vehicle: "Sedan - Black",
    priority: "standard",
    pickupLocation: "JFK Airport Terminal 4",
    dropoffLocation: "Marriott Marquis, Times Square",
    passengers: [{ name: "Alice Johnson", phone: "************" }],
    startTime: "10:30 AM",
    endTime: "11:45 AM",
    currentLocation: {
      lat: 40.7128,
      lng: -74.006,
    },
    date: "2023-06-15",
  },
  {
    id: "trip-2",
    status: "in_progress",
    driver: {
      name: "Maria Rodriguez",
      phone: "************",
    },
    vehicle: "SUV - Black",
    priority: "vip",
    pickupLocation: "LaGuardia Airport Terminal B",
    dropoffLocation: "Four Seasons Hotel, Manhattan",
    passengers: [
      { name: "Robert Chen", phone: "************" },
      { name: "Sarah Williams", phone: "************" },
    ],
    startTime: "11:15 AM",
    endTime: "12:30 PM",
    alerts: [
      {
        message: "Heavy traffic on FDR Drive",
        type: "warning",
      },
    ],
    currentLocation: {
      lat: 40.7282,
      lng: -73.994,
    },
    date: "2023-06-15",
  },
  {
    id: "trip-3",
    status: "in_progress",
    driver: {
      name: "David Johnson",
      phone: "************",
    },
    vehicle: "Minibus - White",
    priority: "standard",
    pickupLocation: "Grand Central Terminal",
    dropoffLocation: "Brooklyn Marriott",
    passengers: [
      { name: "Michael Brown", phone: "************" },
      { name: "Jennifer Davis", phone: "************" },
      { name: "James Wilson", phone: "************" },
      { name: "Patricia Moore", phone: "************" },
    ],
    startTime: "1:00 PM",
    endTime: "2:15 PM",
    currentLocation: {
      lat: 40.7512,
      lng: -73.9762,
    },
    date: "2023-06-15",
  },
];

export default function LiveTripsPage() {
  const { showOnboardingBanner } = useAffiliateAccess();
  const [viewMode, setViewMode] = useState<"list" | "map">("map");
  
  if (showOnboardingBanner) {
    return (
      <div className="p-8">
        <AffiliateOnboardingBanner>
          <div className="mt-2 text-yellow-800">You must complete onboarding to access live trips.</div>
        </AffiliateOnboardingBanner>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold">Live Trips</h1>
        <div className="flex space-x-2">
          <Button 
            variant={viewMode === "list" ? "default" : "outline"} 
            onClick={() => setViewMode("list")}
          >
            List View
          </Button>
          <Button 
            variant={viewMode === "map" ? "default" : "outline"} 
            onClick={() => setViewMode("map")}
          >
            Map View
          </Button>
        </div>
      </div>

      {viewMode === "list" ? (
        <Card>
          <CardHeader>
            <CardTitle>Active Trips</CardTitle>
            <CardDescription>
              Real-time view of all your active trips
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {sampleTrips.map((trip) => (
                <Card key={trip.id} className="overflow-hidden">
                  <CardContent className="p-0">
                    <div className="flex border-b">
                      <div className="p-4 flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <Car className="h-5 w-5 text-muted-foreground" />
                            <span className="font-medium">{trip.vehicle}</span>
                          </div>
                          <Badge variant={trip.priority === "vip" ? "destructive" : "secondary"}>
                            {trip.priority === "vip" ? "VIP" : "Standard"}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <div className="text-sm font-medium">Driver</div>
                            <div className="flex items-center gap-2 mt-1">
                              <User className="h-4 w-4 text-muted-foreground" />
                              <span>{trip.driver?.name}</span>
                            </div>
                          </div>
                          <div>
                            <div className="text-sm font-medium">Time</div>
                            <div className="flex items-center gap-2 mt-1">
                              <Clock className="h-4 w-4 text-muted-foreground" />
                              <span>{trip.startTime} - {trip.endTime}</span>
                            </div>
                          </div>
                          <div>
                            <div className="text-sm font-medium">Pickup</div>
                            <div className="flex items-center gap-2 mt-1">
                              <MapPin className="h-4 w-4 text-green-500" />
                              <span className="text-sm">{trip.pickupLocation}</span>
                            </div>
                          </div>
                          <div>
                            <div className="text-sm font-medium">Dropoff</div>
                            <div className="flex items-center gap-2 mt-1">
                              <MapPin className="h-4 w-4 text-red-500" />
                              <span className="text-sm">{trip.dropoffLocation}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="p-4 border-l bg-muted/10 flex flex-col justify-center items-center w-48">
                        <div className="text-center mb-4">
                          <div className="text-sm font-medium">Passengers</div>
                          <div className="text-2xl font-bold">{trip.passengers?.length || 0}</div>
                        </div>
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline">
                            <Phone className="h-4 w-4 mr-1" />
                            Call
                          </Button>
                          <Button size="sm" variant="outline">
                            <MessageSquare className="h-4 w-4 mr-1" />
                            Message
                          </Button>
                        </div>
                      </div>
                    </div>
                    {trip.alerts && trip.alerts.length > 0 && (
                      <div className="p-3 bg-yellow-50 border-t">
                        <div className="flex items-center gap-2">
                          <span className="text-yellow-600 text-sm font-medium">Alert:</span>
                          <span className="text-sm">{trip.alerts[0].message}</span>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="h-[calc(100vh-12rem)]">
          <GodsView trips={sampleTrips} portalType="affiliate" />
        </div>
      )}
    </div>
  )
} 