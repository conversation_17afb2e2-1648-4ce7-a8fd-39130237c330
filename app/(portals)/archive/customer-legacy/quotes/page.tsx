'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import { Search, FilterX, Plus, Calendar } from "lucide-react"
import { Badge } from "@/app/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { Card, CardHeader, CardTitle, CardContent } from "@/app/components/ui/card"
import { QuoteRow, QuoteRowData } from "@/app/components/shared/rows"
import { useToast } from "@/app/components/ui/use-toast"
import { useRouter } from "next/navigation"
import { CommonTabs, TabItem } from "@/app/components/shared/tabs/CommonTabs"

export default function CustomerQuotesPage() {
  const [quotes, setQuotes] = useState<QuoteRowData[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedQuote, setSelectedQuote] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredQuotes, setFilteredQuotes] = useState<{
    all: QuoteRowData[];
    pending: QuoteRowData[];
    fixed_offer: QuoteRowData[];
    accepted: QuoteRowData[];
    rejected: QuoteRowData[];
  }>({
    all: [],
    pending: [],
    fixed_offer: [],
    accepted: [],
    rejected: []
  })
  
  const { toast } = useToast()
  const router = useRouter()
  
  // Fetch quotes data
  useEffect(() => {
    const fetchQuotes = async () => {
      try {
        setLoading(true)
        
        const response = await fetch('/api/quotes')
        
        if (!response.ok) {
          throw new Error(`Error: ${response.statusText || response.status}`)
        }
        
        const data = await response.json()
        
        if (data && Array.isArray(data)) {
          console.log(`Customer portal fetched ${data.length} quotes`)
          
          // Map the API data to QuoteRowData format
          const mappedQuotes: QuoteRowData[] = data.map((q: any) => ({
            id: q.id,
            customer_id: q.customer_id,
            reference_number: q.reference_number,
            service_type: q.service_type || 'point',
            vehicle_type: q.vehicle_type || 'Standard',
            pickup_location: q.pickup_location || 'N/A',
            dropoff_location: q.dropoff_location || 'N/A',
            date: q.date,
            time: q.time,
            duration: q.duration || '0',
            distance: q.distance || '0',
            status: q.status,
            passenger_count: q.passenger_count || 0,
            luggage_count: q.luggage_count || 0,
            special_requests: q.special_requests,
            priority: q.priority || 'medium',
            total_amount: q.total_amount,
            created_at: q.created_at,
            updated_at: q.updated_at,
            customer: q.customer,
            intermediate_stops: q.intermediate_stops,
            expiry_time: q.expiry_time,
            timeline: q.timeline || [],
            city: q.city || '',
            // Only include trips when status is 'accepted' (per business rule)
            trips: q.status === 'accepted' ? (q.trips || []) : []
          }))
          
          setQuotes(mappedQuotes)
        } else {
          console.warn('Unexpected data format from API:', data)
          setQuotes([])
        }
      } catch (err) {
        console.error('Error fetching quotes:', err)
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setLoading(false)
      }
    }
    
    fetchQuotes()
  }, [])
  
  // Filter quotes based on search and status
  useEffect(() => {
    const filtered = {
      all: quotes.filter(quote => filterBySearch(quote)),
      pending: quotes.filter(quote => 
        filterBySearch(quote) && 
        ['pending', 'pending_quote', 'new'].includes(quote.status.toLowerCase())
      ),
      fixed_offer: quotes.filter(quote => 
        filterBySearch(quote) && 
        ['fixed_offer', 'quote_ready', 'quote_assigned'].includes(quote.status.toLowerCase())
      ),
      accepted: quotes.filter(quote => 
        filterBySearch(quote) && 
        quote.status.toLowerCase() === 'accepted'
      ),
      rejected: quotes.filter(quote => 
        filterBySearch(quote) && 
        quote.status.toLowerCase() === 'rejected'
      )
    }
    
    setFilteredQuotes(filtered)
  }, [quotes, searchTerm])
  
  // Search filter function
  const filterBySearch = (quote: QuoteRowData) => {
    if (!searchTerm) return true
    
    const search = searchTerm.toLowerCase()
    return (
      (quote.pickup_location.toLowerCase().includes(search)) ||
      (quote.dropoff_location.toLowerCase().includes(search)) ||
      (quote.vehicle_type.toLowerCase().includes(search)) ||
      (quote.reference_number && quote.reference_number.toLowerCase().includes(search))
    )
  }
  
  // Quote action handlers
  const handleQuoteSelection = (quote: QuoteRowData) => {
    setSelectedQuote(quote.id === selectedQuote ? null : quote.id)
    router.push(`/customer/quotes/${quote.id}`)
  }
  
  const handleAcceptQuote = (quoteId: string) => {
    // Find the quote
    const quote = quotes.find(q => q.id === quoteId)
    if (!quote) return
    
    // In a real implementation, this would call an API to accept the quote
    router.push(`/customer/quotes/${quoteId}/accept`)
  }
  
  const handleRejectQuote = (quoteId: string) => {
    // Find the quote
    const quote = quotes.find(q => q.id === quoteId)
    if (!quote) return
    
    // In a real implementation, this would call an API to reject the quote
    router.push(`/customer/quotes/${quoteId}/reject`)
  }
  
  const handleRequestChanges = (quoteId: string) => {
    // Find the quote
    const quote = quotes.find(q => q.id === quoteId)
    if (!quote) return
    
    // In a real implementation, this would call an API to request changes
    router.push(`/customer/quotes/${quoteId}/request-changes`)
  }
  
  // Create a reusable content renderer function
  const renderTabContent = (tabId: string, quotes: QuoteRowData[]) => {
    if (quotes.length === 0) {
      return (
        <div className="text-center p-4 border rounded-md">
          <p className="text-muted-foreground">No {tabId.replace('_', ' ')} quotes found</p>
        </div>
      );
    }
    
    return (
      <div className="space-y-2">
        {quotes.map((quote) => {
          if (tabId === 'all') {
            return (
              <QuoteRow
                key={quote.id}
                quote={quote}
                isSelected={selectedQuote === quote.id}
                onClick={() => handleQuoteSelection(quote)}
                onAccept={() => handleAcceptQuote(quote.id)}
                onReject={() => handleRejectQuote(quote.id)}
                onRequestChanges={() => handleRequestChanges(quote.id)}
                userType="customer"
                expandable={true}
              />
            );
          } else if (tabId === 'pending') {
            return (
              <QuoteRow
                key={quote.id}
                quote={quote}
                isSelected={selectedQuote === quote.id}
                onClick={() => handleQuoteSelection(quote)}
                userType="customer"
                expandable={true}
              />
            );
          } else if (tabId === 'fixed_offer') {
            return (
              <QuoteRow
                key={quote.id}
                quote={quote}
                isSelected={selectedQuote === quote.id}
                onClick={() => handleQuoteSelection(quote)}
                onAccept={() => handleAcceptQuote(quote.id)}
                onReject={() => handleRejectQuote(quote.id)}
                onRequestChanges={() => handleRequestChanges(quote.id)}
                userType="customer"
                expandable={true}
              />
            );
          } else if (tabId === 'accepted') {
            return (
              <QuoteRow
                key={quote.id}
                quote={quote}
                isSelected={selectedQuote === quote.id}
                onClick={() => handleQuoteSelection(quote)}
                userType="customer"
                expandable={true}
                onCall={() => router.push(`/customer/quotes/${quote.id}/contact`)}
                onMessage={() => router.push(`/customer/quotes/${quote.id}/message`)}
              />
            );
          } else if (tabId === 'rejected') {
            return (
              <QuoteRow
                key={quote.id}
                quote={quote}
                isSelected={selectedQuote === quote.id}
                onClick={() => handleQuoteSelection(quote)}
                userType="customer"
                expandable={true}
              />
            );
          }
        })}
      </div>
    );
  };

  // Create tab items for the CommonTabs component
  const tabItems: TabItem[] = [
    {
      id: 'all',
      label: 'All',
      count: filteredQuotes.all.length,
      content: renderTabContent('all', filteredQuotes.all)
    },
    {
      id: 'pending',
      label: 'Pending',
      count: filteredQuotes.pending.length,
      content: renderTabContent('pending', filteredQuotes.pending)
    },
    {
      id: 'fixed_offer',
      label: 'Fixed Offer',
      count: filteredQuotes.fixed_offer.length,
      content: renderTabContent('fixed_offer', filteredQuotes.fixed_offer)
    },
    {
      id: 'accepted',
      label: 'Accepted',
      count: filteredQuotes.accepted.length,
      content: renderTabContent('accepted', filteredQuotes.accepted)
    },
    {
      id: 'rejected',
      label: 'Rejected',
      count: filteredQuotes.rejected.length,
      content: renderTabContent('rejected', filteredQuotes.rejected)
    }
  ];
  
  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }
  
  // Error state
  if (error) {
    return (
      <div className="p-8">
        <div className="bg-destructive/10 p-4 rounded-md text-destructive">
          <p>Error loading quotes: {error}</p>
          <Button 
            variant="outline" 
            onClick={() => window.location.reload()} 
            className="mt-2"
          >
            Retry
          </Button>
        </div>
      </div>
    )
  }
  
  return (
    <div className="p-8 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">My Quotes</h1>
          <p className="text-muted-foreground">
            View and manage your transportation quotes
          </p>
        </div>
        <Button onClick={() => router.push('/customer/quotes/new')}>
          <Plus className="mr-2 h-4 w-4" /> New Quote Request
        </Button>
      </div>
      
      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Quotes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{quotes.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredQuotes.pending.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Fixed Offers</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredQuotes.fixed_offer.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Accepted</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredQuotes.accepted.length}</div>
          </CardContent>
        </Card>
      </div>
      
      {/* Search & Filters */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search quotes..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <Select defaultValue="all">
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Date Filter" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Dates</SelectItem>
            <SelectItem value="today">Today</SelectItem>
            <SelectItem value="week">This Week</SelectItem>
            <SelectItem value="month">This Month</SelectItem>
            <SelectItem value="upcoming">Upcoming</SelectItem>
          </SelectContent>
        </Select>
        
        {searchTerm && (
          <Button variant="outline" onClick={() => setSearchTerm('')}>
            <FilterX className="mr-2 h-4 w-4" />
            Clear
          </Button>
        )}
      </div>
      
      {/* Quotes List */}
      <CommonTabs 
        tabs={tabItems} 
        defaultTab="all" 
        fullWidth={true} 
      />
    </div>
  )
} 