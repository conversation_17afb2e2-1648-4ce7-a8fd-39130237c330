"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card } from "@/app/components/ui/card"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { QuoteForm } from "@/app/components/features/quotes/quote-form"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { getQuoteById } from "@/lib/api/quotes"
import { Skeleton } from "@/app/components/ui/skeleton"
import { Alert, AlertDescription, AlertTitle } from "@/app/components/ui/alert"
import { AlertCircle } from "lucide-react"

interface Quote {
  id: string;
  service_type: 'airport' | 'hourly' | 'point';
  vehicle_type: string;
  pickup_location: string;
  dropoff_location: string;
  date: string;
  time: string;
  passenger_count: string;
  luggage_count: string;
  special_requests: string;
  status: string;
  created_at: string;
  updated_at: string;
  city?: string;
  stops?: {
    location: string;
    type: 'pickup' | 'dropoff';
  }[];
}

export default function EditQuotePage({ params }: { params: { quoteId: string } }) {
  const router = useRouter()
  const quoteId = params.quoteId as string
  
  const [quote, setQuote] = useState<Quote | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchQuote = async () => {
      try {
        const response = await fetch(`/api/quotes/${params.quoteId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch quote');
        }
        const data = await response.json();
        setQuote(data);
      } catch (error) {
        console.error('Error fetching quote:', error);
        setError('Failed to fetch quote');
      } finally {
        setLoading(false);
      }
    };

    fetchQuote();
  }, [params.quoteId]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-x-4">
            <Link href="/customer/quotes">
              <Button variant="ghost" size="icon">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <Skeleton className="h-8 w-40" />
          </div>
        </div>
        <Card className="p-6">
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-x-4">
            <Link href="/customer/quotes">
              <Button variant="ghost" size="icon">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <h2 className="text-2xl font-bold tracking-tight">Edit Quote</h2>
          </div>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        <Button onClick={() => router.push('/customer/quotes')}>
          Return to Quotes
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-x-4">
          <Link href="/customer/quotes">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h2 className="text-2xl font-bold tracking-tight">Edit Quote</h2>
        </div>
      </div>
      <Card className="p-6">
        {quote && (
          <QuoteForm defaultValues={quote} userRole="direct_passenger" />
        )}
      </Card>
    </div>
  )
} 