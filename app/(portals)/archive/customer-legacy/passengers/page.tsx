"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Badge } from "@/app/components/ui/badge"
import { Avatar } from "@/app/components/ui/avatar"
import { Checkbox } from "@/app/components/ui/checkbox"
import { Input } from "@/app/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, <PERSON><PERSON>Content } from "@/app/components/ui/tabs"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/app/components/ui/dialog"
import { Plus, Search, Star, MoreVertical, Users, Mail, Car, Filter, UserPlus } from "lucide-react"
import Link from "next/link"

// Types
interface Passenger {
  id: string
  name: string
  email: string
  jobTitle?: string
  status: "active" | "pending"
  permission: "accepted" | "request"
  initials: string
  isVIP?: boolean
  group?: string
  travelArrangers?: Array<{
    name: string
    status: "accepted" | "pending"
  }>
  ridePreferences?: {
    allowedCars: string[]
    preferredCarType: string
  }
}

// Mock data
const mockPassengers: Passenger[] = [
  {
    id: "p1",
    name: "Lamar Latrelle",
    email: "<EMAIL>",
    jobTitle: "designer",
    status: "active",
    permission: "accepted",
    initials: "LL",
    isVIP: true,
    group: "VIP Guests",
    travelArrangers: [
      { name: "Matt Sasso", status: "accepted" }
    ],
    ridePreferences: {
      allowedCars: ["Economy", "Luxury", "SUV", "Mercedes Benz", "Van"],
      preferredCarType: "Luxury"
    }
  },
  {
    id: "p2",
    name: "Nikola Lukic",
    email: "<EMAIL>",
    jobTitle: "tester",
    status: "active",
    permission: "accepted",
    initials: "NL",
    isVIP: true,
    group: "VIP Guests"
  },
  {
    id: "p3",
    name: "Dan Berg",
    email: "<EMAIL>",
    status: "active",
    permission: "request",
    initials: "DB",
    group: "Staff"
  }
]

export default function PassengersPage() {
  const [passengers] = useState<Passenger[]>(mockPassengers)
  const [selectedPassengers, setSelectedPassengers] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedPassenger, setSelectedPassenger] = useState<Passenger | null>(null)
  const [detailsOpen, setDetailsOpen] = useState(false)
  const [filterGroup, setFilterGroup] = useState<string>("all")
  const [filterStatus, setFilterStatus] = useState<string>("all")

  const filteredPassengers = passengers.filter(passenger => {
    const matchesSearch =
      passenger.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      passenger.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      passenger.jobTitle?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesGroup = filterGroup === "all" || passenger.group === filterGroup
    const matchesStatus = filterStatus === "all" || passenger.status === filterStatus

    return matchesSearch && matchesGroup && matchesStatus
  })

  const vipPassengers = filteredPassengers.filter(p => p.isVIP)
  const regularPassengers = filteredPassengers.filter(p => !p.isVIP)

  return (
    <div className="container space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">Passengers</h2>
        <div className="flex items-center gap-x-2">
          <Button variant="outline" size="sm">
            <Mail className="h-4 w-4 mr-2" />
            Invite Passenger
          </Button>
          <Link href="/customer/passengers/new">
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Passenger
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Passengers</p>
                <h3 className="text-2xl font-bold mt-2">{passengers.length}</h3>
              </div>
              <Users className="h-8 w-8 text-primary/20" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">VIP Passengers</p>
                <h3 className="text-2xl font-bold mt-2">{vipPassengers.length}</h3>
              </div>
              <Star className="h-8 w-8 text-yellow-500/20" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Groups</p>
                <h3 className="text-2xl font-bold mt-2">2</h3>
              </div>
              <Users className="h-8 w-8 text-primary/20" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Invites</p>
                <h3 className="text-2xl font-bold mt-2">3</h3>
              </div>
              <UserPlus className="h-8 w-8 text-primary/20" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle>All Passengers</CardTitle>
            <div className="flex items-center gap-x-2">
              <Link href="/customer/passengers/groups">
                <Button variant="outline" size="sm">
                  <Users className="h-4 w-4 mr-2" />
                  Manage Groups
                </Button>
              </Link>
              {selectedPassengers.length > 0 && (
                <Button variant="outline" size="sm">
                  Bulk Actions ({selectedPassengers.length})
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search passengers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={filterGroup} onValueChange={setFilterGroup}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by Group" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Groups</SelectItem>
                <SelectItem value="VIP Guests">VIP Guests</SelectItem>
                <SelectItem value="Staff">Staff</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* VIP Section */}
          {vipPassengers.length > 0 && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                VIP Passengers
              </h3>
              <div className="space-y-4">
                {vipPassengers.map((passenger) => (
                  <PassengerCard
                    key={passenger.id}
                    passenger={passenger}
                    isSelected={selectedPassengers.includes(passenger.id)}
                    onSelect={(checked) => {
                      setSelectedPassengers(prev =>
                        checked
                          ? [...prev, passenger.id]
                          : prev.filter(id => id !== passenger.id)
                      )
                    }}
                    onViewDetails={() => {
                      setSelectedPassenger(passenger)
                      setDetailsOpen(true)
                    }}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Regular Passengers */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Regular Passengers</h3>
            <div className="space-y-4">
              {regularPassengers.map((passenger) => (
                <PassengerCard
                  key={passenger.id}
                  passenger={passenger}
                  isSelected={selectedPassengers.includes(passenger.id)}
                  onSelect={(checked) => {
                    setSelectedPassengers(prev =>
                      checked
                        ? [...prev, passenger.id]
                        : prev.filter(id => id !== passenger.id)
                    )
                  }}
                  onViewDetails={() => {
                    setSelectedPassenger(passenger)
                    setDetailsOpen(true)
                  }}
                />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Passenger Details Dialog */}
      <Dialog open={detailsOpen} onOpenChange={setDetailsOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Passenger Details</DialogTitle>
            <DialogDescription>
              View and manage passenger information
            </DialogDescription>
          </DialogHeader>
          {selectedPassenger && (
            <div className="space-y-6">
              <div>
                <h4 className="font-medium mb-2">User's Information</h4>
                <div className="space-y-1">
                  <p className="text-sm">
                    <span className="text-muted-foreground">Name:</span> {selectedPassenger.name}
                  </p>
                  <p className="text-sm">
                    <span className="text-muted-foreground">Email:</span> {selectedPassenger.email}
                  </p>
                  {selectedPassenger.jobTitle && (
                    <p className="text-sm">
                      <span className="text-muted-foreground">Job Title:</span> {selectedPassenger.jobTitle}
                    </p>
                  )}
                  {selectedPassenger.group && (
                    <p className="text-sm">
                      <span className="text-muted-foreground">Group:</span> {selectedPassenger.group}
                    </p>
                  )}
                </div>
              </div>

              {selectedPassenger.ridePreferences && (
                <div>
                  <h4 className="font-medium mb-2">Ride Policy / Preferences</h4>
                  <div className="space-y-1">
                    <p className="text-sm">
                      <span className="text-muted-foreground">Allowed Cars:</span>{" "}
                      {selectedPassenger.ridePreferences.allowedCars.join(", ")}
                    </p>
                    <p className="text-sm">
                      <span className="text-muted-foreground">Preferred Car Type:</span>{" "}
                      {selectedPassenger.ridePreferences.preferredCarType}
                    </p>
                  </div>
                </div>
              )}

              {selectedPassenger.travelArrangers && (
                <div>
                  <h4 className="font-medium mb-2">Travel Arrangers</h4>
                  <div className="space-y-2">
                    {selectedPassenger.travelArrangers.map((arranger, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm">{arranger.name}</span>
                        <Badge variant={arranger.status === "accepted" ? "default" : "secondary"}>
                          {arranger.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex justify-end gap-x-2">
                <Button variant="outline" onClick={() => setDetailsOpen(false)}>
                  Close
                </Button>
                <Link href={`/customer/passengers/${selectedPassenger.id}/edit`}>
                  <Button>
                    Edit Passenger
                  </Button>
                </Link>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

function PassengerCard({
  passenger,
  isSelected,
  onSelect,
  onViewDetails
}: {
  passenger: Passenger
  isSelected: boolean
  onSelect: (checked: boolean) => void
  onViewDetails: () => void
}) {
  return (
    <div className="flex items-center justify-between p-4 border rounded-lg">
      <div className="flex items-center gap-x-4">
        <Checkbox
          checked={isSelected}
          onCheckedChange={onSelect}
        />
        <Avatar className="h-10 w-10">
          <div className="bg-primary/10 h-full w-full flex items-center justify-center text-sm font-medium">
            {passenger.initials}
          </div>
        </Avatar>
        <div>
          <div className="flex items-center gap-x-2">
            <span className="font-medium">{passenger.name}</span>
            {passenger.isVIP && (
              <Star className="h-4 w-4 text-yellow-400 fill-yellow-400" />
            )}
            {passenger.group && (
              <Badge variant="secondary" className="ml-2">
                {passenger.group}
              </Badge>
            )}
          </div>
          <div className="text-sm text-muted-foreground">
            {passenger.email} {passenger.jobTitle && `• ${passenger.jobTitle}`}
          </div>
        </div>
      </div>
      <div className="flex items-center gap-x-4">
        <Badge variant={passenger.status === "active" ? "outline" : "secondary"}>
          {passenger.status}
        </Badge>
        <Badge variant={passenger.permission === "accepted" ? "default" : "outline"}>
          {passenger.permission}
        </Badge>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={onViewDetails}>
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Link href={`/customer/passengers/${passenger.id}/edit`} className="flex w-full">
                Edit Passenger
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Link href={`/customer/passengers/${passenger.id}/requirements`} className="flex w-full">
                Manage Requirements
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>Create Quote</DropdownMenuItem>
            <DropdownMenuItem>Add to Group</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  )
} 