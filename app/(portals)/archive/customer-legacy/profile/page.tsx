"use client"

import { useEffect, useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Label } from "@/app/components/ui/label"
import { Input } from "@/app/components/ui/input"
import { But<PERSON> } from "@/app/components/ui/button"
import { Separator } from "@/app/components/ui/separator"
import { Avatar, AvatarFallback, AvatarImage } from "@/app/components/ui/avatar"
import { 
  Camera, 
  Mail, 
  Phone, 
  Building, 
  MapPin, 
  CreditCard, 
  Plus,
  Trash2,
  CheckCircle2,
  Loader2
} from "lucide-react"
import { Badge } from "@/app/components/ui/badge"
import { Switch } from "@/app/components/ui/switch"
import { useToast } from "@/app/components/ui/use-toast"
import { useAuth } from "@/lib/auth/context"
import { useProfile } from "@/lib/hooks/use-profile"

interface PaymentMethod {
  id: string
  type: string
  last4: string
  exp_month: string
  exp_year: string
  is_default: boolean
  billing_address_id: string
  billing_addresses: BillingAddress
}

interface BillingAddress {
  id: string
  street: string
  city: string
  state: string
  zip_code: string
  country: string
}

export default function CustomerProfile() {
  const router = useRouter()
  const { toast } = useToast()
  const { user } = useAuth()
  const { profile, loading: profileLoading, updateProfile } = useProfile()

  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([])
  const [showAddCard, setShowAddCard] = useState(false)
  const [newCard, setNewCard] = useState({
    type: 'credit',
    number: '',
    exp_month: '',
    exp_year: '',
    cvc: '',
    billing_address: {
      street: '',
      city: '',
      state: '',
      zip_code: '',
      country: 'US'
    }
  })

  useEffect(() => {
    if (!user) {
      router.push('/login')
      return
    }
    
    // Load payment methods from API
    loadPaymentMethods()
  }, [user])

  // Load payment methods from API
  const loadPaymentMethods = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/payment-methods')
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || 'Failed to load payment methods')
      }
      
      const data = await response.json()
      setPaymentMethods(data.paymentMethods || [])
    } catch (error) {
      console.error('Error loading payment methods:', error)
      toast({
        title: 'Note',
        description: 'Payment methods functionality is in development',
        variant: 'default'
      })
    } finally {
      setLoading(false)
    }
  }

  // Handle adding a new card
  const handleAddCard = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      setSaving(true)
      
      // Validate card details
      if (!newCard.number || !newCard.exp_month || !newCard.exp_year || !newCard.cvc) {
        toast({
          title: 'Error',
          description: 'Please fill in all card details',
          variant: 'destructive'
        })
        return
      }
      
      // Validate billing address
      if (!newCard.billing_address.street || !newCard.billing_address.city || 
          !newCard.billing_address.state || !newCard.billing_address.zip_code) {
        toast({
          title: 'Error',
          description: 'Please fill in all billing address details',
          variant: 'destructive'
        })
        return
      }
      
      // First create the billing address
      const billingAddressResponse = await fetch('/api/billing-addresses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(newCard.billing_address)
      })
      
      if (!billingAddressResponse.ok) {
        const errorData = await billingAddressResponse.json().catch(() => ({}))
        throw new Error(errorData.message || 'Failed to create billing address')
      }
      
      const billingAddressData = await billingAddressResponse.json()
      
      // Then create the payment method
      const paymentMethodResponse = await fetch('/api/payment-methods', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: newCard.type,
          last4: newCard.number.slice(-4),
          exp_month: newCard.exp_month,
          exp_year: newCard.exp_year,
          billing_address_id: billingAddressData.billingAddress.id,
          is_default: paymentMethods.length === 0 // Make default if it's the first card
        })
      })
      
      if (!paymentMethodResponse.ok) {
        const errorData = await paymentMethodResponse.json().catch(() => ({}))
        throw new Error(errorData.message || 'Failed to create payment method')
      }
      
      // Reset form and reload payment methods
      setNewCard({
        type: 'credit',
        number: '',
        exp_month: '',
        exp_year: '',
        cvc: '',
        billing_address: {
          street: '',
          city: '',
          state: '',
          zip_code: '',
          country: 'US'
        }
      })
      setShowAddCard(false)
      await loadPaymentMethods()
      
      toast({
        title: 'Success',
        description: 'Payment method added successfully'
      })
    } catch (error) {
      console.error('Error adding card:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to add payment method',
        variant: 'destructive'
      })
    } finally {
      setSaving(false)
    }
  }

  // Handle removing a card
  const handleRemoveCard = async (id: string) => {
    try {
      const response = await fetch(`/api/payment-methods?id=${id}`, {
        method: 'DELETE'
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || 'Failed to remove payment method')
      }
      
      await loadPaymentMethods()
      
      toast({
        title: 'Success',
        description: 'Payment method removed successfully'
      })
    } catch (error) {
      console.error('Error removing card:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to remove payment method',
        variant: 'destructive'
      })
    }
  }

  // Handle setting a card as default
  const handleSetDefaultCard = async (id: string) => {
    try {
      const response = await fetch('/api/payment-methods', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          id,
          is_default: true
        })
      })
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || 'Failed to set default payment method')
      }
      
      await loadPaymentMethods()
      
      toast({
        title: 'Success',
        description: 'Default payment method updated'
      })
    } catch (error) {
      console.error('Error setting default card:', error)
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to set default payment method',
        variant: 'destructive'
      })
    }
  }

  const handleSaveProfile = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)

    try {
      if (profile) {
        await updateProfile({
          full_name: profile.full_name,
          email: profile.email,
          phone: profile.phone,
          company: profile.company
        })
        toast({
          title: 'Success',
          description: 'Profile updated successfully'
        })
      }
    } catch (error) {
      console.error('Error saving profile:', error)
      toast({
        title: 'Error',
        description: 'Failed to update profile',
        variant: 'destructive'
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading || profileLoading) {
    return <div>Loading...</div>
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Profile Information</CardTitle>
          <CardDescription>Update your personal information and preferences</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSaveProfile} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="full_name">Full Name</Label>
                <Input
                  id="full_name"
                  value={profile?.full_name || ''}
                  onChange={(e) => profile && updateProfile({ ...profile, full_name: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={profile?.email || ''}
                  onChange={(e) => profile && updateProfile({ ...profile, email: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  value={profile?.phone || ''}
                  onChange={(e) => profile && updateProfile({ ...profile, phone: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="company">Company</Label>
                <Input
                  id="company"
                  value={profile?.company || ''}
                  onChange={(e) => profile && updateProfile({ ...profile, company: e.target.value })}
                />
              </div>
            </div>
            <Button type="submit" disabled={saving}>
              {saving ? 'Saving...' : 'Save Changes'}
            </Button>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Payment Methods</CardTitle>
          <CardDescription>Manage your payment methods</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {paymentMethods.map((method) => (
              <div key={method.id} className="flex items-center justify-between p-4 border rounded">
                <div className="flex items-center space-x-4">
                  <CreditCard className="h-6 w-6" />
                  <div>
                    <p className="font-medium">
                      {method.type.charAt(0).toUpperCase() + method.type.slice(1)} ending in {method.last4}
                    </p>
                    <p className="text-sm text-gray-500">
                      Expires {method.exp_month}/{method.exp_year}
                    </p>
                    <p className="text-sm text-gray-500">
                      {method.billing_addresses ? 
                        `${method.billing_addresses.street}, ${method.billing_addresses.city}, ${method.billing_addresses.state}` : 
                        'No billing address available'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {!method.is_default && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSetDefaultCard(method.id)}
                    >
                      Set Default
                    </Button>
                  )}
                  {method.is_default && (
                    <div className="flex items-center text-green-600">
                      <CheckCircle2 className="h-5 w-5 mr-1" />
                      Default
                    </div>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveCard(method.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}

            {showAddCard ? (
              <form onSubmit={handleAddCard} className="space-y-4 border rounded p-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="col-span-2">
                    <Label htmlFor="card_number">Card Number</Label>
                    <Input
                      id="card_number"
                      value={newCard.number}
                      onChange={(e) => setNewCard({ ...newCard, number: e.target.value })}
                      placeholder="1234 5678 9012 3456"
                    />
                  </div>
                  <div>
                    <Label htmlFor="exp_month">Expiration Month</Label>
                    <Input
                      id="exp_month"
                      value={newCard.exp_month}
                      onChange={(e) => setNewCard({ ...newCard, exp_month: e.target.value })}
                      placeholder="MM"
                    />
                  </div>
                  <div>
                    <Label htmlFor="exp_year">Expiration Year</Label>
                    <Input
                      id="exp_year"
                      value={newCard.exp_year}
                      onChange={(e) => setNewCard({ ...newCard, exp_year: e.target.value })}
                      placeholder="YYYY"
                    />
                  </div>
                  <div>
                    <Label htmlFor="cvc">CVC</Label>
                    <Input
                      id="cvc"
                      value={newCard.cvc}
                      onChange={(e) => setNewCard({ ...newCard, cvc: e.target.value })}
                      placeholder="123"
                    />
                  </div>
                </div>

                <div className="space-y-4 mt-4">
                  <h4 className="font-medium">Billing Address</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="col-span-2">
                      <Label htmlFor="street">Street Address</Label>
                      <Input
                        id="street"
                        value={newCard.billing_address.street}
                        onChange={(e) => setNewCard({
                          ...newCard,
                          billing_address: { ...newCard.billing_address, street: e.target.value }
                        })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="city">City</Label>
                      <Input
                        id="city"
                        value={newCard.billing_address.city}
                        onChange={(e) => setNewCard({
                          ...newCard,
                          billing_address: { ...newCard.billing_address, city: e.target.value }
                        })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="state">State</Label>
                      <Input
                        id="state"
                        value={newCard.billing_address.state}
                        onChange={(e) => setNewCard({
                          ...newCard,
                          billing_address: { ...newCard.billing_address, state: e.target.value }
                        })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="zip_code">ZIP Code</Label>
                      <Input
                        id="zip_code"
                        value={newCard.billing_address.zip_code}
                        onChange={(e) => setNewCard({
                          ...newCard,
                          billing_address: { ...newCard.billing_address, zip_code: e.target.value }
                        })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="country">Country</Label>
                      <Input
                        id="country"
                        value={newCard.billing_address.country}
                        onChange={(e) => setNewCard({
                          ...newCard,
                          billing_address: { ...newCard.billing_address, country: e.target.value }
                        })}
                      />
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowAddCard(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={saving}>
                    {saving ? 'Adding...' : 'Add Card'}
                  </Button>
                </div>
              </form>
            ) : (
              <Button
                variant="outline"
                className="w-full"
                onClick={() => setShowAddCard(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add New Card
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 