"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/app/components/ui/tabs"
import NotificationBanner from "@/app/components/ui/NotificationBanner"

type NotificationType = "default" | "info" | "success" | "warning" | "error"

interface Notification {
  id: number
  variant: NotificationType
  title: string
  message: string
}

export default function NotificationsPage() {
  const notifications: Notification[] = [
    {
      id: 1,
      variant: "info",
      title: "New Quote Available",
      message: "You have received a new quote for Event #123",
    },
    {
      id: 2,
      variant: "success",
      title: "Trip Request Approved",
      message: "Your trip request for Event #456 has been approved",
    },
    {
      id: 3,
      variant: "warning",
      title: "Pending Requirements",
      message: "Please complete the requirements for Event #789",
    },
  ]

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold tracking-tight">Notifications</h2>
      <div className="space-y-4">
        {notifications.map((notification) => (
          <NotificationBanner
            key={notification.id}
            variant={notification.variant}
            title={notification.title}
            message={notification.message}
          />
        ))}
      </div>
    </div>
  )
} 