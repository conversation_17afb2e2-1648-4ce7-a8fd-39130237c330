"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Plus } from "lucide-react"
import Link from "next/link"
import { TripRequestList } from '@/app/components/trip-requests/trip-request-list'

export default function TripRequestsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">Trip Requests</h2>
        <div className="flex items-center gap-x-2">
          <Button variant="outline">Export</Button>
          <Link href="/customer/requests/new">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Request
            </Button>
          </Link>
        </div>
      </div>
      <TripRequestList />
    </div>
  )
} 