"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card } from "@/app/components/ui/card"
import { ArrowLeft, Plus, Bus } from "lucide-react"
import Link from "next/link"
import { useParams } from "next/navigation"
import { Badge } from "@/app/components/ui/badge"

export default function EventTransportationPage() {
  const params = useParams()
  const eventId = params.id as string

  // Mock data - replace with API call
  const services = [
    {
      id: "1",
      type: "airport_transfer",
      status: "pending",
      pickupLocation: "SFO Airport",
      dropoffLocation: "Convention Center",
      date: "2024-06-15",
      time: "09:00",
      passengerCount: 200,
      quoteStatus: "pending",
    },
    {
      id: "2",
      type: "venue_transfer",
      status: "confirmed",
      pickupLocation: "Hotel A",
      dropoffLocation: "Convention Center",
      date: "2024-06-15",
      time: "08:30",
      passengerCount: 50,
      quoteStatus: "accepted",
    },
  ]

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-x-4">
          <Link href={`/customer/events/${eventId}`}>
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h2 className="text-2xl font-bold tracking-tight">Transportation Services</h2>
        </div>
        <Link href={`/events/${eventId}/transportation/new`}>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Service
          </Button>
        </Link>
      </div>

      <div className="grid gap-4">
        {services.map((service) => (
          <Card key={service.id} className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="flex items-center gap-x-2">
                  <Badge variant="outline">{service.type.replace(/_/g, " ")}</Badge>
                  <Badge variant={service.quoteStatus === "accepted" ? "default" : "secondary"}>
                    Quote {service.quoteStatus}
                  </Badge>
                </div>
                <div className="text-sm text-muted-foreground">
                  {service.pickupLocation} → {service.dropoffLocation}
                </div>
                <div className="text-sm text-muted-foreground">
                  {service.date} at {service.time}
                </div>
                <div className="text-sm text-muted-foreground">
                  {service.passengerCount} passengers
                </div>
              </div>
              <Link href={`/events/${eventId}/transportation/${service.id}`}>
                <Button variant="ghost" size="sm">
                  <Bus className="h-4 w-4 mr-2" />
                  View Details
                </Button>
              </Link>
            </div>
          </Card>
        ))}

        {services.length === 0 && (
          <Card className="p-6">
            <div className="text-center text-muted-foreground">
              <Bus className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No transportation services added yet</p>
              <p className="text-sm">Click the button above to add a service</p>
            </div>
          </Card>
        )}
      </div>
    </div>
  )
} 