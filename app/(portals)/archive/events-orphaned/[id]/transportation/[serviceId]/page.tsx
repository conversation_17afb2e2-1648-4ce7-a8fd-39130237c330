"use client"

import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation"
import { Card } from "@/app/components/ui/card"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Separator } from "@/app/components/ui/separator"
import { ArrowLeft, Bus, MapPin, Calendar, Clock, User, Phone } from "lucide-react"
import Link from "next/link"
import { useQuery } from "@tanstack/react-query"
import { PostgrestError } from "@supabase/supabase-js"

interface TransportationService {
  id: string
  eventId: string
  status: string
  pickupLocation: string
  dropoffLocation: string
  date: string
  time: string
  vehicleType: string
  passengerCount: number
  driverName: string
  driverPhone: string
  specialRequirements?: string
}

export default function EventTransportationDetailPage() {
  const params = useParams()
  const router = useRouter()
  const eventId = params.id
  const serviceId = params.serviceId

  console.log('Event Transportation Detail Page - Event ID:', eventId)
  console.log('Event Transportation Detail Page - Service ID:', serviceId)

  // Mock data - replace with actual API call
  const { data: service, isLoading, error } = useQuery<TransportationService, PostgrestError>({
    queryKey: ['transportation', eventId, serviceId],
    queryFn: async () => ({
      id: serviceId as string,
      eventId: eventId as string,
      status: "in_progress",
      pickupLocation: "San Francisco International Airport",
      dropoffLocation: "Moscone Center",
      date: "2024-03-15",
      time: "09:00 AM",
      vehicleType: "Luxury Bus",
      passengerCount: 25,
      driverName: "Michael Johnson",
      driverPhone: "+****************",
      specialRequirements: "Wheelchair accessibility required"
    })
  })

  if (isLoading) {
    return <div>Loading...</div>
  }

  if (error) {
    return <div>Error: {error.message}</div>
  }

  if (!service) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center space-x-4 mb-6">
          <Link href={`/events/${eventId}`}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Event
            </Button>
          </Link>
        </div>
        <p>Transportation service not found.</p>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Link href={`/events/${eventId}`}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Event
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Transportation Service Details</h1>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => router.push(`/events/${eventId}/transportation/${serviceId}/edit`)}>
            Edit Service
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-4">Service Details</h2>
          <div className="grid gap-4">
            <div className="flex items-start space-x-3">
              <Calendar className="h-5 w-5 text-gray-500 mt-0.5" />
              <div>
                <p className="font-medium">Date</p>
                <p className="text-gray-600">{service.date}</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Clock className="h-5 w-5 text-gray-500 mt-0.5" />
              <div>
                <p className="font-medium">Time</p>
                <p className="text-gray-600">{service.time}</p>
              </div>
            </div>
            <Separator />
            <div className="flex items-start space-x-3">
              <MapPin className="h-5 w-5 text-gray-500 mt-0.5" />
              <div>
                <p className="font-medium">Pickup Location</p>
                <p className="text-gray-600">{service.pickupLocation}</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <MapPin className="h-5 w-5 text-gray-500 mt-0.5" />
              <div>
                <p className="font-medium">Dropoff Location</p>
                <p className="text-gray-600">{service.dropoffLocation}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h2 className="text-lg font-semibold mb-4">Vehicle & Passengers</h2>
          <div className="grid gap-4">
            <div className="flex items-start space-x-3">
              <Bus className="h-5 w-5 text-gray-500 mt-0.5" />
              <div>
                <p className="font-medium">Vehicle Type</p>
                <p className="text-gray-600">{service.vehicleType}</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <User className="h-5 w-5 text-gray-500 mt-0.5" />
              <div>
                <p className="font-medium">Passenger Count</p>
                <p className="text-gray-600">{service.passengerCount} passengers</p>
              </div>
            </div>
            <Separator />
            <div className="flex items-start space-x-3">
              <User className="h-5 w-5 text-gray-500 mt-0.5" />
              <div>
                <p className="font-medium">Driver</p>
                <p className="text-gray-600">{service.driverName}</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Phone className="h-5 w-5 text-gray-500 mt-0.5" />
              <div>
                <p className="font-medium">Driver Contact</p>
                <p className="text-gray-600">{service.driverPhone}</p>
              </div>
            </div>
          </div>
        </Card>

        {service.specialRequirements && (
          <Card className="p-6">
            <h2 className="text-lg font-semibold mb-4">Special Requirements</h2>
            <p className="text-gray-600">{service.specialRequirements}</p>
          </Card>
        )}
      </div>
    </div>
  )
} 