# Admin Portal

## Overview
The Admin Portal provides system administrators with comprehensive tools to manage all aspects of the application, including users, companies, affiliates, events, quotes, and trips. This portal is designed for users with the `ADMIN` role.

## Features
- **Dashboard**: Overview of system activity
- **Events**: Manage all events in the system
- **Quotes**: Manage all transportation quotes
- **Companies**: Manage customer companies
- **Affiliates**: Manage transportation providers
- **Coverage**: Manage service coverage areas
- **Users**: User management
- **Settings**: System settings
- **Trips**: Monitor all transportation trips
- **Notifications**: System notifications

## Directory Structure
- `dashboard/` - Admin dashboard
- `events/` - Event management
  - `[id]/` - Individual event details
  - `new/` - Create new event
- `quotes/` - Quote management
  - `[id]/` - Individual quote details
- `companies/` - Company management
  - `[id]/` - Individual company details
  - `new/` - Create new company
- `affiliates/` - Affiliate management
  - `[id]/` - Individual affiliate details
  - `new/` - Create new affiliate
- `coverage/` - Coverage area management
- `users/` - User management
- `settings/` - System settings
- `trips/` - Trip management
- `notifications/` - System notifications
- `passengers/` - Passenger management
- `profile/` - Admin profile
- `requests/` - Request management
- `transportation/` - Transportation management

## Key Components
- `layout.tsx` - Defines the layout for the admin portal
- `companies/[id]/page.tsx` - Company details page
- `affiliates/[id]/page.tsx` - Affiliate details page
- `events/[id]/page.tsx` - Event details page

## API Integration
The admin portal interacts with all API endpoints in the system:
- `/api/events` - Event management
- `/api/quotes` - Quote management
- `/api/trips` - Trip management
- `/api/companies` - Company management
- `/api/affiliates` - Affiliate management
- `/api/users` - User management
- `/api/coverage` - Coverage management

## Testing
Tests for the admin portal are located in `/tests/admin/` and should mirror this directory structure. 