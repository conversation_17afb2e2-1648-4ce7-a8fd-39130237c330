"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Plus } from "lucide-react"
import Link from "next/link"
import { EventList } from "@/app/components/features/events/event-list"

export default function EventsPage() {
  return (
    <div className="container py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Events</h1>
          <p className="text-muted-foreground mt-1">View and manage quotes by event</p>
        </div>
        <Link href="/admin/events/new">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Event
          </Button>
        </Link>
      </div>

      <EventList />
    </div>
  )
} 