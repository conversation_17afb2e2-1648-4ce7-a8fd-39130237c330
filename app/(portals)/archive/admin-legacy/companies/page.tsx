"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { Button } from "@/app/components/ui/button"
import { Badge } from "@/app/components/ui/badge"
import { Search, MapPin, Bus, Star } from "lucide-react"
import Link from "next/link"

interface Company {
  id: string
  name: string
  location: string
  rating: number
  fleetSize: number
  serviceAreas: string[]
  status: "active" | "inactive"
}

const mockCompanies: Company[] = [
  {
    id: "1",
    name: "Premier Transport Co.",
    location: "New York, NY",
    rating: 4.8,
    fleetSize: 25,
    serviceAreas: ["Manhattan", "Brooklyn", "Queens"],
    status: "active"
  },
  {
    id: "2",
    name: "Elite Fleet Services",
    location: "Los Angeles, CA",
    rating: 4.5,
    fleetSize: 18,
    serviceAreas: ["Downtown LA", "Beverly Hills", "Santa Monica"],
    status: "active"
  }
]

export default function CompaniesPage() {
  const [searchQuery, setSearchQuery] = useState("")

  const filteredCompanies = mockCompanies.filter(company =>
    company.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    company.location.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Transportation Companies</h2>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search companies..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
        </div>
        <Button>
          Filter
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredCompanies.map((company) => (
          <Link key={company.id} href={`/companies/${company.id}`}>
            <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-lg">{company.name}</h3>
                  <Badge variant={company.status === "active" ? "default" : "secondary"}>
                    {company.status}
                  </Badge>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4 mr-2" />
                    {company.location}
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Bus className="h-4 w-4 mr-2" />
                    {company.fleetSize} vehicles
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Star className="h-4 w-4 mr-2 text-yellow-400" />
                    {company.rating} rating
                  </div>
                </div>

                <div className="flex flex-wrap gap-2">
                  {company.serviceAreas.map((area) => (
                    <Badge key={area} variant="outline">
                      {area}
                    </Badge>
                  ))}
                </div>
              </div>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  )
} 