"use client"

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { cn } from "@/lib/utils"
import { UserNav } from "@/app/components/features/navigation/user-nav"
import { MobileNav } from "@/app/components/features/navigation/mobile-nav"
import { useAuth } from '@/lib/auth/context'
import { UserRole } from '@/src/types/roles'
import { Loader2 } from "lucide-react"
import {
  Building2,
  LayoutDashboard,
  Users,
  FileText,
  MapPin,
  Clock,
  Settings,
  Menu,
  Activity,
  Calendar
} from "lucide-react"
import Link from "next/link"

const navigation = [
  {
    title: "Dashboard",
    href: "/admin/dashboard",
    icon: LayoutDashboard
  },
  {
    title: "Affiliates",
    href: "/admin/affiliates",
    icon: Building2
  },
  {
    title: "Quotes",
    href: "/admin/quotes",
    icon: FileText,
    highlight: true
  },
  {
    title: "Events",
    href: "/admin/events",
    icon: Calendar,
    highlight: true
  },
  {
    title: "Trips",
    href: "/admin/trips",
    icon: Clock,
    highlight: true
  },
  {
    title: "Users",
    href: "/admin/users",
    icon: Users
  },
  {
    title: "Coverage",
    href: "/admin/coverage",
    icon: MapPin
  },
  {
    title: "Settings",
    href: "/admin/settings",
    icon: Settings
  }
]

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const { user, loading, initialized } = useAuth()
  const router = useRouter()
  
  useEffect(() => {
    console.log('AdminLayout: Mounted. Path:', pathname, 'Auth State:', { user: !!user, roles: user?.roles, loading, initialized });
    return () => {
      console.log('AdminLayout: Unmounting. Path:', pathname);
    }
  }, [pathname, user, loading, initialized]);
  
  // Use an effect to handle the redirect when authentication changes
  useEffect(() => {
    console.log('AdminLayout: Auth check effect. State:', { loading, initialized, userEmail: user?.email, roles: user?.roles });
    if (!loading && initialized) {
      if (!user || !user.roles?.includes('ADMIN' as UserRole)) {
        console.log('AdminLayout: Auth initialized. User is NOT an ADMIN or no user. REDIRECTING to /login. User object was:', JSON.stringify(user));
        router.push('/login');
      } else {
        console.log('AdminLayout: Auth initialized. User IS an ADMIN. No redirect needed. User object:', JSON.stringify(user));
      }
    } else if (loading) {
      console.log('AdminLayout: Auth is loading... no redirect decision yet.');
    } else if (!initialized) {
      console.log('AdminLayout: Auth not yet initialized... no redirect decision yet.');
    }
  }, [user, loading, initialized, router]);
  
  // 1. Show loading state while auth is loading or not yet initialized
  if (loading || !initialized) {
    console.log('AdminLayout: Rendering spinner (loading || !initialized)');
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p>Loading admin dashboard...</p>
        </div>
      </div>
    )
  }
  
  // 2. If initialized and we have an ADMIN user, show the content
  if (initialized && user && user.roles?.includes('ADMIN' as UserRole)) {
    console.log('AdminLayout: Rendering dashboard content (initialized && isAdmin)');
    // Transform navigation items to include icon as ReactNode
    const mobileNavItemsForFeaturesNav = navigation.map(item => ({
      title: item.title,
      href: item.href
    }))

    return (
      <div className="relative flex min-h-screen flex-col">
        {/* Navigation */}
        <header className="sticky top-0 z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="container flex h-14 items-center">
            <div className="mr-4 flex">
              <Link href="/admin" className="mr-6 flex items-center space-x-2">
                <span className="font-bold">Admin Portal</span>
              </Link>
            </div>
            <nav className="flex items-center space-x-6 text-sm font-medium">
              {navigation.map((item) => {
                const isActive = pathname === item.href
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "flex items-center gap-2 transition-colors hover:text-foreground/80",
                      isActive ? "text-foreground" : "text-foreground/60",
                      item.highlight && "text-primary"
                    )}
                  >
                    <item.icon className="h-4 w-4" />
                    {item.title}
                  </Link>
                )
              })}
            </nav>
            <div className="ml-auto flex items-center space-x-4">
              <UserNav />
            </div>
          </div>
        </header>
        <MobileNav superAdminItems={mobileNavItemsForFeaturesNav} tenantDataItems={[]} />
        <main className="flex-1 space-y-4 p-8 pt-6">
          {children}
        </main>
      </div>
    );
  }

  // 3. If initialized but not an ADMIN (redirect is imminent or has happened)
  //    or if user is null after initialization (should also be caught by useEffect for redirect)
  //    Show a redirecting/loading message.
  //    This state should ideally be very brief as the useEffect should have pushed to /login.
  if (initialized) { // Covers cases where user is null or not ADMIN after init
    console.log('AdminLayout: Rendering redirecting/fallback message (initialized but not admin or no user)');
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p>Loading or redirecting...</p>
        </div>
      </div>
    );
  }
  
  // Fallback for any other unexpected state, though the above conditions should cover all scenarios.
  console.log('AdminLayout: Rendering fallback (should not happen)');
  return (
    <div className="flex items-center justify-center min-h-screen">
      <p>An unexpected error occurred.</p>
    </div>
  );
} 