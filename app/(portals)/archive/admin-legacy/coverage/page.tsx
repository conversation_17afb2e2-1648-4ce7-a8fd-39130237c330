"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { Badge } from "@/app/components/ui/badge"
import { Progress } from "@/app/components/ui/progress"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/app/components/ui/tabs"
import {
  Building2,
  Car,
  DollarSign,
  TrendingUp,
  Users,
  Clock,
  Star,
  Calendar,
  Search,
  ArrowUpDown,
  Filter,
  MapPin,
  AlertCircle
} from "lucide-react"
import { cn } from "@/lib/utils"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/app/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/app/components/ui/popover"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/app/components/ui/sheet"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/app/components/ui/form"
import { Checkbox } from "@/app/components/ui/checkbox"
import { Switch } from "@/app/components/ui/switch"
import { Slider } from "@/app/components/ui/slider"
import { ScrollArea } from "@/app/components/ui/scroll-area"
import { Separator } from "@/app/components/ui/separator"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/app/components/ui/alert-dialog"
import { Label } from "@/app/components/ui/label"
import { format } from "date-fns"
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from "recharts"
import { useRouter } from "next/navigation"

interface AffiliateRate {
  vehicleType: VehicleType;
  baseRate: number;
  minimumHours: number;
  hourlyRate: number;
  specialtyServices: string[];
}

interface CityStats {
  totalAffiliates: number;
  activeAffiliates: number;
  totalRevenue: number;
  totalTrips: number;
  totalQuotes: number;
  avgResponseTime: string;
  conversionRate: number;
}

// Add type for vehicle type selection
type VehicleType = "all" | "sedan" | "suv" | "van" | "sprinter";

interface VehicleRates {
  type: VehicleType;
  lowestRate: number;
  avgRate: number;
  highestRate: number;
}

interface TopAffiliate {
  id: string;
  name: string;
  rating: number;
  totalTrips: number;
  revenue: number;
  responseTime: string;
  conversionRate: number;
  vehicleTypes: VehicleType[];
}

interface DynamicPricingRule {
  id: string;
  name: string;
  condition: string;
  adjustment: number;
  isActive: boolean;
}

interface CityFilter {
  vehicleTypes: string[];
  minRating: number;
  maxResponseTime: number;
  minTrips: number;
  isActive: boolean;
}

interface SpecialEvent {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
  location: string;
  expectedDemand: "High" | "Medium" | "Low";
  adjustment: number;
  vehicleTypes: string[];
}

interface DetailedAffiliate extends TopAffiliate {
  rates: AffiliateRate[];
  averagePickupTime: string;
  onTimeRate: number;
  cancellationRate: number;
  lastActiveDate: string;
  preferredAreas: string[];
  insuranceCoverage: string;
  yearInBusiness: number;
  fleetSize: number;
}

interface RevenueData {
  date: string;
  revenue: number;
  trips: number;
}

interface ConversionData {
  month: string;
  quotes: number;
  conversions: number;
  rate: number;
}

interface ResponseTimeData {
  time: string;
  count: number;
}

interface VehicleDemandData {
  type: VehicleType;
  demand: number;
}

const cities = [
  { value: "new-york", label: "New York City", state: "NY" },
  { value: "los-angeles", label: "Los Angeles", state: "CA" },
  { value: "chicago", label: "Chicago", state: "IL" },
  { value: "miami", label: "Miami", state: "FL" },
  { value: "san-francisco", label: "San Francisco", state: "CA" },
  { value: "boston", label: "Boston", state: "MA" },
  { value: "las-vegas", label: "Las Vegas", state: "NV" },
  { value: "houston", label: "Houston", state: "TX" },
] as const;

export default function CoveragePage() {
  const [selectedCity, setSelectedCity] = useState("New York")
  const [activeTab, setActiveTab] = useState("overview")
  const [open, setOpen] = useState(false)
  const [filterOpen, setFilterOpen] = useState(false)
  const [value, setValue] = useState("")
  const [isAddRuleOpen, setIsAddRuleOpen] = useState(false)
  const [newRule, setNewRule] = useState({
    name: "",
    condition: "",
    adjustment: 0,
    isActive: true
  })
  const [selectedVehicleType, setSelectedVehicleType] = useState<VehicleType>("all")
  const [affiliateSearchQuery, setAffiliateSearchQuery] = useState("")
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([])
  const router = useRouter()

  // Mock data
  const cityStats: CityStats = {
    totalAffiliates: 150,
    activeAffiliates: 120,
    totalRevenue: 1250000,
    totalTrips: 3500,
    totalQuotes: 4800,
    avgResponseTime: "15 minutes",
    conversionRate: 0.72,
  }

  // Update mock data to remove availability
  const vehicleRates: VehicleRates[] = [
    {
      type: "sedan",
      lowestRate: 85,
      avgRate: 110,
      highestRate: 150
    },
    {
      type: "suv",
      lowestRate: 120,
      avgRate: 155,
      highestRate: 200
    },
    {
      type: "van",
      lowestRate: 150,
      avgRate: 185,
      highestRate: 250
    },
    {
      type: "sprinter",
      lowestRate: 200,
      avgRate: 250,
      highestRate: 350
    }
  ]

  const topAffiliates: TopAffiliate[] = [
    {
      id: "1",
      name: "Elite Transportation",
      rating: 4.9,
      totalTrips: 250,
      revenue: 375000,
      responseTime: "18m",
      conversionRate: 85,
      vehicleTypes: ["sedan", "suv", "van"]
    },
    {
      id: "2",
      name: "Luxury Limo NYC",
      rating: 4.8,
      totalTrips: 180,
      revenue: 290000,
      responseTime: "22m",
      conversionRate: 78,
      vehicleTypes: ["sedan", "suv"]
    }
  ]

  // Mock dynamic pricing rules
  const [pricingRules, setPricingRules] = useState<DynamicPricingRule[]>([
    {
      id: "1",
      name: "Peak Hours Markup",
      condition: "Time between 7 AM - 10 AM",
      adjustment: 15,
      isActive: true
    },
    {
      id: "2",
      name: "Weekend Rates",
      condition: "Saturdays and Sundays",
      adjustment: 20,
      isActive: true
    },
    {
      id: "3",
      name: "Event Surge",
      condition: "Major events within 5 miles",
      adjustment: 25,
      isActive: false
    }
  ])

  const [specialEvents, setSpecialEvents] = useState<SpecialEvent[]>([
    {
      id: "1",
      name: "New Year's Eve",
      startDate: new Date(2024, 11, 31),
      endDate: new Date(2025, 0, 1),
      location: "Downtown",
      expectedDemand: "High",
      adjustment: 50,
      vehicleTypes: ["sedan", "suv", "van"]
    },
    {
      id: "2",
      name: "Fashion Week",
      startDate: new Date(2024, 8, 10),
      endDate: new Date(2024, 8, 15),
      location: "Various Venues",
      expectedDemand: "High",
      adjustment: 35,
      vehicleTypes: ["sedan", "suv"]
    }
  ])

  const detailedAffiliates: DetailedAffiliate[] = [
    {
      id: "1",
      name: "Elite Transportation",
      rating: 4.9,
      totalTrips: 250,
      revenue: 375000,
      responseTime: "18m",
      conversionRate: 85,
      vehicleTypes: ["sedan", "suv", "van"],
      rates: [
        {
          vehicleType: "sedan",
          baseRate: 85,
          minimumHours: 3,
          hourlyRate: 65,
          specialtyServices: ["Meet & Greet", "Airport Transfer"]
        },
        {
          vehicleType: "suv",
          baseRate: 120,
          minimumHours: 3,
          hourlyRate: 85,
          specialtyServices: ["Meet & Greet", "Airport Transfer", "Executive Service"]
        },
        {
          vehicleType: "van",
          baseRate: 150,
          minimumHours: 4,
          hourlyRate: 95,
          specialtyServices: ["Group Transfer", "Airport Transfer"]
        }
      ],
      averagePickupTime: "5m",
      onTimeRate: 98,
      cancellationRate: 0.5,
      lastActiveDate: "2024-01-20",
      preferredAreas: ["Manhattan", "Brooklyn", "Queens"],
      insuranceCoverage: "$2M",
      yearInBusiness: 12,
      fleetSize: 25
    },
    {
      id: "2",
      name: "Luxury Limo NYC",
      rating: 4.8,
      totalTrips: 180,
      revenue: 290000,
      responseTime: "22m",
      conversionRate: 78,
      vehicleTypes: ["sedan", "suv"],
      rates: [
        {
          vehicleType: "sedan",
          baseRate: 90,
          minimumHours: 3,
          hourlyRate: 70,
          specialtyServices: ["Meet & Greet", "Airport Transfer"]
        },
        {
          vehicleType: "suv",
          baseRate: 125,
          minimumHours: 3,
          hourlyRate: 90,
          specialtyServices: ["Meet & Greet", "Airport Transfer"]
        }
      ],
      averagePickupTime: "8m",
      onTimeRate: 95,
      cancellationRate: 1.2,
      lastActiveDate: "2024-01-19",
      preferredAreas: ["Manhattan", "JFK", "LGA"],
      insuranceCoverage: "$1.5M",
      yearInBusiness: 8,
      fleetSize: 18
    }
  ]

  // Filter form
  const filterForm = useForm<CityFilter>({
    defaultValues: {
      vehicleTypes: [],
      minRating: 4.0,
      maxResponseTime: 30,
      minTrips: 50,
      isActive: true
    }
  })

  const revenueData: RevenueData[] = [
    { date: "Jan 1", revenue: 12500, trips: 85 },
    { date: "Jan 2", revenue: 14200, trips: 95 },
    { date: "Jan 3", revenue: 15800, trips: 105 },
    { date: "Jan 4", revenue: 13900, trips: 92 },
    { date: "Jan 5", revenue: 16200, trips: 108 },
    { date: "Jan 6", revenue: 15500, trips: 103 },
    { date: "Jan 7", revenue: 17800, trips: 118 }
  ]

  const conversionData: ConversionData[] = [
    { month: "Aug", quotes: 180, conversions: 126, rate: 70 },
    { month: "Sep", quotes: 220, conversions: 158, rate: 72 },
    { month: "Oct", quotes: 250, conversions: 185, rate: 74 },
    { month: "Nov", quotes: 280, conversions: 215, rate: 77 },
    { month: "Dec", quotes: 320, conversions: 253, rate: 79 },
    { month: "Jan", quotes: 350, conversions: 280, rate: 80 }
  ]

  const responseTimeData: ResponseTimeData[] = [
    { time: "0-5m", count: 125 },
    { time: "5-10m", count: 85 },
    { time: "10-15m", count: 45 },
    { time: "15-20m", count: 25 },
    { time: "20+m", count: 15 }
  ]

  const vehicleDemandData: VehicleDemandData[] = [
    { type: "sedan", demand: 45 },
    { type: "suv", demand: 30 },
    { type: "van", demand: 15 },
    { type: "sprinter", demand: 10 }
  ]

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042']

  return (
    <div className="container p-6 space-y-6">
      {/* City Overview Stats */}
      <div className="space-y-4">
        {/* Header with Enhanced City Selection */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Coverage Management</h1>
            <p className="text-muted-foreground">
              Manage rates, affiliates, and analytics by city
            </p>
          </div>
          <div className="flex items-center gap-x-2">
            <Button variant="outline" onClick={() => router.push('/admin/coverage/rates')}>
              Manage Rates
            </Button>
          </div>
        </div>

        {/* City Selection */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Popover open={open} onOpenChange={setOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={open}
                  className="w-[250px] justify-between"
                >
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    {cities.find((city) => city.value === selectedCity)?.label ?? "Select city..."}
                  </div>
                  <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[250px] p-0">
                <Command>
                  <CommandInput placeholder="Search cities..." />
                  <CommandEmpty>No city found.</CommandEmpty>
                  <CommandGroup>
                    {cities.map((city) => (
                      <CommandItem
                        key={city.value}
                        value={city.value}
                        onSelect={(currentValue) => {
                          setSelectedCity(currentValue)
                          setOpen(false)
                        }}
                      >
                        <MapPin className="mr-2 h-4 w-4" />
                        {city.label}
                        <span className="ml-auto text-xs text-muted-foreground">{city.state}</span>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </Command>
              </PopoverContent>
            </Popover>

            <Sheet open={filterOpen} onOpenChange={setFilterOpen}>
              <SheetTrigger asChild>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  Filters
                </Button>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>Filter Options</SheetTitle>
                  <SheetDescription>
                    Customize your view of {cities.find((city) => city.value === selectedCity)?.label}
                  </SheetDescription>
                </SheetHeader>
                <Form {...filterForm}>
                  <form className="space-y-6 pt-6">
                    <FormField
                      control={filterForm.control}
                      name="vehicleTypes"
                      render={() => (
                        <FormItem>
                          <FormLabel>Vehicle Types</FormLabel>
                          <div className="grid grid-cols-2 gap-2 pt-2">
                            {vehicleRates.map((vehicle) => (
                              <FormField
                                key={vehicle.type}
                                control={filterForm.control}
                                name="vehicleTypes"
                                render={({ field }) => (
                                  <FormItem className="flex items-center space-x-2">
                                    <FormControl>
                                      <Checkbox
                                        checked={field.value?.includes(vehicle.type)}
                                        onCheckedChange={(checked) => {
                                          const value = field.value || []
                                          if (checked) {
                                            field.onChange([...value, vehicle.type])
                                          } else {
                                            field.onChange(value.filter((v) => v !== vehicle.type))
                                          }
                                        }}
                                      />
                                    </FormControl>
                                    <FormLabel className="font-normal cursor-pointer">
                                      {vehicle.type}
                                    </FormLabel>
                                  </FormItem>
                                )}
                              />
                            ))}
                          </div>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={filterForm.control}
                      name="minRating"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Minimum Rating</FormLabel>
                          <FormControl>
                            <div className="space-y-2">
                              <Slider
                                min={1}
                                max={5}
                                step={0.1}
                                value={[field.value]}
                                onValueChange={([value]) => field.onChange(value)}
                              />
                              <div className="flex justify-between text-xs text-muted-foreground">
                                <span>1.0</span>
                                <span className="font-medium">{field.value.toFixed(1)}</span>
                                <span>5.0</span>
                              </div>
                            </div>
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={filterForm.control}
                      name="maxResponseTime"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Max Response Time (minutes)</FormLabel>
                          <FormControl>
                            <div className="space-y-2">
                              <Slider
                                min={5}
                                max={60}
                                step={5}
                                value={[field.value]}
                                onValueChange={([value]) => field.onChange(value)}
                              />
                              <div className="flex justify-between text-xs text-muted-foreground">
                                <span>5m</span>
                                <span className="font-medium">{field.value}m</span>
                                <span>60m</span>
                              </div>
                            </div>
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={filterForm.control}
                      name="isActive"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between">
                          <div>
                            <FormLabel>Active Affiliates Only</FormLabel>
                            <FormDescription>
                              Show only currently active affiliates
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => filterForm.reset()}>
                        Reset
                      </Button>
                      <Button onClick={() => setFilterOpen(false)}>
                        Apply Filters
                      </Button>
                    </div>
                  </form>
                </Form>
              </SheetContent>
            </Sheet>
          </div>
        </div>

        {/* City Overview Stats */}
        <div className="grid grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col gap-1">
                <div className="flex items-baseline gap-2">
                  <span className="text-3xl font-semibold">${(cityStats.totalRevenue / 1000).toFixed(1)}k</span>
                  <span className="text-sm text-muted-foreground">revenue</span>
                </div>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <TrendingUp className="h-4 w-4 text-green-500" />
                  <span className="text-green-500">+12%</span>
                  <span>vs last month</span>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col gap-1">
                <div className="flex items-baseline gap-2">
                  <span className="text-3xl font-semibold">{cityStats.activeAffiliates}</span>
                  <span className="text-sm text-muted-foreground">active affiliates</span>
                </div>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <span>{cityStats.totalAffiliates} total registered</span>
                  <span>•</span>
                  <span className="text-green-500">{Math.round((cityStats.activeAffiliates / cityStats.totalAffiliates) * 100)}% active</span>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col gap-1">
                <div className="flex items-baseline gap-2">
                  <span className="text-3xl font-semibold">{cityStats.conversionRate}%</span>
                  <span className="text-sm text-muted-foreground">conversion rate</span>
                </div>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <span>{cityStats.totalTrips} trips</span>
                  <span>•</span>
                  <span>{cityStats.totalQuotes} quotes</span>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col gap-1">
                <div className="flex items-baseline gap-2">
                  <span className="text-3xl font-semibold">{cityStats.avgResponseTime}</span>
                  <span className="text-sm text-muted-foreground">avg response</span>
                </div>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  <span>12 blocked dates this month</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="rates">Rates</TabsTrigger>
          <TabsTrigger value="dynamic-pricing">Dynamic Pricing</TabsTrigger>
          <TabsTrigger value="affiliates">Affiliates</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Rate Management Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-8">
                  <div className="grid grid-cols-2 gap-4">
                    {vehicleRates.map((rate) => (
                      <div key={rate.type} className="p-4 border rounded-lg space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="font-medium">{rate.type.toUpperCase()}</span>
                        </div>
                        <div className="space-y-1">
                          <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">Base Rate:</span>
                            <span className="font-medium">${rate.lowestRate}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">Market Average:</span>
                            <span className="font-medium">${rate.avgRate}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">Premium Rate:</span>
                            <span className="font-medium">${rate.highestRate}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Active Rate Adjustments</h3>
                    <div className="space-y-2">
                      {pricingRules.filter(rule => rule.isActive).map((rule) => (
                        <div key={rule.id} className="flex items-center justify-between p-2 bg-muted rounded-lg">
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">{rule.name}</span>
                          </div>
                          <Badge variant="outline">+{rule.adjustment}%</Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Market Coverage</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-2xl font-bold">{cityStats.activeAffiliates}</div>
                      <p className="text-sm text-muted-foreground">Active Affiliates</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {cityStats.totalAffiliates} total registered • {Math.round((cityStats.activeAffiliates / cityStats.totalAffiliates) * 100)}% active
                      </p>
                    </div>
                    <div>
                      <div className="text-2xl font-bold">{cityStats.avgResponseTime}</div>
                      <p className="text-sm text-muted-foreground">Avg Response Time</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        12 blocked dates this month
                      </p>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium mb-2">Vehicle Types</h3>
                    {vehicleRates.map((rate) => (
                      <div key={rate.type} className="mb-2">
                        <div className="flex justify-between text-sm mb-1">
                          <span>{rate.type.toUpperCase()}</span>
                          <span>${rate.avgRate} avg rate</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="rates">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg font-semibold">Rate Comparison</CardTitle>
                    <p className="text-sm text-muted-foreground">
                      Compare rates across all affiliates by vehicle type
                    </p>
                  </div>
                  <div className="flex items-center gap-4">
                    <Select
                      value={selectedVehicleType}
                      onValueChange={(value: VehicleType) => setSelectedVehicleType(value)}
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select vehicle type">
                          {selectedVehicleType === "all" ? "All Vehicle Types" :
                            selectedVehicleType.toUpperCase()}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        {vehicleRates.map((v) => (
                          <SelectItem key={v.type} value={v.type}>
                            {v.type.toUpperCase()}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Input
                      placeholder="Search affiliates..."
                      className="w-[300px]"
                      value={affiliateSearchQuery}
                      onChange={(e) => setAffiliateSearchQuery(e.target.value)}
                    />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between text-sm text-muted-foreground px-4">
                    <div>Market Range: ${vehicleRates.find(v => v.type === selectedVehicleType)?.lowestRate} -
                      ${vehicleRates.find(v => v.type === selectedVehicleType)?.highestRate}
                    </div>
                    <div>Average Rate: ${vehicleRates.find(v => v.type === selectedVehicleType)?.avgRate}</div>
                  </div>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Affiliate</TableHead>
                        <TableHead>Base Rate</TableHead>
                        <TableHead>Hourly Rate</TableHead>
                        <TableHead>Min Hours</TableHead>
                        <TableHead>Rating</TableHead>
                        <TableHead>Response Time</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {detailedAffiliates
                        .filter(a => a.vehicleTypes.includes(selectedVehicleType))
                        .sort((a, b) => {
                          const rateA = a.rates.find(r => r.vehicleType === selectedVehicleType)?.baseRate || 0;
                          const rateB = b.rates.find(r => r.vehicleType === selectedVehicleType)?.baseRate || 0;
                          return rateA - rateB;
                        })
                        .map((affiliate) => {
                          const rate = affiliate.rates.find(r => r.vehicleType === selectedVehicleType)
                          return (
                            <TableRow key={affiliate.id}>
                              <TableCell>
                                <div className="flex items-center gap-2">
                                  <Building2 className="h-4 w-4 text-muted-foreground" />
                                  <div>
                                    <div className="font-medium">{affiliate.name}</div>
                                    <div className="text-xs text-muted-foreground">
                                      {affiliate.yearInBusiness} years in business
                                    </div>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="font-medium">${rate?.baseRate}</div>
                                <div className="text-xs text-muted-foreground">
                                  {rate?.baseRate && rate?.baseRate > (vehicleRates.find(v => v.type === selectedVehicleType)?.avgRate || 0) ?
                                    '+' + (((rate.baseRate / (vehicleRates.find(v => v.type === selectedVehicleType)?.avgRate || 1)) - 1) * 100).toFixed(1) + '% vs avg' :
                                    ((((vehicleRates.find(v => v.type === selectedVehicleType)?.avgRate || 0) - (rate?.baseRate || 0)) / (vehicleRates.find(v => v.type === selectedVehicleType)?.avgRate || 1)) * 100).toFixed(1) + '% below avg'
                                  }
                                </div>
                              </TableCell>
                              <TableCell>${rate?.hourlyRate}/hr</TableCell>
                              <TableCell>{rate?.minimumHours}hrs</TableCell>
                              <TableCell>
                                <div className="flex items-center gap-1">
                                  <Star className="h-4 w-4 fill-primary" />
                                  <span>{affiliate.rating}</span>
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center gap-1">
                                  <Clock className="h-4 w-4 text-muted-foreground" />
                                  <span>{affiliate.responseTime}</span>
                                </div>
                              </TableCell>
                            </TableRow>
                          )
                        })}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="dynamic-pricing">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg font-semibold">Dynamic Pricing Rules</CardTitle>
                    <p className="text-sm text-muted-foreground">
                      Configure pricing adjustments based on conditions
                    </p>
                  </div>
                  <AlertDialog open={isAddRuleOpen} onOpenChange={setIsAddRuleOpen}>
                    <AlertDialogTrigger asChild>
                      <Button>Add New Rule</Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent className="sm:max-w-[500px]">
                      <AlertDialogHeader>
                        <AlertDialogTitle>Add Dynamic Pricing Rule</AlertDialogTitle>
                        <AlertDialogDescription>
                          Create a new pricing rule to automatically adjust rates based on conditions.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <div className="grid gap-4 py-4">
                        <div className="space-y-2">
                          <Label htmlFor="name">Rule Name</Label>
                          <Input
                            id="name"
                            value={newRule.name}
                            onChange={(e) => setNewRule({ ...newRule, name: e.target.value })}
                            placeholder="e.g., Peak Hours Markup"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="condition">Condition</Label>
                          <Input
                            id="condition"
                            value={newRule.condition}
                            onChange={(e) => setNewRule({ ...newRule, condition: e.target.value })}
                            placeholder="e.g., Time between 7 AM - 10 AM"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="adjustment">Price Adjustment (%)</Label>
                          <Input
                            id="adjustment"
                            type="number"
                            value={newRule.adjustment}
                            onChange={(e) => setNewRule({ ...newRule, adjustment: Number(e.target.value) })}
                          />
                        </div>
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="active"
                            checked={newRule.isActive}
                            onCheckedChange={(checked) => setNewRule({ ...newRule, isActive: checked })}
                          />
                          <Label htmlFor="active">Active</Label>
                        </div>
                      </div>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={() => {
                          setPricingRules([...pricingRules, { ...newRule, id: String(pricingRules.length + 1) }])
                          setIsAddRuleOpen(false)
                        }}>
                          Add Rule
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {pricingRules.map((rule) => (
                    <div
                      key={rule.id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="space-y-1">
                        <div className="font-medium">{rule.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {rule.condition}
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <div className="font-medium text-green-600">+{rule.adjustment}%</div>
                          <div className="text-xs text-muted-foreground">Price Adjustment</div>
                        </div>
                        <Switch checked={rule.isActive} />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-semibold">Special Event Rates</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Input
                      placeholder="Search events..."
                      className="w-[300px]"
                    />
                    <Button variant="outline">
                      <Calendar className="h-4 w-4 mr-2" />
                      Add Event
                    </Button>
                  </div>
                  <div className="space-y-4">
                    {specialEvents.map((event) => (
                      <div
                        key={event.id}
                        className="flex items-start justify-between p-4 border rounded-lg"
                      >
                        <div className="space-y-1">
                          <div className="font-medium">{event.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {format(event.startDate, "MMM d, yyyy")} - {format(event.endDate, "MMM d, yyyy")}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            Location: {event.location}
                          </div>
                          <div className="flex gap-1 mt-2">
                            {event.vehicleTypes.map((type) => (
                              <Badge key={type} variant="outline">{type.toUpperCase()}</Badge>
                            ))}
                          </div>
                        </div>
                        <div className="text-right space-y-1">
                          <div className="font-medium text-green-600">+{event.adjustment}%</div>
                          <Badge variant={
                            event.expectedDemand === "High" ? "destructive" :
                              event.expectedDemand === "Medium" ? "default" : "secondary"
                          }>
                            {event.expectedDemand} Demand
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="affiliates">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg font-semibold">City Affiliates</CardTitle>
                    <p className="text-sm text-muted-foreground">
                      {cityStats.activeAffiliates} active affiliates in {cities.find((city) => city.value === selectedCity)?.label}
                    </p>
                  </div>
                  <div className="flex items-center gap-4">
                    <Select
                      value={selectedVehicleType}
                      onValueChange={(value: VehicleType) => setSelectedVehicleType(value)}
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select vehicle type">
                          {selectedVehicleType === "all" ? "All Vehicle Types" :
                            vehicleRates.find(v => v.type === selectedVehicleType)?.type.toUpperCase()}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Vehicle Types</SelectItem>
                        {vehicleRates.map((v) => (
                          <SelectItem key={v.type} value={v.type}>
                            {v.type.toUpperCase()}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Input
                      placeholder="Search affiliates..."
                      className="w-[300px]"
                      value={affiliateSearchQuery}
                      onChange={(e) => setAffiliateSearchQuery(e.target.value)}
                    />
                    <Sheet>
                      <SheetTrigger asChild>
                        <Button variant="outline">
                          <Filter className="h-4 w-4 mr-2" />
                          Comparison Metrics
                        </Button>
                      </SheetTrigger>
                      <SheetContent>
                        <SheetHeader>
                          <SheetTitle>Select Comparison Metrics</SheetTitle>
                          <SheetDescription>
                            Choose which metrics to display in the comparison table
                          </SheetDescription>
                        </SheetHeader>
                        <div className="grid gap-4 py-4">
                          <div className="space-y-4">
                            {[
                              { id: "rates", label: "Rates & Pricing" },
                              { id: "response", label: "Response Metrics" },
                              { id: "performance", label: "Performance Stats" },
                              { id: "coverage", label: "Fleet Size" },
                              { id: "financial", label: "Financial Data" }
                            ].map((metric) => (
                              <div key={metric.id} className="flex items-center space-x-2">
                                <Checkbox
                                  id={metric.id}
                                  checked={selectedMetrics.includes(metric.id)}
                                  onCheckedChange={(checked) => {
                                    if (checked) {
                                      setSelectedMetrics([...selectedMetrics, metric.id])
                                    } else {
                                      setSelectedMetrics(selectedMetrics.filter(m => m !== metric.id))
                                    }
                                  }}
                                />
                                <label
                                  htmlFor={metric.id}
                                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                >
                                  {metric.label}
                                </label>
                              </div>
                            ))}
                          </div>
                        </div>
                      </SheetContent>
                    </Sheet>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Affiliate</TableHead>
                      {selectedVehicleType && (
                        <>
                          <TableHead>Base Rate</TableHead>
                          <TableHead>Hourly Rate</TableHead>
                          <TableHead>Min Hours</TableHead>
                          <TableHead>Specialty Services</TableHead>
                        </>
                      )}
                      {selectedMetrics.includes("response") && (
                        <>
                          <TableHead>Response Time</TableHead>
                          <TableHead>Pickup Time</TableHead>
                        </>
                      )}
                      {selectedMetrics.includes("performance") && (
                        <>
                          <TableHead>Rating</TableHead>
                          <TableHead>On-Time Rate</TableHead>
                          <TableHead>Cancellation Rate</TableHead>
                        </>
                      )}
                      {selectedMetrics.includes("coverage") && (
                        <>
                          <TableHead>Fleet Size</TableHead>
                        </>
                      )}
                      {selectedMetrics.includes("financial") && (
                        <>
                          <TableHead>Revenue</TableHead>
                          <TableHead>Conversion Rate</TableHead>
                        </>
                      )}
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {detailedAffiliates
                      .filter(affiliate =>
                        (selectedVehicleType === "all" || affiliate.vehicleTypes.map(t => t.toLowerCase()).includes(selectedVehicleType)) &&
                        (!affiliateSearchQuery || affiliate.name.toLowerCase().includes(affiliateSearchQuery.toLowerCase()))
                      )
                      .map((affiliate) => (
                        <TableRow key={affiliate.id}>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Building2 className="h-4 w-4 text-muted-foreground" />
                              <div>
                                <div className="font-medium">{affiliate.name}</div>
                                <div className="text-xs text-muted-foreground">
                                  {affiliate.yearInBusiness} years in business
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          {selectedVehicleType && (() => {
                            const rate = affiliate.rates.find(r => r.vehicleType === selectedVehicleType)
                            return (
                              <>
                                <TableCell>${rate?.baseRate}</TableCell>
                                <TableCell>${rate?.hourlyRate}/hr</TableCell>
                                <TableCell>{rate?.minimumHours}hrs</TableCell>
                                <TableCell>
                                  <div className="flex gap-1">
                                    {rate?.specialtyServices.map((service) => (
                                      <Badge key={service} variant="outline">{service}</Badge>
                                    ))}
                                  </div>
                                </TableCell>
                              </>
                            )
                          })()}
                          {selectedMetrics.includes("response") && (
                            <>
                              <TableCell>
                                <div className="flex items-center gap-1">
                                  <Clock className="h-4 w-4 text-muted-foreground" />
                                  <span>{affiliate.responseTime}</span>
                                </div>
                              </TableCell>
                              <TableCell>{affiliate.averagePickupTime}</TableCell>
                            </>
                          )}
                          {selectedMetrics.includes("performance") && (
                            <>
                              <TableCell>
                                <div className="flex items-center gap-1">
                                  <Star className="h-4 w-4 fill-primary" />
                                  <span>{affiliate.rating}</span>
                                </div>
                              </TableCell>
                              <TableCell>{affiliate.onTimeRate}%</TableCell>
                              <TableCell>{affiliate.cancellationRate}%</TableCell>
                            </>
                          )}
                          {selectedMetrics.includes("coverage") && (
                            <>
                              <TableCell>{affiliate.fleetSize} vehicles</TableCell>
                            </>
                          )}
                          {selectedMetrics.includes("financial") && (
                            <>
                              <TableCell>${(affiliate.revenue / 1000).toFixed(1)}k</TableCell>
                              <TableCell>{affiliate.conversionRate}%</TableCell>
                            </>
                          )}
                          <TableCell>
                            <Badge variant="outline" className="bg-green-50">Active</Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Revenue Overview</CardTitle>
              </CardHeader>
              <CardContent className="pl-2">
                <ResponsiveContainer width="100%" height={350}>
                  <LineChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Legend />
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="revenue"
                      stroke="#8884d8"
                      name="Revenue ($)"
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="trips"
                      stroke="#82ca9d"
                      name="Trips"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Vehicle Demand</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={350}>
                  <BarChart data={vehicleDemandData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="type"
                      tickFormatter={(value) => value.toUpperCase()}
                    />
                    <YAxis />
                    <Tooltip
                      labelFormatter={(value) => value.toUpperCase()}
                    />
                    <Legend />
                    <Bar dataKey="demand" fill="#8884d8" name="Demand %" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Quote Conversion Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={350}>
                  <BarChart data={conversionData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="quotes" fill="#8884d8" name="Total Quotes" />
                    <Bar dataKey="conversions" fill="#82ca9d" name="Conversions" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Response Time Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={350}>
                  <BarChart data={responseTimeData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill="#8884d8" name="Number of Responses">
                      {responseTimeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
} 