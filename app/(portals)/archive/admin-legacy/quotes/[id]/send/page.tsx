"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card } from "@/app/components/ui/card"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/app/components/ui/form"
import { Input } from "@/app/components/ui/input"
import { Textarea } from "@/app/components/ui/textarea"
import { useQuery, useMutation } from "@tanstack/react-query"
import { toast } from "sonner"
import { PostgrestError } from "@supabase/supabase-js"

const formSchema = z.object({
  baseRate: z.string().min(1, {
    message: "Base rate is required.",
  }),
  hourlyRate: z.string().min(1, {
    message: "Hourly rate is required.",
  }),
  minimumHours: z.string().min(1, {
    message: "Minimum hours is required.",
  }),
  additionalFees: z.array(z.object({
    description: z.string(),
    amount: z.string(),
  })).optional(),
  notes: z.string().optional(),
  validUntil: z.string().min(1, {
    message: "Quote validity period is required.",
  }),
})

type FormValues = z.infer<typeof formSchema>

// Mock function - replace with actual API call
const getQuoteDetails = async (id: string) => {
  return {
    id,
    eventName: "Annual Tech Conference 2024",
    transportationDetails: {
      type: "airport_transfer",
      passengerCount: 50,
      pickupLocation: "Airport Terminal 1",
      dropoffLocation: "Convention Center",
      date: new Date("2024-06-15"),
    },
  }
}

export default function SendQuotePage() {
  const params = useParams()
  const router = useRouter()
  const quoteId = params.id as string

  const { data: quote, isLoading: isLoadingQuote } = useQuery({
    queryKey: ["quote", quoteId],
    queryFn: () => getQuoteDetails(quoteId),
  })

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      baseRate: "",
      hourlyRate: "",
      minimumHours: "4",
      additionalFees: [],
      notes: "",
      validUntil: "",
    },
  })

  const { mutate: sendQuote, isLoading } = useMutation({
    mutationFn: async (values: FormValues) => {
      // TODO: Implement quote sending
      console.log("Sending quote:", values)
      return new Promise((resolve) => setTimeout(resolve, 1000))
    },
    onSuccess: () => {
      toast.success("Quote sent successfully")
      router.push("/admin/quotes")
    },
    onError: (error: PostgrestError) => {
      console.error("Error sending quote:", error)
      toast.error(error.message || "Failed to send quote")
    },
  })

  if (isLoadingQuote) {
    return (
      <Card className="p-6">
        <div className="text-center text-muted-foreground">Loading quote details...</div>
      </Card>
    )
  }

  if (!quote) {
    return (
      <Card className="p-6">
        <div className="text-center text-muted-foreground">Quote not found.</div>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-x-4">
          <Link href="/admin/quotes">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h2 className="text-2xl font-bold tracking-tight">Send Quote</h2>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Request Details</h3>
          <div className="space-y-4">
            <div>
              <div className="text-sm font-medium">Event</div>
              <div className="text-sm text-muted-foreground">{quote.eventName}</div>
            </div>
            <div>
              <div className="text-sm font-medium">Service Type</div>
              <div className="text-sm text-muted-foreground">
                {quote.transportationDetails.type.replace(/_/g, " ")}
              </div>
            </div>
            <div>
              <div className="text-sm font-medium">Passengers</div>
              <div className="text-sm text-muted-foreground">
                {quote.transportationDetails.passengerCount} passengers
              </div>
            </div>
            <div>
              <div className="text-sm font-medium">Pickup</div>
              <div className="text-sm text-muted-foreground">
                {quote.transportationDetails.pickupLocation}
              </div>
            </div>
            <div>
              <div className="text-sm font-medium">Dropoff</div>
              <div className="text-sm text-muted-foreground">
                {quote.transportationDetails.dropoffLocation}
              </div>
            </div>
            <div>
              <div className="text-sm font-medium">Date</div>
              <div className="text-sm text-muted-foreground">
                {quote.transportationDetails.date.toLocaleDateString()}
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit((values) => sendQuote(values))} className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="baseRate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Base Rate</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Enter base rate" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="hourlyRate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hourly Rate</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Enter hourly rate" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="minimumHours"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Minimum Hours</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="validUntil"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Valid Until</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter any additional notes or terms"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Any special terms, conditions, or notes for this quote.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push("/admin/quotes")}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Sending..." : "Send Quote"}
                </Button>
              </div>
            </form>
          </Form>
        </Card>
      </div>
    </div>
  )
} 