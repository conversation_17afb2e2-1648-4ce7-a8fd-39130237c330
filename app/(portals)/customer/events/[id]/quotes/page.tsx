'use client'

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card } from "@/app/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/app/components/ui/tabs"
import { Badge } from "@/app/components/ui/badge"
import { Input } from "@/app/components/ui/input"
import { QuoteList } from "@/app/components/features/quotes/quote-list"
import { Calendar, Clock, Download, Filter, Plus, Search, Users, Car, MapPin, Building2, ArrowRight, AlertTriangle } from "lucide-react"
import Link from "next/link"
import { useParams } from "next/navigation"

interface ServiceDependency {
  id: string;
  sourceService: string;
  targetService: string;
  type: 'must_start_after' | 'must_end_before' | 'must_overlap';
  description: string;
}

interface SchedulingConflict {
  id: string;
  services: string[];
  type: 'time_overlap' | 'location_conflict' | 'resource_conflict';
  severity: 'high' | 'medium' | 'low';
  description: string;
}

export default function EventQuotesPage() {
  const params = useParams()
  const eventId = params.id as string

  // Example event data
  const eventData = {
    id: eventId,
    name: "Tech Conference 2024",
    date: new Date("2024-06-15"),
    location: "San Francisco Convention Center",
    totalBudget: 25000,
    allocatedBudget: 15000,
    remainingBudget: 10000,
    requirements: {
      shuttles: 3,
      vip: 2,
      standard: 5
    }
  }

  // Example dependencies and conflicts
  const serviceDependencies: ServiceDependency[] = [
    {
      id: '1',
      sourceService: 'Airport Shuttle',
      targetService: 'Hotel Check-in',
      type: 'must_end_before',
      description: 'Airport shuttle must arrive before hotel check-in transportation'
    },
    {
      id: '2',
      sourceService: 'Conference Shuttle',
      targetService: 'VIP Transport',
      type: 'must_overlap',
      description: 'Conference shuttle service must run during VIP transport hours'
    }
  ]

  const schedulingConflicts: SchedulingConflict[] = [
    {
      id: '1',
      services: ['VIP Transport', 'Executive Shuttle'],
      type: 'time_overlap',
      severity: 'high',
      description: 'Services scheduled for the same time with shared resources'
    },
    {
      id: '2',
      services: ['Airport Shuttle', 'Hotel Shuttle'],
      type: 'location_conflict',
      severity: 'medium',
      description: 'Pickup locations too far apart for efficient service'
    }
  ]

  return (
    <div className="container p-6 space-y-6">
      {/* Event Context Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold">{eventData.name} - Transportation Quotes</h1>
          <p className="text-sm text-muted-foreground">Manage and track transportation quotes for this event</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Quotes
          </Button>
          <Link href={`/customer/events/${eventId}/quotes/new`}>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Create Quote
            </Button>
          </Link>
        </div>
      </div>

      {/* Event Overview Cards */}
      <div className="grid grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Calendar className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Event Date</div>
              <div className="font-medium">{eventData.date.toLocaleDateString()}</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-green-100 rounded-lg">
              <Building2 className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Location</div>
              <div className="font-medium truncate">{eventData.location}</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Car className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Vehicle Requirements</div>
              <div className="font-medium">
                {eventData.requirements.shuttles + eventData.requirements.vip + eventData.requirements.standard} total
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Clock className="h-5 w-5 text-yellow-600" />
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Transport Budget</div>
              <div className="font-medium">${eventData.allocatedBudget.toLocaleString()}</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Requirements Summary */}
      <Card className="p-4">
        <h3 className="text-lg font-semibold mb-4">Transportation Requirements</h3>
        <div className="grid grid-cols-3 gap-4">
          <div className="space-y-2">
            <div className="text-sm font-medium">Shuttle Services</div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Required</span>
              <Badge variant="outline">{eventData.requirements.shuttles}</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Quoted</span>
              <Badge variant="outline">2</Badge>
            </div>
          </div>

          <div className="space-y-2">
            <div className="text-sm font-medium">VIP Transportation</div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Required</span>
              <Badge variant="outline">{eventData.requirements.vip}</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Quoted</span>
              <Badge variant="outline">1</Badge>
            </div>
          </div>

          <div className="space-y-2">
            <div className="text-sm font-medium">Standard Services</div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Required</span>
              <Badge variant="outline">{eventData.requirements.standard}</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Quoted</span>
              <Badge variant="outline">3</Badge>
            </div>
          </div>
        </div>
      </Card>

      {/* Service Dependencies */}
      <Card className="p-4">
        <h3 className="text-lg font-semibold mb-4">Service Dependencies</h3>
        <div className="space-y-4">
          {serviceDependencies.map((dep) => (
            <div key={dep.id} className="flex items-start gap-4 p-3 bg-gray-50 rounded-lg">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Clock className="h-5 w-5 text-blue-600" />
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium">{dep.sourceService}</span>
                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{dep.targetService}</span>
                </div>
                <div className="text-sm text-muted-foreground mt-1">
                  {dep.description}
                </div>
              </div>
              <Badge variant="outline" className="capitalize">
                {dep.type.replace(/_/g, ' ')}
              </Badge>
            </div>
          ))}
        </div>
      </Card>

      {/* Scheduling Conflicts */}
      <Card className="p-4">
        <h3 className="text-lg font-semibold mb-4">Scheduling Conflicts</h3>
        <div className="space-y-4">
          {schedulingConflicts.map((conflict) => (
            <div key={conflict.id} className="flex items-start gap-4 p-3 bg-gray-50 rounded-lg border-l-4 border-red-500">
              <div className="p-2 bg-red-100 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-red-600" />
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium">
                    {conflict.services.join(' & ')}
                  </span>
                  <Badge
                    variant="outline"
                    className={
                      conflict.severity === 'high' ? 'border-red-200 text-red-600' :
                        conflict.severity === 'medium' ? 'border-yellow-200 text-yellow-600' :
                          'border-blue-200 text-blue-600'
                    }
                  >
                    {conflict.severity} priority
                  </Badge>
                </div>
                <div className="text-sm text-muted-foreground mt-1">
                  {conflict.description}
                </div>
                <div className="flex items-center gap-2 mt-2">
                  <Badge variant="outline" className="capitalize">
                    {conflict.type.replace(/_/g, ' ')}
                  </Badge>
                  <Button variant="ghost" size="sm" className="h-7">
                    Resolve Conflict
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Quotes Section */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <Tabs defaultValue="all" className="w-full">
            <div className="flex items-center justify-between mb-4">
              <TabsList>
                <TabsTrigger value="all">All Quotes</TabsTrigger>
                <TabsTrigger value="shuttle">Shuttle</TabsTrigger>
                <TabsTrigger value="airport">Airport</TabsTrigger>
                <TabsTrigger value="other">Other</TabsTrigger>
              </TabsList>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search quotes..."
                    className="pl-9 w-[250px]"
                  />
                </div>
                <Button variant="outline" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <TabsContent value="all" className="mt-0">
              <QuoteList />
            </TabsContent>
            <TabsContent value="shuttle" className="mt-0">
              <QuoteList />
            </TabsContent>
            <TabsContent value="airport" className="mt-0">
              <QuoteList />
            </TabsContent>
            <TabsContent value="other" className="mt-0">
              <QuoteList />
            </TabsContent>
          </Tabs>
        </div>
      </Card>
    </div>
  )
} 