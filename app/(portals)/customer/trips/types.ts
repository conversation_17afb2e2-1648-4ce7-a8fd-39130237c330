export interface Trip {
  id: string
  eventName: string
  date: string
  pickupLocation: string
  dropoffLocation: string
  passengerCount: number
  status: "scheduled" | "in_progress" | "completed" | "cancelled"
  startTime: string
  endTime: string
  driver?: {
    name: string
    phone: string
    rating: number
    location?: {
      lat: number
      lng: number
    }
  }
  weather?: string
  trafficAlert?: string
  tripType: "airport_arrival" | "airport_departure" | "transfer" | "vip"
  priority: "normal" | "high" | "vip"
  eventId: string
  companyId: string
  vehicle?: string
  passengers: {
    name: string
    phone?: string
    email?: string
    isVIP?: boolean
  }[]
  notes?: string
  specialRequirements?: string[]
  estimatedDuration: number
  realTimeDuration?: number
  currentLocation?: {
    lat: number
    lng: number
  }
  lastUpdate: string
  alerts?: {
    type: "info" | "warning" | "error"
    message: string
    timestamp: string
  }[]
}

export interface TripFilters {
  tripType: string[]
  priority: string[]
  status: string[]
  dateRange: {
    start: string
    end: string
  }
  events: string[]
  companies: string[]
}

export interface TripStats {
  totalTrips: number
  activeTrips: number
  completedTrips: number
  onTimeRate: number
  averageRating: number
  alerts: number
} 