"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Spinner } from '@/app/components/ui/spinner'
import { Head<PERSON> } from "@/app/components/layout/header"
import { useAuth } from '@/lib/auth/context'
import { UserRole } from '@/src/types/roles'
import { Button } from '@/app/components/ui/button'

// Define debug info type
interface DebugInfo {
  timestamp: string;
  authState: { user: any; loading: boolean; roles: UserRole[] };
  location: string;
  error?: string;
  refreshAttempts: number;
  [key: string]: any;
}

export default function CustomerLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, loading, refreshAuth } = useAuth()
  const router = useRouter()
  const [debugInfo, setDebugInfo] = useState<DebugInfo>({
    timestamp: new Date().toISOString(),
    authState: { user: null, loading: true, roles: [] },
    location: typeof window !== 'undefined' ? window.location.href : '',
    refreshAttempts: 0
  })
  const [refreshAttempts, setRefreshAttempts] = useState(0)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const MAX_REFRESH_ATTEMPTS = 3

  const hasRequiredRole = (userRoles: UserRole[] = []) => {
    return userRoles.some(role => ['CUSTOMER', 'EVENT_MANAGER'].includes(role));
  }

  useEffect(() => {
    console.log('CustomerLayout - Component mounted');
    let mounted = true;
    let refreshTimeout: NodeJS.Timeout;

    const updateDebugInfo = (info: Partial<DebugInfo>) => {
      if (mounted) {
        setDebugInfo(prev => ({
          ...prev,
          ...info,
          timestamp: new Date().toISOString()
        }));
      }
    };

    const attemptSessionRefresh = async () => {
      if (!mounted || isRefreshing) {
        console.log('CustomerLayout - Skipping refresh, already refreshing or unmounted');
        return;
      }
      
      // Check if direct login is in progress
      const directLoginInProgress = localStorage.getItem('direct_login_in_progress');
      if (directLoginInProgress === 'true') {
        console.log('CustomerLayout - Skipping refresh, direct login in progress');
        return;
      }
      
      // Skip if we're already on the refresh endpoint
      if (typeof window !== 'undefined' && window.location.pathname.includes('/api/auth/refresh')) {
        console.log('CustomerLayout - Skipping refresh, already on refresh endpoint');
        return;
      }
      
      // Check if we've refreshed recently (within the last 10 seconds)
      const lastRefreshStr = localStorage.getItem('last_layout_refresh_time');
      if (lastRefreshStr) {
        const lastRefreshTime = parseInt(lastRefreshStr, 10);
        const now = Date.now();
        const timeSinceLastRefresh = now - lastRefreshTime;
        
        console.log(`CustomerLayout - Last refresh was ${timeSinceLastRefresh}ms ago`);
        
        if (timeSinceLastRefresh < 10000) { // 10 seconds
          console.log(`CustomerLayout - Skipping refresh, last refresh was ${timeSinceLastRefresh}ms ago`);
          return;
        }
      } else {
        console.log('CustomerLayout - No last refresh time found in localStorage');
      }
      
      setIsRefreshing(true);
      console.log(`CustomerLayout - Attempting session refresh (attempt ${refreshAttempts + 1})`);
      updateDebugInfo({ refreshAttempts: refreshAttempts + 1 });
      
      try {
        // Use the auth provider's refreshAuth function
        await refreshAuth();
        // Store the refresh time
        const now = Date.now();
        localStorage.setItem('last_layout_refresh_time', now.toString());
        console.log(`CustomerLayout - Stored refresh time: ${now}`);
      } catch (err) {
        console.error('CustomerLayout - Error refreshing auth:', err);
        updateDebugInfo({ error: err instanceof Error ? err.message : String(err) });
      } finally {
        // Increment the refresh attempts counter
        if (mounted) {
          setRefreshAttempts(prev => prev + 1);
          setIsRefreshing(false);
        }
      }
    };

    const handleAuthState = async () => {
      if (!mounted) return;

      updateDebugInfo({ authState: { user, loading, roles: user?.roles || [] } });
      console.log('CustomerLayout - Auth state:', { user, loading, roles: user?.roles });

      // If still loading, wait
      if (loading) {
        console.log('CustomerLayout - Still loading auth state');
        return;
      }

      // If no user and we haven't maxed out refresh attempts, try refreshing
      if (!user && refreshAttempts < MAX_REFRESH_ATTEMPTS && !isRefreshing) {
        console.log('CustomerLayout - No user, attempting refresh');
        refreshTimeout = setTimeout(attemptSessionRefresh, 1000); // Wait 1 second between attempts
        return;
      }

      // If no user and we've maxed out refresh attempts, redirect to login
      if (!user && refreshAttempts >= MAX_REFRESH_ATTEMPTS) {
        console.log('CustomerLayout - Max refresh attempts reached, redirecting to login');
        // Don't redirect automatically - let the user choose what to do
        return;
      }

      // If user exists but doesn't have required role, redirect to login
      if (user && !hasRequiredRole(user.roles)) {
        console.log('CustomerLayout - User does not have required role:', user.roles);
        // Don't redirect automatically - let the user choose what to do
        return;
      }

      // If we get here with a valid user, they're authenticated and authorized
      if (user && hasRequiredRole(user.roles)) {
        console.log('CustomerLayout - User authenticated and authorized');
      }
    };

    handleAuthState();

    return () => {
      mounted = false;
      if (refreshTimeout) {
        clearTimeout(refreshTimeout);
      }
    };
  }, [user, loading, refreshAttempts, router, refreshAuth, isRefreshing]);

  // Show loading state while authentication is in progress
  if (loading || (isRefreshing && refreshAttempts < MAX_REFRESH_ATTEMPTS)) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <Spinner size="lg" />
          <p className="mt-4">Loading user data...</p>
          {refreshAttempts > 0 && (
            <p className="text-sm text-gray-500">
              Refresh attempt {refreshAttempts} of {MAX_REFRESH_ATTEMPTS}
            </p>
          )}
        </div>
      </div>
    );
  }

  // If no user after max refresh attempts, show redirect message with debug options
  if (!user) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <p className="text-xl font-bold mb-4">Unable to authenticate</p>
          <p className="mb-6">You need to be logged in to access this page.</p>
          
          <div className="flex flex-col gap-2 items-center mb-6">
            <Button onClick={() => router.push('/login')}>
              Go to Login Page
            </Button>
            <Button variant="outline" onClick={() => router.push('/debug')}>
              Go to Debug Page
            </Button>
            <Button variant="outline" onClick={() => router.push('/direct-login')}>
              Try Direct Login
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // If user exists but doesn't have required role, show unauthorized message
  if (!hasRequiredRole(user.roles)) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <p className="text-xl font-bold mb-4">Unauthorized</p>
          <p className="mb-6">You do not have permission to access this page.</p>
          
          <div className="flex flex-col gap-2 items-center mb-6">
            <Button onClick={() => router.push('/login')}>
              Go to Login Page
            </Button>
            <Button variant="outline" onClick={() => router.push('/debug')}>
              Go to Debug Page
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // If we already have user data, render immediately
  if (!loading && user) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header user={{
          name: user.email || '',
          email: user.email || '',
          roles: user.roles,
          image: user.user_metadata?.avatar_url
        }} />
        <div className="container flex-1 py-6">
          <main className="flex w-full flex-1 flex-col overflow-hidden">
            {children}
          </main>
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header user={{
        name: user.email || '',
        email: user.email || '',
        roles: user.roles,
        image: user.user_metadata?.avatar_url
      }} />
      <div className="container flex-1 py-6">
        <main className="flex w-full flex-1 flex-col overflow-hidden">
          {children}
        </main>
      </div>
    </div>
  )
}