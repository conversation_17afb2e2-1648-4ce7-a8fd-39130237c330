"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/app/components/ui/card"
import { ArrowLeft, Pencil, Send, MessageSquare, Calendar, Clock, MapPin, Users, Briefcase, Car, DollarSign } from "lucide-react"
import Link from "next/link"
import { Badge } from "@/app/components/ui/badge"
import { Textarea } from "@/app/components/ui/textarea"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/app/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/app/components/ui/avatar"
import { format } from "date-fns"
import { useToast } from "@/app/components/ui/use-toast"
import { useRouter } from "next/navigation"
import { useQuotes, type Quote, type QuoteTimeline, type QuoteCommunication } from "@/lib/hooks/use-quotes"
import { Separator } from "@/app/components/ui/separator"
import { useAuth } from '@/lib/auth/context'
import { UserRole } from '@/src/types/roles'

const statusColors: Record<string, string> = {
  pending_quote: "bg-yellow-500",
  quote_ready: "bg-blue-500",
  changes_requested: "bg-purple-500",
  accepted: "bg-green-500",
  rejected: "bg-red-500",
  cancelled: "bg-gray-500",
}

export default function QuoteDetailPage({ params }: { params: { id: string } }) {
  const { toast } = useToast()
  const router = useRouter()
  const { user } = useAuth()
  const { getQuote, updateQuote, cancelQuote, getQuoteTimeline, getQuoteCommunications, sendQuoteMessage } = useQuotes()
  
  const [quote, setQuote] = useState<Quote | null>(null)
  const [timeline, setTimeline] = useState<QuoteTimeline[]>([])
  const [communications, setCommunications] = useState<QuoteCommunication[]>([])
  const [loading, setLoading] = useState(true)
  const [messageText, setMessageText] = useState("")
  const [sendingMessage, setSendingMessage] = useState(false)
  
  useEffect(() => {
    if (!user) return
    
    const loadQuoteData = async () => {
      try {
        setLoading(true)
        const quoteData = await getQuote(params.id)
        setQuote(quoteData)
        
        // Load timeline and communications
        const timelineData = await getQuoteTimeline(params.id)
        setTimeline(timelineData)
        
        const communicationsData = await getQuoteCommunications(params.id)
        setCommunications(communicationsData)
      } catch (error) {
        console.error('Error loading quote data:', error)
        toast({
          title: 'Error',
          description: 'Failed to load quote data',
          variant: 'destructive'
        })
      } finally {
        setLoading(false)
      }
    }
    
    loadQuoteData()
  }, [params.id, user])
  
  const handleSendMessage = async () => {
    if (!messageText.trim()) return
    
    try {
      setSendingMessage(true)
      const newMessage = await sendQuoteMessage(params.id, messageText)
      setCommunications(prev => [...prev, newMessage])
      setMessageText("")
      
      toast({
        title: 'Message Sent',
        description: 'Your message has been sent successfully'
      })
    } catch (error) {
      console.error('Error sending message:', error)
      toast({
        title: 'Error',
        description: 'Failed to send message',
        variant: 'destructive'
      })
    } finally {
      setSendingMessage(false)
    }
  }
  
  const handleCancelQuote = async () => {
    if (!confirm('Are you sure you want to cancel this quote?')) return
    
    try {
      await cancelQuote(params.id)
      setQuote(prev => prev ? { ...prev, status: 'cancelled' } : null)
      
      toast({
        title: 'Quote Cancelled',
        description: 'Your quote has been cancelled successfully'
      })
    } catch (error) {
      console.error('Error cancelling quote:', error)
      toast({
        title: 'Error',
        description: 'Failed to cancel quote',
        variant: 'destructive'
      })
    }
  }
  
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!quote) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-10">
            <h2 className="text-xl font-semibold mb-2">Quote Not Found</h2>
            <p className="text-muted-foreground mb-4">The quote you're looking for doesn't exist or you don't have permission to view it.</p>
            <Button asChild>
              <Link href="/customer/quotes">Back to Quotes</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" asChild>
            <Link href="/customer/quotes">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-2xl font-bold">Quote #{quote.reference_number}</h1>
        </div>
        <div className="flex items-center gap-2">
          {quote.status !== 'cancelled' && (
            <>
              <Button variant="outline" asChild>
                <Link href={`/customer/quotes/${params.id}/edit`}>
                  <Pencil className="h-4 w-4 mr-2" />
                  Edit
        </Link>
              </Button>
              <Button variant="destructive" onClick={handleCancelQuote}>
                Cancel Quote
              </Button>
            </>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Tabs defaultValue="details">
            <TabsList className="grid grid-cols-3 mb-4">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="timeline">Timeline</TabsTrigger>
              <TabsTrigger value="messages">Messages</TabsTrigger>
            </TabsList>
            
            <TabsContent value="details" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Quote Details</CardTitle>
                  <CardDescription>Information about your transportation request</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">Service Type</p>
                      <p className="font-medium flex items-center">
                        <Car className="h-4 w-4 mr-2 text-muted-foreground" />
                        {quote.service_type.charAt(0).toUpperCase() + quote.service_type.slice(1)}
                      </p>
                    </div>
                    
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">Vehicle Type</p>
                      <p className="font-medium flex items-center">
                        <Car className="h-4 w-4 mr-2 text-muted-foreground" />
                        {quote.vehicle_type.charAt(0).toUpperCase() + quote.vehicle_type.slice(1)}
                      </p>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Pickup Location</p>
                    <p className="font-medium flex items-center">
                      <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                      {quote.pickup_location}
                    </p>
                  </div>
                  
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Dropoff Location</p>
                    <p className="font-medium flex items-center">
                      <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                      {quote.dropoff_location}
                    </p>
                  </div>
                  
                  <Separator />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">Pickup Date</p>
                      <p className="font-medium flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                        {format(new Date(quote.pickup_date), 'PPP')}
                      </p>
                    </div>
                    
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">Pickup Time</p>
                      <p className="font-medium flex items-center">
                        <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                        {quote.pickup_time}
                      </p>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">Passengers</p>
                      <p className="font-medium flex items-center">
                        <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                        {quote.passenger_count}
                      </p>
                    </div>
                    
                    <div className="space-y-1">
                      <p className="text-sm text-muted-foreground">Luggage</p>
                      <p className="font-medium flex items-center">
                        <Briefcase className="h-4 w-4 mr-2 text-muted-foreground" />
                        {quote.luggage_count}
                      </p>
                    </div>
                  </div>
                  
                  {quote.special_requests && (
                    <>
                      <Separator />
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Special Requests</p>
                        <p className="font-medium">{quote.special_requests}</p>
                      </div>
                    </>
                  )}
                  
                  {quote.notes && (
                    <>
                      <Separator />
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Additional Notes</p>
                        <p className="font-medium">{quote.notes}</p>
                      </div>
                    </>
                  )}
                  
                  {quote.total_amount && (
                    <>
                      <Separator />
                      <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">Total Amount</p>
                        <p className="font-medium text-xl flex items-center">
                          <DollarSign className="h-4 w-4 mr-1 text-muted-foreground" />
                          {quote.total_amount.toFixed(2)}
                        </p>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="timeline" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Quote Timeline</CardTitle>
                  <CardDescription>History of your quote</CardDescription>
                </CardHeader>
                <CardContent>
                  {timeline.length === 0 ? (
                    <p className="text-center py-6 text-muted-foreground">No timeline events yet</p>
                  ) : (
                    <div className="space-y-4">
                      {timeline.map((event) => (
                        <div key={event.id} className="flex gap-4">
                          <div className="mt-1">
                            <div className={`w-3 h-3 rounded-full ${statusColors[event.action] || 'bg-gray-400'}`}></div>
                          </div>
                          <div className="flex-1 space-y-1">
                            <div className="flex items-center justify-between">
                              <p className="font-medium">{event.action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>
                              <p className="text-sm text-muted-foreground">
                                {format(new Date(event.created_at), 'PPp')}
                              </p>
                            </div>
                            {event.details && <p className="text-sm text-muted-foreground">{event.details}</p>}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="messages" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Messages</CardTitle>
                  <CardDescription>Communication about your quote</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4 max-h-[400px] overflow-y-auto mb-4">
                    {communications.length === 0 ? (
                      <p className="text-center py-6 text-muted-foreground">No messages yet</p>
                    ) : (
                      communications.map((message) => (
                        <div 
                          key={message.id} 
                          className={`flex gap-3 ${message.sender_id === user?.id ? 'justify-end' : ''}`}
                        >
                          {message.sender_id !== user?.id && (
                            <Avatar className="h-8 w-8">
                              <AvatarImage src="/placeholder-avatar.jpg" />
                              <AvatarFallback>CS</AvatarFallback>
                            </Avatar>
                          )}
                          <div className={`max-w-[80%] ${message.sender_id === user?.id ? 'bg-primary text-primary-foreground' : 'bg-muted'} p-3 rounded-lg`}>
                            <p className="text-sm">{message.message}</p>
                            <p className="text-xs opacity-70 mt-1">
                              {format(new Date(message.created_at), 'PPp')}
                            </p>
                          </div>
                          {message.sender_id === user?.id && (
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={user?.user_metadata?.avatar_url || ""} />
                              <AvatarFallback>{user?.email?.charAt(0).toUpperCase()}</AvatarFallback>
                            </Avatar>
                          )}
                        </div>
                      ))
                    )}
                  </div>
                  
                  <div className="flex gap-2">
                    <Textarea
                      placeholder="Type your message here..."
                      value={messageText}
                      onChange={(e) => setMessageText(e.target.value)}
                      disabled={quote.status === 'cancelled'}
                    />
                    <Button 
                      size="icon" 
                      onClick={handleSendMessage} 
                      disabled={!messageText.trim() || sendingMessage || quote.status === 'cancelled'}
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
        
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Quote Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-center">
                <Badge className="text-lg py-2 px-4" variant={
                  quote.status === 'accepted' ? 'default' :
                  quote.status === 'rejected' || quote.status === 'cancelled' ? 'destructive' :
                  'secondary'
                }>
                  {quote.status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </Badge>
              </div>
              
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Created On</p>
                <p className="font-medium">{format(new Date(quote.created_at), 'PPP')}</p>
              </div>
              
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Last Updated</p>
                <p className="font-medium">{format(new Date(quote.updated_at), 'PPP')}</p>
              </div>
              
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Reference Number</p>
                <p className="font-medium">{quote.reference_number}</p>
              </div>
              
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Priority</p>
                <Badge variant={
                  quote.priority === 'high' ? 'destructive' :
                  quote.priority === 'medium' ? 'secondary' :
                  'outline'
                }>
                  {quote.priority.charAt(0).toUpperCase() + quote.priority.slice(1)}
                </Badge>
        </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}