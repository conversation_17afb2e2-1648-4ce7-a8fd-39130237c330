"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Badge } from "@/app/components/ui/badge"
import {
  ArrowLeft,
  Mail,
  Phone,
  Building2,
  Users,
  Bus,
  Calendar,
} from "lucide-react"
import Link from "next/link"
import { DataTable } from "@/app/components/ui/data-table"
import { ColumnDef } from "@tanstack/react-table"
import { formatDate } from "@/lib/utils"
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/app/components/ui/tabs"
import { TripRequestList } from '@/app/components/trip-requests/trip-request-list'
import { QuoteList } from "@/app/components/features/quotes/quote-list"

type Event = {
  id: number
  name: string
  date: Date
  location: string
  attendees: number
  status: string
}

const columns: ColumnDef<Event>[] = [
  {
    header: "Event Name",
    accessorKey: "name",
    cell: ({ row }) => (
      <Link
        href={`/customer/events/${row.original.id}`}
        className="hover:underline"
      >
        {row.getValue("name")}
      </Link>
    ),
  },
  {
    header: "Date",
    accessorKey: "date",
    cell: ({ row }) => formatDate(row.getValue("date")),
  },
  {
    header: "Location",
    accessorKey: "location",
  },
  {
    header: "Attendees",
    accessorKey: "attendees",
  },
  {
    header: "Status",
    accessorKey: "status",
    cell: ({ row }) => {
      const status = row.getValue("status") as string
      return (
        <Badge
          variant={
            status === "upcoming"
              ? "default"
              : status === "completed"
                ? "secondary"
                : "destructive"
          }
        >
          {status}
        </Badge>
      )
    },
  },
]

export default function CompanyDetailsPage({
  params,
}: {
  params: { id: string }
}) {
  // TODO: Fetch company details from API
  const company = {
    id: parseInt(params.id),
    name: "Acme Corporation",
    contact: "John Smith",
    email: "<EMAIL>",
    phone: "+1 ***********",
    status: "active",
    totalEvents: 5,
    upcomingEvents: 2,
    totalTrips: 15,
    totalQuotes: 8,
  }

  const events: Event[] = [
    {
      id: 1,
      name: "Annual Conference 2024",
      date: new Date("2024-06-15"),
      location: "Convention Center",
      attendees: 500,
      status: "upcoming",
    },
    {
      id: 2,
      name: "Tech Summit 2024",
      date: new Date("2024-02-20"),
      location: "Grand Hotel",
      attendees: 300,
      status: "completed",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-x-3">
          <Link href="/customer/companies">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <h2 className="text-2xl font-bold tracking-tight">{company.name}</h2>
          <Badge
            variant={company.status === "active" ? "default" : "secondary"}
          >
            {company.status}
          </Badge>
        </div>
        <div className="flex items-center gap-x-2">
          <Link href={`/customer/companies/${company.id}/edit`}>
            <Button variant="outline">Edit Company</Button>
          </Link>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Company Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-x-2">
              <Building2 className="h-4 w-4 text-muted-foreground" />
              <span>{company.contact}</span>
            </div>
            <div className="flex items-center gap-x-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <span>{company.email}</span>
            </div>
            <div className="flex items-center gap-x-2">
              <Phone className="h-4 w-4 text-muted-foreground" />
              <span>{company.phone}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Activity Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Total Events
                </div>
                <div className="flex items-center gap-x-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-2xl font-bold">{company.totalEvents}</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Upcoming Events
                </div>
                <div className="flex items-center gap-x-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-2xl font-bold">
                    {company.upcomingEvents}
                  </span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Total Trips
                </div>
                <div className="flex items-center gap-x-2">
                  <Bus className="h-4 w-4 text-muted-foreground" />
                  <span className="text-2xl font-bold">{company.totalTrips}</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Total Quotes
                </div>
                <div className="flex items-center gap-x-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span className="text-2xl font-bold">{company.totalQuotes}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Events</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable columns={columns} data={events} />
        </CardContent>
      </Card>

      <Tabs defaultValue="trips" className="space-y-4">
        <TabsList>
          <TabsTrigger value="trips" className="flex items-center gap-x-2">
            <Bus className="h-4 w-4" />
            Trip Requests
          </TabsTrigger>
          <TabsTrigger value="quotes" className="flex items-center gap-x-2">
            <Users className="h-4 w-4" />
            Quotes
          </TabsTrigger>
        </TabsList>
        <TabsContent value="trips">
          <TripRequestList />
        </TabsContent>
        <TabsContent value="quotes">
          <QuoteList />
        </TabsContent>
      </Tabs>
    </div>
  )
} 