"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { PassengerForm } from '@/app/components/passengers/passenger-form'

export default function NewPassengerPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-x-3">
        <Link href="/customer/passengers">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </Link>
        <h2 className="text-2xl font-bold tracking-tight">New Passenger</h2>
      </div>

      <PassengerForm />
    </div>
  )
} 