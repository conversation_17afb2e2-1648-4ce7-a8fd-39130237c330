"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { ArrowLeft, Mail, Phone, Pencil, UserCog } from "lucide-react"
import Link from "next/link"
import { useParams, useRouter } from "next/navigation"
import { Badge } from "@/app/components/ui/badge"
import { formatDate } from "@/lib/utils"
import { useState, useEffect } from "react"

type PassengerData = {
  id: string
  name: string
  email: string
  phone: string
  type: "vip" | "regular" | "staff"
  status: "active" | "inactive"
  requirements: {
    accessibility?: string[]
    other?: string[]
  }
  notes?: string
  associatedEvents: {
    id: string
    name: string
    date: Date
    status: string
  }[]
}

// Mock function - replace with actual API call
const getPassengerDetails = async (id: string): Promise<PassengerData> => {
  return {
    id,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+1234567890",
    type: "vip",
    status: "active",
    requirements: {
      accessibility: ["wheelchair"],
    },
    notes: "VIP guest speaker for tech conference",
    associatedEvents: [
      {
        id: "evt-1",
        name: "Annual Tech Conference 2024",
        date: new Date("2024-06-15"),
        status: "confirmed",
      },
    ],
  }
}

export default function PassengerDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const passengerId = params.id as string
  const [passenger, setPassenger] = useState<PassengerData | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchPassenger = async () => {
      try {
        const data = await getPassengerDetails(passengerId)
        setPassenger(data)
      } catch (error) {
        console.error("Failed to fetch passenger:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchPassenger()
  }, [passengerId])

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-x-4">
            <Link href="/customer/passengers">
              <Button variant="ghost" size="icon">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <div className="h-6 w-48 animate-pulse bg-muted rounded" />
          </div>
        </div>
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4 animate-pulse">
                <div className="h-4 w-24 bg-muted rounded" />
                <div className="h-4 w-32 bg-muted rounded" />
                <div className="h-4 w-28 bg-muted rounded" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (!passenger) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-x-4">
            <Link href="/customer/passengers">
              <Button variant="ghost" size="icon">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <h2 className="text-2xl font-bold tracking-tight">Passenger Not Found</h2>
          </div>
        </div>
        <Card>
          <CardContent className="p-6">
            <p className="text-muted-foreground">The requested passenger could not be found.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-x-4">
          <Link href="/customer/passengers">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h2 className="text-2xl font-bold tracking-tight">{passenger.name}</h2>
            <p className="text-muted-foreground">{passenger.email}</p>
          </div>
        </div>
        <div className="flex items-center gap-x-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/customer/passengers/${passenger.id}/requirements`)}
          >
            <UserCog className="h-4 w-4 mr-2" />
            Manage Requirements
          </Button>
          <Button
            onClick={() => router.push(`/customer/passengers/${passenger.id}/edit`)}
          >
            <Pencil className="h-4 w-4 mr-2" />
            Edit Passenger
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Passenger Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="text-sm font-medium">Type</div>
              <Badge variant={passenger.type === "vip" ? "default" : "secondary"}>
                {passenger.type.toUpperCase()}
              </Badge>
            </div>
            <div>
              <div className="text-sm font-medium">Status</div>
              <Badge variant={passenger.status === "active" ? "default" : "destructive"}>
                {passenger.status}
              </Badge>
            </div>
            <div>
              <div className="text-sm font-medium">Contact</div>
              <div className="flex flex-col gap-y-1 mt-1.5">
                <div className="flex items-center gap-x-2 text-sm text-muted-foreground">
                  <Mail className="h-4 w-4" />
                  {passenger.email}
                </div>
                <div className="flex items-center gap-x-2 text-sm text-muted-foreground">
                  <Phone className="h-4 w-4" />
                  {passenger.phone}
                </div>
              </div>
            </div>
            {passenger.notes && (
              <div>
                <div className="text-sm font-medium">Notes</div>
                <p className="text-sm text-muted-foreground mt-1.5">{passenger.notes}</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Requirements</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {passenger.requirements.accessibility && passenger.requirements.accessibility.length > 0 && (
              <div>
                <div className="text-sm font-medium mb-2">Accessibility Requirements</div>
                <div className="flex flex-wrap gap-1.5">
                  {passenger.requirements.accessibility.map((req) => (
                    <Badge key={req} variant="outline">
                      {req}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            {(!passenger.requirements.accessibility || passenger.requirements.accessibility.length === 0) && (
              <p className="text-sm text-muted-foreground">No special requirements</p>
            )}
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Associated Events</CardTitle>
          </CardHeader>
          <CardContent>
            {passenger.associatedEvents.length > 0 ? (
              <div className="space-y-4">
                {passenger.associatedEvents.map((event) => (
                  <div
                    key={event.id}
                    className="flex items-center justify-between border-b pb-4 last:border-0 last:pb-0"
                  >
                    <div>
                      <Link
                        href={`/customer/events/${event.id}`}
                        className="font-medium hover:underline"
                      >
                        {event.name}
                      </Link>
                      <div className="text-sm text-muted-foreground">
                        {formatDate(event.date)}
                      </div>
                    </div>
                    <Badge variant="secondary">{event.status}</Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No associated events</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 