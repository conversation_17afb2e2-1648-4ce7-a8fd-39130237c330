# Customer Portal

## Overview
The Customer Portal provides event organizers with tools to manage their events, transportation requests, quotes, and trips. This portal is designed for users with the `CUSTOMER` role.

## Features
- **Dashboard**: Overview of events, quotes, and trips
- **Events**: Create and manage events
- **Quotes**: View and manage transportation quotes
- **Trips**: Track and manage transportation trips
- **Companies**: Manage company information
- **Passengers**: Manage passenger information
- **Profile**: User profile management
- **Settings**: User settings
- **Notifications**: System notifications

## Directory Structure
- `dashboard/` - Customer dashboard
- `events/` - Event management
  - `[id]/` - Individual event details
  - `new/` - Create new event
- `quotes/` - Quote management
  - `[id]/` - Individual quote details
- `trips/` - Trip management
  - `components/` - Trip-specific components
  - `gods-view/` - Map view of all trips
- `companies/` - Company management
- `passengers/` - Passenger management
- `profile/` - User profile
- `settings/` - User settings
- `notifications/` - Notifications
- `requests/` - Request management
- `transportation/` - Transportation management

## Key Components
- `layout.tsx` - Defines the layout for the customer portal
- `trips/components/gods-view.tsx` - Map view component for trips
- `events/[id]/page.tsx` - Event details page

## API Integration
The customer portal interacts with several API endpoints:
- `/api/events` - Event management
- `/api/quotes` - Quote management
- `/api/trips` - Trip management
- `/api/companies` - Company management
- `/api/passengers` - Passenger management

## Testing
Tests for the customer portal are located in `/tests/customer/` and should mirror this directory structure. 