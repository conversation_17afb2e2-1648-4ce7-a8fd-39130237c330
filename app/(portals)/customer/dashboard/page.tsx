"use client"

import { useEffect, useState } from 'react'
import { useAuth } from '@/lib/auth/context'
import { UserRole } from '@/src/types/roles'
import { Button } from '@/app/components/ui/button'
import { 
  CalendarPlus, 
  MapPin, 
  Clock, 
  FileText, 
  CreditCard, 
  Car, 
  ChevronRight, 
  Calendar, 
  User, 
  Bell, 
  Bookmark,
  Star,
  Zap,
  Activity,
  AlertTriangle,
  Filter,
  Search,
  List,
  MapIcon,
  CheckCircle2,
  Phone,
  MessageSquare,
  Plus,
  XCircle,
  X
} from 'lucide-react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/app/components/ui/card'
import { Badge } from '@/app/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs'
import { Avatar, AvatarFallback } from '@/app/components/ui/avatar'
import { Progress } from '@/app/components/ui/progress'
import { Separator } from '@/app/components/ui/separator'
import { getQuotes } from '@/lib/api/quotes'
import { format } from 'date-fns'
import { ToggleGroup, ToggleGroupItem } from '@/app/components/ui/toggle-group'
import { Input } from '@/app/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { useRouter, useSearchParams } from "next/navigation";
import { getSupabaseClient } from '@/lib/supabase';
import { SupabaseClient } from '@supabase/supabase-js';
import { formatDate } from "@/lib/utils";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/app/components/ui/dialog"
import { toast } from "@/app/components/ui/use-toast"
import { QuoteCard } from "@/app/components/quotes/cards/quote-card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/app/components/ui/alert-dialog"

export default function CustomerDashboard() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [supabase, setSupabase] = useState<SupabaseClient | null>(null);
  
  // State for UI
  const [mounted, setMounted] = useState(false)
  const [userData, setUserData] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('overview')
  const { user, loading } = useAuth()
  const isEventManager = user?.roles?.includes('EVENT_MANAGER' as UserRole) || false
  const [profileData, setProfileData] = useState<any>(null)
  
  // State for real data
  const [upcomingTrips, setUpcomingTrips] = useState<any[]>([])
  const [recentQuotes, setRecentQuotes] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Add these states for the enhanced trips tab
  const [tripsView, setTripsView] = useState<"list" | "map">("list")
  const [tripsSearchTerm, setTripsSearchTerm] = useState("")
  const [tripsDateRange, setTripsDateRange] = useState("today")
  const [tripsTabValue, setTripsTabValue] = useState("upcoming")

  // Add these states for the enhanced quotes tab
  const [quotesSearchTerm, setQuotesSearchTerm] = useState("")
  const [quotesDateRange, setQuotesDateRange] = useState("all")
  const [quotesTabValue, setQuotesTabValue] = useState("pending")

  // Add state for detail dialogs
  const [selectedQuote, setSelectedQuote] = useState<any>(null);
  const [selectedTrip, setSelectedTrip] = useState<any>(null);

  // Add confirmation dialog state
  const [confirmationDialog, setConfirmationDialog] = useState<{
    isOpen: boolean;
    title: string;
    description: string;
    action: () => void;
  }>({
    isOpen: false,
    title: '',
    description: '',
    action: () => {},
  });

  // Initialize Supabase client from @supabase/ssr
  useEffect(() => {
    const client = getSupabaseClient();
    setSupabase(client);
  }, []);

  // Set mounted state when component mounts
  useEffect(() => {
    setMounted(true)
  }, [])

  // Add this useEffect to handle URL parameters for tab selection
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Check for URL parameters
      const urlParams = new URLSearchParams(window.location.search)
      const tabParam = urlParams.get('tab')
      
      // Set the active tab based on URL parameter
      if (tabParam) {
        setActiveTab(tabParam)
        
        // Set the subtab if specified
        const subtabParam = urlParams.get('subtab')
        if (subtabParam) {
          if (tabParam === 'trips') {
            setTripsTabValue(subtabParam)
          } else if (tabParam === 'quotes') {
            setQuotesTabValue(subtabParam)
          }
        }
      }
    }
  }, [])

  // Fetch user data and quotes/trips on mount
  useEffect(() => {
      if (user && supabase) {
      fetchData()
    }
  }, [user, supabase])

  // Function to fetch all necessary data
  const fetchData = async () => {
    if (!supabase) {
      setError('Supabase client is not available.');
      setIsLoading(false);
      return;
    }
    try {
      setIsLoading(true)
      setError(null)

      // Fetch quotes
      const quotesResponse = await fetch('/api/quotes')
      if (!quotesResponse.ok) {
        const errorData = await quotesResponse.json().catch(() => ({}))
        throw new Error(`Error fetching quotes: ${errorData.message || quotesResponse.statusText}`)
      }
      const quotesData = await quotesResponse.json()
      
      // Fetch trips
      const tripsResponse = await fetch('/api/trips')
      if (!tripsResponse.ok) {
        const errorData = await tripsResponse.json().catch(() => ({}))
        throw new Error(`Error fetching trips: ${errorData.message || tripsResponse.statusText}`)
      }
      const tripsData = await tripsResponse.json()
      
      // Fetch user profile
      const profileResponse = await fetch('/api/profile')
      if (!profileResponse.ok) {
        const errorData = await profileResponse.json().catch(() => ({}))
        throw new Error(`Error fetching profile: ${errorData.message || profileResponse.statusText}`)
      }
      const profileData = await profileResponse.json()

      // Handle both formats - direct array or nested in quotes property
      const quotes = Array.isArray(quotesData) ? quotesData : quotesData.quotes || []

      setUpcomingTrips(tripsData.trips || [])
      setRecentQuotes(quotes)
      setProfileData({
        firstName: profileData.profile?.first_name || profileData.profile?.full_name?.split(' ')[0] || '',
        lastName: profileData.profile?.last_name || profileData.profile?.full_name?.split(' ')[1] || '',
      })

      // Set user data from profile
      if (profileData.profile) {
        setUserData({
          firstName: profileData.profile.first_name || profileData.profile.full_name?.split(' ')[0] || '',
          lastName: profileData.profile.last_name || profileData.profile.full_name?.split(' ')[1] || '',
          email: profileData.profile.email || user?.email || '',
          roles: user?.roles || []
        })
      }

      console.log('Dashboard data loaded:', { 
        quotes: quotes.length,
        trips: tripsData.trips?.length || 0,
        profile: profileData.profile
      })
    } catch (error) {
      console.error('Error fetching data:', error)
      setError(error instanceof Error ? error.message : 'Failed to load data')
    } finally {
      setIsLoading(false)
    }
  }

  // Show loading state if not mounted or still loading initial data
  if (!mounted) {
    return <div className="flex justify-center items-center h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
        <p className="mt-4">Loading dashboard...</p>
      </div>
    </div>
  }

  if (error) {
    console.error('CustomerDashboard - Error:', error)
    return <div>Error loading dashboard: {error}</div>
  }

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true
    }).format(date)
  }

  // Get greeting based on time of day
  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return 'Good morning'
    if (hour < 18) return 'Good afternoon'
    return 'Good evening'
  }

  // Get user's first name
  const firstName = profileData?.firstName || userData?.firstName || 'there'

  // Add this function to filter quotes by status
  const getFilteredQuotes = (status: string) => {
    if (!recentQuotes) return []
    
    // Filter out accepted quotes from the quotes tab since they become trips
    const activeQuotes = recentQuotes.filter(quote => quote.status !== 'accepted')
    
    if (status === "pending") {
      return activeQuotes.filter(quote => quote.status === "pending")
    } else if (status === "rejected") {
      return activeQuotes.filter(quote => quote.status === "rejected")
    } else if (status === "change_requested") {
      return activeQuotes.filter(quote => quote.status === "change_requested")
    } else if (status === "past") {
      // Past quotes are those that are neither pending, rejected, nor change_requested
      return activeQuotes.filter(quote => 
        quote.status !== "pending" && 
        quote.status !== "rejected" && 
        quote.status !== "change_requested" && 
        quote.status !== "accepted"
      )
    }
    
    return activeQuotes
  }
  
  // Add this function to filter trips by status
  const getFilteredTrips = (status: string) => {
    if (!upcomingTrips) return []
    
    if (status === "upcoming") {
      return upcomingTrips.filter(trip => 
        trip.status === "confirmed" || 
        trip.status === "assigned" || 
        trip.status === "en_route"
      )
    } else if (status === "active") {
      return upcomingTrips.filter(trip => 
        trip.status === "in_progress" || 
        trip.status === "en_route" || 
        trip.status === "assigned"
      )
    } else if (status === "completed") {
      return upcomingTrips.filter(trip => trip.status === "completed")
    } else if (status === "cancelled") {
      return upcomingTrips.filter(trip => trip.status === "cancelled")
    }
    
    return upcomingTrips
  }

  // Update handleQuoteAction to use confirmation dialogs
  const handleQuoteAction = async (quoteId: string, action: 'accept' | 'reject' | 'change' | 'cancel') => {
    const actionConfigs = {
      accept: {
        title: "Accept Quote",
        description: "Are you sure you want to accept this quote? This will create a trip and you'll be charged the quoted amount.",
        variant: "default" as const
      },
      reject: {
        title: "Reject Quote",
        description: "Are you sure you want to reject this quote? This action cannot be undone.",
        variant: "destructive" as const
      },
      change: {
        title: "Request Changes",
        description: "Do you want to request changes to this quote? The provider will review your request and may adjust the rate.",
        variant: "secondary" as const
      },
      cancel: {
        title: "Cancel Quote",
        description: "Are you sure you want to cancel this quote request? This action cannot be undone.",
        variant: "destructive" as const
      }
    };

    const config = actionConfigs[action];
    
    setConfirmationDialog({
      isOpen: true,
      title: config.title,
      description: config.description,
      action: async () => {
    try {
      setIsLoading(true);
      
      const response = await fetch(`/api/quotes/${quoteId}/${action}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          userId: user?.id,
          action 
        }),
      });
      
      if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to ${action} quote: ${response.statusText}`);
      }
      
          await fetchData();
      
      toast({
            title: `Quote ${
              action === 'accept' ? 'accepted' : 
              action === 'reject' ? 'rejected' : 
              action === 'change' ? 'change requested' :
              'cancelled'
            }`,
        description: action === 'accept' 
          ? 'A trip has been created from your quote.' 
          : action === 'reject' 
          ? 'The quote has been rejected.' 
              : action === 'change'
              ? 'Your change request has been submitted.'
              : 'The quote request has been cancelled.',
      });
      
      if (action === 'accept') {
        setTimeout(() => {
          navigateToTab('trips', 'upcoming');
        }, 1000);
      }
    } catch (error) {
      console.error(`Error handling quote action (${action}):`, error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : `Failed to ${action} quote. Please try again.`,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
          setConfirmationDialog(prev => ({ ...prev, isOpen: false }));
    }
      }
    });
  };

  // Function to handle navigation to specific tabs
  const navigateToTab = (tab: string, subtab?: string) => {
    setActiveTab(tab)
    if (tab === 'trips' && subtab) {
      setTripsTabValue(subtab)
    } else if (tab === 'quotes' && subtab) {
      setQuotesTabValue(subtab)
    }
  }

  // Function to handle viewing quote details
  const handleViewQuoteDetails = (quote: any) => {
    setSelectedQuote(quote);
  };
  
  // Function to handle viewing trip details
  const handleViewTripDetails = (trip: any) => {
    setSelectedTrip(trip);
  };

  return (
    <div className="container mx-auto p-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <h1 className="text-3xl font-bold">{getGreeting()}, {firstName}!</h1>
          <p className="text-muted-foreground mt-1">Welcome to your transportation dashboard</p>
        </div>
        
        <div className="flex gap-3">
          <Button 
            className="flex items-center gap-2"
            onClick={() => navigateToTab('quotes')}
            asChild
          >
            <Link href="/customer/quotes/new">
              <Car className="h-4 w-4" />
              Book Transportation
            </Link>
          </Button>
          
          {!isEventManager && (
        <Link href="/customer/events/new">
              <Button variant="outline" className="flex items-center gap-2">
            <CalendarPlus className="h-4 w-4" />
            Create Event
          </Button>
        </Link>
          )}
        </div>
      </div>

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3 md:w-auto">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="quotes">My Quotes</TabsTrigger>
          <TabsTrigger value="trips">My Trips</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-6">
          {/* Quick Stats */}
          <div className="grid gap-6 md:grid-cols-2">
        <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Recent Quotes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{getFilteredQuotes("all").length}</div>
                <p className="text-xs text-muted-foreground">Latest: {recentQuotes.length > 0 ? formatDate(recentQuotes[0].date) : 'No quotes yet'}</p>
              </CardContent>
              <CardFooter>
                <Button 
                  variant="link" 
                  className="text-xs text-blue-600 flex items-center p-0 h-auto"
                  onClick={() => navigateToTab('quotes', 'pending')}
                >
                  View all quotes <ChevronRight className="h-3 w-3 ml-1" />
                </Button>
              </CardFooter>
            </Card>
            
        <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Upcoming Trips</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{upcomingTrips.length}</div>
                <p className="text-xs text-muted-foreground">Next trip: {upcomingTrips.length > 0 ? formatDate(upcomingTrips[0].date) : 'None scheduled'}</p>
              </CardContent>
              <CardFooter>
                <Button 
                  variant="link" 
                  className="text-xs text-blue-600 flex items-center p-0 h-auto"
                  onClick={() => navigateToTab('trips', 'upcoming')}
                >
                  View all trips <ChevronRight className="h-3 w-3 ml-1" />
                </Button>
              </CardFooter>
            </Card>
          </div>
          
          {/* Main Content */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2">
            {/* Recent Quotes */}
            <Card className="md:col-span-1">
          <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-green-500" />
                  Recent Quotes
                </CardTitle>
                <CardDescription>Your transportation quotes</CardDescription>
          </CardHeader>
          <CardContent>
                {getFilteredQuotes("all").length > 0 ? (
                  <div className="space-y-4">
                    {getFilteredQuotes("all").slice(0, 3).map(quote => (
                      <div key={quote.id} className="flex items-start p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                        <div className="bg-green-100 p-2 rounded-full mr-4">
                          <FileText className="h-5 w-5 text-green-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div>
                              <p className="font-medium">{formatDate(quote.date)}</p>
                              <p className="text-sm text-muted-foreground mt-1">
                                {quote.pickup_location} to {quote.dropoff_location}
                              </p>
                            </div>
                            <div className="text-right">
                              <Badge variant={
                                quote.status === 'pending' ? 'outline' : 
                                quote.status === 'accepted' ? 'default' : 'secondary'
                              }>
                                {quote.status}
                              </Badge>
                              <p className="text-sm font-medium mt-1">${quote.total_amount?.toFixed(2) || '0.00'}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <FileText className="h-12 w-12 mx-auto text-gray-300 mb-3" />
                    <h3 className="text-lg font-medium mb-1">No quotes yet</h3>
                    <p className="text-muted-foreground mb-4">Request your first quote</p>
                    <Link href="/customer/quotes/new">
                      <Button>Request Quote</Button>
                    </Link>
                  </div>
            )}
          </CardContent>
              {getFilteredQuotes("all").length > 0 && (
                <CardFooter>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => navigateToTab('quotes', 'pending')}
                  >
                    View All Quotes
                  </Button>
                </CardFooter>
              )}
        </Card>

            {/* Upcoming Trips */}
            <Card className="md:col-span-1">
          <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2 text-blue-500" />
                  Upcoming Trips
                </CardTitle>
                <CardDescription>Your scheduled transportation</CardDescription>
          </CardHeader>
          <CardContent>
                {upcomingTrips.length > 0 ? (
                  <div className="space-y-4">
                    {upcomingTrips.map(trip => (
                      <div key={trip.id} className="flex items-start p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                        <div className="bg-blue-100 p-2 rounded-full mr-4">
                          <Car className="h-5 w-5 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div>
                              <p className="font-medium">{formatDate(trip.date)}</p>
                              <p className="text-sm text-muted-foreground mt-1">
                                {trip.pickup_location} to {trip.dropoff_location}
                              </p>
                            </div>
                            <div className="text-right">
                              <Badge variant={
                                trip.status === 'confirmed' ? 'outline' : 
                                trip.status === 'completed' ? 'default' : 'secondary'
                              }>
                                {trip.status}
                              </Badge>
                              <p className="text-sm font-medium mt-1">{trip.vehicle_type || 'Standard'}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <Car className="h-12 w-12 mx-auto text-gray-300 mb-3" />
                    <h3 className="text-lg font-medium mb-1">No upcoming trips</h3>
                    <p className="text-muted-foreground mb-4">Book your first transportation service</p>
                    <Link href="/customer/quotes/new">
                      <Button>Book Now</Button>
                  </Link>
                  </div>
            )}
          </CardContent>
              {upcomingTrips.length > 0 && (
                <CardFooter>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => navigateToTab('trips', 'upcoming')}
                  >
                    View All Trips
                  </Button>
                </CardFooter>
              )}
        </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="quotes" className="space-y-6">
          {isLoading ? (
            <div className="flex items-center justify-center h-40">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                <p className="mt-2 text-sm text-muted-foreground">Loading quotes...</p>
              </div>
            </div>
          ) : (
            <>
              {/* Stats Grid for Quotes */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Quotes</CardTitle>
                    <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
                    <div className="text-2xl font-bold">{getFilteredQuotes("all").length}</div>
                    <p className="text-xs text-muted-foreground">
                      All your transportation quotes
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Pending</CardTitle>
                    <Clock className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{getFilteredQuotes("pending").length}</div>
                    <p className="text-xs text-muted-foreground">
                      Awaiting response
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Rejected</CardTitle>
                    <XCircle className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{getFilteredQuotes("rejected").length}</div>
                    <p className="text-xs text-muted-foreground">
                      Not proceeding
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Filters and Search for Quotes */}
              <div className="flex flex-col md:flex-row items-center justify-between gap-4">
                <div className="flex items-center gap-4 w-full md:w-auto">
                  <div className="relative w-full md:w-[300px]">
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search quotes..."
                      className="pl-9"
                      value={quotesSearchTerm}
                      onChange={(e) => setQuotesSearchTerm(e.target.value)}
                    />
                  </div>
                  <Button variant="outline" className="gap-2 whitespace-nowrap">
                    <Filter className="h-4 w-4" />
                    Filters
                  </Button>
                </div>
                
                <div className="flex items-center gap-4 w-full md:w-auto">
                  <Select value={quotesDateRange} onValueChange={setQuotesDateRange}>
                    <SelectTrigger className="w-full md:w-[180px]">
                      <SelectValue placeholder="Select date range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Time</SelectItem>
                      <SelectItem value="week">This Week</SelectItem>
                      <SelectItem value="month">This Month</SelectItem>
                      <SelectItem value="custom">Custom Range</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Button variant="outline" size="sm" asChild>
                    <Link href="/customer/quotes/new">
                      <Plus className="h-4 w-4 mr-2" />
                      New Quote
                  </Link>
                  </Button>
                </div>
              </div>

              {/* Quotes Tabs */}
              <Tabs defaultValue="pending" value={quotesTabValue} onValueChange={setQuotesTabValue}>
                <TabsList>
                  <TabsTrigger value="pending" className="gap-2">
                    <Clock className="h-4 w-4" />
                    Pending ({getFilteredQuotes("pending").length})
                  </TabsTrigger>
                  <TabsTrigger value="rejected" className="gap-2">
                    <XCircle className="h-4 w-4" />
                    Rejected ({getFilteredQuotes("rejected").length})
                  </TabsTrigger>
                  <TabsTrigger value="past" className="gap-2">
                    <FileText className="h-4 w-4" />
                    Past Quotes ({getFilteredQuotes("past").length})
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="pending" className="mt-4">
                  {getFilteredQuotes("pending").length === 0 ? (
                    <div className="text-center py-10">
                      <Clock className="h-10 w-10 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium">No pending quotes</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        You don't have any quotes awaiting response.
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {getFilteredQuotes("pending").map((quote) => (
                        <QuoteCard
                          key={quote.id}
                          quote={{
                            ...quote,
                            has_rate_offer: Boolean(quote.total_amount && quote.total_amount > 0),
                            can_be_cancelled: quote.status === 'pending' && (!quote.total_amount || quote.total_amount === 0)
                          }}
                          onAccept={() => handleQuoteAction(quote.id, 'accept')}
                          onReject={() => handleQuoteAction(quote.id, 'reject')}
                          onRequestChanges={() => handleQuoteAction(quote.id, 'change')}
                          onCancel={() => handleQuoteAction(quote.id, 'cancel')}
                          onClick={() => handleViewQuoteDetails(quote)}
                        />
                      ))}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="rejected" className="mt-4">
                  {getFilteredQuotes("rejected").length === 0 ? (
                    <div className="text-center py-10">
                      <XCircle className="h-10 w-10 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium">No rejected quotes</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        You don't have any rejected quotes.
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {getFilteredQuotes("rejected").map((quote) => (
                        <QuoteCard
                          key={quote.id}
                          quote={{
                            ...quote,
                            has_rate_offer: quote.total_amount !== null && quote.total_amount > 0,
                            can_be_cancelled: false
                          }}
                          onAccept={() => handleQuoteAction(quote.id, 'accept')}
                          onReject={() => handleQuoteAction(quote.id, 'reject')}
                          onRequestChanges={() => handleQuoteAction(quote.id, 'change')}
                          onCancel={() => handleQuoteAction(quote.id, 'cancel')}
                          onClick={() => handleViewQuoteDetails(quote)}
                        />
                      ))}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="past" className="mt-4">
                  {getFilteredQuotes("past").length === 0 ? (
                    <div className="text-center py-10">
                      <FileText className="h-10 w-10 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium">No past quotes</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        You don't have any past quotes.
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {getFilteredQuotes("past").map((quote) => (
                        <QuoteCard
                          key={quote.id}
                          quote={{
                            ...quote,
                            has_rate_offer: quote.total_amount !== null && quote.total_amount > 0,
                            can_be_cancelled: false
                          }}
                          onAccept={() => handleQuoteAction(quote.id, 'accept')}
                          onReject={() => handleQuoteAction(quote.id, 'reject')}
                          onRequestChanges={() => handleQuoteAction(quote.id, 'change')}
                          onCancel={() => handleQuoteAction(quote.id, 'cancel')}
                          onClick={() => handleViewQuoteDetails(quote)}
                        />
                      ))}
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </>
          )}
        </TabsContent>
        
        <TabsContent value="trips" className="space-y-6">
          {isLoading ? (
            <div className="flex items-center justify-center h-40">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                <p className="mt-2 text-sm text-muted-foreground">Loading trips...</p>
              </div>
            </div>
          ) : (
            <>
              {/* Stats Grid - from trips page */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Trips</CardTitle>
                    <Car className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
                    <div className="text-2xl font-bold">{upcomingTrips.length}</div>
                    <p className="text-xs text-muted-foreground">
                      All your transportation trips
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Active Trips</CardTitle>
                    <Activity className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{getFilteredTrips("active").length}</div>
                    <p className="text-xs text-muted-foreground">
                      Currently in progress
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Upcoming</CardTitle>
                    <Clock className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{getFilteredTrips("upcoming").length}</div>
                    <p className="text-xs text-muted-foreground">
                      Scheduled for the future
                    </p>
          </CardContent>
        </Card>
        <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Completed</CardTitle>
                    <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
                    <div className="text-2xl font-bold">{getFilteredTrips("completed").length}</div>
                    <p className="text-xs text-muted-foreground">
                      Successfully completed trips
                    </p>
          </CardContent>
        </Card>
      </div>
      
              {/* Filters and Search - from trips page */}
              <div className="flex flex-col md:flex-row items-center justify-between gap-4">
                <div className="flex items-center gap-4 w-full md:w-auto">
                  <div className="relative w-full md:w-[300px]">
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search trips..."
                      className="pl-9"
                      value={tripsSearchTerm}
                      onChange={(e) => setTripsSearchTerm(e.target.value)}
                    />
      </div>
                  <Button variant="outline" className="gap-2 whitespace-nowrap">
                    <Filter className="h-4 w-4" />
                    Filters
                  </Button>
                </div>
                
                <div className="flex items-center gap-4 w-full md:w-auto">
                  <Select value={tripsDateRange} onValueChange={setTripsDateRange}>
                    <SelectTrigger className="w-full md:w-[180px]">
                      <SelectValue placeholder="Select date range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="today">Today</SelectItem>
                      <SelectItem value="week">This Week</SelectItem>
                      <SelectItem value="month">This Month</SelectItem>
                      <SelectItem value="custom">Custom Range</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <ToggleGroup type="single" value={tripsView} onValueChange={(v) => v && setTripsView(v as any)}>
                    <ToggleGroupItem value="list" aria-label="List view">
                      <List className="h-4 w-4" />
                    </ToggleGroupItem>
                    <ToggleGroupItem value="map" aria-label="Map view">
                      <MapIcon className="h-4 w-4" />
                    </ToggleGroupItem>
                  </ToggleGroup>
                </div>
              </div>

              {/* Trips Tabs - from trips page */}
              <Tabs defaultValue="upcoming" value={tripsTabValue} onValueChange={setTripsTabValue}>
                <TabsList>
                  <TabsTrigger value="upcoming" className="gap-2">
                    <Clock className="h-4 w-4" />
                    Upcoming ({getFilteredTrips("upcoming").length})
                  </TabsTrigger>
                  <TabsTrigger value="active" className="gap-2">
                    <Activity className="h-4 w-4" />
                    Active ({getFilteredTrips("active").length})
                  </TabsTrigger>
                  <TabsTrigger value="completed" className="gap-2">
                    <CheckCircle2 className="h-4 w-4" />
                    Completed ({getFilteredTrips("completed").length})
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="upcoming" className="mt-4">
                  {getFilteredTrips("upcoming").length === 0 ? (
                    <div className="text-center py-10">
                      <Car className="h-10 w-10 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium">No upcoming trips</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        You don't have any upcoming trips. Book transportation to get started.
                      </p>
                      <Button className="mt-4" asChild>
                        <Link href="/customer/quotes/new">Book Transportation</Link>
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {getFilteredTrips("upcoming").map((trip) => (
                        <Card key={trip.id} className="overflow-hidden">
                          <div className="flex flex-col md:flex-row">
                            <div className="p-6 flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <Badge variant={
                                  trip.status === 'confirmed' ? 'default' : 
                                  trip.status === 'assigned' ? 'secondary' : 
                                  trip.status === 'en_route' ? 'outline' : 
                                  'secondary'
                                }>
                                  {trip.status === 'confirmed' ? 'Confirmed' : 
                                   trip.status === 'assigned' ? 'Driver Assigned' :
                                   trip.status === 'en_route' ? 'En Route' :
                                   trip.status.charAt(0).toUpperCase() + trip.status.slice(1).replace('_', ' ')}
                                </Badge>
                                <span className="text-xs text-muted-foreground">
                                  {formatDate(trip.date)}
                                </span>
                              </div>
                              <h3 className="font-semibold">{trip.vehicle_type || 'Standard'} Transportation</h3>
                              <div className="mt-4 space-y-2">
                                <div className="flex items-start gap-2">
                                  <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                                  <div>
                                    <p className="text-sm font-medium">Pickup</p>
                                    <p className="text-sm text-muted-foreground">{trip.pickup_location}</p>
                                  </div>
                                </div>
                                <div className="flex items-start gap-2">
                                  <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                                  <div>
                                    <p className="text-sm font-medium">Dropoff</p>
                                    <p className="text-sm text-muted-foreground">{trip.dropoff_location}</p>
                                  </div>
                                </div>
                                {trip.special_requests && (
                                  <div className="flex items-start gap-2">
                                    <MessageSquare className="h-4 w-4 text-muted-foreground mt-0.5" />
                                    <div>
                                      <p className="text-sm font-medium">Special Requests</p>
                                      <p className="text-sm text-muted-foreground">{trip.special_requests}</p>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className="bg-muted p-6 md:w-64 flex flex-col justify-between">
                              <div>
                                <h4 className="font-medium mb-2">Trip Details</h4>
                                <div className="space-y-1">
                                  <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Status</span>
                                    <span className="text-sm font-medium">
                                      {trip.status === 'confirmed' ? 'Confirmed' : 
                                       trip.status === 'assigned' ? 'Driver Assigned' :
                                       trip.status === 'en_route' ? 'En Route' :
                                       trip.status.charAt(0).toUpperCase() + trip.status.slice(1).replace('_', ' ')}
                                    </span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Vehicle</span>
                                    <span className="text-sm font-medium">{trip.vehicle_type || 'Standard'}</span>
                                  </div>
                                  {trip.passenger_count && (
                                    <div className="flex justify-between">
                                      <span className="text-sm text-muted-foreground">Passengers</span>
                                      <span className="text-sm font-medium">{trip.passenger_count}</span>
                                    </div>
                                  )}
                                  {trip.luggage_count && (
                                    <div className="flex justify-between">
                                      <span className="text-sm text-muted-foreground">Luggage</span>
                                      <span className="text-sm font-medium">{trip.luggage_count}</span>
                                    </div>
                                  )}
                                </div>
                              </div>
                              <div className="space-y-2 mt-4">
                                <Button 
                                  className="w-full" 
                                  onClick={() => handleViewTripDetails(trip)}
                                >
                                  View Details
                                </Button>
                                {(trip.status === 'confirmed' || trip.status === 'assigned' || trip.status === 'en_route') && (
                                  <Button 
                                    variant="outline" 
                                    className="w-full"
                                    onClick={() => router.push(`/customer/trips/${trip.id}/track`)}
                                  >
                                    Track Trip
                                  </Button>
                                )}
                              </div>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="active" className="mt-4">
                  {getFilteredTrips("active").length === 0 ? (
                    <div className="text-center py-10">
                      <Activity className="h-10 w-10 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium">No active trips</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        You don't have any trips in progress at the moment.
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {getFilteredTrips("active").map((trip) => (
                        <Card key={trip.id} className="overflow-hidden border-2 border-blue-200">
                          <div className="flex flex-col md:flex-row">
                            <div className="p-6 flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <Badge variant={
                                  trip.status === 'in_progress' ? 'default' : 
                                  trip.status === 'assigned' ? 'secondary' : 
                                  trip.status === 'en_route' ? 'outline' : 
                                  'secondary'
                                }>
                                  {trip.status === 'in_progress' ? 'In Progress' : 
                                   trip.status === 'assigned' ? 'Driver Assigned' :
                                   trip.status === 'en_route' ? 'En Route' :
                                   trip.status.charAt(0).toUpperCase() + trip.status.slice(1).replace('_', ' ')}
                                </Badge>
                                <span className="text-xs text-muted-foreground">
                                  {formatDate(trip.date)}
                                </span>
                              </div>
                              <h3 className="font-semibold">{trip.vehicle_type || 'Standard'} Transportation</h3>
                              <div className="mt-4 space-y-2">
                                <div className="flex items-start gap-2">
                                  <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                                  <div>
                                    <p className="text-sm font-medium">Pickup</p>
                                    <p className="text-sm text-muted-foreground">{trip.pickup_location}</p>
                                  </div>
                                </div>
                                <div className="flex items-start gap-2">
                                  <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                                  <div>
                                    <p className="text-sm font-medium">Dropoff</p>
                                    <p className="text-sm text-muted-foreground">{trip.dropoff_location}</p>
                                  </div>
                                </div>
                                {trip.driver_name && (
                                  <div className="flex items-start gap-2">
                                    <User className="h-4 w-4 text-muted-foreground mt-0.5" />
                                    <div>
                                      <p className="text-sm font-medium">Driver</p>
                                      <p className="text-sm text-muted-foreground">{trip.driver_name}</p>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className="bg-muted p-6 md:w-64 flex flex-col justify-between">
                              <div>
                                <h4 className="font-medium mb-2">Trip Details</h4>
                                <div className="space-y-1">
                                  <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Status</span>
                                    <span className="text-sm font-medium">
                                      {trip.status === 'in_progress' ? 'In Progress' : 
                                       trip.status === 'assigned' ? 'Driver Assigned' :
                                       trip.status === 'en_route' ? 'En Route' :
                                       trip.status.charAt(0).toUpperCase() + trip.status.slice(1).replace('_', ' ')}
                                    </span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Vehicle</span>
                                    <span className="text-sm font-medium">{trip.vehicle_type || 'Standard'}</span>
                                  </div>
                                  {trip.estimated_arrival && (
                                    <div className="flex justify-between">
                                      <span className="text-sm text-muted-foreground">ETA</span>
                                      <span className="text-sm font-medium">{trip.estimated_arrival}</span>
                                    </div>
                                  )}
                                </div>
                              </div>
                              <div className="space-y-2 mt-4">
                                <Button 
                                  className="w-full" 
                                  onClick={() => router.push(`/customer/trips/${trip.id}/track`)}
                                >
                                  Track Live
                                </Button>
                                <Button 
                                  variant="outline" 
                                  className="w-full"
                                  onClick={() => handleViewTripDetails(trip)}
                                >
                                  View Details
                                </Button>
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  className="w-full"
                                  onClick={() => {
                                    // This would open the contact dialog or redirect to contact page
                                    toast({
                                      title: "Contact Driver",
                                      description: "This feature is coming soon.",
                                    });
                                  }}
                                >
                                  <Phone className="h-4 w-4 mr-2" />
                                  Contact Driver
                                </Button>
                              </div>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="completed" className="mt-4">
                  {getFilteredTrips("completed").length === 0 ? (
                    <div className="text-center py-10">
                      <CheckCircle2 className="h-10 w-10 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium">No completed trips</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        You don't have any completed trips yet.
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {getFilteredTrips("completed").map((trip) => (
                        <Card key={trip.id} className="overflow-hidden">
                          <div className="flex flex-col md:flex-row">
                            <div className="p-6 flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <Badge variant="default">Completed</Badge>
                                <span className="text-xs text-muted-foreground">
                                  {formatDate(trip.date)}
                                </span>
                              </div>
                              <h3 className="font-semibold">{trip.vehicle_type || 'Standard'} Transportation</h3>
                              <div className="mt-4 space-y-2">
                                <div className="flex items-start gap-2">
                                  <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                                  <div>
                                    <p className="text-sm font-medium">Pickup</p>
                                    <p className="text-sm text-muted-foreground">{trip.pickup_location}</p>
                                  </div>
                                </div>
                                <div className="flex items-start gap-2">
                                  <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                                  <div>
                                    <p className="text-sm font-medium">Dropoff</p>
                                    <p className="text-sm text-muted-foreground">{trip.dropoff_location}</p>
                                  </div>
                                </div>
                                {trip.completed_at && (
                                  <div className="flex items-start gap-2">
                                    <Clock className="h-4 w-4 text-muted-foreground mt-0.5" />
                                    <div>
                                      <p className="text-sm font-medium">Completed</p>
                                      <p className="text-sm text-muted-foreground">{formatDate(trip.completed_at)}</p>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className="bg-muted p-6 md:w-64 flex flex-col justify-between">
                              <div>
                                <h4 className="font-medium mb-2">Trip Details</h4>
                                <div className="space-y-1">
                                  <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Status</span>
                                    <span className="text-sm font-medium">Completed</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-sm text-muted-foreground">Vehicle</span>
                                    <span className="text-sm font-medium">{trip.vehicle_type || 'Standard'}</span>
                                  </div>
                                  {trip.total_amount && (
                                    <div className="flex justify-between">
                                      <span className="text-sm text-muted-foreground">Total</span>
                                      <span className="text-sm font-medium">${trip.total_amount.toFixed(2)}</span>
                                    </div>
                                  )}
                                </div>
                              </div>
                              <div className="space-y-2 mt-4">
                                <Button 
                                  className="w-full" 
                                  onClick={() => handleViewTripDetails(trip)}
                                >
                                  View Details
                                </Button>
                                {!trip.is_rated && (
                                  <Button 
                                    variant="outline" 
                                    className="w-full"
                                    onClick={() => {
                                      // This would open the rating dialog
                                      toast({
                                        title: "Rate Trip",
                                        description: "Rating feature is coming soon.",
                                      });
                                    }}
                                  >
                                    <Star className="h-4 w-4 mr-2" />
                                    Rate Trip
                                  </Button>
                                )}
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  className="w-full"
                                  onClick={() => {
                                    // This would open the booking dialog with prefilled values
                                    router.push('/customer/quotes/new?prefill=' + trip.id);
                                  }}
                                >
                                  <Plus className="h-4 w-4 mr-2" />
                                  Book Similar Trip
                                </Button>
                              </div>
                            </div>
                          </div>
                        </Card>
                      ))}
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </>
          )}
        </TabsContent>
      </Tabs>

      {/* Quote Details Dialog */}
      <Dialog open={selectedQuote !== null} onOpenChange={(open) => !open && setSelectedQuote(null)}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2 text-green-500" />
              Quote Details
            </DialogTitle>
            <DialogDescription>
              Complete information about your transportation quote
            </DialogDescription>
          </DialogHeader>
          
          {selectedQuote && (
            <div className="space-y-6 py-4">
              {/* Quote Status */}
              <div className="flex items-center justify-between">
                <Badge variant={
                  selectedQuote.status === 'pending' ? 'outline' : 
                  selectedQuote.status === 'accepted' ? 'default' : 'secondary'
                }>
                  {selectedQuote.status}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  Quote #{selectedQuote.id.substring(0, 8)}
                </span>
              </div>
              
              {/* Quote Date and Time */}
              <div className="bg-muted p-4 rounded-lg">
                <h3 className="font-medium mb-2 flex items-center">
                  <Calendar className="h-4 w-4 mr-2" />
                  Date & Time
                </h3>
                <p className="text-sm">{formatDate(selectedQuote.date)}</p>
              </div>
              
              {/* Locations */}
              <div className="space-y-4">
                <div className="bg-muted p-4 rounded-lg">
                  <h3 className="font-medium mb-2 flex items-center">
                    <MapPin className="h-4 w-4 mr-2" />
                    Pickup Location
                  </h3>
                  <p className="text-sm">{selectedQuote.pickup_location}</p>
                </div>
                
                <div className="bg-muted p-4 rounded-lg">
                  <h3 className="font-medium mb-2 flex items-center">
                    <MapPin className="h-4 w-4 mr-2" />
                    Dropoff Location
                  </h3>
                  <p className="text-sm">{selectedQuote.dropoff_location}</p>
                </div>
              </div>
              
              {/* Price Information */}
              <div className="bg-muted p-4 rounded-lg">
                <h3 className="font-medium mb-2">Price Breakdown</h3>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Base fare</span>
                    <span>${(selectedQuote.price * 0.8).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Service fee</span>
                    <span>${(selectedQuote.price * 0.15).toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Taxes</span>
                    <span>${(selectedQuote.price * 0.05).toFixed(2)}</span>
                  </div>
                  <Separator className="my-2" />
                  <div className="flex justify-between font-medium">
                    <span>Total</span>
                    <span>${selectedQuote.total_amount?.toFixed(2) || '0.00'}</span>
                  </div>
                </div>
              </div>
              
              {/* Actions */}
              {selectedQuote.status === 'pending' && (
                <div className="flex gap-3">
                  <Button 
                    className="flex-1" 
                    onClick={() => {
                      handleQuoteAction(selectedQuote.id, 'accept');
                      setSelectedQuote(null);
                    }}
                  >
                    Accept Quote
                  </Button>
                  <Button 
                    variant="outline" 
                    className="flex-1"
                    onClick={() => {
                      handleQuoteAction(selectedQuote.id, 'reject');
                      setSelectedQuote(null);
                    }}
                  >
                    Reject
                  </Button>
                </div>
              )}
              
              {selectedQuote.status === 'rejected' && (
                <Button className="w-full" asChild>
                  <Link href="/customer/quotes/new">Request New Quote</Link>
                </Button>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
      
      {/* Trip Details Dialog */}
      <Dialog open={selectedTrip !== null} onOpenChange={(open) => !open && setSelectedTrip(null)}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Car className="h-5 w-5 mr-2 text-blue-500" />
              Trip Details
            </DialogTitle>
            <DialogDescription>
              Complete information about your transportation trip
            </DialogDescription>
          </DialogHeader>
          
          {selectedTrip && (
            <div className="space-y-6 py-4">
              {/* Trip Status */}
              <div className="flex items-center justify-between">
                <Badge variant={
                  selectedTrip.status === 'confirmed' ? 'default' : 
                  selectedTrip.status === 'completed' ? 'default' : 
                  selectedTrip.status === 'in_progress' ? 'secondary' : 
                  selectedTrip.status === 'cancelled' ? 'destructive' : 
                  'outline'
                }>
                  {selectedTrip.status === 'confirmed' ? 'Confirmed' : 
                   selectedTrip.status === 'in_progress' ? 'In Progress' : 
                   selectedTrip.status === 'completed' ? 'Completed' : 
                   selectedTrip.status === 'cancelled' ? 'Cancelled' : 
                   selectedTrip.status === 'assigned' ? 'Driver Assigned' :
                   selectedTrip.status === 'en_route' ? 'En Route' :
                   selectedTrip.status.charAt(0).toUpperCase() + selectedTrip.status.slice(1).replace('_', ' ')}
                </Badge>
                <span className="text-sm text-muted-foreground">
                  Trip #{selectedTrip.id.substring(0, 8)}
                </span>
              </div>
              
              {/* Trip Date and Time */}
              <div className="bg-muted p-4 rounded-lg">
                <h3 className="font-medium mb-2 flex items-center">
                  <Calendar className="h-4 w-4 mr-2" />
                  Date & Time
                </h3>
                <p className="text-sm">{formatDate(selectedTrip.date)}</p>
              </div>
              
              {/* Vehicle Information */}
              <div className="bg-muted p-4 rounded-lg">
                <h3 className="font-medium mb-2 flex items-center">
                  <Car className="h-4 w-4 mr-2" />
                  Vehicle
                </h3>
                <p className="text-sm">{selectedTrip.vehicle_type || 'Standard'}</p>
                {selectedTrip.passenger_count && (
                  <div className="flex items-center mt-2">
                    <User className="h-4 w-4 mr-2 text-muted-foreground" />
                    <p className="text-sm">{selectedTrip.passenger_count} Passengers</p>
                    {selectedTrip.luggage_count && (
                      <>
                        <span className="mx-2 text-muted-foreground">•</span>
                        <p className="text-sm">{selectedTrip.luggage_count} Luggage</p>
                      </>
                    )}
                  </div>
                )}
              </div>
              
              {/* Locations */}
              <div className="space-y-4">
                <div className="bg-muted p-4 rounded-lg">
                  <h3 className="font-medium mb-2 flex items-center">
                    <MapPin className="h-4 w-4 mr-2" />
                    Pickup Location
                  </h3>
                  <p className="text-sm">{selectedTrip.pickup_location}</p>
                </div>
                
                <div className="bg-muted p-4 rounded-lg">
                  <h3 className="font-medium mb-2 flex items-center">
                    <MapPin className="h-4 w-4 mr-2" />
                    Dropoff Location
                  </h3>
                  <p className="text-sm">{selectedTrip.dropoff_location}</p>
                </div>
              </div>
              
              {/* Special Requests */}
              {selectedTrip.special_requests && (
                <div className="bg-muted p-4 rounded-lg">
                  <h3 className="font-medium mb-2 flex items-center">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Special Requests
                  </h3>
                  <p className="text-sm">{selectedTrip.special_requests}</p>
                </div>
              )}
              
              {/* Driver Information */}
              {(selectedTrip.status === 'assigned' || selectedTrip.status === 'en_route' || 
                selectedTrip.status === 'in_progress') && (
                <div className="bg-muted p-4 rounded-lg">
                  <h3 className="font-medium mb-2 flex items-center">
                    <User className="h-4 w-4 mr-2" />
                    Driver
                  </h3>
                  <div className="flex items-center">
                    <Avatar className="h-10 w-10 mr-3">
                      <AvatarFallback>{selectedTrip.driver_name ? selectedTrip.driver_name.split(' ').map((n: string) => n[0]).join('') : 'DR'}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium">{selectedTrip.driver_name || 'Assigned Driver'}</p>
                      <p className="text-xs text-muted-foreground">
                        {selectedTrip.driver_rating ? `${selectedTrip.driver_rating} ★` : ''}
                        {selectedTrip.driver_trips ? ` • ${selectedTrip.driver_trips} trips` : ''}
                      </p>
                    </div>
                  </div>
                </div>
              )}
              
              {/* Price Information for completed trips */}
              {selectedTrip.status === 'completed' && selectedTrip.total_amount && (
                <div className="bg-muted p-4 rounded-lg">
                  <h3 className="font-medium mb-2">Price Breakdown</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Base fare</span>
                      <span>${(selectedTrip.total_amount * 0.8).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Service fee</span>
                      <span>${(selectedTrip.total_amount * 0.15).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Taxes</span>
                      <span>${(selectedTrip.total_amount * 0.05).toFixed(2)}</span>
                    </div>
                    <Separator className="my-2" />
                    <div className="flex justify-between font-medium">
                      <span>Total</span>
                      <span className="text-sm font-medium">${selectedTrip.total_amount.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              )}
              
              {/* Contact Options */}
              {(selectedTrip.status === 'assigned' || selectedTrip.status === 'en_route' || 
                selectedTrip.status === 'in_progress') && (
                <div className="flex gap-3">
                  <Button variant="outline" className="flex-1" onClick={() => {
                    toast({
                      title: "Contact Driver",
                      description: "This feature is coming soon.",
                    });
                  }}>
                    <Phone className="h-4 w-4 mr-2" />
                    Call Driver
                  </Button>
                  <Button variant="outline" className="flex-1" onClick={() => {
                    toast({
                      title: "Message Driver",
                      description: "This feature is coming soon.",
                    });
                  }}>
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Message
                  </Button>
                </div>
              )}
              
              {/* Action Buttons */}
              <div className="flex gap-3">
                {(selectedTrip.status === 'assigned' || selectedTrip.status === 'en_route' || 
                  selectedTrip.status === 'in_progress') && (
                  <Button className="flex-1" onClick={() => router.push(`/customer/trips/${selectedTrip.id}/track`)}>
                    Track Trip
                  </Button>
                )}
                
                {selectedTrip.status === 'completed' && !selectedTrip.is_rated && (
                  <Button className="flex-1" onClick={() => {
                    toast({
                      title: "Rate Trip",
                      description: "Rating feature is coming soon.",
                    });
                  }}>
                    <Star className="h-4 w-4 mr-2" />
                    Rate Trip
                  </Button>
                )}
                
                {selectedTrip.status === 'completed' && (
                  <Button variant="outline" className="flex-1" onClick={() => {
                    router.push('/customer/quotes/new?prefill=' + selectedTrip.id);
                  }}>
                    Book Similar Trip
                  </Button>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Add confirmation dialog */}
      <AlertDialog 
        open={confirmationDialog.isOpen} 
        onOpenChange={(isOpen) => 
          setConfirmationDialog(prev => ({ ...prev, isOpen }))
        }
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{confirmationDialog.title}</AlertDialogTitle>
            <AlertDialogDescription>
              {confirmationDialog.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmationDialog.action}>
              Continue
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
} 