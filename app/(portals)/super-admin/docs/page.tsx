"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/app/components/ui/card"
import { But<PERSON> } from "@/app/components/ui/button"
import { Book, HelpCircle, Shield, FileText, Link2, LifeBuoy } from "lucide-react"

const docsLinks = [
  {
    title: "API Reference",
    description: "Explore the full API documentation for developers.",
    icon: FileText,
    href: "https://docs.example.com/api",
  },
  {
    title: "User Guide",
    description: "Step-by-step instructions for using the platform.",
    icon: Book,
    href: "https://docs.example.com/user-guide",
  },
  {
    title: "Integration",
    description: "How to connect with third-party services and tools.",
    icon: Link2,
    href: "https://docs.example.com/integration",
  },
  {
    title: "Security",
    description: "Best practices and compliance information.",
    icon: Shield,
    href: "https://docs.example.com/security",
  },
  {
    title: "FAQ",
    description: "Frequently asked questions and troubleshooting.",
    icon: HelpCircle,
    href: "https://docs.example.com/faq",
  },
  {
    title: "Support",
    description: "Contact support or open a ticket.",
    icon: LifeBuoy,
    href: "mailto:<EMAIL>",
  },
]

export default function SuperAdminDocsPage() {
  return (
    <div className="space-y-8 max-w-5xl mx-auto p-6">
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold tracking-tight">Documentation</h1>
        <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
          Find guides, API references, and support resources for the platform. Use the links below to access key documentation areas.
        </p>
      </div>
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
        {docsLinks.map(link => (
          <Card key={link.title} className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center gap-3 pb-2">
              <link.icon className="h-6 w-6 text-primary" />
              <CardTitle className="text-lg font-semibold">{link.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="mb-4">{link.description}</CardDescription>
              <Button asChild variant="outline" className="w-full">
                <a href={link.href} target="_blank" rel="noopener noreferrer">
                  {link.title === "Support" ? "Contact Support" : "View Documentation"}
                </a>
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
      {/* TODO: Integrate with real documentation search or embed docs here */}
    </div>
  )
} 