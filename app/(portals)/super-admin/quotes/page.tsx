"use client";

import { useState, useEffect } from "react";
import { ViewModeProvider } from "@/app/contexts/ViewModeContext";
import { ViewModeSelector } from "@/app/components/shared/ViewModeSelector";
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import {
  Send,
  Filter,
  Search,
  Calendar,
  Plus,
  ChevronDown,
  Building2,
  DollarSign,
  BarChart3,
  Clock,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Users,
  Car,
} from "lucide-react";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/app/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import { Badge } from "@/app/components/ui/badge";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from "@/app/components/ui/card";
import { QuoteRow, QuoteRowData } from "@/app/components/shared/rows/QuoteRow";
import { toast } from "@/app/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { QuoteActionPanel } from "@/app/components/features/quotes/panels/quote-action-panel";
import { Label } from "@/app/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/app/components/ui/radio-group";
import { Checkbox } from "@/app/components/ui/checkbox";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
  SheetFooter,
} from "@/app/components/ui/sheet";
import {
  sendQuoteToAffiliates,
  getQuoteResponses,
  type QuoteResponse,
} from "@/lib/api/quote-responses";
import { QuoteStatusDebugger, QuoteStatusFixerTool } from "./debug";
import { Quote as ActionPanelQuote } from "@/app/components/features/quotes/types";
import {
  transitionToQuoteReady,
  transitionToRateRequested,
} from "@/lib/utils/quote-status-transitions";
import { CommonTabs, TabItem } from "@/app/components/shared/tabs/CommonTabs";
import { useGlobalFilters } from "@/app/contexts/GlobalFilterContext";
import { QuoteAnalyticsDashboard } from "./components/QuoteAnalyticsDashboard";
import { QuotePerformanceMetrics } from "./components/QuotePerformanceMetrics";
import { QuoteBulkOperations } from "./components/QuoteBulkOperations";
import { VehicleInventoryBrowser } from "./components/VehicleInventoryBrowser";

interface LoadingState {
  [key: string]: boolean;
}

interface Quote
  extends Omit<QuoteRowData, "status">,
    Omit<ActionPanelQuote, keyof QuoteRowData> {
  status: ActionPanelQuote["status"];
}

export default function QuotesPage() {
  const [quotes, setQuotes] = useState<QuoteRowData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedQuote, setSelectedQuote] = useState<string | null>(null);
  const [filteredQuotes, setFilteredQuotes] = useState<{
    all: QuoteRowData[];
    pending: QuoteRowData[];
    rate_requested: QuoteRowData[];
    fixed_offer: QuoteRowData[];
    assigned: QuoteRowData[];
    accepted: QuoteRowData[];
    rejected: QuoteRowData[];
    responses: QuoteRowData[];
  }>({
    all: [],
    pending: [],
    rate_requested: [],
    fixed_offer: [],
    assigned: [],
    accepted: [],
    rejected: [],
    responses: [],
  });
  const [searchTerm, setSearchTerm] = useState("");
  const router = useRouter();

  // Modal states
  const [showSendToAffiliatesModal, setShowSendToAffiliatesModal] =
    useState(false);
  const [showFixedRateModal, setShowFixedRateModal] = useState(false);
  const [activeQuote, setActiveQuote] = useState<QuoteRowData | null>(null);
  const [quoteModalTab, setQuoteModalTab] = useState<string>("details");
  const [selectedAffiliates, setSelectedAffiliates] = useState<string[]>([]);
  const [rateType, setRateType] = useState<"fixed" | "request">("request");
  const [affiliates, setAffiliates] = useState<any[]>([]);
  const [showQuoteDetailsModal, setShowQuoteDetailsModal] = useState(false);

  // Add state for expiry time
  const [responseDeadline, setResponseDeadline] = useState<string>("24h");

  // Add state for FixedRateSheet inputs
  const [fixedBaseRate, setFixedBaseRate] = useState<string>("");
  const [fixedGratuity, setFixedGratuity] = useState<string>("");
  const [fixedAdminFee, setFixedAdminFee] = useState<string>("");
  const [fixedAdditionalServicesFee, setFixedAdditionalServicesFee] =
    useState<string>("");

  // Add a debug mode state
  const [debugMode, setDebugMode] = useState(false);

  const [loadingQuotes, setLoadingQuotes] = useState<LoadingState>({});
  const { selectedOrg, dateRange } = useGlobalFilters();

  // Bulk operations state
  const [selectedQuotes, setSelectedQuotes] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState("quotes");

  // Fetch affiliates data
  useEffect(() => {
    const fetchAffiliates = async () => {
      try {
        // TODO: Update to a proper super-admin endpoint. Potentially pass tenantId if affiliates are tenant-specific.
        const tenantId =
          selectedOrg && selectedOrg !== "all" ? selectedOrg.id : undefined;
        const url = tenantId
          ? `/api/super-admin/affiliates?tenantId=${tenantId}`
          : "/api/super-admin/affiliates";
        const response = await fetch(url); // Use the constructed URL
        if (response.ok) {
          const data = await response.json();
          setAffiliates(data.affiliates || []);
        } else {
          console.warn(
            "Failed to fetch affiliates for super-admin:",
            response.status
          );
          setAffiliates([]); // Clear or set to empty on error
        }
      } catch (error) {
        console.error("Error fetching affiliates for super-admin:", error);
        setAffiliates([]); // Clear or set to empty on error
      }
    };

    fetchAffiliates();
  }, [selectedOrg]);

  useEffect(() => {
    let cleanupFunction = () => {}; // Default no-op cleanup
    let subscription: any; // Declare subscription here to be accessible in cleanup

    const fetchQuotes = async () => {
      try {
        setLoading(true);

        // Build query parameters for super-admin API
        const params = new URLSearchParams();

        // Add organization filter
        if (selectedOrg && selectedOrg !== "all") {
          params.set("org_id", selectedOrg.id);
        }

        // Add date range filters
        if (dateRange.from) {
          params.set("from", dateRange.from.toISOString());
        }
        if (dateRange.to) {
          params.set("to", dateRange.to.toISOString());
        }

        // Fetch quotes using super-admin API endpoint
        const response = await fetch(
          `/api/super-admin/quotes?${params.toString()}`
        );

        if (!response.ok) {
          throw new Error(`Failed to fetch quotes: ${response.statusText}`);
        }

        const data = await response.json();
        const mappedQuotes = data.quotes || [];

        console.log(
          `Super Admin portal fetched ${
            mappedQuotes.length
          } quotes with filters: org=${
            selectedOrg === "all" ? "all" : selectedOrg?.id
          }, dateRange: ${JSON.stringify(dateRange)}`
        );

        // Auto-fix any inconsistent statuses
        const quotesWithInconsistencies = mappedQuotes.filter(
          (q: any) =>
            q.affiliate_responses &&
            q.affiliate_responses.length > 0 &&
            (q.status === "pending" || q.status === "pending_quote")
        );

        if (quotesWithInconsistencies.length > 0) {
          console.log(
            "Found quotes with inconsistent statuses:",
            quotesWithInconsistencies.length
          );
          const fixedQuotes = [...mappedQuotes];
          for (const q of quotesWithInconsistencies) {
            const index = fixedQuotes.findIndex((fq) => fq.id === q.id);
            if (index !== -1) {
              fixedQuotes[index].status = "sent_to_affiliates";
            }
          }
          setQuotes(fixedQuotes);
        } else {
          setQuotes(mappedQuotes);
        }

        // Initialize real-time updates AFTER setting quotes
        // Import the real-time updates utility
        const { initializeRealTimeUpdates } = await import(
          "@/lib/utils/quote-realtime-updates"
        );
        subscription = initializeRealTimeUpdates();

        // Assign the actual cleanup function
        cleanupFunction = () => {
          // Check if subscription and its unsubscribe method exist
          if (subscription && typeof subscription.unsubscribe === "function") {
            subscription.unsubscribe();
          } else {
            console.warn(
              "Attempted to unsubscribe from an invalid or non-existent global quote subscription."
            );
          }
        };
      } catch (err) {
        console.error("Error fetching quotes:", err);
        setError(err instanceof Error ? err.message : "An error occurred");
      } finally {
        setLoading(false);
      }
    };

    fetchQuotes(); // Call the async function

    // Return the cleanup function (either default or the assigned one)
    return () => {
      cleanupFunction();
    };
  }, [selectedOrg, dateRange]);

  // Set up real-time updates for individual quotes
  useEffect(() => {
    let cleanupFunction = () => {}; // Default no-op cleanup

    const setupQuoteSubscriptions = async () => {
      try {
        const { subscribeToQuoteUpdates } = await import(
          "@/lib/utils/quote-realtime-updates"
        );

        // Create a map to store unsubscribe functions
        const unsubscribeFunctions: (() => void)[] = [];

        // Subscribe to updates for each quote
        quotes.forEach((quote) => {
          const unsubscribe = subscribeToQuoteUpdates(
            quote.id,
            (updatedQuote) => {
              // Handle quote updates
              setQuotes((prevQuotes) => {
                // Create a new array with the updated quote
                return prevQuotes.map((q) =>
                  q.id === updatedQuote.id ? updatedQuote : q
                );
              });

              // If this is the selected quote, update the active quote
              if (activeQuote && activeQuote.id === updatedQuote.id) {
                setActiveQuote(updatedQuote);
              }
            }
          );

          // Only add to array if unsubscribe is a function
          if (typeof unsubscribe === "function") {
            unsubscribeFunctions.push(unsubscribe);
          } else {
            console.warn(
              `Failed to set up subscription for quote ${quote.id} or unsubscribe function was not returned.`
            );
          }
        });

        // Assign the actual cleanup function
        cleanupFunction = () => {
          unsubscribeFunctions.forEach((unsubscribe) => unsubscribe());
        };
      } catch (error) {
        console.error("Error setting up quote subscriptions:", error);
        // No return needed here, cleanupFunction remains no-op on error
      }
    };

    // Only set up subscriptions if we have quotes
    if (quotes.length > 0) {
      setupQuoteSubscriptions(); // Call the async function
    }

    // Return the cleanup function (either default or the assigned one)
    return () => {
      cleanupFunction();
    };
  }, [quotes, activeQuote]);

  useEffect(() => {
    // More forgiving status matchers
    const isPendingStatus = (status: string) => {
      // First, check if status is null or undefined
      if (!status) return false;

      // Normalize the status to lowercase and trim
      const normalizedStatus = status.toLowerCase().trim();

      // Expanding the list of statuses that should be considered as "pending"
      return ["pending", "pending_quote", "new", "pending quote"].includes(
        normalizedStatus
      );
    };

    const isRateRequestedStatus = (status: string) =>
      [
        "rate_requested",
        "sent_to_affiliates",
        "rate-requested",
        "sent-to-affiliates",
      ].includes(status?.toLowerCase?.() || "");

    const isFixedOfferStatus = (status: string) =>
      [
        "fixed_offer",
        "quote_ready",
        "fixed-offer",
        "quote-ready",
        "quote-assigned",
        "quote_assigned",
      ].includes(status?.toLowerCase?.() || "");

    const isAssignedStatus = (status: string) =>
      ["quote_assigned", "assigned", "quote-assigned"].includes(
        status?.toLowerCase?.() || ""
      );

    const isAcceptedStatus = (status: string) =>
      (status?.toLowerCase?.() || "") === "accepted";

    const isRejectedStatus = (status: string) =>
      (status?.toLowerCase?.() || "") === "rejected";

    // Function to check if a quote has affiliate responses
    const hasAffiliateResponses = (quote: QuoteRowData) =>
      (quote?.affiliate_responses && quote.affiliate_responses.length > 0) ||
      (quote?.rate_proposals && quote.rate_proposals.length > 0);

    console.log("Filtering quotes...");

    // Log a sample of quotes to see what we're working with
    if (quotes.length > 0) {
      console.log("Sample quote data:", {
        id: quotes[0].id,
        status: quotes[0].status,
        hasResponses: hasAffiliateResponses(quotes[0]),
        responseCount: quotes[0].affiliate_responses?.length || 0,
      });
    }

    // For quotes with affiliate responses, they should NOT be in pending status
    // Let's do immediate client-side fixing
    const fixedQuotes = quotes.map((quote) => {
      // Don't change status if it's already in a valid state
      if (
        isFixedOfferStatus(quote.status) ||
        isRateRequestedStatus(quote.status)
      ) {
        return quote;
      }

      // Only fix quotes that are in pending but have responses
      if (hasAffiliateResponses(quote) && isPendingStatus(quote.status)) {
        console.log(
          `Client fix: Quote ${quote.id} has responses but status is still ${quote.status}, fixing...`
        );

        // Check if this is a fixed offer quote
        const hasFixedOfferType = quote.affiliate_responses?.some(
          (r) => (r as any).type === "fixed_offer" || r.status === "fixed_offer"
        );

        // Determine the correct status
        const newStatus = hasFixedOfferType ? "quote_ready" : "rate_requested";

        console.log(
          `Updating quote ${quote.id} status from ${quote.status} to ${newStatus}`
        );

        // Update the server
        fetch(`/api/admin/quotes/${quote.id}/status`, {
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ status: newStatus }),
        }).catch((error) => {
          console.error(`Error updating quote ${quote.id} status:`, error);
        });

        return {
          ...quote,
          status: newStatus,
        } as QuoteRowData;
      }
      return quote;
    });

    // Filter quotes based on search and status
    const filtered = {
      all: fixedQuotes.filter((quote) => filterBySearch(quote)),
      pending: fixedQuotes.filter((quote) => {
        console.log(
          `Checking if quote ${quote.id} status "${quote.status}" is pending`
        );
        return filterBySearch(quote) && isPendingStatus(quote.status);
      }),
      rate_requested: fixedQuotes.filter(
        (quote) => filterBySearch(quote) && isRateRequestedStatus(quote.status)
      ),
      fixed_offer: fixedQuotes.filter(
        (quote) => filterBySearch(quote) && isFixedOfferStatus(quote.status)
      ),
      assigned: fixedQuotes.filter(
        (quote) => filterBySearch(quote) && isAssignedStatus(quote.status)
      ),
      accepted: fixedQuotes.filter(
        (quote) => filterBySearch(quote) && isAcceptedStatus(quote.status)
      ),
      rejected: fixedQuotes.filter(
        (quote) => filterBySearch(quote) && isRejectedStatus(quote.status)
      ),
      responses: fixedQuotes.filter(
        (quote) =>
          filterBySearch(quote) &&
          hasAffiliateResponses(quote) &&
          // Exclude quotes that already appear in other specific tabs
          !isRateRequestedStatus(quote.status) &&
          !isFixedOfferStatus(quote.status) &&
          !isAssignedStatus(quote.status) &&
          !isAcceptedStatus(quote.status) &&
          !isRejectedStatus(quote.status)
      ),
    };

    console.log("Filtered quote counts:", {
      all: filtered.all.length,
      pending: filtered.pending.length,
      rate_requested: filtered.rate_requested.length,
      fixed_offer: filtered.fixed_offer.length,
      assigned: filtered.assigned.length,
      accepted: filtered.accepted.length,
      rejected: filtered.rejected.length,
      responses: filtered.responses.length,
      rawData: quotes.map((q) => ({ id: q.id, status: q.status })),
    });

    // Log statuses that might not be categorized correctly
    const uncategorizedQuotes = quotes.filter((quote) => {
      const status = quote.status.toLowerCase();
      return (
        !isPendingStatus(status) &&
        !isRateRequestedStatus(status) &&
        !isFixedOfferStatus(status) &&
        !isAssignedStatus(status) &&
        !isAcceptedStatus(status) &&
        !isRejectedStatus(status)
      );
    });

    if (uncategorizedQuotes.length > 0) {
      console.log(
        "Uncategorized quotes:",
        uncategorizedQuotes.map((q) => ({
          id: q.id,
          status: q.status,
        }))
      );
    }

    setFilteredQuotes(filtered);
  }, [quotes, searchTerm]);

  const filterBySearch = (quote: QuoteRowData) => {
    if (!searchTerm) return true;

    const search = searchTerm.toLowerCase();
    return (
      (quote.customer?.full_name &&
        quote.customer.full_name.toLowerCase().includes(search)) ||
      (quote.reference_number &&
        quote.reference_number.toLowerCase().includes(search)) ||
      (quote.pickup_location &&
        quote.pickup_location.toLowerCase().includes(search)) ||
      (quote.dropoff_location &&
        quote.dropoff_location.toLowerCase().includes(search))
    );
  };

  const handleQuoteSelection = (quote: QuoteRowData) => {
    setActiveQuote(quote);
    setSelectedQuote(quote.id === selectedQuote ? null : quote.id);

    // Show the quote details modal
    setShowQuoteDetailsModal(true);
  };

  const handleSendToAffiliates = (quoteId: string) => {
    // Get the quote details
    const quote = quotes.find((q) => q.id === quoteId);
    if (!quote) return;

    // Show the send to affiliates modal instead of navigating
    setActiveQuote(quote);
    setRateType("request");
    setSelectedAffiliates([]);
    setShowSendToAffiliatesModal(true);
  };

  const handleFixedRate = (quoteId: string) => {
    // Get the quote details
    const quote = quotes.find((q) => q.id === quoteId);
    if (!quote) return;

    // Show the fixed rate modal
    setActiveQuote(quote);
    setRateType("fixed");
    setShowFixedRateModal(true);
  };

  const handleArchive = (quoteId: string) => {
    console.log("Archiving quote:", quoteId);
    // In the future, this would update the quote status to "archived" in the database
    // For now, just log the action
    toast({
      title: "Quote marked for archiving",
      description: "This functionality will be implemented in a future update.",
    });
  };

  const handleSubmitRateRequest = async () => {
    if (!activeQuote) return;

    try {
      console.log("Submitting quote:", {
        id: activeQuote.id,
        status: activeQuote.status,
        rateType,
        affiliates: selectedAffiliates,
        deadline: responseDeadline,
      });

      // Use the proper function directly instead of calling a non-existent API
      const { success, error } = await sendQuoteToAffiliates(
        activeQuote.id,
        selectedAffiliates,
        rateType === "fixed" ? "fixed_offer" : "rate_request",
        parseDeadlineToHours(responseDeadline), // Use the selected deadline
        undefined // No override rate
      );

      console.log("Response from sendQuoteToAffiliates:", { success, error });

      if (error) {
        console.error("Error sending quote to affiliates:", error);
        toast({
          title: "Error",
          description: "Failed to process quote: " + error.message,
          variant: "destructive",
        });
        return;
      }

      if (success) {
        toast({
          title: "Success",
          description:
            rateType === "fixed"
              ? "Fixed rate quote has been created"
              : "Rate request has been sent to affiliates",
        });

        // Refresh the quotes list
        console.log("Refreshing quotes list after successful submission");
        await refreshQuotes();
        console.log("Quotes refreshed");
      } else {
        toast({
          title: "Error",
          description: "Failed to process quote",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error processing quote:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      // Close modals
      setShowSendToAffiliatesModal(false);
      setShowFixedRateModal(false);
    }
  };

  const handleSubmitFixedRateOffer = async () => {
    if (!activeQuote) {
      toast({
        title: "Error",
        description: "No active quote selected.",
        variant: "destructive",
      });
      return;
    }
    if (selectedAffiliates.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one affiliate.",
        variant: "destructive",
      });
      // Potentially open the affiliate selection modal here or guide the user
      // For now, just showing an error.
      return;
    }

    const base = parseFloat(fixedBaseRate) || 0;
    const gratuity = parseFloat(fixedGratuity) || 0;
    const adminFee = parseFloat(fixedAdminFee) || 0;
    const additional = parseFloat(fixedAdditionalServicesFee) || 0;
    const totalFixedRate = base + gratuity + adminFee + additional;

    if (totalFixedRate <= 0) {
      toast({
        title: "Error",
        description: "Total fixed rate must be greater than 0.",
        variant: "destructive",
      });
      return;
    }

    console.log("Submitting Fixed Rate Offer:", {
      quoteId: activeQuote.id,
      affiliates: selectedAffiliates,
      totalFixedRate,
      deadline: responseDeadline,
    });

    setLoadingQuotes((prev) => ({ ...prev, [activeQuote.id]: true }));
    const { success, error } = await sendQuoteToAffiliates(
      activeQuote.id,
      selectedAffiliates, // This uses the general selectedAffiliates state. Needs review for UI flow.
      "fixed_offer",
      parseDeadlineToHours(responseDeadline),
      totalFixedRate
    );
    setLoadingQuotes((prev) => ({ ...prev, [activeQuote.id]: false }));

    if (error) {
      console.error("Error sending fixed rate offer:", error);
      toast({
        title: "Error",
        description:
          "Failed to send fixed rate offer: " +
          ((error as Error).message || "Unknown error"),
        variant: "destructive",
      });
    } else {
      toast({
        description:
          "Fixed rate offer sent successfully to selected affiliates.",
      });
      setShowFixedRateModal(false); // Close the sheet on success
      // Optionally reset fixed rate form fields
      setFixedBaseRate("");
      setFixedGratuity("");
      setFixedAdminFee("");
      setFixedAdditionalServicesFee("");
      refreshQuotes(); // Refresh quotes to reflect status changes
    }
  };

  // Helper function to convert QuoteRowData to the Quote type expected by QuoteActionPanel
  const adaptQuoteRowDataToQuote = (
    quoteData: QuoteRowData
  ): ActionPanelQuote => {
    // 1. Map status
    let mappedStatus: ActionPanelQuote["status"] = "pending"; // Default ActionPanelQuote status
    const currentStatus = quoteData.status
      ? quoteData.status.toLowerCase()
      : "pending";
    // This mapping assumes ActionPanelQuote.status is a general string.
    // If ActionPanelQuote.status is a specific literal union, this mapping needs to be more precise.
    if (["new", "pending", "pending_quote"].includes(currentStatus)) {
      mappedStatus = "pending";
    } else if (
      ["rate_requested", "sent_to_affiliates"].includes(currentStatus)
    ) {
      mappedStatus = "rate_requested";
    } else if (
      ["fixed_offer", "quote_ready", "quote_assigned"].includes(currentStatus)
    ) {
      mappedStatus = "fixed_offer";
    } else if (currentStatus === "accepted") {
      mappedStatus = "accepted";
    } else if (currentStatus === "rejected") {
      mappedStatus = "rejected";
    } else {
      mappedStatus = currentStatus; // Pass through if not explicitly mapped
    }

    // 2. Map customer details
    const customerName =
      quoteData.customer?.full_name || quoteData.customer_id || "N/A";
    const customerEmail = quoteData.customer?.email || "N/A";
    const customerPhone = quoteData.customer?.phone || "N/A";

    // 3. Map trip_date
    let tripDate = new Date().toISOString(); // Fallback
    if (quoteData.date) {
      try {
        tripDate = quoteData.time
          ? new Date(`${quoteData.date}T${quoteData.time}`).toISOString()
          : new Date(quoteData.date).toISOString();
      } catch (e) {
        console.warn(
          `Error parsing date/time for quote ${quoteData.id}: ${quoteData.date} ${quoteData.time}`,
          e
        );
      }
    }

    // 4. Map responses (affiliate_responses and rate_proposals)
    const adaptedResponses: ActionPanelQuote["responses"] = [];
    if (quoteData.affiliate_responses) {
      quoteData.affiliate_responses.forEach((res) => {
        adaptedResponses.push({
          id:
            (res as any).id ||
            `res_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // Ensure ID
          affiliate_name: res.affiliate_name || "Unknown Affiliate",
          rate: res.rate_amount ?? 0,
          status: String(res.status || "pending"),
          created_at: (res as any).created_at || new Date().toISOString(), // Add created_at
          description: (res as any).description || "",
          terms: (res as any).terms || "",
        });
      });
    }
    if (quoteData.rate_proposals) {
      quoteData.rate_proposals.forEach((proposal) => {
        adaptedResponses.push({
          id:
            proposal.company_id ||
            `prop_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          affiliate_name: `Affiliate ${proposal.company_id || "Proposal"}`,
          rate: proposal.rate_amount ?? 0,
          status: String(proposal.status || "pending"),
          created_at: new Date().toISOString(), // Add created_at
          description: "",
          terms: "",
        });
      });
    }

    // 5. Map notes (from timeline and quoteData.notes)
    const adaptedNotes: ActionPanelQuote["notes"] = [];
    if (quoteData.timeline) {
      quoteData.timeline.forEach((t) => {
        let noteTimestamp = new Date().toISOString();
        if (t.timestamp) {
          try {
            noteTimestamp = new Date(t.timestamp).toISOString();
          } catch (e) {
            /* fallback to now */
          }
        }
        adaptedNotes.push({
          author: t.user || "System",
          content: `${t.action || "Event"}${t.details ? ": " + t.details : ""}`,
          created_at: noteTimestamp,
        });
      });
    }
    if (typeof quoteData.notes === "string" && quoteData.notes.trim() !== "") {
      adaptedNotes.push({
        author: "System Note", // Or determine author if possible
        content: quoteData.notes,
        created_at: quoteData.updated_at || new Date().toISOString(),
      });
    }

    // 6. Construct the final ActionPanelQuote object
    return {
      id: quoteData.id,
      customer_name: customerName,
      customer_email: customerEmail,
      customer_phone: customerPhone,
      pickup_location: quoteData.pickup_location || "",
      dropoff_location: quoteData.dropoff_location || "",
      trip_date: tripDate,
      status: mappedStatus,
      total_amount: quoteData.total_amount ?? 0,
      base_rate: (quoteData as any).base_rate ?? quoteData.total_amount ?? 0, // Required by ActionPanelQuote
      markup_percentage: (quoteData as any).markup_percentage ?? 0, // Required by ActionPanelQuote
      expiry_time:
        quoteData.expiry_time ||
        new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      special_requests: Array.isArray(quoteData.special_requests)
        ? quoteData.special_requests.join(", ")
        : quoteData.special_requests || "",
      vehicle_type: quoteData.vehicle_type || "",
      affiliate_name: (quoteData as any).affiliate_name || "", // Optional in ActionPanelQuote
      affiliate_count: quoteData.affiliate_count ?? 0,
      responses: adaptedResponses,
      notes: adaptedNotes,
      // Optional fields in ActionPanelQuote, provide defaults or map if available
      payment_status: (quoteData as any).payment_status || "pending",
      payment_date: (quoteData as any).payment_date || "",
      declined_by: (quoteData as any).declined_by || "",
      declined_at: (quoteData as any).declined_at || "",
      decline_reason: (quoteData as any).decline_reason || "",
    };
  };

  // Function to refresh quotes
  const refreshQuotes = async () => {
    try {
      console.log("Starting quote refresh");
      setLoading(true);
      const response = await fetch("/api/admin/quotes");

      console.log("Quote API response status:", response.status);

      if (!response.ok) {
        throw new Error(`Error: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(
        "Quote API data received, quotes count:",
        data.quotes?.length || 0
      );

      if (data && Array.isArray(data.quotes)) {
        // Map the API data to QuoteRowData format
        const mappedQuotes: QuoteRowData[] = data.quotes.map((q: any) => ({
          id: q.id || "",
          reference_number: q.reference_number || "",
          pickup_location: q.pickup_location || "",
          dropoff_location: q.dropoff_location || "",
          date: q.date || "",
          time: q.time || "",
          vehicle_type: q.vehicle_type || "",
          status: q.status || "pending",
          price: q.total_amount || 0,
          base_fare: q.base_rate || 0,
          gratuity: q.gratuity || 0,
          admin_fee: q.admin_fee || 0,
          contact_name: q.contact_name || "",
          contact_email: q.contact_email || "",
          contact_phone: q.contact_phone || "",
          passenger_count: q.passenger_count || 0,
          special_requirements: q.special_requirements || "",
          notes: q.notes || "",
          priority: q.priority || "medium",
          event_name: q.event_name || "",
          city: q.city || "",
          affiliate_count: q.affiliate_count || 0,
          expiry_time: q.expiry_time || "",
          last_updated: q.updated_at || "",
          affiliate_responses: q.affiliate_responses || [],
          rate_proposals: q.rate_proposals || [],
          is_vip: q.is_vip || false,
          timeline: q.timeline || [],
        }));

        // Helper function to determine if a status is pending
        const isPendingStatus = (status: string) => {
          // First, check if status is null or undefined
          if (!status) return false;

          // Normalize the status to lowercase and trim
          const normalizedStatus = status.toLowerCase().trim();

          // Expanding the list of statuses that should be considered as "pending"
          return ["pending", "pending_quote", "new", "pending quote"].includes(
            normalizedStatus
          );
        };

        // Client-side fix for quotes that have affiliate responses but are still in pending status
        const fixedQuotes = mappedQuotes.map((quote) => {
          if (
            ((quote.affiliate_responses &&
              quote.affiliate_responses.length > 0) ||
              (quote.rate_proposals && quote.rate_proposals.length > 0)) &&
            isPendingStatus(quote.status)
          ) {
            console.log(
              `Client fixing quote ${quote.id} from ${quote.status} to rate_requested`
            );

            // Determine if it should be a fixed offer or rate requested
            const hasFixedOfferType = quote.affiliate_responses?.some(
              (r: any) =>
                r?.type === "fixed_offer" || r?.status === "fixed_offer"
            );

            return {
              ...quote,
              status: hasFixedOfferType ? "fixed_offer" : "rate_requested",
            };
          }
          return quote;
        });

        console.log("Setting quotes state with fixed mapped data");
        setQuotes(fixedQuotes);
      } else {
        console.log("No quote data received or data is not an array");
        setQuotes([]);
      }
    } catch (err) {
      console.error("Error fetching quotes:", err);
      toast({
        title: "Error",
        description:
          err instanceof Error ? err.message : "Failed to refresh quotes list",
        variant: "destructive",
      });
    } finally {
      console.log("Quote refresh completed");
      setLoading(false);
    }
  };

  // Add a function to parse the deadline string to hours
  const parseDeadlineToHours = (deadline: string): number => {
    const value = parseInt(deadline.replace(/[^0-9]/g, ""));
    const unit = deadline.replace(/[0-9]/g, "");

    if (unit === "h") {
      return value;
    } else if (unit === "d") {
      return value * 24;
    }

    return 24; // Default to 24 hours
  };

  // Add debugging log for component render to see the current state
  useEffect(() => {
    if (quotes.length > 0) {
      // Log a few sample quotes with their statuses and responses
      const sampleQuotes = quotes.slice(0, Math.min(3, quotes.length));
      console.log(
        "Current sample quotes:",
        sampleQuotes.map((q) => ({
          id: q.id,
          status: q.status,
          hasResponses:
            q.affiliate_responses && q.affiliate_responses.length > 0,
          responseCount: q.affiliate_responses?.length || 0,
          responseTypes: q.affiliate_responses
            ?.map((r: any) => r.type || "unknown")
            .join(", "),
        }))
      );

      // Log the counts in each status category
      console.log("Current status distribution:", {
        pending: quotes.filter((q) =>
          ["pending", "pending_quote", "new"].includes(
            q.status?.toLowerCase() || ""
          )
        ).length,
        rateRequested: quotes.filter((q) =>
          ["rate_requested", "sent_to_affiliates"].includes(
            q.status?.toLowerCase() || ""
          )
        ).length,
        fixedOffer: quotes.filter((q) =>
          ["fixed_offer", "quote_ready"].includes(q.status?.toLowerCase() || "")
        ).length,
        withResponses: quotes.filter(
          (q) => q.affiliate_responses && q.affiliate_responses.length > 0
        ).length,
      });
    }
  }, [quotes]);

  const handleQuoteAction = async (
    quoteId: string,
    action: string,
    data?: any
  ) => {
    try {
      let success = false;
      let error = null;

      // Start loading state for this specific quote
      setLoadingQuotes((prev) => ({ ...prev, [quoteId]: true }));

      switch (action) {
        case "quote_sent": {
          const { affiliateIds, submissionType } = data;

          // First update the status
          const transitionResult =
            submissionType === "fixed_offer"
              ? await transitionToQuoteReady(quoteId, affiliateIds)
              : await transitionToRateRequested(quoteId, affiliateIds);

          if (!transitionResult.success) {
            throw new Error(
              `Failed to transition quote status: ${transitionResult.error?.message}`
            );
          }

          // Then send to affiliates
          const sendResult = await sendQuoteToAffiliates(
            quoteId,
            affiliateIds,
            submissionType
          );
          if (!sendResult.success) {
            throw new Error(
              `Failed to send quote to affiliates: ${sendResult.error?.message}`
            );
          }

          success = true;
          toast({
            description: "Quote sent successfully",
          });

          // For rate requests, set up polling to check for responses
          if (submissionType === "rate_request") {
            let attempts = 0;
            const maxAttempts = 3;
            const pollInterval = 2000; // 2 seconds

            const pollForResponses = async () => {
              try {
                const libResponses = await getQuoteResponses(quoteId); // Type from lib/api/quote-responses
                if (libResponses && libResponses.length > 0) {
                  // Adapt LibQuoteResponse to TypesQuoteResponse before updating state
                  const adaptedResponses = libResponses.map((res) => ({
                    // Ensure all fields for features/quotes/types.ts#QuoteResponse are present
                    id: res.id || String(Date.now() + Math.random()),
                    affiliate_name: res.affiliate_name || "Unknown Affiliate",
                    rate: res.rate ?? res.rate_amount ?? 0, // Use rate if available, else rate_amount, else 0
                    status: res.status || "pending",
                    created_at: new Date().toISOString(), // Add created_at as it's required by types.ts#QuoteResponse
                    description: "", // Not in lib/api/quote-responses#QuoteResponse, provide default for types.ts version
                    terms: "", // Not in lib/api/quote-responses#QuoteResponse, provide default for types.ts version
                  }));
                  // Update the quote in state with the new adapted responses
                  updateQuoteInState(quoteId, { responses: adaptedResponses });
                  return true;
                }

                attempts++;
                if (attempts < maxAttempts) {
                  setTimeout(pollForResponses, pollInterval);
                }
              } catch (error) {
                console.error("Error polling for responses:", error);
              }
            };

            setTimeout(pollForResponses, pollInterval);
          }
          break;
        }

        case "manually_sent": {
          const transitionResult = await transitionToQuoteReady(quoteId, []);
          if (!transitionResult.success) {
            throw new Error(
              `Failed to transition quote status: ${transitionResult.error?.message}`
            );
          }
          success = true;
          toast({
            description: "Quote marked as manually sent",
          });
          break;
        }

        // ... other cases ...
      }

      if (success) {
        // Refresh the quote list to get updated counts
        await refreshQuotes();
      }
    } catch (error) {
      console.error("Error in handleQuoteAction:", error);
      toast({
        variant: "destructive",
        description:
          error instanceof Error ? error.message : "An error occurred",
      });
    } finally {
      setLoadingQuotes((prev) => ({ ...prev, [quoteId]: false }));
    }
  };

  // Helper function to update a single quote in state
  const updateQuoteInState = (quoteId: string, updates: Partial<Quote>) => {
    setQuotes((prev) => {
      const quoteIndex = prev.findIndex((q) => q.id === quoteId);
      if (quoteIndex === -1) return prev;

      const updatedQuotes = [...prev];
      updatedQuotes[quoteIndex] = {
        ...updatedQuotes[quoteIndex],
        ...updates,
      };

      return updatedQuotes;
    });
  };

  // Create a reusable content renderer function
  const renderTabContent = (tabId: string, quotes: QuoteRowData[]) => {
    if (quotes.length === 0) {
      return (
        <div className="text-center p-4 border rounded-md">
          <p className="text-muted-foreground">
            No {tabId.replace("_", " ")} quotes found
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-2">
        {quotes.map((quote) => {
          const isLoading = !!loadingQuotes[quote.id];

          return (
            <QuoteRow
              key={quote.id}
              quote={quote}
              isSelected={selectedQuote === quote.id}
              onClick={() => handleQuoteSelection(quote)}
              onSendToAffiliates={() => handleSendToAffiliates(quote.id)}
              onFixedRate={() => handleFixedRate(quote.id)}
              onArchive={() => handleArchive(quote.id)}
              userType="admin"
              expandable={true}
            />
          );
        })}
      </div>
    );
  };

  // Create tab items for the CommonTabs component
  const tabItems: TabItem[] = [
    {
      id: "all",
      label: "All",
      count: filteredQuotes.all.length,
      content: renderTabContent("all", filteredQuotes.all),
    },
    {
      id: "pending",
      label: "Pending",
      count: filteredQuotes.pending.length,
      content: renderTabContent("pending", filteredQuotes.pending),
    },
    {
      id: "rate_requested",
      label: "Rate Requested",
      count: filteredQuotes.rate_requested.length,
      content: renderTabContent(
        "rate_requested",
        filteredQuotes.rate_requested
      ),
    },
    {
      id: "fixed_offer",
      label: "Fixed Offer",
      count: filteredQuotes.fixed_offer.length,
      content: renderTabContent("fixed_offer", filteredQuotes.fixed_offer),
    },
    {
      id: "assigned",
      label: "Assigned",
      count: filteredQuotes.assigned.length,
      content: renderTabContent("assigned", filteredQuotes.assigned),
    },
    {
      id: "accepted",
      label: "Accepted",
      count: filteredQuotes.accepted.length,
      content: renderTabContent("accepted", filteredQuotes.accepted),
    },
    {
      id: "rejected",
      label: "Rejected",
      count: filteredQuotes.rejected.length,
      content: renderTabContent("rejected", filteredQuotes.rejected),
    },
    {
      id: "responses",
      label: "Responses",
      count: filteredQuotes.responses.length,
      content: renderTabContent("responses", filteredQuotes.responses),
    },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <div className="bg-destructive/10 p-4 rounded-md text-destructive">
          <p>Error loading quotes: {error}</p>
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
            className="mt-2"
          >
            Retry
          </Button>
        </div>
      </div>
    );
  }

  // Add function to refresh quotes for bulk operations
  const handleQuotesUpdate = async () => {
    await refreshQuotes();
  };

  return (
    <ViewModeProvider>
      <div className="p-8 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">
              Quote Workflow Management
            </h1>
            <p className="text-muted-foreground">
              Comprehensive quote analytics, performance monitoring, and bulk
              operations
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <ViewModeSelector />
            <Button
              variant="destructive"
              size="sm"
              onClick={async () => {
                try {
                  setLoading(true);

                  // Helper function to check if a status is pending
                  const isPendingStatus = (status: string) => {
                    // First, check if status is null or undefined
                    if (!status) return false;

                    // Normalize the status to lowercase and trim
                    const normalizedStatus = status.toLowerCase().trim();

                    // Expanding the list of statuses that should be considered as "pending"
                    return [
                      "pending",
                      "pending_quote",
                      "new",
                      "pending quote",
                    ].includes(normalizedStatus);
                  };

                  // Force-fix on the client side first
                  const fixedQuotes = quotes.map((quote) => {
                    // Fix quotes with affiliate responses that are not in the right state
                    if (
                      quote.affiliate_responses &&
                      quote.affiliate_responses.length > 0 &&
                      isPendingStatus(quote.status)
                    ) {
                      const hasFixedOfferType = quote.affiliate_responses.some(
                        (r) =>
                          (r as any).type === "fixed_offer" ||
                          r.status === "fixed_offer"
                      );

                      return {
                        ...quote,
                        status: hasFixedOfferType
                          ? "fixed_offer"
                          : "rate_requested",
                      };
                    }

                    // Fix quotes with rate proposals but incorrect status
                    if (
                      quote.rate_proposals &&
                      quote.rate_proposals.length > 0 &&
                      isPendingStatus(quote.status)
                    ) {
                      return {
                        ...quote,
                        status: "rate_requested",
                      };
                    }

                    return quote;
                  });

                  // Count the fixes made
                  const fixCount = fixedQuotes.filter(
                    (q, i) => q.status !== quotes[i].status
                  ).length;

                  // Log what was fixed for debugging
                  if (fixCount > 0) {
                    console.log(
                      "Fixed quotes:",
                      fixedQuotes
                        .filter((q, i) => q.status !== quotes[i].status)
                        .map((q) => ({
                          id: q.id,
                          reference: q.reference_number,
                          oldStatus: quotes.find((oq) => oq.id === q.id)
                            ?.status,
                          newStatus: q.status,
                          hasResponses:
                            (q.affiliate_responses?.length || 0) > 0 ||
                            (q.rate_proposals?.length || 0) > 0,
                        }))
                    );
                  }

                  // Update the state immediately for better UX
                  setQuotes(fixedQuotes);

                  toast({
                    title: `${fixCount} quotes fixed`,
                    description:
                      fixCount > 0
                        ? "Quote statuses have been updated to match their responses"
                        : "No inconsistent quotes found",
                    variant: fixCount > 0 ? "default" : undefined,
                  });

                  // Refresh to make sure we have the latest data
                  await refreshQuotes();
                } catch (error) {
                  console.error("Error forcing update:", error);
                  toast({
                    title: "Error",
                    description:
                      error instanceof Error
                        ? error.message
                        : "Failed to update quotes",
                    variant: "destructive",
                  });
                } finally {
                  setLoading(false);
                }
              }}
            >
              Force Fix Quotes
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setDebugMode(!debugMode)}
            >
              {debugMode ? "Hide Debug" : "Show Debug"}
            </Button>
            <Button onClick={() => router.push("/admin/quotes/new")}>
              <Plus className="mr-2 h-4 w-4" /> New Quote
            </Button>
          </div>
        </div>

        {/* Main Analytics Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger
              value="analytics"
              className="flex items-center space-x-2"
            >
              <BarChart3 className="h-4 w-4" />
              <span>Analytics</span>
            </TabsTrigger>
            <TabsTrigger
              value="performance"
              className="flex items-center space-x-2"
            >
              <TrendingUp className="h-4 w-4" />
              <span>Performance</span>
            </TabsTrigger>
            <TabsTrigger
              value="inventory"
              className="flex items-center space-x-2"
            >
              <Car className="h-4 w-4" />
              <span>Vehicle Inventory</span>
            </TabsTrigger>
            <TabsTrigger value="quotes" className="flex items-center space-x-2">
              <Users className="h-4 w-4" />
              <span>Quote Management</span>
            </TabsTrigger>
            <TabsTrigger value="bulk" className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4" />
              <span>Bulk Operations</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="analytics" className="space-y-6">
            <QuoteAnalyticsDashboard
              dateRange={
                dateRange.from && dateRange.to
                  ? { from: dateRange.from, to: dateRange.to }
                  : undefined
              }
              selectedOrg={
                typeof selectedOrg === "object" ? selectedOrg?.id : selectedOrg
              }
            />
          </TabsContent>

          <TabsContent value="performance" className="space-y-6">
            <QuotePerformanceMetrics
              dateRange={
                dateRange.from && dateRange.to
                  ? { from: dateRange.from, to: dateRange.to }
                  : undefined
              }
              selectedOrg={
                typeof selectedOrg === "object" ? selectedOrg?.id : selectedOrg
              }
            />
          </TabsContent>

          <TabsContent value="inventory" className="space-y-6">
            <VehicleInventoryBrowser />
          </TabsContent>

          <TabsContent value="quotes" className="space-y-6">
            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-5">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Total Quotes
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{quotes.length}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Pending</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {filteredQuotes.pending.length}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Rate Requested
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {filteredQuotes.rate_requested.length}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Fixed Offers
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {filteredQuotes.fixed_offer.length}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Assigned
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {filteredQuotes.assigned.length}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Search & Filters */}
            <div className="flex items-center space-x-2">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search quotes..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
              <Select defaultValue="today">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select date range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="yesterday">Yesterday</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                  <SelectItem value="custom">Custom Range</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Quotes List */}
            <div className="space-y-6">
              <CommonTabs
                tabs={tabItems}
                defaultTab="pending"
                fullWidth={true}
              />
            </div>
          </TabsContent>

          <TabsContent value="bulk" className="space-y-6">
            <QuoteBulkOperations
              quotes={quotes.map((q) => ({
                ...q,
                reference_number: q.reference_number || q.id,
                total_amount: q.total_amount ?? undefined,
              }))}
              selectedQuotes={selectedQuotes}
              onSelectionChange={setSelectedQuotes}
              onQuotesUpdate={handleQuotesUpdate}
            />

            {/* Show selected quotes for bulk operations */}
            {selectedQuotes.length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">
                  Selected Quotes ({selectedQuotes.length})
                </h3>
                <div className="space-y-2">
                  {quotes
                    .filter((q) => selectedQuotes.includes(q.id))
                    .map((quote) => (
                      <QuoteRow
                        key={quote.id}
                        quote={quote}
                        isSelected={true}
                        onClick={() => {
                          // Toggle selection
                          setSelectedQuotes((prev) =>
                            prev.includes(quote.id)
                              ? prev.filter((id) => id !== quote.id)
                              : [...prev, quote.id]
                          );
                        }}
                        onSendToAffiliates={() =>
                          handleSendToAffiliates(quote.id)
                        }
                        onFixedRate={() => handleFixedRate(quote.id)}
                        onArchive={() => handleArchive(quote.id)}
                        userType="admin"
                        expandable={true}
                      />
                    ))}
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Quote Action Panel */}
        {showQuoteDetailsModal && activeQuote && (
          <QuoteActionPanel
            quote={adaptQuoteRowDataToQuote(activeQuote)}
            onClose={() => setShowQuoteDetailsModal(false)}
            onAction={(action, data) => {
              console.log("Quote action:", action, data);

              // Handle different action types
              handleQuoteAction(activeQuote.id, action, data);
            }}
          />
        )}

        {/* Send to Affiliates Sheet */}
        <Sheet
          open={showSendToAffiliatesModal}
          onOpenChange={setShowSendToAffiliatesModal}
        >
          <SheetContent className="sm:max-w-md md:max-w-lg lg:max-w-xl overflow-y-auto">
            <SheetHeader>
              <SheetTitle>Send Quote for Rates</SheetTitle>
              <SheetDescription>
                Select affiliates to receive this quote request
              </SheetDescription>
            </SheetHeader>

            <Tabs defaultValue="affiliates" className="mt-4">
              <TabsList>
                <TabsTrigger value="affiliates">Affiliates</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
              </TabsList>

              <TabsContent value="affiliates" className="space-y-4">
                <div className="flex justify-between">
                  <RadioGroup
                    value={rateType}
                    onValueChange={(value) =>
                      setRateType(value as "fixed" | "request")
                    }
                    className="flex space-x-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="request" id="request-rate" />
                      <Label htmlFor="request-rate">Request Rate</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="fixed" id="fixed-rate" />
                      <Label htmlFor="fixed-rate">Fixed Rate</Label>
                    </div>
                  </RadioGroup>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (selectedAffiliates.length === affiliates.length) {
                        setSelectedAffiliates([]);
                      } else {
                        setSelectedAffiliates(affiliates.map((a) => a.id));
                      }
                    }}
                  >
                    {selectedAffiliates.length === affiliates.length
                      ? "Deselect All"
                      : "Select All"}
                  </Button>
                </div>

                <div className="max-h-[300px] overflow-y-auto space-y-2">
                  {affiliates.map((affiliate) => (
                    <Card key={affiliate.id} className="p-2">
                      <div className="flex items-start">
                        <Checkbox
                          id={`affiliate-${affiliate.id}`}
                          checked={selectedAffiliates.includes(affiliate.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedAffiliates([
                                ...selectedAffiliates,
                                affiliate.id,
                              ]);
                            } else {
                              setSelectedAffiliates(
                                selectedAffiliates.filter(
                                  (id) => id !== affiliate.id
                                )
                              );
                            }
                          }}
                        />
                        <div className="ml-3 flex-1">
                          <Label
                            htmlFor={`affiliate-${affiliate.id}`}
                            className="font-medium"
                          >
                            {affiliate.name}
                          </Label>
                          <div className="text-xs text-muted-foreground">
                            {affiliate.city}, {affiliate.state}
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}

                  {affiliates.length === 0 && (
                    <div className="text-center p-4 border rounded-md">
                      <p className="text-muted-foreground">
                        No affiliates found
                      </p>
                    </div>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="settings">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="expiry-time">Response deadline</Label>
                    <Select
                      defaultValue="24h"
                      value={responseDeadline}
                      onValueChange={setResponseDeadline}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select deadline" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="4h">4 hours</SelectItem>
                        <SelectItem value="8h">8 hours</SelectItem>
                        <SelectItem value="12h">12 hours</SelectItem>
                        <SelectItem value="24h">24 hours</SelectItem>
                        <SelectItem value="48h">48 hours</SelectItem>
                        <SelectItem value="3d">3 days</SelectItem>
                        <SelectItem value="7d">7 days</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox id="auto-accept" />
                    <Label htmlFor="auto-accept">Auto-accept lowest rate</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox id="customer-visibility" />
                    <Label htmlFor="customer-visibility">
                      Make quotes visible to customer immediately
                    </Label>
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <SheetFooter className="mt-4">
              <Button
                variant="outline"
                onClick={() => setShowSendToAffiliatesModal(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmitRateRequest}
                disabled={selectedAffiliates.length === 0}
              >
                {rateType === "fixed"
                  ? "Create Fixed Rate Quote"
                  : "Send Rate Request"}
              </Button>
            </SheetFooter>
          </SheetContent>
        </Sheet>

        {/* Fixed Rate Sheet */}
        <Sheet open={showFixedRateModal} onOpenChange={setShowFixedRateModal}>
          <SheetContent className="sm:max-w-md md:max-w-lg lg:max-w-xl overflow-y-auto">
            <SheetHeader>
              <SheetTitle>Create Fixed Rate Quote</SheetTitle>
              <SheetDescription>
                Set a fixed rate for this quote
              </SheetDescription>
            </SheetHeader>

            <div className="space-y-4 mt-4">
              <div>
                <Label htmlFor="base-rate">Base Rate ($)</Label>
                <Input
                  id="base-rate"
                  type="number"
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                  value={fixedBaseRate}
                  onChange={(e) => setFixedBaseRate(e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="gratuity">Gratuity ($)</Label>
                <Input
                  id="gratuity"
                  type="number"
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                  value={fixedGratuity}
                  onChange={(e) => setFixedGratuity(e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="admin-fee">Admin Fee ($)</Label>
                <Input
                  id="admin-fee"
                  type="number"
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                  value={fixedAdminFee}
                  onChange={(e) => setFixedAdminFee(e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="additional-services">
                  Additional Services ($)
                </Label>
                <Input
                  id="additional-services"
                  type="number"
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                  value={fixedAdditionalServicesFee}
                  onChange={(e) =>
                    setFixedAdditionalServicesFee(e.target.value)
                  }
                />
              </div>
            </div>

            <SheetFooter className="mt-4">
              <Button
                variant="outline"
                onClick={() => setShowFixedRateModal(false)}
              >
                Cancel
              </Button>
              <Button onClick={handleSubmitFixedRateOffer}>Create Quote</Button>
            </SheetFooter>
          </SheetContent>
        </Sheet>

        {/* Quote Status Debugger */}
        {debugMode && (
          <>
            <QuoteStatusFixerTool onRefresh={refreshQuotes} />
            <QuoteStatusDebugger onRefresh={refreshQuotes} />
          </>
        )}
      </div>
    </ViewModeProvider>
  );
}

// Helper function to check if a quote is in pending state
function isPending(quote: QuoteRowData | null) {
  if (!quote) return false;
  const status = quote.status.toLowerCase();
  return ["pending", "pending_quote", "new"].includes(status);
}
