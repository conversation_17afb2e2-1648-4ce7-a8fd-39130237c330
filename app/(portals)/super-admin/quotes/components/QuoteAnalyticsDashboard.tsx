"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Badge } from "@/app/components/ui/badge"
import { Button } from "@/app/components/ui/button"
import { 
  BarChart3, 
  Clock, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Users, 
  DollarSign,
  Target,
  Activity,
  Timer
} from "lucide-react"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/app/components/ui/tabs"
import { Progress } from "@/app/components/ui/progress"

interface QuoteAnalytics {
  totalQuotes: number
  pendingQuotes: number
  activeQuotes: number
  completedQuotes: number
  averageResponseTime: number
  conversionRate: number
  totalRevenue: number
  averageQuoteValue: number
  topPerformingAffiliates: Array<{
    id: string
    name: string
    responseTime: number
    conversionRate: number
    totalQuotes: number
  }>
  recentActivity: Array<{
    id: string
    type: 'quote_created' | 'affiliate_responded' | 'quote_accepted' | 'quote_rejected'
    description: string
    timestamp: string
    amount?: number
  }>
  performanceMetrics: {
    slaCompliance: number
    customerSatisfaction: number
    affiliateUtilization: number
    systemUptime: number
  }
}

interface QuoteAnalyticsDashboardProps {
  dateRange?: {
    from: Date
    to: Date
  }
  selectedOrg?: string
}

export function QuoteAnalyticsDashboard({ dateRange, selectedOrg }: QuoteAnalyticsDashboardProps) {
  const [analytics, setAnalytics] = useState<QuoteAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true)
        
        // Build query parameters
        const params = new URLSearchParams()
        if (selectedOrg && selectedOrg !== 'all') {
          params.append('orgId', selectedOrg)
        }
        if (dateRange?.from) {
          params.append('from', dateRange.from.toISOString())
        }
        if (dateRange?.to) {
          params.append('to', dateRange.to.toISOString())
        }

        const response = await fetch(`/api/super-admin/analytics/quotes?${params}`)
        if (!response.ok) {
          throw new Error('Failed to fetch quote analytics')
        }

        const data = await response.json()
        setAnalytics(data)
      } catch (err) {
        console.error('Error fetching quote analytics:', err)
        setError(err instanceof Error ? err.message : 'Failed to load analytics')
        
        // Set mock data for development
        setAnalytics({
          totalQuotes: 156,
          pendingQuotes: 23,
          activeQuotes: 45,
          completedQuotes: 88,
          averageResponseTime: 2.4,
          conversionRate: 68.5,
          totalRevenue: 125400,
          averageQuoteValue: 850,
          topPerformingAffiliates: [
            { id: '1', name: 'Elite Transport Co', responseTime: 1.2, conversionRate: 85, totalQuotes: 34 },
            { id: '2', name: 'Premium Rides LLC', responseTime: 1.8, conversionRate: 78, totalQuotes: 28 },
            { id: '3', name: 'City Executive Cars', responseTime: 2.1, conversionRate: 72, totalQuotes: 25 }
          ],
          recentActivity: [
            { id: '1', type: 'quote_created', description: 'New quote from John Smith', timestamp: '2024-01-15T10:30:00Z', amount: 450 },
            { id: '2', type: 'affiliate_responded', description: 'Elite Transport responded to Q123456', timestamp: '2024-01-15T10:25:00Z' },
            { id: '3', type: 'quote_accepted', description: 'Quote Q123455 accepted by customer', timestamp: '2024-01-15T10:20:00Z', amount: 680 }
          ],
          performanceMetrics: {
            slaCompliance: 94.2,
            customerSatisfaction: 4.7,
            affiliateUtilization: 78.5,
            systemUptime: 99.8
          }
        })
      } finally {
        setLoading(false)
      }
    }

    fetchAnalytics()
  }, [dateRange, selectedOrg])

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="space-y-0 pb-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error || !analytics) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p>{error || 'Failed to load analytics'}</p>
            <Button variant="outline" className="mt-2" onClick={() => window.location.reload()}>
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Quotes</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalQuotes}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.pendingQuotes} pending • {analytics.activeQuotes} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.averageResponseTime}h</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="h-3 w-3 inline mr-1" />
              15% faster than last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.conversionRate}%</div>
            <Progress value={analytics.conversionRate} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${analytics.totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Avg: ${analytics.averageQuoteValue} per quote
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>System Performance</CardTitle>
          <CardDescription>Key operational metrics and SLA compliance</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{analytics.performanceMetrics.slaCompliance}%</div>
              <p className="text-sm text-muted-foreground">SLA Compliance</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{analytics.performanceMetrics.customerSatisfaction}/5</div>
              <p className="text-sm text-muted-foreground">Customer Rating</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{analytics.performanceMetrics.affiliateUtilization}%</div>
              <p className="text-sm text-muted-foreground">Affiliate Utilization</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{analytics.performanceMetrics.systemUptime}%</div>
              <p className="text-sm text-muted-foreground">System Uptime</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Top Performing Affiliates & Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Top Performing Affiliates</CardTitle>
            <CardDescription>Based on response time and conversion rate</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.topPerformingAffiliates.map((affiliate, index) => (
                <div key={affiliate.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Badge variant="outline" className="w-6 h-6 rounded-full p-0 flex items-center justify-center">
                      {index + 1}
                    </Badge>
                    <div>
                      <p className="font-medium">{affiliate.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {affiliate.totalQuotes} quotes • {affiliate.responseTime}h avg
                      </p>
                    </div>
                  </div>
                  <Badge variant={affiliate.conversionRate > 80 ? "default" : "secondary"}>
                    {affiliate.conversionRate}%
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest quote workflow events</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    {activity.type === 'quote_created' && <Activity className="h-4 w-4 text-blue-500" />}
                    {activity.type === 'affiliate_responded' && <Timer className="h-4 w-4 text-orange-500" />}
                    {activity.type === 'quote_accepted' && <CheckCircle className="h-4 w-4 text-green-500" />}
                    {activity.type === 'quote_rejected' && <AlertTriangle className="h-4 w-4 text-red-500" />}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">{activity.description}</p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(activity.timestamp).toLocaleString()}
                      {activity.amount && ` • $${activity.amount}`}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
