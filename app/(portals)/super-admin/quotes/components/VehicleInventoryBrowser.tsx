"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Badge } from "@/app/components/ui/badge"
import { But<PERSON> } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import { 
  Car, 
  MapPin, 
  Users, 
  Clock, 
  Star, 
  Filter,
  Search,
  Calendar,
  DollarSign,
  Shield,
  Wifi,
  Coffee,
  Zap,
  Crown,
  CheckCircle,
  AlertTriangle,
  Building2,
  Eye,
  Edit,
  MoreHorizontal
} from "lucide-react"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu"

interface VehicleInventoryItem {
  id: string
  affiliateId: string
  affiliateName: string
  affiliateTier: 'Elite' | 'Premium' | 'Standard'
  vehicleType: string
  vehicleModel: string
  capacity: number
  baseRate: number
  hourlyRate?: number
  airportRate?: number
  amenities: string[]
  rating: number
  responseTime: string
  availability: 'active' | 'inactive' | 'maintenance'
  location: string
  distance?: number
  features: {
    wifi: boolean
    refreshments: boolean
    luxury: boolean
    eco: boolean
  }
  images: string[]
  description: string
  lastUpdated: string
  totalBookings: number
  revenue: number
  utilizationRate: number
}

interface InventoryFilters {
  location: string
  vehicleType: string
  capacity: number
  priceRange: [number, number]
  affiliateTier: string
  availability: string
  affiliate: string
}

export function VehicleInventoryBrowser() {
  const [vehicles, setVehicles] = useState<VehicleInventoryItem[]>([])
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState<InventoryFilters>({
    location: '',
    vehicleType: 'all',
    capacity: 1,
    priceRange: [0, 1000],
    affiliateTier: 'all',
    availability: 'all',
    affiliate: 'all'
  })
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    const fetchVehicleInventory = async () => {
      try {
        setLoading(true)
        
        // Build query parameters
        const params = new URLSearchParams()
        if (filters.location) params.append('location', filters.location)
        if (filters.vehicleType !== 'all') params.append('vehicleType', filters.vehicleType)
        if (filters.capacity > 1) params.append('minCapacity', filters.capacity.toString())
        if (filters.affiliateTier !== 'all') params.append('affiliateTier', filters.affiliateTier)
        if (filters.availability !== 'all') params.append('availability', filters.availability)
        if (filters.affiliate !== 'all') params.append('affiliate', filters.affiliate)

        const response = await fetch(`/api/super-admin/inventory/vehicles?${params}`)
        if (!response.ok) {
          throw new Error('Failed to fetch vehicle inventory')
        }

        const data = await response.json()
        setVehicles(data.vehicles || [])
      } catch (error) {
        console.error('Error fetching vehicle inventory:', error)
        
        // Set mock data for development
        setVehicles([
          {
            id: '1',
            affiliateId: 'aff-1',
            affiliateName: 'Elite Transport Co',
            affiliateTier: 'Elite',
            vehicleType: 'Luxury Sedan',
            vehicleModel: 'BMW 7 Series',
            capacity: 3,
            baseRate: 120,
            hourlyRate: 85,
            airportRate: 150,
            amenities: ['WiFi', 'Refreshments', 'Phone Charger'],
            rating: 4.9,
            responseTime: '< 5 min',
            availability: 'active',
            location: 'Downtown San Francisco',
            distance: 2.3,
            features: { wifi: true, refreshments: true, luxury: true, eco: false },
            images: ['/vehicles/bmw-7-series.jpg'],
            description: 'Premium luxury sedan with professional chauffeur service',
            lastUpdated: '2024-01-15T10:30:00Z',
            totalBookings: 156,
            revenue: 18720,
            utilizationRate: 78.5
          },
          {
            id: '2',
            affiliateId: 'aff-2',
            affiliateName: 'Premium Rides LLC',
            affiliateTier: 'Premium',
            vehicleType: 'SUV',
            vehicleModel: 'Tesla Model X',
            capacity: 6,
            baseRate: 95,
            hourlyRate: 70,
            airportRate: 125,
            amenities: ['WiFi', 'Climate Control', 'USB Charging'],
            rating: 4.7,
            responseTime: '< 10 min',
            availability: 'active',
            location: 'Mission District',
            distance: 4.1,
            features: { wifi: true, refreshments: false, luxury: false, eco: true },
            images: ['/vehicles/tesla-model-x.jpg'],
            description: 'Eco-friendly electric SUV with spacious interior',
            lastUpdated: '2024-01-14T15:45:00Z',
            totalBookings: 89,
            revenue: 8455,
            utilizationRate: 65.2
          },
          {
            id: '3',
            affiliateId: 'aff-3',
            affiliateName: 'City Executive Cars',
            affiliateTier: 'Standard',
            vehicleType: 'Van',
            vehicleModel: 'Mercedes Sprinter',
            capacity: 12,
            baseRate: 180,
            hourlyRate: 120,
            airportRate: 220,
            amenities: ['WiFi', 'Refreshments', 'Entertainment System'],
            rating: 4.5,
            responseTime: '< 15 min',
            availability: 'active',
            location: 'SOMA',
            distance: 3.7,
            features: { wifi: true, refreshments: true, luxury: false, eco: false },
            images: ['/vehicles/mercedes-sprinter.jpg'],
            description: 'Spacious van perfect for group transportation',
            lastUpdated: '2024-01-13T09:20:00Z',
            totalBookings: 67,
            revenue: 12060,
            utilizationRate: 55.8
          },
          {
            id: '4',
            affiliateId: 'aff-4',
            affiliateName: 'Bay Area Luxury',
            affiliateTier: 'Elite',
            vehicleType: 'Luxury SUV',
            vehicleModel: 'Cadillac Escalade',
            capacity: 7,
            baseRate: 140,
            hourlyRate: 95,
            airportRate: 175,
            amenities: ['WiFi', 'Premium Sound', 'Leather Seats', 'Mini Bar'],
            rating: 4.8,
            responseTime: '< 8 min',
            availability: 'maintenance',
            location: 'Financial District',
            distance: 1.8,
            features: { wifi: true, refreshments: true, luxury: true, eco: false },
            images: ['/vehicles/cadillac-escalade.jpg'],
            description: 'Ultimate luxury SUV with premium amenities',
            lastUpdated: '2024-01-12T14:10:00Z',
            totalBookings: 134,
            revenue: 18760,
            utilizationRate: 82.1
          }
        ])
      } finally {
        setLoading(false)
      }
    }

    fetchVehicleInventory()
  }, [filters])

  const filteredVehicles = vehicles.filter(vehicle => {
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase()
      return (
        vehicle.affiliateName.toLowerCase().includes(searchLower) ||
        vehicle.vehicleType.toLowerCase().includes(searchLower) ||
        vehicle.vehicleModel.toLowerCase().includes(searchLower) ||
        vehicle.location.toLowerCase().includes(searchLower)
      )
    }
    return true
  })

  const getTierBadgeVariant = (tier: string) => {
    switch (tier) {
      case 'Elite': return 'default'
      case 'Premium': return 'secondary'
      case 'Standard': return 'outline'
      default: return 'outline'
    }
  }

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'active': return 'text-green-600'
      case 'inactive': return 'text-gray-600'
      case 'maintenance': return 'text-orange-600'
      default: return 'text-gray-600'
    }
  }

  const getAvailabilityBadge = (availability: string) => {
    switch (availability) {
      case 'active': return <Badge variant="outline" className="text-green-600 border-green-600">Active</Badge>
      case 'inactive': return <Badge variant="outline" className="text-gray-600 border-gray-600">Inactive</Badge>
      case 'maintenance': return <Badge variant="outline" className="text-orange-600 border-orange-600">Maintenance</Badge>
      default: return <Badge variant="outline">Unknown</Badge>
    }
  }

  const handleViewDetails = (vehicle: VehicleInventoryItem) => {
    // Navigate to vehicle details page
    window.location.href = `/super-admin/affiliates/${vehicle.affiliateId}/vehicles/${vehicle.id}`
  }

  const handleEditRates = (vehicle: VehicleInventoryItem) => {
    // Navigate to rate editing interface
    window.location.href = `/super-admin/affiliates/${vehicle.affiliateId}/rates?vehicle=${vehicle.id}`
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-32 bg-gray-200 rounded mb-4"></div>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Vehicle Inventory Browser</CardTitle>
          <CardDescription>Browse and manage all vehicles across the affiliate network</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search vehicles, affiliates, or locations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9"
              />
            </div>
            
            <Select value={filters.vehicleType} onValueChange={(value) => setFilters(prev => ({ ...prev, vehicleType: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Vehicle Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="sedan">Sedan</SelectItem>
                <SelectItem value="suv">SUV</SelectItem>
                <SelectItem value="van">Van</SelectItem>
                <SelectItem value="luxury">Luxury</SelectItem>
                <SelectItem value="bus">Bus</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.affiliateTier} onValueChange={(value) => setFilters(prev => ({ ...prev, affiliateTier: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Affiliate Tier" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Tiers</SelectItem>
                <SelectItem value="Elite">Elite Only</SelectItem>
                <SelectItem value="Premium">Premium+</SelectItem>
                <SelectItem value="Standard">Standard+</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.availability} onValueChange={(value) => setFilters(prev => ({ ...prev, availability: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Availability" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active Only</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Filter by location"
                value={filters.location}
                onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                className="w-auto"
              />
            </div>
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <Input
                type="number"
                min="1"
                max="50"
                value={filters.capacity}
                onChange={(e) => setFilters(prev => ({ ...prev, capacity: parseInt(e.target.value) || 1 }))}
                placeholder="Min capacity"
                className="w-24"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Vehicle Inventory Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredVehicles.map((vehicle) => (
          <Card key={vehicle.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-lg">{vehicle.vehicleModel}</CardTitle>
                  <CardDescription className="flex items-center gap-2">
                    <Building2 className="h-3 w-3" />
                    {vehicle.affiliateName}
                  </CardDescription>
                </div>
                <div className="flex flex-col items-end gap-1">
                  <Badge variant={getTierBadgeVariant(vehicle.affiliateTier)}>
                    {vehicle.affiliateTier}
                  </Badge>
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                    <span className="text-xs font-medium">{vehicle.rating}</span>
                  </div>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Vehicle Image Placeholder */}
              <div className="h-32 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg flex items-center justify-center">
                <Car className="h-12 w-12 text-blue-400" />
              </div>

              {/* Vehicle Details */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Type</span>
                  <span className="font-medium">{vehicle.vehicleType}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Capacity</span>
                  <span className="font-medium">{vehicle.capacity} passengers</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Location</span>
                  <span className="font-medium">{vehicle.location}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Status</span>
                  {getAvailabilityBadge(vehicle.availability)}
                </div>
              </div>

              {/* Performance Metrics */}
              <div className="border-t pt-3 space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Total Bookings</span>
                  <span className="font-medium">{vehicle.totalBookings}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Revenue</span>
                  <span className="font-medium">${vehicle.revenue.toLocaleString()}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Utilization</span>
                  <span className="font-medium">{vehicle.utilizationRate}%</span>
                </div>
              </div>

              {/* Features */}
              <div className="flex items-center gap-2">
                {vehicle.features.wifi && <Wifi className="h-4 w-4 text-blue-500" />}
                {vehicle.features.refreshments && <Coffee className="h-4 w-4 text-brown-500" />}
                {vehicle.features.luxury && <Crown className="h-4 w-4 text-yellow-500" />}
                {vehicle.features.eco && <Zap className="h-4 w-4 text-green-500" />}
              </div>

              {/* Pricing */}
              <div className="border-t pt-3">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-lg font-bold">${vehicle.baseRate}</div>
                    <div className="text-xs text-muted-foreground">Base rate</div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">${vehicle.hourlyRate}/hr</div>
                    <div className="text-xs text-muted-foreground">Hourly rate</div>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Button 
                  variant="outline"
                  onClick={() => handleViewDetails(vehicle)}
                  className="flex-1"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleEditRates(vehicle)}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Rates
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleViewDetails(vehicle)}>
                      <Eye className="h-4 w-4 mr-2" />
                      View Performance
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredVehicles.length === 0 && !loading && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <AlertTriangle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No vehicles found</h3>
              <p className="text-muted-foreground mb-4">
                No vehicles match your current search criteria. Try adjusting your filters.
              </p>
              <Button onClick={() => {
                setSearchTerm('')
                setFilters({
                  location: '',
                  vehicleType: 'all',
                  capacity: 1,
                  priceRange: [0, 1000],
                  affiliateTier: 'all',
                  availability: 'all',
                  affiliate: 'all'
                })
              }}>
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
