"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import {
  Building2,
  Clock,
  DollarSign,
  MapPin,
  Users,
  Info,
  Timer,
  Briefcase
} from "lucide-react"
import { Badge } from "@/app/components/ui/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Progress } from "@/app/components/ui/progress"
import { Alert, AlertDescription } from "@/app/components/ui/alert"
import { getAffiliatesForQuote } from "@/lib/api/affiliates"
import { useToast } from "@/app/components/ui/use-toast"

interface Quote {
  id: string;
  customer_name?: string;
  service_type?: string;
  pickup_location?: string;
  dropoff_location?: string;
  date?: string;
  time?: string;
  passenger_count?: number;
  luggage_count?: number;
  vehicle_type?: string;
  special_requests?: string;
  status: string;
  city?: string;
  affiliate_id?: string;
  company_id?: string;
  marketAnalysis?: {
    avgMarketRate: number;
    demandLevel: string;
    suggestedMarkup: number;
    peakPricing: boolean;
    similarTripsLastMonth: number;
  };
  urgency?: 'high' | 'medium' | 'low';
  smartAssignmentSettings?: {
    enabled: boolean;
    responseTimer: string;
    nextAssignment?: string;
  };
  responses?: number;
  timeLeft?: string;
  confidenceScore?: number;
}

interface Affiliate {
  id: string;
  name: string;
  city?: string;
  state?: string;
  coverage?: string;
  rating?: number;
  baseRate?: number;
  totalRate?: number;
  confidenceScore?: number;
  avgResponseTime?: string;
  completedSimilar?: number;
  onTimeRate?: number;
  historicalPricing?: {
    avg: number;
    min: number;
    max: number;
  };
}

export default function SuperAdminQuoteDetailPage() {
  const params = useParams()
  const quoteId = params.quoteId as string
  const [quote, setQuote] = useState<Quote | null>(null)
  const [affiliates, setAffiliates] = useState<Affiliate[]>([])
  const [loading, setLoading] = useState(true)
  const [timelineEntries, setTimelineEntries] = useState<any[]>([])
  const { toast } = useToast()

  useEffect(() => {
    const fetchQuoteData = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/super-admin/quotes/${params.quoteId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch quote');
        }
        const data = await response.json();
        setQuote(data);
        // Fetch timeline entries
        const timelineResponse = await fetch(`/api/super-admin/quotes/${params.quoteId}/timeline`);
        if (timelineResponse.ok) {
          const timelineData = await timelineResponse.json();
          setTimelineEntries(timelineData);
        }
        // Fetch affiliates if city is available
        if (data.city) {
          fetchAffiliatesForCity(data.city);
        } else {
          fetchAllAffiliates();
        }
      } catch (error) {
        console.error('Error fetching quote:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch quote details',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };
    fetchQuoteData();
    const refreshInterval = setInterval(fetchQuoteData, 15000);
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        fetchQuoteData();
      }
    };
    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      clearInterval(refreshInterval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [params.quoteId, toast]);

  const fetchAffiliatesForCity = async (city: string) => {
    try {
      const affiliatesWithStats = await getAffiliatesForQuote(city);
      if (!affiliatesWithStats || affiliatesWithStats.length === 0) {
        setAffiliates([]);
        return;
      }
      const transformedAffiliates: Affiliate[] = affiliatesWithStats.map((affiliate: any) => ({
        id: affiliate.id,
        name: affiliate.name,
        city: affiliate.city,
        state: affiliate.state,
        coverage: `${affiliate.city || ''}, ${affiliate.state || ''}`,
        rating: affiliate.rating || 4.5,
        baseRate: affiliate.baseRate || 150,
        totalRate: affiliate.totalRate || 180,
        confidenceScore: 90,
        avgResponseTime: affiliate.avgResponse || "25m",
        completedSimilar: affiliate.similarTrips || 120,
        onTimeRate: affiliate.on_time_percentage || 95,
        historicalPricing: {
          avg: affiliate.priceRange ? (affiliate.priceRange.min + affiliate.priceRange.max) / 2 : 220,
          min: affiliate.priceRange ? affiliate.priceRange.min : 200,
          max: affiliate.priceRange ? affiliate.priceRange.max : 240
        }
      }));
      setAffiliates(transformedAffiliates);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load available affiliates",
        variant: "destructive"
      });
    }
  }

  const fetchAllAffiliates = async () => {
    try {
      const affiliatesWithStats = await getAffiliatesForQuote('');
      if (!affiliatesWithStats || affiliatesWithStats.length === 0) {
        setAffiliates([]);
        return;
      }
      const transformedAffiliates: Affiliate[] = affiliatesWithStats.map((affiliate: any) => ({
        id: affiliate.id,
        name: affiliate.name,
        city: affiliate.city,
        state: affiliate.state,
        coverage: `${affiliate.city || ''}, ${affiliate.state || ''}`,
        rating: affiliate.rating || 4.5,
        baseRate: affiliate.baseRate || 150,
        totalRate: affiliate.totalRate || 180,
        confidenceScore: 90,
        avgResponseTime: affiliate.avgResponse || "25m",
        completedSimilar: affiliate.similarTrips || 120,
        onTimeRate: affiliate.on_time_percentage || 95,
        historicalPricing: {
          avg: affiliate.priceRange ? (affiliate.priceRange.min + affiliate.priceRange.max) / 2 : 220,
          min: affiliate.priceRange ? affiliate.priceRange.min : 200,
          max: affiliate.priceRange ? affiliate.priceRange.max : 240
        }
      }));
      setAffiliates(transformedAffiliates);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load available affiliates",
        variant: "destructive"
      });
    }
  }

  if (loading) {
    return <div className="p-8 text-center">Loading quote details...</div>
  }
  if (!quote) {
    return <div className="p-8 text-center text-red-500">Quote not found.</div>
  }

  return (
    <div className="max-w-5xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Quote Details (Read-Only)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="mb-2 flex items-center gap-2"><Users className="w-4 h-4" /> Customer: <span>{quote.customer_name}</span></div>
              <div className="mb-2 flex items-center gap-2"><Briefcase className="w-4 h-4" /> Service: <span>{quote.service_type}</span></div>
              <div className="mb-2 flex items-center gap-2"><MapPin className="w-4 h-4" /> Pickup: <span>{quote.pickup_location}</span></div>
              <div className="mb-2 flex items-center gap-2"><MapPin className="w-4 h-4" /> Dropoff: <span>{quote.dropoff_location}</span></div>
              <div className="mb-2 flex items-center gap-2"><Clock className="w-4 h-4" /> Date/Time: <span>{quote.date} {quote.time}</span></div>
              <div className="mb-2 flex items-center gap-2"><Users className="w-4 h-4" /> Passengers: <span>{quote.passenger_count}</span></div>
              <div className="mb-2 flex items-center gap-2"><Info className="w-4 h-4" /> Status: <Badge>{quote.status}</Badge></div>
              <div className="mb-2 flex items-center gap-2"><Building2 className="w-4 h-4" /> Company ID: <span>{quote.company_id}</span></div>
              <div className="mb-2 flex items-center gap-2"><DollarSign className="w-4 h-4" /> Vehicle: <span>{quote.vehicle_type}</span></div>
              <div className="mb-2 flex items-center gap-2"><Timer className="w-4 h-4" /> Urgency: <span>{quote.urgency}</span></div>
              <div className="mb-2 flex items-center gap-2"><Info className="w-4 h-4" /> Special Requests: <span>{quote.special_requests}</span></div>
            </div>
            <div>
              {quote.marketAnalysis && (
                <div className="mb-4">
                  <div className="font-semibold mb-1">Market Analysis</div>
                  <div>Avg Market Rate: ${quote.marketAnalysis.avgMarketRate}</div>
                  <div>Demand Level: {quote.marketAnalysis.demandLevel}</div>
                  <div>Suggested Markup: {quote.marketAnalysis.suggestedMarkup}%</div>
                  <div>Peak Pricing: {quote.marketAnalysis.peakPricing ? 'Yes' : 'No'}</div>
                  <div>Similar Trips Last Month: {quote.marketAnalysis.similarTripsLastMonth}</div>
                </div>
              )}
              <div className="mb-4">
                <div className="font-semibold mb-1">Affiliates (for this quote/city)</div>
                {affiliates.length === 0 ? (
                  <div>No affiliates found for this quote.</div>
                ) : (
                  <ul className="space-y-2">
                    {affiliates.map((a) => (
                      <li key={a.id} className="border rounded p-2 flex flex-col">
                        <span className="font-semibold">{a.name}</span>
                        <span className="text-xs text-muted-foreground">{a.coverage}</span>
                        <span className="text-xs">Rating: {a.rating}</span>
                        <span className="text-xs">Base Rate: ${a.baseRate}</span>
                        <span className="text-xs">Total Rate: ${a.totalRate}</span>
                        <span className="text-xs">Avg Response: {a.avgResponseTime}</span>
                        <span className="text-xs">Completed Similar: {a.completedSimilar}</span>
                        <span className="text-xs">On Time Rate: {a.onTimeRate}%</span>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardHeader>
          <CardTitle>Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          {timelineEntries.length === 0 ? (
            <div>No timeline entries found.</div>
          ) : (
            <ul className="space-y-2">
              {timelineEntries.map((entry, idx) => (
                <li key={idx} className="border rounded p-2 flex flex-col">
                  <span className="font-semibold">{entry.event}</span>
                  <span className="text-xs text-muted-foreground">{entry.timestamp}</span>
                  <span className="text-xs">{entry.details}</span>
                </li>
              ))}
            </ul>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 