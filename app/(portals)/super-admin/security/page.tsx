"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON>eader, CardTitle, CardDescription, CardFooter } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { Button } from "@/app/components/ui/button"
import { Switch } from "@/app/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { Shield, UserCheck, LogIn, ListChecks } from "lucide-react"

export default function SuperAdminSecurityPage() {
  // Mock state for security settings
  const [mfaEnabled, setMfaEnabled] = useState(true)
  const [sessionTimeout, setSessionTimeout] = useState("30")
  const [roleManagement, setRoleManagement] = useState([
    { role: "SUPER_ADMIN", description: "Full access to all features", users: 2 },
    { role: "ADMIN", description: "Manage orgs, users, and settings", users: 5 },
    { role: "USER", description: "Standard platform access", users: 48 },
  ])

  // TODO: Replace with Supabase integration and validation

  return (
    <div className="space-y-8 max-w-3xl mx-auto p-6">
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold tracking-tight">Security</h1>
        <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
          Manage authentication, session, and role-based access control for your platform.
        </p>
      </div>

      {/* Authentication Settings */}
      <Card>
        <CardHeader className="flex flex-row items-center gap-3 pb-2">
          <Shield className="h-5 w-5 text-primary" />
          <CardTitle className="text-lg font-semibold">Authentication</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm">Require Multi-Factor Authentication (MFA)</span>
            <Switch checked={mfaEnabled} onCheckedChange={setMfaEnabled} />
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="default">Save Authentication Settings</Button>
        </CardFooter>
      </Card>

      {/* Session Management */}
      <Card>
        <CardHeader className="flex flex-row items-center gap-3 pb-2">
          <LogIn className="h-5 w-5 text-primary" />
          <CardTitle className="text-lg font-semibold">Session Management</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Session Timeout</label>
            <Select value={sessionTimeout} onValueChange={setSessionTimeout}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select timeout" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="15">15 minutes</SelectItem>
                <SelectItem value="30">30 minutes</SelectItem>
                <SelectItem value="60">1 hour</SelectItem>
                <SelectItem value="180">3 hours</SelectItem>
                <SelectItem value="480">8 hours</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="default">Save Session Settings</Button>
        </CardFooter>
      </Card>

      {/* Role Management */}
      <Card>
        <CardHeader className="flex flex-row items-center gap-3 pb-2">
          <UserCheck className="h-5 w-5 text-primary" />
          <CardTitle className="text-lg font-semibold">Role Management</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <table className="w-full text-sm">
            <thead>
              <tr className="text-muted-foreground">
                <th className="text-left font-medium">Role</th>
                <th className="text-left font-medium">Description</th>
                <th className="text-right font-medium">Users</th>
              </tr>
            </thead>
            <tbody>
              {roleManagement.map(role => (
                <tr key={role.role}>
                  <td className="py-2 font-semibold">{role.role}</td>
                  <td className="py-2">{role.description}</td>
                  <td className="py-2 text-right">{role.users}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </CardContent>
        <CardFooter>
          <Button variant="default">Update Roles</Button>
        </CardFooter>
      </Card>

      {/* Audit Log */}
      <Card>
        <CardHeader className="flex flex-row items-center gap-3 pb-2">
          <ListChecks className="h-5 w-5 text-primary" />
          <CardTitle className="text-lg font-semibold">Audit Log</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="text-sm text-muted-foreground mb-2">Recent security-related events:</div>
          <ul className="space-y-1 text-sm">
            <li>2024-06-28 10:12 — <b><EMAIL></b> enabled MFA</li>
            <li>2024-06-27 09:45 — <b><EMAIL></b> updated session timeout</li>
            <li>2024-06-26 14:22 — <b><EMAIL></b> added new SUPER_ADMIN</li>
          </ul>
        </CardContent>
        <CardFooter>
          <Button variant="outline">View Full Audit Log</Button>
        </CardFooter>
      </Card>
      {/* TODO: Add Supabase integration, validation, and error handling */}
    </div>
  )
} 