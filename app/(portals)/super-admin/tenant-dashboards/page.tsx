"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/app/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/app/components/ui/tabs"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import {
  Activity,
  Users,
  Car,
  BarChart3,
  Calendar,
  Clock,
  FileCheck,
  Flag,
  Globe,
  Map,
  Timer,
  Truck,
  AlertTriangle,
  Network,
  Package,
  Filter,
  Download,
  RefreshCw,
  Plane,
  Repeat,
  Scroll
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Input } from "@/app/components/ui/input"
import { Badge } from "@/app/components/ui/badge"
import { AuthCheck } from "@/app/components/auth-check"
import {
  ArrowUpRight,
  Building2,
  CheckCircle2,
  DollarSign,
  FileText,
  Map<PERSON>in,
  <PERSON><PERSON>,
  <PERSON>R<PERSON>,
  Shield<PERSON>heck,
  Search,
  XCircle
} from "lucide-react"
import Link from "next/link"
import { useEffect } from 'react'
import { useGlobalFilters } from "@/app/contexts/GlobalFilterContext"

function AdminDashboardContent() {
  const { selectedOrg, setSelectedOrg, dateRange, setDateRange } = useGlobalFilters()
  const [refreshing, setRefreshing] = useState(false)
  
  const handleRefresh = () => {
    setRefreshing(true)
    // Simulate data refresh
    setTimeout(() => setRefreshing(false), 1000)
  }

  useEffect(() => {
    console.log('Admin dashboard mounted, URL:', window.location.href);
    
    // Log any redirects that might happen
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;
    
    history.pushState = function(data: any, unused: string, url?: string | URL | null) {
      console.log('Admin dashboard - Navigation pushState:', url);
      return originalPushState.apply(this, [data, unused, url]);
    };
    
    history.replaceState = function(data: any, unused: string, url?: string | URL | null) {
      console.log('Admin dashboard - Navigation replaceState:', url);
      return originalReplaceState.apply(this, [data, unused, url]);
    };
    
    return () => {
      // Restore original functions
      history.pushState = originalPushState;
      history.replaceState = originalReplaceState;
      console.log('Admin dashboard unmounting');
    };
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">OPS Snapshot</h2>
          <p className="text-muted-foreground">
            Global operational overview: requests, affiliates, live trips, and system performance
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select
            value={selectedOrg === 'all' ? 'all' : selectedOrg?.id || ''}
            onValueChange={(value) => {
              if (value === 'all') {
                setSelectedOrg('all');
              } else if (!value) {
                setSelectedOrg(null);
              } else {
                setSelectedOrg({
                  id: value,
                  name: value === 't1' ? 'Acme Transportation' : value === 't2' ? 'City Tours LLC' : 'Swift Transit'
                });
              }
            }}
          >
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder="Select tenant" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Tenants</SelectItem>
              <SelectItem value="t1">Acme Transportation</SelectItem>
              <SelectItem value="t2">City Tours LLC</SelectItem>
              <SelectItem value="t3">Swift Transit</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={(() => {
              if (!dateRange.from || !dateRange.to) return '7d';
              const diff = dateRange.to.getTime() - dateRange.from.getTime();
              if (diff <= 24 * 60 * 60 * 1000) return '24h';
              if (diff <= 7 * 24 * 60 * 60 * 1000) return '7d';
              if (diff <= 30 * 24 * 60 * 60 * 1000) return '30d';
              return '90d';
            })()}
            onValueChange={(value) => {
              const now = new Date();
              let from: Date | undefined;
              let to: Date | undefined = now;
              switch (value) {
                case '24h':
                  from = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                  break;
                case '7d':
                  from = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                  break;
                case '30d':
                  from = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                  break;
                case '90d':
                  from = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
                  break;
                default:
                  from = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
              }
              setDateRange({ from, to });
            }}
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Date range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="24h">Last 24 hours</SelectItem>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last quarter</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon" onClick={handleRefresh} disabled={refreshing}>
            <RefreshCw className={cn("h-4 w-4", refreshing && "animate-spin")} />
          </Button>
          <Button variant="outline" size="icon">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
            <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Quotes</CardTitle>
            <CardDescription>In progress quotes</CardDescription>
              </CardHeader>
              <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">248</div>
              <div className="p-2 bg-green-100 text-green-800 rounded">
                <Activity className="h-4 w-4" />
              </div>
            </div>
            <div className="text-xs text-green-600 flex items-center mt-2">
              <span className="font-medium">↑ 12%</span>
              <span className="text-muted-foreground ml-1">vs previous period</span>
            </div>
              </CardContent>
            </Card>
        
            <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Live Trips</CardTitle>
            <CardDescription>Currently in progress</CardDescription>
              </CardHeader>
              <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">42</div>
              <div className="p-2 bg-blue-100 text-blue-800 rounded">
                <Plane className="h-4 w-4" />
              </div>
            </div>
            <div className="text-xs text-muted-foreground flex items-center mt-2">
              <Badge variant="outline" className="text-xs font-normal">
                <Clock className="h-3 w-3 mr-1" />
                14 departing soon
              </Badge>
            </div>
              </CardContent>
            </Card>
        
            <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Affiliates</CardTitle>
            <CardDescription>Currently online</CardDescription>
              </CardHeader>
              <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">137</div>
              <div className="p-2 bg-purple-100 text-purple-800 rounded">
                <Network className="h-4 w-4" />
              </div>
            </div>
            <div className="text-xs text-amber-600 flex items-center mt-2">
              <span className="font-medium">↓ 3%</span>
              <span className="text-muted-foreground ml-1">vs previous period</span>
            </div>
              </CardContent>
            </Card>
        
            <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
            <CardDescription>API & service status</CardDescription>
              </CardHeader>
              <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold text-green-600">99.8%</div>
              <div className="p-2 bg-green-100 text-green-800 rounded">
                <Activity className="h-4 w-4" />
              </div>
            </div>
            <div className="text-xs text-muted-foreground flex items-center mt-2">
              <Badge variant="outline" className="text-xs font-normal bg-green-50 text-green-700 border-green-200">
                All systems operational
              </Badge>
            </div>
              </CardContent>
            </Card>
          </div>
      
      <div className="grid gap-4 grid-cols-1 lg:grid-cols-3">
        <Card className="lg:col-span-2">
              <CardHeader>
            <CardTitle>Trip Activity</CardTitle>
            <CardDescription>Trip volume over time with tenant breakdown</CardDescription>
              </CardHeader>
          <CardContent className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
            <div className="text-center p-6">
              <BarChart3 className="h-10 w-10 mb-4 mx-auto text-muted-foreground" />
              <div className="text-sm text-muted-foreground">Trip volume chart will appear here</div>
              <div className="text-xs text-muted-foreground mt-1">Shows tenant distribution, trends, and volume by day</div>
            </div>
              </CardContent>
            </Card>
        
        <Card>
              <CardHeader>
            <CardTitle>Global Coverage</CardTitle>
            <CardDescription>Active service regions</CardDescription>
              </CardHeader>
          <CardContent className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
            <div className="text-center p-6">
              <Globe className="h-10 w-10 mb-4 mx-auto text-muted-foreground" />
              <div className="text-sm text-muted-foreground">Coverage map will appear here</div>
              <div className="text-xs text-muted-foreground mt-1">Shows global service availability and density</div>
                </div>
              </CardContent>
            </Card>
          </div>
      
      <Tabs defaultValue="active" className="space-y-4">
        <TabsList>
          <TabsTrigger value="active">
            <Plane className="h-4 w-4 mr-2" />
            Active Trips
          </TabsTrigger>
          <TabsTrigger value="quotes">
            <Scroll className="h-4 w-4 mr-2" />
            Recent Quotes
          </TabsTrigger>
          <TabsTrigger value="alerts">
            <AlertTriangle className="h-4 w-4 mr-2" />
            System Alerts
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="active" className="space-y-4">
            <Card>
              <CardHeader>
              <CardTitle>Active Trips</CardTitle>
              <CardDescription>Currently in progress across all tenants</CardDescription>
              </CardHeader>
            <CardContent className="p-0">
              <div className="border-b border-t">
                <div className="flex p-4 hover:bg-muted/50 cursor-pointer">
                  <div className="flex-1">
                    <div className="font-medium">JFK to LAX</div>
                    <div className="text-sm text-muted-foreground">Private Jet • 6 passengers</div>
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">In Flight</div>
                    <div className="text-sm text-muted-foreground">ETA: 3:45pm EST</div>
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">Acme Transportation</div>
                    <div className="text-sm text-muted-foreground">Trip #AC-4392</div>
                  </div>
                </div>
                
                <div className="flex p-4 hover:bg-muted/50 cursor-pointer">
                  <div className="flex-1">
                    <div className="font-medium">Chicago Downtown to O'Hare</div>
                    <div className="text-sm text-muted-foreground">Executive Car • 2 passengers</div>
                  </div>
                    <div className="flex-1">
                    <div className="font-medium">En Route to Pickup</div>
                    <div className="text-sm text-muted-foreground">Arriving in 5 minutes</div>
                    </div>
                  <div className="flex-1">
                    <div className="font-medium">City Tours LLC</div>
                    <div className="text-sm text-muted-foreground">Trip #CT-9871</div>
                  </div>
                </div>
                
                <div className="flex p-4 hover:bg-muted/50 cursor-pointer">
                  <div className="flex-1">
                    <div className="font-medium">Miami Beach to Miami Int'l</div>
                    <div className="text-sm text-muted-foreground">SUV • 4 passengers</div>
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">In Progress</div>
                    <div className="text-sm text-muted-foreground">ETA: 2:15pm EST</div>
          </div>
                  <div className="flex-1">
                    <div className="font-medium">Swift Transit</div>
                    <div className="text-sm text-muted-foreground">Trip #ST-3456</div>
          </div>
                </div>
              </div>
              <div className="p-4 text-center">
                <Button variant="outline" size="sm">View All Active Trips</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="quotes" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Quotes</CardTitle>
              <CardDescription>Quote requests in the last 24 hours</CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              {/* Similar content structure to Active Trips tab */}
              <div className="p-4 text-center">
                <div className="text-muted-foreground">Recent quotes will appear here</div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="alerts" className="space-y-4">
            <Card>
            <CardHeader>
              <CardTitle>System Alerts</CardTitle>
              <CardDescription>Warnings and notifications</CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              {/* Similar content structure to Active Trips tab */}
              <div className="p-4 text-center">
                <div className="text-muted-foreground">System alerts will appear here</div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
            <Card>
              <CardHeader>
            <CardTitle>Tenant Activity</CardTitle>
            <CardDescription>Usage distribution by tenant</CardDescription>
              </CardHeader>
          <CardContent className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
            <div className="text-center p-6">
              <Users className="h-10 w-10 mb-4 mx-auto text-muted-foreground" />
              <div className="text-sm text-muted-foreground">Tenant activity chart will appear here</div>
              <div className="text-xs text-muted-foreground mt-1">Shows tenant usage metrics and comparative analysis</div>
                </div>
              </CardContent>
            </Card>
        
        <Card>
              <CardHeader>
            <CardTitle>Affiliate Performance</CardTitle>
            <CardDescription>Top performing service providers</CardDescription>
              </CardHeader>
          <CardContent className="h-[300px] flex items-center justify-center bg-muted/20 rounded-md">
            <div className="text-center p-6">
              <Network className="h-10 w-10 mb-4 mx-auto text-muted-foreground" />
              <div className="text-sm text-muted-foreground">Affiliate performance chart will appear here</div>
              <div className="text-xs text-muted-foreground mt-1">Shows ratings, trip volume, and reliability metrics</div>
                </div>
              </CardContent>
            </Card>
          </div>
    </div>
  )
}

export default function AdminDashboard() {
  return (
    <AuthCheck requiredRoles={['SUPER_ADMIN']}>
      <AdminDashboardContent />
    </AuthCheck>
  )
}
