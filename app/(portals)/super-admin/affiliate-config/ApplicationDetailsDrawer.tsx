"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetClose } from "@/app/components/ui/sheet"
import { Button } from "@/app/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/app/components/ui/tabs"
import { Badge } from "@/app/components/ui/badge"
import { Progress } from "@/app/components/ui/progress"
import { Separator } from "@/app/components/ui/separator"
import { Textarea } from "@/app/components/ui/textarea"
import {
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  User,
  Building,
  MapPin,
  Mail,
  Phone,
  Calendar,
  MessageSquare
} from "lucide-react"

interface Application {
  id: string
  name: string
  email: string
  phone?: string
  city: string
  state: string
  status: 'pending' | 'in_verification' | 'approved' | 'rejected'
  progress: number
  submitted: string
  businessType?: string
  yearsInBusiness?: number
  fleetSize?: number
  servicesOffered?: string[]
  notes?: string
}

interface ApplicationDetailsDrawerProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  application: Application | null
}

export function ApplicationDetailsDrawer({ open, onOpenChange, application }: ApplicationDetailsDrawerProps) {
  if (!application) return null

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved': return <Badge className="bg-green-100 text-green-800 border-green-200"><CheckCircle className="mr-1 h-3 w-3" />Approved</Badge>
      case 'rejected': return <Badge className="bg-red-100 text-red-800 border-red-200"><XCircle className="mr-1 h-3 w-3" />Rejected</Badge>
      case 'in_verification': return <Badge className="bg-blue-100 text-blue-800 border-blue-200"><Clock className="mr-1 h-3 w-3" />In Verification</Badge>
      case 'pending': return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200"><AlertTriangle className="mr-1 h-3 w-3" />Pending Review</Badge>
      default: return <Badge variant="secondary">{status}</Badge>
    }
  }

  const handleApprove = async () => {
    try {
      const response = await fetch(`/api/super-admin/affiliates/${application.id}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notes: 'Approved via super admin interface'
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Application approved successfully:', result)
        // TODO: Show success message and refresh data
        onOpenChange(false) // Close the drawer
        window.location.reload() // Refresh the page to show updated status
      } else {
        console.error('Failed to approve application')
        // TODO: Show error message
      }
    } catch (error) {
      console.error('Error approving application:', error)
      // TODO: Show error message
    }
  }

  const handleReject = async () => {
    const reason = prompt('Please provide a reason for rejection:')
    if (!reason) return

    try {
      const response = await fetch(`/api/super-admin/affiliates/${application.id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reason: reason,
          notes: 'Rejected via super admin interface'
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Application rejected successfully:', result)
        // TODO: Show success message and refresh data
        onOpenChange(false) // Close the drawer
        window.location.reload() // Refresh the page to show updated status
      } else {
        console.error('Failed to reject application')
        // TODO: Show error message
      }
    } catch (error) {
      console.error('Error rejecting application:', error)
      // TODO: Show error message
    }
  }

  const handleRequestMoreInfo = () => {
    // TODO: Implement request more info logic
    console.log('Requesting more info for application:', application.id)
    // This could open an email composer or send a notification
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-4xl w-full p-0">
        <SheetHeader className="px-6 pt-6 pb-4 border-b">
          <div className="flex justify-between items-center">
            <div>
              <SheetTitle className="text-xl font-semibold">Application Details: {application.name}</SheetTitle>
              <p className="text-sm text-muted-foreground">Review and manage affiliate application</p>
            </div>
            <SheetClose asChild>
              <Button size="icon" variant="ghost">
                <span className="sr-only">Close</span>
                ×
              </Button>
            </SheetClose>
          </div>
        </SheetHeader>

        <div className="p-6 overflow-y-auto h-[calc(100vh-80px)]">
          {/* Status and Progress */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold">Application Status</h3>
                <p className="text-sm text-muted-foreground">Submitted on {formatDate(application.submitted)}</p>
              </div>
              {getStatusBadge(application.status)}
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Application Progress</span>
                <span>{application.progress}% Complete</span>
              </div>
              <Progress value={application.progress} className="h-2" />
            </div>
          </div>

          <Tabs defaultValue="details">
            <TabsList className="mb-4">
              <TabsTrigger value="details"><User className="mr-2 h-4 w-4" />Application Details</TabsTrigger>
              <TabsTrigger value="business"><Building className="mr-2 h-4 w-4" />Business Info</TabsTrigger>
              <TabsTrigger value="review"><MessageSquare className="mr-2 h-4 w-4" />Review & Actions</TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-6">
              {/* Applicant Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Applicant Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Company Name</label>
                      <p className="text-lg font-semibold">{application.name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Email Address</label>
                      <p className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        {application.email}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Phone Number</label>
                      <p className="flex items-center gap-2">
                        <Phone className="h-4 w-4" />
                        {application.phone || 'Not provided'}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Location</label>
                      <p className="flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        {application.city}, {application.state}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="business" className="space-y-6">
              {/* Business Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building className="h-5 w-5" />
                    Business Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Business Type</label>
                      <p>{application.businessType || 'Not specified'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Years in Business</label>
                      <p>{application.yearsInBusiness || 'Not provided'} years</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Fleet Size</label>
                      <p>{application.fleetSize || 'Not provided'} vehicles</p>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Services Offered</label>
                    <div className="mt-2 flex flex-wrap gap-2">
                      {application.servicesOffered && application.servicesOffered.length > 0 ? (
                        application.servicesOffered.map((service, index) => (
                          <Badge key={index} variant="outline">{service}</Badge>
                        ))
                      ) : (
                        <p className="text-muted-foreground">No services specified</p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="review" className="space-y-6">
              {/* Review Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Review Actions</CardTitle>
                  <CardDescription>Take action on this application</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {application.status === 'pending' && (
                    <div className="flex flex-wrap gap-3">
                      <Button onClick={handleApprove} className="bg-green-600 hover:bg-green-700">
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Approve Application
                      </Button>
                      <Button onClick={handleReject} variant="destructive">
                        <XCircle className="mr-2 h-4 w-4" />
                        Reject Application
                      </Button>
                      <Button onClick={handleRequestMoreInfo} variant="outline">
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Request More Info
                      </Button>
                    </div>
                  )}

                  {application.status === 'in_verification' && (
                    <div className="flex flex-wrap gap-3">
                      <Button onClick={handleApprove} className="bg-green-600 hover:bg-green-700">
                        <CheckCircle className="mr-2 h-4 w-4" />
                        Complete Verification
                      </Button>
                      <Button onClick={handleRequestMoreInfo} variant="outline">
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Request Additional Documents
                      </Button>
                    </div>
                  )}

                  {(application.status === 'approved' || application.status === 'rejected') && (
                    <div className="p-4 bg-muted rounded-lg">
                      <p className="text-sm text-muted-foreground">
                        This application has been {application.status}. No further actions are available.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Notes Section */}
              <Card>
                <CardHeader>
                  <CardTitle>Internal Notes</CardTitle>
                  <CardDescription>Add notes about this application for internal reference</CardDescription>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="Add your notes here..."
                    defaultValue={application.notes || ''}
                    className="min-h-[100px]"
                  />
                  <div className="mt-3 flex justify-end">
                    <Button variant="outline" size="sm">
                      Save Notes
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Application Timeline */}
              <Card>
                <CardHeader>
                  <CardTitle>Application Timeline</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <div>
                        <p className="font-medium">Application Submitted</p>
                        <p className="text-sm text-muted-foreground">{formatDate(application.submitted)}</p>
                      </div>
                    </div>
                    {application.status !== 'pending' && (
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        <div>
                          <p className="font-medium">Under Review</p>
                          <p className="text-sm text-muted-foreground">Review process started</p>
                        </div>
                      </div>
                    )}
                    {(application.status === 'approved' || application.status === 'rejected') && (
                      <div className="flex items-center gap-3">
                        <div className={`w-2 h-2 rounded-full ${application.status === 'approved' ? 'bg-green-500' : 'bg-red-500'}`}></div>
                        <div>
                          <p className="font-medium">Application {application.status === 'approved' ? 'Approved' : 'Rejected'}</p>
                          <p className="text-sm text-muted-foreground">Final decision made</p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </SheetContent>
    </Sheet>
  )
}
