"use client";

import React from "react";
import { Affiliate } from "@/app/lib/types/affiliates";

interface AffiliateDetailDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  affiliate: Affiliate | null;
}

// This component opens the existing affiliate profile page in a new tab
export function AffiliateDetailDrawer({
  open,
  onOpenChange,
  affiliate,
}: AffiliateDetailDrawerProps) {
  React.useEffect(() => {
    if (open && affiliate) {
      // Open the affiliate profile page in a new tab
      window.open(`/super-admin/affiliates/${affiliate.id}`, "_blank");
      // Close the drawer immediately since we're opening in a new tab
      onOpenChange(false);
    }
  }, [open, affiliate, onOpenChange]);

  // Return null since we're opening in a new tab
  return null;
}
