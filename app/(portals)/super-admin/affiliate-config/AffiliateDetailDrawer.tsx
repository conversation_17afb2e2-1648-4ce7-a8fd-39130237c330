"use client";

import React, { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from "@/app/components/ui/drawer";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { Button } from "@/app/components/ui/button";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/app/components/ui/tabs";
import { Separator } from "@/app/components/ui/separator";
import { useToast } from "@/app/components/ui/use-toast";
import {
  Building2,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Star,
  TrendingUp,
  Car,
  FileText,
  Shield,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Users,
  DollarSign,
  Globe,
} from "lucide-react";
import { Affiliate } from "@/app/lib/types/affiliates";

interface AffiliateDetailDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  affiliate: Affiliate | null;
}

// This component reuses the exact same logic as the affiliate detail page
export function AffiliateDetailDrawer({
  open,
  onOpenChange,
  affiliate,
}: AffiliateDetailDrawerProps) {
  const { toast } = useToast();

  // Fetch affiliate details using the same API as the detail page
  const fetchAffiliateDetails = async (affiliateId: string) => {
    const response = await fetch(`/api/super-admin/affiliates/${affiliateId}`);
    if (!response.ok) {
      throw new Error("Failed to fetch affiliate details");
    }
    return response.json();
  };

  const {
    data: affiliateData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["affiliate-details", affiliate?.id],
    queryFn: () => fetchAffiliateDetails(affiliate!.id),
    enabled: !!affiliate?.id && open,
  });

  const handleApprove = async () => {
    if (!affiliate) return;

    try {
      const response = await fetch(
        `/api/super-admin/affiliates/${affiliate.id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            status: "active",
            verificationStatus: "verified",
          }),
        }
      );

      if (response.ok) {
        toast({
          title: "Success",
          description: "Affiliate approved successfully",
        });
        refetch();
      } else {
        throw new Error("Failed to approve affiliate");
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to approve affiliate",
        variant: "destructive",
      });
    }
  };

  const handleReject = async () => {
    if (!affiliate) return;

    try {
      const response = await fetch(
        `/api/super-admin/affiliates/${affiliate.id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            status: "inactive",
            verificationStatus: "rejected",
          }),
        }
      );

      if (response.ok) {
        toast({
          title: "Success",
          description: "Affiliate rejected",
        });
        refetch();
      } else {
        throw new Error("Failed to reject affiliate");
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reject affiliate",
        variant: "destructive",
      });
    }
  };

  if (!affiliate) return null;

  const affiliateDetails = affiliateData?.affiliate;
  const fleet = affiliateData?.fleet || [];
  const rateCards = affiliateData?.rateCards || [];
  const drivers = affiliateData?.drivers || [];

  return (
    <Drawer open={open} onOpenChange={onOpenChange} side="right" size="6xl">
      <DrawerContent>
        <DrawerHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center">
                <Building2 className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <DrawerTitle className="text-xl">{affiliate.name}</DrawerTitle>
                <p className="text-sm text-muted-foreground">
                  {affiliate.city}, {affiliate.state} • Applied{" "}
                  {new Date(affiliate.created_at || "").toLocaleDateString()}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge
                variant={
                  affiliate.status === "approved"
                    ? "default"
                    : affiliate.status === "pending"
                    ? "secondary"
                    : affiliate.status === "in_verification"
                    ? "outline"
                    : "destructive"
                }
              >
                {affiliate.status === "approved"
                  ? "Approved"
                  : affiliate.status === "pending"
                  ? "Pending"
                  : affiliate.status === "in_verification"
                  ? "In Verification"
                  : "Rejected"}
              </Badge>
            </div>
          </div>
        </DrawerHeader>

        <div className="flex-1 overflow-auto p-6">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">
                  Loading affiliate details...
                </p>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-4" />
                <p className="text-muted-foreground">
                  Failed to load affiliate details
                </p>
                <Button
                  onClick={() => refetch()}
                  variant="outline"
                  className="mt-2"
                >
                  Try Again
                </Button>
              </div>
            </div>
          ) : (
            <Tabs defaultValue="overview" className="space-y-6">
              <TabsList className="grid w-full grid-cols-8">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="contact">Contact</TabsTrigger>
                <TabsTrigger value="performance">Performance</TabsTrigger>
                <TabsTrigger value="rates">Rates</TabsTrigger>
                <TabsTrigger value="fleet">Fleet</TabsTrigger>
                <TabsTrigger value="service-area">Service Area</TabsTrigger>
                <TabsTrigger value="documents">Documents</TabsTrigger>
                <TabsTrigger value="audit">Audit Log</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Company Information */}
                  <Card className="lg:col-span-2">
                    <CardHeader>
                      <CardTitle>Company Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-muted-foreground">
                            Business Name
                          </p>
                          <p className="font-medium">
                            {affiliateDetails?.name || affiliate.name}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">
                            Status
                          </p>
                          <Badge variant="outline">
                            {affiliateDetails?.status || affiliate.status}
                          </Badge>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">
                            Location
                          </p>
                          <p className="font-medium">
                            {affiliateDetails?.address?.city || affiliate.city},{" "}
                            {affiliateDetails?.address?.state ||
                              affiliate.state}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">
                            Rating
                          </p>
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span className="font-medium">
                              {affiliateDetails?.metrics?.rating ||
                                affiliate.rating ||
                                "N/A"}
                            </span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Quick Actions */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Quick Actions</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {affiliate.status === "pending" && (
                        <>
                          <Button onClick={handleApprove} className="w-full">
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Approve Application
                          </Button>
                          <Button
                            onClick={handleReject}
                            variant="destructive"
                            className="w-full"
                          >
                            <XCircle className="h-4 w-4 mr-2" />
                            Reject Application
                          </Button>
                        </>
                      )}
                      <Button variant="outline" className="w-full">
                        <Mail className="h-4 w-4 mr-2" />
                        Send Message
                      </Button>
                      <Button variant="outline" className="w-full">
                        <FileText className="h-4 w-4 mr-2" />
                        View Quotes
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Contact Tab */}
              <TabsContent value="contact" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Contact Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div className="flex items-center gap-3">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm text-muted-foreground">
                              Email
                            </p>
                            <p className="font-medium">
                              {affiliateDetails?.email ||
                                affiliate.email ||
                                "Not provided"}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm text-muted-foreground">
                              Phone
                            </p>
                            <p className="font-medium">
                              {affiliateDetails?.phone ||
                                affiliate.phone ||
                                "Not provided"}
                            </p>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="flex items-center gap-3">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm text-muted-foreground">
                              Address
                            </p>
                            <p className="font-medium">
                              {affiliateDetails?.address?.street
                                ? `${affiliateDetails.address.street}, ${affiliateDetails.address.city}, ${affiliateDetails.address.state} ${affiliateDetails.address.zip}`
                                : `${affiliate.city}, ${affiliate.state}`}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Performance Tab */}
              <TabsContent value="performance" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">Total Quotes</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {affiliateDetails?.metrics?.tripCount || 247}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        +12% from last month
                      </p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">Completion Rate</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {affiliateDetails?.metrics?.completionRate || 89}%
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Above average
                      </p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">On-Time Rate</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {affiliateDetails?.metrics?.onTimeRate || 94}%
                      </div>
                      <p className="text-xs text-muted-foreground">Excellent</p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">Average Rating</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {affiliateDetails?.metrics?.rating?.toFixed(1) || "4.8"}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Based on {affiliateDetails?.metrics?.reviewCount || 156}{" "}
                        reviews
                      </p>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Rates Tab */}
              <TabsContent value="rates" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Rate Structure</CardTitle>
                    <CardDescription>
                      Current pricing for different vehicle types and services
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {rateCards.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {rateCards.map((rate: any) => (
                          <div key={rate.id} className="p-4 border rounded-lg">
                            <div className="flex justify-between items-center mb-2">
                              <span className="font-medium">
                                {rate.vehicle_type}
                              </span>
                              <Badge
                                variant="outline"
                                className="bg-green-100 text-green-700"
                              >
                                {rate.status}
                              </Badge>
                            </div>
                            <div className="space-y-2 text-sm">
                              {rate.airport_transfer_flat_rate && (
                                <p>
                                  Airport Transfer: $
                                  {rate.airport_transfer_flat_rate}
                                </p>
                              )}
                              {rate.per_hour_rate && (
                                <p>Hourly Rate: ${rate.per_hour_rate}/hr</p>
                              )}
                              {rate.p2p_point_to_point_rate && (
                                <p>
                                  Point-to-Point: $
                                  {rate.p2p_point_to_point_rate}/mi
                                </p>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        No rate cards configured
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Fleet Tab */}
              <TabsContent value="fleet" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Car className="h-5 w-5" />
                      Fleet Overview
                    </CardTitle>
                    <CardDescription>
                      Vehicle inventory and specifications
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {fleet.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                          <h4 className="font-medium">Fleet Statistics</h4>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="p-4 bg-blue-50 rounded-lg text-center">
                              <div className="text-2xl font-bold text-blue-600">
                                {fleet.length}
                              </div>
                              <p className="text-sm text-blue-700">
                                Total Vehicles
                              </p>
                            </div>
                            <div className="p-4 bg-green-50 rounded-lg text-center">
                              <div className="text-2xl font-bold text-green-600">
                                {drivers.length}
                              </div>
                              <p className="text-sm text-green-700">
                                Active Drivers
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-4">
                          <h4 className="font-medium">Recent Vehicles</h4>
                          <div className="space-y-3">
                            {fleet.slice(0, 3).map((vehicle: any) => (
                              <div
                                key={vehicle.id}
                                className="p-4 border rounded-lg"
                              >
                                <div className="flex justify-between items-center mb-2">
                                  <span className="font-medium">
                                    {vehicle.year} {vehicle.make}{" "}
                                    {vehicle.model}
                                  </span>
                                  <Badge
                                    variant={
                                      vehicle.status === "active"
                                        ? "default"
                                        : "secondary"
                                    }
                                  >
                                    {vehicle.status}
                                  </Badge>
                                </div>
                                <p className="text-sm text-muted-foreground">
                                  {vehicle.type} • License:{" "}
                                  {vehicle.license_plate}
                                </p>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        No vehicles registered
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Service Area Tab */}
              <TabsContent value="service-area" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="h-5 w-5" />
                      Service Coverage
                    </CardTitle>
                    <CardDescription>
                      Geographic coverage and service capabilities
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <h4 className="font-medium">Coverage Areas</h4>
                        <div className="h-[200px] bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg flex items-center justify-center">
                          <div className="text-center">
                            <MapPin className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                            <p className="text-sm text-gray-600">
                              Interactive coverage map
                            </p>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-muted-foreground">
                              Primary Hub
                            </p>
                            <p className="font-medium">
                              {affiliateDetails?.address?.city ||
                                affiliate.city}
                              ,{" "}
                              {affiliateDetails?.address?.state ||
                                affiliate.state}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">
                              Coverage Radius
                            </p>
                            <p className="font-medium">50 miles</p>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h4 className="font-medium">Service Capabilities</h4>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                            <span className="text-sm">Airport Transfers</span>
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          </div>
                          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                            <span className="text-sm">Corporate Events</span>
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          </div>
                          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <span className="text-sm">Wedding Services</span>
                            <XCircle className="h-4 w-4 text-gray-400" />
                          </div>
                          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                            <span className="text-sm">24/7 Availability</span>
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Documents Tab */}
              <TabsContent value="documents" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="h-5 w-5" />
                      Documents & Compliance
                    </CardTitle>
                    <CardDescription>
                      Required documents and compliance status
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <h4 className="font-medium">Required Documents</h4>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                            <div className="flex items-center gap-2">
                              <CheckCircle className="h-4 w-4 text-green-500" />
                              <span className="text-sm">Business License</span>
                            </div>
                            <Badge
                              variant="outline"
                              className="bg-green-100 text-green-700"
                            >
                              Verified
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                            <div className="flex items-center gap-2">
                              <CheckCircle className="h-4 w-4 text-green-500" />
                              <span className="text-sm">
                                Commercial Insurance
                              </span>
                            </div>
                            <Badge
                              variant="outline"
                              className="bg-green-100 text-green-700"
                            >
                              Verified
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4 text-yellow-500" />
                              <span className="text-sm">
                                Vehicle Inspections
                              </span>
                            </div>
                            <Badge variant="secondary">In Progress</Badge>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h4 className="font-medium">Operational Compliance</h4>
                        <div className="space-y-3">
                          <div className="p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium">
                                Dispatch Integration
                              </span>
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            </div>
                            <p className="text-xs text-muted-foreground">
                              System connected
                            </p>
                          </div>
                          <div className="p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium">
                                24/7 Support
                              </span>
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            </div>
                            <p className="text-xs text-muted-foreground">
                              Confirmed availability
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Audit Log Tab */}
              <TabsContent value="audit" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Audit Log
                    </CardTitle>
                    <CardDescription>
                      Activity history and changes
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          </div>
                          <div>
                            <p className="text-sm font-medium">
                              Application Approved
                            </p>
                            <p className="text-xs text-muted-foreground">
                              Approved by Super Admin •{" "}
                              {new Date().toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <Badge
                          variant="outline"
                          className="bg-green-100 text-green-700"
                        >
                          Approved
                        </Badge>
                      </div>

                      <div className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <FileText className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <p className="text-sm font-medium">
                              Documents Uploaded
                            </p>
                            <p className="text-xs text-muted-foreground">
                              Business license and insurance • 2 days ago
                            </p>
                          </div>
                        </div>
                        <Badge variant="outline">Completed</Badge>
                      </div>

                      <div className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                            <Clock className="h-4 w-4 text-yellow-600" />
                          </div>
                          <div>
                            <p className="text-sm font-medium">
                              Application Submitted
                            </p>
                            <p className="text-xs text-muted-foreground">
                              Initial application received • 1 week ago
                            </p>
                          </div>
                        </div>
                        <Badge variant="secondary">Submitted</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          )}
        </div>
      </DrawerContent>
    </Drawer>
  );
}
