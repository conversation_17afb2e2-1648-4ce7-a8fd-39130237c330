'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle, DialogFooter } from '@/app/components/ui/dialog'
import { Button } from '@/app/components/ui/button'
import { Textarea } from '@/app/components/ui/textarea'
import { Checkbox } from '@/app/components/ui/checkbox'
import { Label } from '@/app/components/ui/label'
import { X } from 'lucide-react'

interface RejectionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onReject: (reasons: string[], customReason: string) => void
  affiliateName: string
}

const REJECTION_REASONS = [
  { id: 'documentation', label: 'Incomplete or Invalid Documentation' },
  { id: 'insurance', label: 'Insurance Requirements Not Met' },
  { id: 'fleet', label: 'Fleet Information Incomplete' },
  { id: 'rates', label: 'Rate Cards Missing or Invalid' },
  { id: 'business_info', label: 'Business Information Incomplete' },
  { id: 'compliance', label: 'Compliance Requirements Not Met' },
  { id: 'service_area', label: 'Service Area Not Covered' },
  { id: 'quality', label: 'Quality Standards Not Met' },
  { id: 'other', label: 'Other (specify below)' }
]

export function RejectionDialog({ open, onOpenChange, onReject, affiliateName }: RejectionDialogProps) {
  const [selectedReasons, setSelectedReasons] = useState<string[]>([])
  const [customReason, setCustomReason] = useState('')

  const handleReasonChange = (reasonId: string, checked: boolean) => {
    if (checked) {
      setSelectedReasons(prev => [...prev, reasonId])
    } else {
      setSelectedReasons(prev => prev.filter(id => id !== reasonId))
    }
  }

  const handleSubmit = () => {
    if (selectedReasons.length === 0 && !customReason.trim()) {
      alert('Please select at least one reason or provide a custom reason.')
      return
    }

    onReject(selectedReasons, customReason)

    // Reset form
    setSelectedReasons([])
    setCustomReason('')
    onOpenChange(false)
  }

  const handleCancel = () => {
    setSelectedReasons([])
    setCustomReason('')
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            Reject Application
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCancel}
              className="h-6 w-6 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            You are about to reject the application for <strong>{affiliateName}</strong>.
            Please select the reason(s) for rejection:
          </p>

          <div className="space-y-3">
            {REJECTION_REASONS.map((reason) => (
              <div key={reason.id} className="flex items-center space-x-2">
                <Checkbox
                  id={reason.id}
                  checked={selectedReasons.includes(reason.id)}
                  onCheckedChange={(checked) => handleReasonChange(reason.id, !!checked)}
                />
                <Label htmlFor={reason.id} className="text-sm font-normal">
                  {reason.label}
                </Label>
              </div>
            ))}
          </div>

          <div className="space-y-2">
            <Label htmlFor="custom-reason" className="text-sm font-medium">
              Additional Comments (Optional)
            </Label>
            <Textarea
              id="custom-reason"
              placeholder="Provide specific details about the rejection..."
              value={customReason}
              onChange={(e) => setCustomReason(e.target.value)}
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleSubmit}
            disabled={selectedReasons.length === 0 && !customReason.trim()}
          >
            Reject Application
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
