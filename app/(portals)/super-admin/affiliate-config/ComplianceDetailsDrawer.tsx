"use client"

import { She<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Sheet<PERSON>lose } from "@/app/components/ui/sheet"
import { But<PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/app/components/ui/tabs"
import { FileText, History, CheckCircle2, AlertTriangle, Upload, Eye } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table"
import { Badge } from "@/app/components/ui/badge"
import { Affiliate, ComplianceEntry } from "@/app/lib/types/affiliates"

interface ComplianceDetailsDrawerProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  affiliate: Affiliate | null
  complianceData: ComplianceEntry | null
}

// Mock detailed document list
const requiredDocuments = [
  { id: 'insurance', name: 'Certificate of Insurance (COI)', required: true },
  { id: 'license', name: 'Business Operating License', required: true },
  { id: 'w9', name: 'W-9 Form', required: true },
  { id: 'drivers', name: 'Driver Documentation (List & Licenses)', required: true },
  { id: 'vehicles', name: 'Vehicle Documentation (Registrations)', required: true },
  { id: 'agreement', name: 'Signed Partner Agreement', required: false },
];

// Mock Audit Log entries for a specific affiliate
const mockAuditLog = [
  { timestamp: '2024-07-01 10:00:00', action: 'Document Verified', user: '<EMAIL>', details: 'Insurance Verified' },
  { timestamp: '2024-06-28 15:30:00', action: 'Document Uploaded', user: '<EMAIL>', details: 'Insurance Certificate v2' },
  { timestamp: '2024-06-25 11:15:00', action: 'Verification Failed', user: '<EMAIL>', details: 'License number mismatch' },
  { timestamp: '2024-06-24 09:00:00', action: 'Document Uploaded', user: '<EMAIL>', details: 'Business License' },
];

export function ComplianceDetailsDrawer({ open, onOpenChange, affiliate, complianceData }: ComplianceDetailsDrawerProps) {
  if (!affiliate) return null

  // Use mock data if no real compliance data is provided
  const mockComplianceData = {
    partnerId: affiliate.id,
    overallStatus: 'Pending',
    insuranceStatus: 'Verified',
    insuranceExpiry: '2024-12-31',
    licenseStatus: 'Pending',
    licenseExpiry: '2024-11-30',
    lastAudit: '2024-06-15'
  }

  const effectiveComplianceData = complianceData || mockComplianceData

  const getStatusBadge = (status: string) => {
     switch (status) {
      case 'Verified': return <Badge variant="success">Verified</Badge>;
      case 'Pending': return <Badge variant="secondary">Pending</Badge>;
      case 'Missing': return <Badge variant="destructive">Missing</Badge>;
      case 'Expired': return <Badge variant="destructive">Expired</Badge>;
      default: return <Badge variant="secondary">{status || 'Unknown'}</Badge>;
    }
  };

  const getDocumentStatus = (docId: string) => {
    switch(docId) {
      case 'insurance': return { status: effectiveComplianceData.insuranceStatus, expiry: effectiveComplianceData.insuranceExpiry };
      case 'license': return { status: effectiveComplianceData.licenseStatus, expiry: effectiveComplianceData.licenseExpiry };
      // Add cases for other documents based on effectiveComplianceData structure
      default: return { status: 'Missing', expiry: null };
    }
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-4xl w-full p-0"> {/* Made wider */}
        <SheetHeader className="px-6 pt-6 pb-4 border-b">
           <div className="flex justify-between items-center">
             <div>
                <SheetTitle className="text-xl font-semibold">Compliance Details: {affiliate.name}</SheetTitle>
                <p className="text-sm text-muted-foreground">Document status and audit log for {affiliate.name}.</p>
             </div>
              <SheetClose asChild>
                <Button size="icon" variant="ghost">
                  <span className="sr-only">Close</span>
                  ×
                </Button>
             </SheetClose>
           </div>
        </SheetHeader>

        <div className="p-6 overflow-y-auto h-[calc(100vh-80px)]"> {/* Adjust height calculation as needed */}
           <Tabs defaultValue="documents">
              <TabsList className="mb-4">
                <TabsTrigger value="documents"><FileText className="mr-2 h-4 w-4" /> Documents</TabsTrigger>
                <TabsTrigger value="auditLog"><History className="mr-2 h-4 w-4" /> Audit Log</TabsTrigger>
              </TabsList>

              <TabsContent value="documents">
                 <Card>
                   <CardHeader>
                     <CardTitle>Document Status</CardTitle>
                     <CardDescription>Review and verify uploaded compliance documents for {affiliate.name}.</CardDescription>
                   </CardHeader>
                   <CardContent>
                     <div className="rounded-md border">
                       <Table>
                         <TableHeader>
                           <TableRow>
                             <TableHead>Document Name</TableHead>
                             <TableHead>Status</TableHead>
                             <TableHead>Expiration Date</TableHead>
                             <TableHead className="text-right">Actions</TableHead>
                           </TableRow>
                         </TableHeader>
                         <TableBody>
                           {requiredDocuments.map(doc => {
                             const docInfo = getDocumentStatus(doc.id);
                             const isMissing = docInfo.status === 'Missing';
                             const isVerified = docInfo.status === 'Verified';
                             return (
                               <TableRow key={doc.id}>
                                 <TableCell className="font-medium">
                                   {doc.name}
                                   {doc.required && !isMissing && <span className="text-destructive ml-1">*</span>}
                                 </TableCell>
                                 <TableCell>{getStatusBadge(docInfo.status)}</TableCell>
                                 <TableCell>{docInfo.expiry ? new Date(docInfo.expiry).toLocaleDateString() : 'N/A'}</TableCell>
                                 <TableCell className="text-right">
                                   {isMissing ? (
                                     <Button variant="outline" size="sm">
                                       <Upload className="mr-2 h-3 w-3" /> Request Upload
                                     </Button>
                                   ) : (
                                     <div className="flex justify-end gap-2">
                                       <Button variant="outline" size="sm">
                                         <Eye className="mr-2 h-3 w-3" /> View
                                       </Button>
                                       <Button
                                         variant={isVerified ? "secondary" : "outline"}
                                         size="sm"
                                         disabled={isVerified}
                                         className={!isVerified ? "text-green-600 hover:text-green-700" : ""}
                                       >
                                         <CheckCircle2 className="mr-2 h-3 w-3" /> {isVerified ? 'Verified' : 'Verify'}
                                       </Button>
                                     </div>
                                   )}
                                 </TableCell>
                               </TableRow>
                             );
                           })}
                         </TableBody>
                       </Table>
                     </div>
                   </CardContent>
                 </Card>
              </TabsContent>

              <TabsContent value="auditLog">
                 <Card>
                   <CardHeader className="flex flex-row items-center justify-between">
                     <div>
                        <CardTitle>Audit Log</CardTitle>
                        <CardDescription>History of compliance-related actions for {affiliate.name}.</CardDescription>
                     </div>
                     <Button variant="outline" size="sm">Export Log</Button>
                   </CardHeader>
                   <CardContent>
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Date & Time</TableHead>
                              <TableHead>Action</TableHead>
                              <TableHead>User</TableHead>
                              <TableHead>Details</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {mockAuditLog.length === 0 ? (
                              <TableRow><TableCell colSpan={4} className="text-center h-24">No audit history found.</TableCell></TableRow>
                            ) : (
                              mockAuditLog.map((log, index) => (
                                <TableRow key={index}>
                                  <TableCell>{new Date(log.timestamp).toLocaleString()}</TableCell>
                                  <TableCell>{log.action}</TableCell>
                                  <TableCell>{log.user}</TableCell>
                                  <TableCell>{log.details}</TableCell>
                                </TableRow>
                              ))
                            )}
                          </TableBody>
                        </Table>
                      </div>
                   </CardContent>
                 </Card>
              </TabsContent>
           </Tabs>
        </div>
      </SheetContent>
    </Sheet>
  )
}