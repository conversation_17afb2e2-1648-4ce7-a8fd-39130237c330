'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from '@/app/components/ui/dialog'
import { Button } from '@/app/components/ui/button'
import { Input } from '@/app/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/app/components/ui/table'
import { Badge } from '@/app/components/ui/badge'
import { ScrollArea } from '@/app/components/ui/scroll-area'
import { Calendar, Filter, Download, Eye, X } from 'lucide-react'
import { format } from 'date-fns'

interface AuditLogEntry {
  id: string
  user_id: string
  action: string
  table_name: string
  record_id: string
  details: any
  created_at: string
  profiles?: {
    email: string
    first_name: string
    last_name: string
  }
}

interface AuditLogViewerProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  affiliateId?: string
}

export function AuditLogViewer({ open, onOpenChange, affiliateId }: AuditLogViewerProps) {
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([])
  const [loading, setLoading] = useState(false)
  const [filters, setFilters] = useState({
    action: '',
    table_name: '',
    start_date: '',
    end_date: '',
    user_id: ''
  })
  const [availableActions, setAvailableActions] = useState<string[]>([])
  const [availableTables, setAvailableTables] = useState<string[]>([])
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    total_pages: 0
  })
  const [selectedLog, setSelectedLog] = useState<AuditLogEntry | null>(null)
  const [showDetails, setShowDetails] = useState(false)

  const fetchAuditLogs = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...Object.fromEntries(Object.entries(filters).filter(([_, value]) => value))
      })

      if (affiliateId) {
        params.append('record_id', affiliateId)
      }

      const response = await fetch(`/api/super-admin/audit-logs?${params}`)
      if (response.ok) {
        const data = await response.json()
        setAuditLogs(data.audit_logs)
        setPagination(data.pagination)
        setAvailableActions(data.filters.actions)
        setAvailableTables(data.filters.tables)
      } else {
        console.error('Failed to fetch audit logs')
      }
    } catch (error) {
      console.error('Error fetching audit logs:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (open) {
      fetchAuditLogs()
    }
  }, [open, pagination.page, filters])

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setPagination(prev => ({ ...prev, page: 1 })) // Reset to first page
  }

  const clearFilters = () => {
    setFilters({
      action: '',
      table_name: '',
      start_date: '',
      end_date: '',
      user_id: ''
    })
  }

  const getActionBadgeColor = (action: string) => {
    if (action.includes('approved')) return 'bg-green-100 text-green-800'
    if (action.includes('rejected')) return 'bg-red-100 text-red-800'
    if (action.includes('created')) return 'bg-blue-100 text-blue-800'
    if (action.includes('updated')) return 'bg-yellow-100 text-yellow-800'
    if (action.includes('deleted')) return 'bg-red-100 text-red-800'
    return 'bg-gray-100 text-gray-800'
  }

  const formatUserName = (log: AuditLogEntry) => {
    if (log.profiles) {
      return `${log.profiles.first_name} ${log.profiles.last_name} (${log.profiles.email})`
    }
    return log.user_id
  }

  const viewDetails = (log: AuditLogEntry) => {
    setSelectedLog(log)
    setShowDetails(true)
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-6xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Audit Log Viewer
              {affiliateId && <span className="text-sm text-muted-foreground">(Filtered by Affiliate)</span>}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* Filters */}
            <div className="flex flex-wrap gap-2 p-4 border rounded-lg bg-muted/50">
              <Select value={filters.action} onValueChange={(value) => handleFilterChange('action', value)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by Action" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Actions</SelectItem>
                  {availableActions.map(action => (
                    <SelectItem key={action} value={action}>{action}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={filters.table_name} onValueChange={(value) => handleFilterChange('table_name', value)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by Table" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Tables</SelectItem>
                  {availableTables.map(table => (
                    <SelectItem key={table} value={table}>{table}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Input
                type="date"
                placeholder="Start Date"
                value={filters.start_date}
                onChange={(e) => handleFilterChange('start_date', e.target.value)}
                className="w-[150px]"
              />

              <Input
                type="date"
                placeholder="End Date"
                value={filters.end_date}
                onChange={(e) => handleFilterChange('end_date', e.target.value)}
                className="w-[150px]"
              />

              <Button variant="outline" onClick={clearFilters}>
                Clear Filters
              </Button>
            </div>

            {/* Audit Logs Table */}
            <div className="border rounded-lg">
              <ScrollArea className="h-[400px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Timestamp</TableHead>
                      <TableHead>User</TableHead>
                      <TableHead>Action</TableHead>
                      <TableHead>Table</TableHead>
                      <TableHead>Record ID</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8">
                          Loading audit logs...
                        </TableCell>
                      </TableRow>
                    ) : auditLogs.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8">
                          No audit logs found
                        </TableCell>
                      </TableRow>
                    ) : (
                      auditLogs.map(log => (
                        <TableRow key={log.id}>
                          <TableCell className="text-sm">
                            {format(new Date(log.created_at), 'MMM dd, yyyy HH:mm:ss')}
                          </TableCell>
                          <TableCell className="text-sm">
                            {formatUserName(log)}
                          </TableCell>
                          <TableCell>
                            <Badge className={getActionBadgeColor(log.action)}>
                              {log.action}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-sm">{log.table_name}</TableCell>
                          <TableCell className="text-sm font-mono">{log.record_id}</TableCell>
                          <TableCell>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => viewDetails(log)}
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              Details
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </ScrollArea>
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                Showing {auditLogs.length} of {pagination.total} entries
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={pagination.page <= 1}
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                >
                  Previous
                </Button>
                <span className="text-sm py-2 px-3">
                  Page {pagination.page} of {pagination.total_pages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={pagination.page >= pagination.total_pages}
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                >
                  Next
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Details Modal */}
      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              Audit Log Details
              <Button variant="ghost" size="sm" onClick={() => setShowDetails(false)}>
                <X className="h-4 w-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>

          {selectedLog && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Timestamp</label>
                  <p className="text-sm text-muted-foreground">
                    {format(new Date(selectedLog.created_at), 'MMM dd, yyyy HH:mm:ss')}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">User</label>
                  <p className="text-sm text-muted-foreground">
                    {formatUserName(selectedLog)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">Action</label>
                  <Badge className={getActionBadgeColor(selectedLog.action)}>
                    {selectedLog.action}
                  </Badge>
                </div>
                <div>
                  <label className="text-sm font-medium">Table</label>
                  <p className="text-sm text-muted-foreground">{selectedLog.table_name}</p>
                </div>
                <div className="col-span-2">
                  <label className="text-sm font-medium">Record ID</label>
                  <p className="text-sm text-muted-foreground font-mono">{selectedLog.record_id}</p>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium">Details</label>
                <ScrollArea className="h-[200px] mt-2">
                  <pre className="text-xs bg-muted p-3 rounded-md overflow-auto">
                    {JSON.stringify(selectedLog.details, null, 2)}
                  </pre>
                </ScrollArea>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
