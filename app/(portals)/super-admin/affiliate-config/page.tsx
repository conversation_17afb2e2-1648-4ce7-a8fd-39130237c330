"use client";

import { useState, use<PERSON>em<PERSON>, use<PERSON><PERSON>back, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";
import { Input } from "@/app/components/ui/input";
import { But<PERSON> } from "@/app/components/ui/button";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/app/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import {
  Search,
  Plus,
  MoreHorizontal,
  Network,
  Filter,
  MapPin,
  Star,
  CheckCircle,
  XCircle,
  Car,
  ArrowUpDown,
  Download,
  Check,
  X,
  CheckCircle2,
  ShieldAlert,
  FileWarning,
  Hourglass,
  Users,
  Truck,
  ClipboardCheck,
  Clock,
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table";
import { Badge } from "@/app/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";
import { useToast } from "@/app/components/ui/use-toast";
import { Progress } from "@/app/components/ui/progress";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Label } from "@/app/components/ui/label";
import { Switch } from "@/app/components/ui/switch";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/app/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/app/components/ui/alert-dialog";
import { RatesFleetDetailsDrawer } from "./RatesFleetDetailsDrawer";
import { ComplianceDetailsDrawer } from "./ComplianceDetailsDrawer";
import { GeneralDetailsDrawer } from "./GeneralDetailsDrawer";
import { ApplicationDetailsDrawer } from "./ApplicationDetailsDrawer";
import { AffiliateProfileDrawer } from "./AffiliateProfileDrawer";
import { RejectionDialog } from "./RejectionDialog";
import { Loader2 } from "lucide-react";
import {
  Affiliate,
  PartnerData,
  ComplianceEntry,
  RateData,
  AffiliateStatus,
} from "@/app/lib/types/affiliates";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

const mockApplications = [
  {
    id: "app_001",
    name: "Executive Car Fleet",
    city: "Chicago",
    state: "IL",
    status: "pending",
    progress: 40,
    submitted: "2024-06-01",
    email: "<EMAIL>",
  },
  {
    id: "app_002",
    name: "Coastal Transfers",
    city: "Miami",
    state: "FL",
    status: "in_verification",
    progress: 70,
    submitted: "2024-05-28",
    email: "<EMAIL>",
  },
  {
    id: "app_003",
    name: "Golden State Chauffeurs",
    city: "Los Angeles",
    state: "CA",
    status: "approved",
    progress: 100,
    submitted: "2024-05-20",
    email: "<EMAIL>",
  },
];

function debounce<T extends (...args: any[]) => void>(fn: T, wait: number) {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => fn(...args), wait);
  };
}

// --- Rates Management ---
const vehicleTypes = [
  { id: "sedan", label: "SEDAN" },
  { id: "suv", label: "SUV" },
  { id: "luxury_sedan", label: "LUXURY SEDAN" },
  { id: "luxury_suv", label: "LUXURY SUV" },
  { id: "sprinter", label: "MERCEDES SPRINTER" },
  { id: "executive_sprinter", label: "EXECUTIVE SPRINTER" },
  { id: "stretch", label: "STRETCH LIMOUSINE" },
  { id: "hummer", label: "HUMMER LIMOUSINE" },
  { id: "sprinter_limo", label: "SPRINTER LIMO" },
  { id: "minibus", label: "MINI BUS" },
  { id: "motorcoach", label: "MOTOR COACH" },
  { id: "tesla", label: "TESLA" },
  { id: "party_bus", label: "PARTY BUS" },
  { id: "wheelchair", label: "WHEELCHAIR ACCESSIBLE VEHICLE" },
];

const ratesFormSchema = z.object({
  vehicles: z.array(
    z.object({
      type: z.string(),
      enabled: z.boolean(),
      useDistanceTime: z.boolean().default(false),
      pointToPointRate: z.string().optional(),
      baseDistanceRate: z.string().optional(),
      perMileRate: z.string().optional(),
      perHourRate: z.string().optional(),
      minimumMiles: z.string().optional(),
      extraHourRate: z.string().optional(),
      airportRate: z.string().optional(),
      hourlyRate: z.string().optional(),
      minimumHours: z.string().optional(),
      cancellationPolicy: z.string().optional(),
    })
  ),
  seasonalRanges: z.array(
    z.object({
      id: z.string(),
      name: z.string().min(1, "Please enter a name for this seasonal period"),
      startDate: z.date(),
      endDate: z.date(),
      description: z.string().optional(),
    })
  ),
});

// --- Fleet Management ---
const fleetVehicleSchema = z.object({
  typeId: z.string().min(1, "Please select a vehicle type"),
  make: z.string().min(1, "Please enter the make"),
  model: z.string().min(1, "Please enter the model"),
  year: z.string().min(4, "Please enter a valid year"),
  capacity: z.string().min(1, "Please enter the capacity"),
  status: z.string().default("active"),
});

// --- Mock Data ---
const mockPartners: PartnerData[] = [
  {
    id: "partner1",
    name: "Prestige Limo",
    location: "New York, NY",
    fleetSize: 15,
    status: "Active",
    configProgress: 80,
  },
  {
    id: "partner2",
    name: "Executive Transport",
    location: "Los Angeles, CA",
    fleetSize: 25,
    status: "Active",
    configProgress: 100,
  },
  {
    id: "partner3",
    name: "City Cruisers",
    location: "Chicago, IL",
    fleetSize: 10,
    status: "Pending Approval",
    configProgress: 30,
  },
  {
    id: "partner4",
    name: "Global Shuttles",
    location: "Miami, FL",
    fleetSize: 12,
    status: "Compliance Review",
    configProgress: 60,
  },
];

const mockComplianceData: ComplianceEntry[] = [
  {
    partnerId: "partner1",
    overallStatus: "Verified",
    insuranceStatus: "Verified",
    insuranceExpiry: "2025-01-01",
    licenseStatus: "Verified",
    licenseExpiry: "2024-12-01",
    driverDocsStatus: "Verified",
    vehicleDocsStatus: "Verified",
    lastAudit: "2024-03-01",
  },
  {
    partnerId: "partner2",
    overallStatus: "Verified",
    insuranceStatus: "Verified",
    insuranceExpiry: "2025-02-15",
    licenseStatus: "Verified",
    licenseExpiry: "2025-01-10",
    driverDocsStatus: "Verified",
    vehicleDocsStatus: "Verified",
    lastAudit: "2024-04-10",
  },
  {
    partnerId: "partner3",
    overallStatus: "Pending",
    insuranceStatus: "Missing",
    insuranceExpiry: "N/A",
    licenseStatus: "Pending",
    licenseExpiry: "N/A",
    driverDocsStatus: "Missing",
    vehicleDocsStatus: "Pending",
    lastAudit: "N/A",
  },
  {
    partnerId: "partner4",
    overallStatus: "Issues Found",
    insuranceStatus: "Expired",
    insuranceExpiry: "2024-06-01",
    licenseStatus: "Verified",
    licenseExpiry: "2024-11-01",
    driverDocsStatus: "Verified",
    vehicleDocsStatus: "Issues Found",
    lastAudit: "2024-05-20",
  },
];

// Mock Rates Data (Example)
const mockRatesData: { [partnerId: string]: RateData[] } = {
  partner1: [
    { vehicleType: "Sedan", p2pRate: 80, hourlyRate: 75, minHours: 3 },
    { vehicleType: "SUV", p2pRate: 100, hourlyRate: 95, minHours: 3 },
  ],
  partner2: [
    { vehicleType: "Sedan", hourlyRate: 80, minHours: 4 },
    { vehicleType: "SUV", hourlyRate: 100, minHours: 4 },
    { vehicleType: "Sprinter", hourlyRate: 150, minHours: 5 },
  ],
  // partner3, partner4 might not have rates configured yet
};

export default function AffiliateConfigPage() {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [showAddModal, setShowAddModal] = useState(false);
  const [showRatesDrawer, setShowRatesDrawer] = useState(false);
  const [showComplianceDrawer, setShowComplianceDrawer] = useState(false);
  const [showGeneralDrawer, setShowGeneralDrawer] = useState(false);
  const [showApplicationDrawer, setShowApplicationDrawer] = useState(false);
  const [showAffiliateProfileDrawer, setShowAffiliateProfileDrawer] =
    useState(false);
  const [selectedAffiliateForDrawer, setSelectedAffiliateForDrawer] =
    useState<Affiliate | null>(null);
  const [selectedApplicationForDrawer, setSelectedApplicationForDrawer] =
    useState<any>(null);
  const [selectedApplicationForRejection, setSelectedApplicationForRejection] =
    useState<{ id: string; name: string } | null>(null);
  const [showRejectionDialog, setShowRejectionDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [partners, setPartners] = useState<PartnerData[]>(mockPartners);
  const [compliance, setCompliance] =
    useState<ComplianceEntry[]>(mockComplianceData);
  const [rates, setRates] = useState<{ [partnerId: string]: RateData[] }>(
    mockRatesData
  );
  const [activeTab, setActiveTab] = useState<string>("affiliates");
  const queryClient = useQueryClient();

  // Function to refresh affiliates data
  const fetchAffiliates = () => {
    queryClient.invalidateQueries({ queryKey: ["affiliates"] });
  };

  const getStatusBadgeClass = (status: AffiliateStatus) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "inactive":
        return "bg-red-100 text-red-800";
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date);
  };

  const handleStatusChange = (id: string, newStatus: AffiliateStatus) => {
    updateAffiliateMutation.mutate({ id, status: newStatus });
  };

  const renderRatingStars = (rating: number) => {
    return (
      <div className="flex items-center">
        <div className="mr-1">{rating.toFixed(1)}</div>
        <div className="flex">
          {[1, 2, 3, 4, 5].map((star) => (
            <Star
              key={star}
              className={`h-3 w-3 ${
                star <= Math.round(rating)
                  ? "text-yellow-500 fill-yellow-500"
                  : "text-gray-300"
              }`}
            />
          ))}
        </div>
      </div>
    );
  };

  // Debounced search handler
  const debouncedSetSearchQuery = useMemo(
    () => debounce(setSearchQuery, 300),
    []
  );
  const handleSearch = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      debouncedSetSearchQuery(e.target.value);
    },
    [debouncedSetSearchQuery]
  );

  // --- Mock Data ---
  const mockApplications = [
    {
      id: "1",
      name: "Elite Transportation",
      city: "Chicago",
      state: "IL",
      email: "<EMAIL>",
      submitted: "2024-06-01",
      status: "pending",
      progress: 40,
    },
    {
      id: "2",
      name: "Coastal Transfers",
      city: "Miami",
      state: "FL",
      email: "<EMAIL>",
      submitted: "2024-05-28",
      status: "in_verification",
      progress: 70,
    },
    {
      id: "3",
      name: "Golden State Chauffeurs",
      city: "Los Angeles",
      state: "CA",
      email: "<EMAIL>",
      submitted: "2024-05-20",
      status: "approved",
      progress: 100,
    },
  ];

  // Memoized filtered applications
  const filteredApplications = useMemo(() => {
    let filtered = mockApplications;

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(
        (a) =>
          a.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          a.city.toLowerCase().includes(searchQuery.toLowerCase()) ||
          a.state.toLowerCase().includes(searchQuery.toLowerCase()) ||
          a.email.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((a) => a.status === statusFilter);
    }

    return filtered;
  }, [searchQuery, statusFilter]);

  // --- Rates State ---
  const [seasonalRanges, setSeasonalRanges] = useState([]);
  const ratesForm = useForm({
    resolver: zodResolver(ratesFormSchema),
    defaultValues: {
      vehicles: vehicleTypes.map((type) => ({
        type: type.id,
        enabled: false,
        useDistanceTime: false,
        pointToPointRate: "",
        baseDistanceRate: "",
        perMileRate: "",
        perHourRate: "",
        minimumMiles: "",
        extraHourRate: "",
        airportRate: "",
        hourlyRate: "",
        minimumHours: "",
        cancellationPolicy: "",
      })),
      seasonalRanges: [],
    },
  });
  // --- Fleet State ---
  const [fleet, setFleet] = useState([]); // TODO: Replace with backend data
  const fleetForm = useForm({
    resolver: zodResolver(fleetVehicleSchema),
    defaultValues: { status: "active" },
  });
  const [showFleetModal, setShowFleetModal] = useState(false);
  const [editingVehicle, setEditingVehicle] = useState(null);

  const complianceSummary = useMemo(() => {
    let issues = 0;
    let expired = 0;
    let pending = 0;
    compliance.forEach((data) => {
      if (
        data.overallStatus === "Issues Found" ||
        data.vehicleDocsStatus === "Issues Found"
      )
        issues++;
      if (
        data.insuranceStatus === "Expired" ||
        data.licenseStatus === "Expired"
      )
        expired++;
      if (
        data.overallStatus === "Pending" ||
        data.insuranceStatus === "Pending" ||
        data.licenseStatus === "Pending" ||
        data.driverDocsStatus === "Pending" ||
        data.vehicleDocsStatus === "Pending"
      )
        pending++;
    });
    return { issues, expired, pending };
  }, [compliance]);

  // Function to open Rates drawer
  const handleOpenRatesDetails = (affiliate: Affiliate) => {
    setSelectedAffiliateForDrawer(affiliate);
    setShowRatesDrawer(true);
  };

  // Function to open Compliance drawer
  const handleOpenComplianceDetails = (affiliate: Affiliate) => {
    setSelectedAffiliateForDrawer(affiliate);
    setShowComplianceDrawer(true);
  };

  // Function to open General drawer
  const handleOpenGeneralDetails = (affiliate: Affiliate) => {
    setSelectedAffiliateForDrawer(affiliate);
    setShowGeneralDrawer(true);
  };

  // Function to open Application drawer (legacy - for small details)
  const handleOpenApplicationDetails = (application: any) => {
    setSelectedApplicationForDrawer(application);
    setShowApplicationDrawer(true);
  };

  // Function to open Affiliate Profile drawer (large profile view)
  const handleOpenAffiliateProfile = (affiliate: Affiliate) => {
    setSelectedAffiliateForDrawer(affiliate);
    setShowAffiliateProfileDrawer(true);
  };

  // Function to approve application
  const handleApproveApplication = async (applicationId: string) => {
    try {
      const response = await fetch(
        `/api/super-admin/affiliates/${applicationId}/approve`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            notes: "Approved via super admin interface",
            send_notification: true,
          }),
        }
      );

      if (response.ok) {
        const result = await response.json();
        console.log("Application approved successfully:", result);
        // Show success message
        alert(
          "Application approved successfully! Notification sent to affiliate."
        );
        // Refresh the data
        fetchAffiliates();
      } else {
        const error = await response.json();
        console.error("Failed to approve application:", error);
        alert(
          "Failed to approve application: " + (error.message || "Unknown error")
        );
      }
    } catch (error) {
      console.error("Error approving application:", error);
      alert(
        "Error approving application: " +
          (error instanceof Error ? error.message : String(error))
      );
    }
  };

  // Function to reject application
  const handleRejectApplication = async (
    applicationId: string,
    affiliateName: string
  ) => {
    setSelectedApplicationForRejection({
      id: applicationId,
      name: affiliateName,
    });
    setShowRejectionDialog(true);
  };

  // Function to handle rejection with structured reasons
  const handleRejectionSubmit = async (
    reasons: string[],
    customReason: string
  ) => {
    if (!selectedApplicationForRejection) return;

    try {
      const response = await fetch(
        `/api/super-admin/affiliates/${selectedApplicationForRejection.id}/reject`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            reasons: reasons,
            customReason: customReason,
            send_notification: true,
          }),
        }
      );

      if (response.ok) {
        const result = await response.json();
        console.log("Application rejected successfully:", result);
        // Show success message
        alert(
          `Application rejected successfully! Notification sent to ${selectedApplicationForRejection.name}.`
        );
        // Refresh the data
        fetchAffiliates();
      } else {
        const error = await response.json();
        console.error("Failed to reject application:", error);
        alert(
          "Failed to reject application: " + (error.message || "Unknown error")
        );
      }
    } catch (error) {
      console.error("Error rejecting application:", error);
      alert(
        "Error rejecting application: " +
          (error instanceof Error ? error.message : String(error))
      );
    }
  };

  useEffect(() => {
    // Simulate initial data load
    setTimeout(() => {
      setIsLoading(false);
    }, 500);
  }, []);

  // Add React Query for fetching affiliates
  const {
    data: affiliatesResponse,
    isLoading: dataLoading,
    error: dataError,
  } = useQuery({
    queryKey: ["affiliates"],
    queryFn: async () => {
      const response = await fetch("/api/super-admin/affiliates");
      if (!response.ok) {
        throw new Error("Failed to fetch affiliates");
      }
      return response.json();
    },
  });

  // Add mutation for changing affiliate status
  const updateAffiliateMutation = useMutation({
    mutationFn: async ({ id, status }: { id: string; status: string }) => {
      const response = await fetch(`/api/super-admin/affiliates/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        throw new Error("Failed to update affiliate status");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["affiliates"] });
      toast({
        title: "Status updated",
        description: "Affiliate status has been updated successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Update failed",
        description:
          error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    },
  });

  // Use fetched data instead of mock data
  const affiliates = affiliatesResponse?.affiliates || [];
  const availableCities = affiliatesResponse?.cities || [];

  // Apply filters
  const filteredAffiliates = useMemo(() => {
    if (!affiliates) return [];

    return affiliates.filter((affiliate: any) => {
      // Apply search
      if (
        searchQuery &&
        !affiliate.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
        !affiliate.address.city
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) &&
        !affiliate.address.state
          .toLowerCase()
          .includes(searchQuery.toLowerCase())
      ) {
        return false;
      }

      // Apply status filter
      if (statusFilter !== "all" && affiliate.status !== statusFilter) {
        return false;
      }

      return true;
    });
  }, [affiliates, searchQuery, statusFilter]);

  // Remove unused affiliatesForTabs since we removed the tabs

  // --- Mock Stats Data ---
  const affiliateStats = useMemo(() => {
    const totalPartners = affiliates.length;
    const pendingPartners = affiliates.filter(
      (a: any) => a.status === "pending"
    ).length;
    const complianceIssues = affiliates.filter(
      (a: any) => a.verificationStatus !== "verified"
    ).length;
    // For total fleet, we might need to sum from partner details if not readily available
    const totalActiveFleet = affiliates.filter(
      (p: any) => p.status === "active"
    ).length; // Since we don't have fleetSize, just count active affiliates

    return {
      totalPartners,
      pendingPartners,
      complianceIssues,
      totalActiveFleet,
    };
  }, [affiliates]);

  // Handle loading state
  if (dataLoading) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">
            Affiliate Configuration
          </h2>
        </div>
        <div className="flex items-center justify-center h-64">
          <p>Loading affiliate data...</p>
        </div>
      </div>
    );
  }

  // Handle error state
  if (dataError) {
    return (
      <div className="flex-1 space-y-6 p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">
            Affiliate Configuration
          </h2>
        </div>
        <div className="flex items-center justify-center h-64 text-red-500">
          <p>
            Error loading affiliate data:{" "}
            {dataError instanceof Error ? dataError.message : "Unknown error"}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Affiliate Onboarding Management
          </h1>
          <p className="text-muted-foreground">
            Control center for affiliate applications, compliance, and duty of
            care oversight
          </p>
        </div>
        <Button
          onClick={() => setShowAddModal(true)}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Plus className="mr-2 h-4 w-4" />
          Add New Affiliate
        </Button>
      </div>

      {/* Enhanced Stats Dashboard */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Partners
            </CardTitle>
            <div className="p-2 bg-blue-100 rounded-lg">
              <Network className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-600">
              {affiliateStats.totalPartners}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              +3 new this month
            </p>
            <div className="mt-2">
              <div className="text-xs text-muted-foreground">
                Active:{" "}
                {affiliateStats.totalPartners - affiliateStats.pendingPartners}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-yellow-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Pending Onboarding
            </CardTitle>
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Hourglass className="h-4 w-4 text-yellow-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-yellow-600">
              {affiliateStats.pendingPartners}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Awaiting review
            </p>
            <div className="mt-2">
              <Progress
                value={
                  (affiliateStats.pendingPartners /
                    affiliateStats.totalPartners) *
                  100
                }
                className="h-2"
              />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-red-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Compliance Issues
            </CardTitle>
            <div className="p-2 bg-red-100 rounded-lg">
              <ShieldAlert className="h-4 w-4 text-red-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-red-600">
              {complianceSummary.issues}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Require immediate attention
            </p>
            <div className="mt-2 space-y-1">
              <div className="text-xs text-muted-foreground">
                Expired docs: {complianceSummary.expired}
              </div>
              <div className="text-xs text-muted-foreground">
                Pending review: {complianceSummary.pending}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Active Fleet
            </CardTitle>
            <div className="p-2 bg-green-100 rounded-lg">
              <Truck className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-600">
              {affiliateStats.totalActiveFleet}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Vehicles in service
            </p>
            <div className="mt-2">
              <div className="text-xs text-muted-foreground">
                Avg per affiliate:{" "}
                {Math.round(
                  affiliateStats.totalActiveFleet / affiliateStats.totalPartners
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Application Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl flex items-center gap-2">
            <ClipboardCheck className="h-5 w-5" />
            Application Progress Overview
          </CardTitle>
          <CardDescription>
            Track affiliate onboarding progress and duty of care compliance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-4">
              <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                Application Stages
              </h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm">Initial Submission</span>
                  </div>
                  <Badge variant="outline">12 pending</Badge>
                </div>
                <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <span className="text-sm">Document Review</span>
                  </div>
                  <Badge variant="outline">8 in review</Badge>
                </div>
                <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span className="text-sm">Compliance Check</span>
                  </div>
                  <Badge variant="outline">5 checking</Badge>
                </div>
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm">Final Approval</span>
                  </div>
                  <Badge variant="outline">3 ready</Badge>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                Duty of Care Status
              </h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Insurance Verified</span>
                  </div>
                  <span className="text-sm font-medium">85%</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Driver Background</span>
                  </div>
                  <span className="text-sm font-medium">78%</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm">Vehicle Inspection</span>
                  </div>
                  <span className="text-sm font-medium">65%</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span className="text-sm">Safety Training</span>
                  </div>
                  <span className="text-sm font-medium">45%</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                Recent Activity
              </h4>
              <div className="space-y-3">
                <div className="p-3 border rounded-lg">
                  <div className="text-sm font-medium">
                    Elite Transportation
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Submitted insurance docs
                  </div>
                  <div className="text-xs text-muted-foreground">
                    2 hours ago
                  </div>
                </div>
                <div className="p-3 border rounded-lg">
                  <div className="text-sm font-medium">Boston Elite Limo</div>
                  <div className="text-xs text-muted-foreground">
                    Completed vehicle inspection
                  </div>
                  <div className="text-xs text-muted-foreground">
                    5 hours ago
                  </div>
                </div>
                <div className="p-3 border rounded-lg">
                  <div className="text-sm font-medium">Company 1</div>
                  <div className="text-xs text-muted-foreground">
                    Application approved
                  </div>
                  <div className="text-xs text-muted-foreground">1 day ago</div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Affiliate Application Management */}
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
            <div>
              <CardTitle>Affiliate Applications</CardTitle>
              <CardDescription>
                Manage affiliate onboarding applications and approvals
              </CardDescription>
            </div>
            <div className="flex flex-col md:flex-row gap-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search applications..."
                  className="pl-8 md:w-[250px]"
                  value={searchQuery}
                  onChange={handleSearch}
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="in_verification">
                    In Verification
                  </SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon" title="Download CSV">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Application Cards - Using real data */}
            {filteredAffiliates.map((affiliate: any) => (
              <div key={affiliate.id} className="border rounded-lg p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-lg font-semibold">
                        {affiliate.name}
                      </h3>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant={
                            affiliate.status === "approved"
                              ? "default"
                              : affiliate.status === "pending"
                              ? "secondary"
                              : affiliate.status === "in_verification"
                              ? "outline"
                              : "destructive"
                          }
                        >
                          {affiliate.status === "approved"
                            ? "Approved"
                            : affiliate.status === "pending"
                            ? "Pending"
                            : affiliate.status === "in_verification"
                            ? "In Verification"
                            : "Rejected"}
                        </Badge>
                      </div>
                    </div>

                    <div className="text-sm text-muted-foreground mb-3">
                      <div>
                        {affiliate.address?.city || "N/A"},{" "}
                        {affiliate.address?.state || "N/A"}
                      </div>
                      <div>
                        Submitted:{" "}
                        {formatDate(
                          affiliate.created_at || new Date().toISOString()
                        )}
                      </div>
                      <div>Email: {affiliate.email || "N/A"}</div>
                    </div>

                    {/* Progress Bar */}
                    <div className="mb-4">
                      <div className="flex justify-between text-sm mb-1">
                        <span>Application Progress</span>
                        <span>{affiliate.progress || 0}% complete</span>
                      </div>
                      <Progress
                        value={affiliate.progress || 0}
                        className="h-2"
                      />
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center gap-2 ml-6">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleOpenAffiliateProfile(affiliate)}
                    >
                      View Details
                    </Button>

                    {affiliate.status === "pending" && (
                      <>
                        <Button
                          size="sm"
                          className="bg-green-600 hover:bg-green-700"
                          onClick={() => handleApproveApplication(affiliate.id)}
                        >
                          <CheckCircle2 className="h-4 w-4 mr-1" />
                          Approve
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() =>
                            handleRejectApplication(
                              affiliate.id,
                              affiliate.name
                            )
                          }
                        >
                          <XCircle className="h-4 w-4 mr-1" />
                          Reject
                        </Button>
                      </>
                    )}

                    {affiliate.status === "in_verification" && (
                      <Button variant="outline" size="sm">
                        Review
                      </Button>
                    )}

                    {affiliate.status === "approved" && (
                      <Button variant="outline" size="sm">
                        Edit
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-4 grid-cols-1 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Affiliate Distribution
            </CardTitle>
            <CardDescription>By tenant usage</CardDescription>
          </CardHeader>
          <CardContent className="h-[200px] flex items-center justify-center bg-muted/20 rounded-md">
            <div className="text-center">
              <div className="text-sm text-muted-foreground">
                Distribution chart placeholder
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Service Coverage
            </CardTitle>
            <CardDescription>By location</CardDescription>
          </CardHeader>
          <CardContent className="h-[200px] flex items-center justify-center bg-muted/20 rounded-md">
            <div className="text-center">
              <div className="text-sm text-muted-foreground">
                Coverage map placeholder
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Performance</CardTitle>
            <CardDescription>Top rated affiliates</CardDescription>
          </CardHeader>
          <CardContent className="h-[200px] flex items-center justify-center bg-muted/20 rounded-md">
            <div className="text-center">
              <div className="text-sm text-muted-foreground">
                Performance chart placeholder
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Add Affiliate Modal (stub) */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 shadow-lg w-full max-w-md">
            <h3 className="text-lg font-bold mb-4">Add Affiliate (Stub)</h3>
            <p className="mb-4 text-muted-foreground">
              TODO: Implement add affiliate form and backend integration.
            </p>
            <Button onClick={() => setShowAddModal(false)} className="w-full">
              Close
            </Button>
          </div>
        </div>
      )}

      {/* Render Drawers */}
      <RatesFleetDetailsDrawer
        open={showRatesDrawer}
        onOpenChange={setShowRatesDrawer}
        affiliate={selectedAffiliateForDrawer}
      />
      <ComplianceDetailsDrawer
        open={showComplianceDrawer}
        onOpenChange={setShowComplianceDrawer}
        affiliate={selectedAffiliateForDrawer}
        complianceData={
          selectedAffiliateForDrawer
            ? compliance.find(
                (c) => c.partnerId === selectedAffiliateForDrawer!.id
              ) ?? null
            : null
        }
      />
      <GeneralDetailsDrawer
        open={showGeneralDrawer}
        onOpenChange={setShowGeneralDrawer}
        affiliate={selectedAffiliateForDrawer}
      />
      <ApplicationDetailsDrawer
        open={showApplicationDrawer}
        onOpenChange={setShowApplicationDrawer}
        application={selectedApplicationForDrawer}
      />
      <AffiliateProfileDrawer
        open={showAffiliateProfileDrawer}
        onOpenChange={setShowAffiliateProfileDrawer}
        affiliate={selectedAffiliateForDrawer}
      />
      <RejectionDialog
        open={showRejectionDialog}
        onOpenChange={setShowRejectionDialog}
        onReject={handleRejectionSubmit}
        affiliateName={selectedApplicationForRejection?.name || ""}
      />
    </div>
  );
}
