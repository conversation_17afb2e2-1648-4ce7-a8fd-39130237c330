"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>er<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  DrawerTitle,
} from "@/app/components/ui/drawer";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { Button } from "@/app/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>bsContent,
  TabsList,
  TabsTrigger,
} from "@/app/components/ui/tabs";
import { Separator } from "@/app/components/ui/separator";
import { useToast } from "@/app/components/ui/use-toast";
import {
  Building2,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Star,
  TrendingUp,
  Car,
  FileText,
  Shield,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
} from "lucide-react";
import { Affiliate } from "@/app/lib/types/affiliates";

interface AffiliateProfileDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  affiliate: Affiliate | null;
}

export function AffiliateProfileDrawer({
  open,
  onOpenChange,
  affiliate,
}: AffiliateProfileDrawerProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [profileData, setProfileData] = useState<any>(null);

  useEffect(() => {
    if (affiliate && open) {
      fetchAffiliateProfile();
    }
  }, [affiliate, open]);

  const fetchAffiliateProfile = async () => {
    if (!affiliate) return;

    setLoading(true);
    try {
      // Fetch comprehensive affiliate data
      const response = await fetch(
        `/api/super-admin/affiliates/${affiliate.id}`
      );
      if (response.ok) {
        const data = await response.json();
        setProfileData(data);
      }
    } catch (error) {
      console.error("Error fetching affiliate profile:", error);
      toast({
        title: "Error",
        description: "Failed to load affiliate profile",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async () => {
    if (!affiliate) return;

    try {
      const response = await fetch(
        `/api/super-admin/affiliates/${affiliate.id}/approve`,
        {
          method: "POST",
        }
      );

      if (response.ok) {
        toast({
          title: "Success",
          description: "Affiliate approved successfully",
        });
        onOpenChange(false);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to approve affiliate",
        variant: "destructive",
      });
    }
  };

  const handleReject = async () => {
    if (!affiliate) return;

    try {
      const response = await fetch(
        `/api/super-admin/affiliates/${affiliate.id}/reject`,
        {
          method: "POST",
        }
      );

      if (response.ok) {
        toast({
          title: "Success",
          description: "Affiliate rejected",
        });
        onOpenChange(false);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to reject affiliate",
        variant: "destructive",
      });
    }
  };

  if (!affiliate) return null;

  return (
    <Drawer open={open} onOpenChange={onOpenChange} side="right" size="4xl">
      <DrawerContent>
        <DrawerHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center">
                <Building2 className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <DrawerTitle className="text-xl">{affiliate.name}</DrawerTitle>
                <p className="text-sm text-muted-foreground">
                  {affiliate.city}, {affiliate.state} • Applied{" "}
                  {new Date(affiliate.created_at || "").toLocaleDateString()}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge
                variant={
                  affiliate.status === "approved"
                    ? "default"
                    : affiliate.status === "pending"
                    ? "secondary"
                    : affiliate.status === "in_verification"
                    ? "outline"
                    : "destructive"
                }
              >
                {affiliate.status === "approved"
                  ? "Approved"
                  : affiliate.status === "pending"
                  ? "Pending"
                  : affiliate.status === "in_verification"
                  ? "In Verification"
                  : "Rejected"}
              </Badge>
            </div>
          </div>
        </DrawerHeader>

        <div className="flex-1 overflow-auto p-6">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">
                  Loading affiliate profile...
                </p>
              </div>
            </div>
          ) : (
            <Tabs defaultValue="overview" className="space-y-6">
              <TabsList className="grid w-full grid-cols-6">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="contact">Contact</TabsTrigger>
                <TabsTrigger value="performance">Performance</TabsTrigger>
                <TabsTrigger value="fleet">Fleet</TabsTrigger>
                <TabsTrigger value="documents">Documents</TabsTrigger>
                <TabsTrigger value="audit">Audit Log</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Company Information */}
                  <Card className="lg:col-span-2">
                    <CardHeader>
                      <CardTitle>Company Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-muted-foreground">
                            Business Name
                          </p>
                          <p className="font-medium">{affiliate.name}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">
                            Status
                          </p>
                          <Badge variant="outline">{affiliate.status}</Badge>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">
                            Location
                          </p>
                          <p className="font-medium">
                            {affiliate.city}, {affiliate.state}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">
                            Rating
                          </p>
                          <div className="flex items-center gap-1">
                            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                            <span className="font-medium">
                              {affiliate.rating || "N/A"}
                            </span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Quick Actions */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Quick Actions</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {affiliate.status === "pending" && (
                        <>
                          <Button onClick={handleApprove} className="w-full">
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Approve Application
                          </Button>
                          <Button
                            onClick={handleReject}
                            variant="destructive"
                            className="w-full"
                          >
                            <XCircle className="h-4 w-4 mr-2" />
                            Reject Application
                          </Button>
                        </>
                      )}
                      <Button variant="outline" className="w-full">
                        <Mail className="h-4 w-4 mr-2" />
                        Send Message
                      </Button>
                      <Button variant="outline" className="w-full">
                        <FileText className="h-4 w-4 mr-2" />
                        View Quotes
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="contact" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Contact Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div className="flex items-center gap-3">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm text-muted-foreground">
                              Email
                            </p>
                            <p className="font-medium">
                              {affiliate.email || "Not provided"}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm text-muted-foreground">
                              Phone
                            </p>
                            <p className="font-medium">
                              {affiliate.phone || "Not provided"}
                            </p>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-4">
                        <div className="flex items-center gap-3">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="text-sm text-muted-foreground">
                              Address
                            </p>
                            <p className="font-medium">
                              {affiliate.address?.street
                                ? `${affiliate.address.street}, ${affiliate.city}, ${affiliate.state} ${affiliate.address.zipCode}`
                                : `${affiliate.city}, ${affiliate.state}`}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Additional tabs would be implemented here */}
              <TabsContent value="performance">
                <Card>
                  <CardHeader>
                    <CardTitle>Performance Metrics</CardTitle>
                    <CardDescription>
                      Key performance indicators and statistics
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">
                      Performance data will be displayed here...
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="fleet">
                <Card>
                  <CardHeader>
                    <CardTitle>Fleet Information</CardTitle>
                    <CardDescription>
                      Vehicle inventory and specifications
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">
                      Fleet data will be displayed here...
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="documents">
                <Card>
                  <CardHeader>
                    <CardTitle>Documents & Compliance</CardTitle>
                    <CardDescription>
                      Required documents and compliance status
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">
                      Document status will be displayed here...
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="audit">
                <Card>
                  <CardHeader>
                    <CardTitle>Audit Log</CardTitle>
                    <CardDescription>
                      Activity history and changes
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">
                      Audit trail will be displayed here...
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          )}
        </div>
      </DrawerContent>
    </Drawer>
  );
}
