# Affiliate Management Security Policies

This document outlines the security policies, RLS (Row-Level Security) rules, and authentication requirements for the affiliate management features of the SaaS platform.

## Authentication and Authorization

### Role Requirements
- **Super Admin**: Full access to all affiliate management features, including overrides and audit operations.
- **Tenant Admin**: Access to affiliates within their tenant only, cannot access cross-tenant data.
- **Affiliate Manager**: Limited access to affiliate data within their tenant, cannot perform certain operations.

### Authentication Flow
1. All requests must include a valid JWT token issued by Supabase Auth.
2. Token is validated in middleware.ts for protected routes.
3. Token claims are checked for appropriate role before allowing operations.
4. Session freshness is validated (not expired).

## Row-Level Security (RLS) Policies

### Affiliates Table

```sql
-- Allow super admins to see all affiliates
CREATE POLICY "super_admin_can_access_all_affiliates" 
ON "public"."affiliates"
FOR ALL
TO authenticated
USING (auth.jwt() ? 'roles' AND auth.jwt()->>'roles' ? 'SUPER_ADMIN');

-- Allow tenant admins to see only affiliates related to their tenant
CREATE POLICY "tenant_admin_can_access_tenant_affiliates" 
ON "public"."affiliates"
FOR SELECT 
TO authenticated
USING (
  (auth.jwt() ? 'roles' AND auth.jwt()->>'roles' ? 'TENANT_ADMIN')
  AND (tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid()))
);

-- Allow affiliate managers to see only affiliates related to their tenant
CREATE POLICY "affiliate_manager_can_access_tenant_affiliates" 
ON "public"."affiliates"
FOR SELECT 
TO authenticated
USING (
  (auth.jwt() ? 'roles' AND auth.jwt()->>'roles' ? 'AFFILIATE_MANAGER')
  AND (tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid()))
);
```

### Affiliate Applications Table

```sql
-- Allow super admins to manage all applications
CREATE POLICY "super_admin_can_manage_all_applications" 
ON "public"."affiliate_applications"
FOR ALL
TO authenticated
USING (auth.jwt() ? 'roles' AND auth.jwt()->>'roles' ? 'SUPER_ADMIN');

-- Allow tenant admins to see only applications related to their tenant
CREATE POLICY "tenant_admin_can_see_tenant_applications" 
ON "public"."affiliate_applications"
FOR SELECT 
TO authenticated
USING (
  (auth.jwt() ? 'roles' AND auth.jwt()->>'roles' ? 'TENANT_ADMIN')
  AND (tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid()))
);
```

### Affiliate Documents Table

```sql
-- Allow super admins to manage all documents
CREATE POLICY "super_admin_can_manage_all_documents" 
ON "public"."affiliate_documents"
FOR ALL
TO authenticated
USING (auth.jwt() ? 'roles' AND auth.jwt()->>'roles' ? 'SUPER_ADMIN');

-- Allow tenant admins to see only documents related to their tenant's affiliates
CREATE POLICY "tenant_admin_can_see_tenant_documents" 
ON "public"."affiliate_documents"
FOR SELECT 
TO authenticated
USING (
  (auth.jwt() ? 'roles' AND auth.jwt()->>'roles' ? 'TENANT_ADMIN')
  AND (
    affiliate_id IN 
    (SELECT id FROM affiliates WHERE tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid()))
  )
);
```

### Audit Log Table

```sql
-- Allow super admins to see all audit logs
CREATE POLICY "super_admin_can_see_all_audit_logs" 
ON "public"."audit_logs"
FOR SELECT
TO authenticated
USING (auth.jwt() ? 'roles' AND auth.jwt()->>'roles' ? 'SUPER_ADMIN');

-- Allow tenant admins to see only audit logs related to their tenant
CREATE POLICY "tenant_admin_can_see_tenant_audit_logs" 
ON "public"."audit_logs"
FOR SELECT 
TO authenticated
USING (
  (auth.jwt() ? 'roles' AND auth.jwt()->>'roles' ? 'TENANT_ADMIN')
  AND (
    tenant_id = (SELECT tenant_id FROM users WHERE id = auth.uid())
  )
);
```

## API Security

### Endpoint Protection
- All API endpoints require authenticated requests with valid JWT tokens.
- Rate limiting is applied to all public endpoints (max 60 requests per minute).
- CSRF protection is implemented for form submissions.

### Data Validation
- All input data is validated using Zod schemas before processing.
- Parameterized queries are used to prevent SQL injection.
- File uploads are validated for type, size, and content.

### Error Handling
- Sensitive information is never exposed in error messages.
- All security events are logged for audit purposes.
- User-friendly error messages are provided to the client.

## Compliance Requirements

### Document Verification
- Insurance documents require manual verification by super admin.
- Driver's licenses require expiration date validation.
- Business licenses require validation against public records.

### Audit Requirements
- All changes to affiliate data must be logged with:
  - User ID
  - Timestamp
  - IP address
  - Action performed
  - Previous and new values

## Session Management

- Sessions expire after 8 hours of inactivity.
- Session refresh tokens are rotated regularly.
- Logout invalidates all session tokens.
- Concurrent sessions are limited to 3 per user.

---

*This document should be updated whenever security policies or RLS rules change.* 