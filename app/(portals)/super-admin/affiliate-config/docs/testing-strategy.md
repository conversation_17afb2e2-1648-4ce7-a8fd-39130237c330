# Testing Strategy for Affiliate Management

This document outlines the comprehensive testing strategy for the affiliate management system, covering all aspects from unit testing to end-to-end testing.

## Testing Overview

Our testing approach follows the testing pyramid principles:

1. **Unit Tests** - Fast, focused tests for individual functions and components
2. **Component Tests** - Testing UI components in isolation
3. **Integration Tests** - Testing interactions between components and services
4. **End-to-End Tests** - Testing complete user journeys through the application

## Tools and Technologies

- **Unit Testing**: Jest
- **Component Testing**: React Testing Library
- **Integration Testing**: Jest + React Testing Library + MSW (Mock Service Worker)
- **End-to-End Testing**: Playwright
- **Test Coverage**: Jest Coverage
- **Mock Data**: Faker.js, MSW, custom fixtures

## Unit Testing Strategy

### What to Test

- **Utility Functions**: All utility functions should have comprehensive tests
- **Custom Hooks**: All custom hooks should be tested with various inputs and conditions
- **Reducers/State Logic**: All state management logic should be thoroughly tested
- **API Client Functions**: All API client functions should be tested

### Unit Testing Best Practices

1. **Follow AAA Pattern**:
   - Arrange: Set up test data and conditions
   - Act: Call the function/code being tested
   - Assert: Verify the results

2. **Use Descriptive Test Names**:
   ```typescript
   describe('filterAffiliates', () => {
     it('should return affiliates that match the search query', () => {
       // Test code
     });
     
     it('should return empty array when no matches found', () => {
       // Test code
     });
   });
   ```

3. **Test Edge Cases**:
   - Empty inputs
   - Invalid inputs
   - Boundary conditions
   - Error handling

4. **Pure Tests**:
   - Tests should be independent and not affect each other
   - Avoid shared mutable state between tests
   - Reset mocks between tests

### Example Unit Test

```typescript
// Function to test
export function filterAffiliatesByStatus(affiliates: Affiliate[], status: AffiliateStatus | 'all'): Affiliate[] {
  if (status === 'all') return affiliates;
  return affiliates.filter(affiliate => affiliate.status === status);
}

// Test
describe('filterAffiliatesByStatus', () => {
  const mockAffiliates = [
    { id: '1', name: 'Affiliate 1', status: 'active' as AffiliateStatus },
    { id: '2', name: 'Affiliate 2', status: 'pending' as AffiliateStatus },
    { id: '3', name: 'Affiliate 3', status: 'active' as AffiliateStatus },
  ];

  it('should return all affiliates when status is "all"', () => {
    const result = filterAffiliatesByStatus(mockAffiliates, 'all');
    expect(result).toEqual(mockAffiliates);
  });

  it('should return only active affiliates when status is "active"', () => {
    const result = filterAffiliatesByStatus(mockAffiliates, 'active');
    expect(result).toHaveLength(2);
    expect(result.every(a => a.status === 'active')).toBe(true);
  });

  it('should return an empty array when no affiliates match the status', () => {
    const result = filterAffiliatesByStatus(mockAffiliates, 'inactive');
    expect(result).toHaveLength(0);
  });

  it('should handle empty array input', () => {
    const result = filterAffiliatesByStatus([], 'active');
    expect(result).toEqual([]);
  });
});
```

## Component Testing Strategy

### What to Test

- **UI Components**: Test rendering, props, user interactions
- **Custom Form Components**: Test validation, submission, error states
- **Layout Components**: Test responsive behavior
- **Complex UI Logic**: Test conditional rendering, dynamic content

### Component Testing Best Practices

1. **Test Behavior, Not Implementation**:
   - Test what the component does, not how it's implemented
   - Avoid testing implementation details that could change

2. **Use User-Centric Queries**:
   - Prefer queries like `getByRole`, `getByLabelText`, `getByText` over `getByTestId`
   - Test from the user's perspective

3. **Test User Interactions**:
   - Test all user interactions (clicks, inputs, etc.)
   - Verify the component responds correctly to events

4. **Test Accessibility**:
   - Verify that components have appropriate ARIA attributes
   - Test keyboard navigation
   - Ensure form elements have labels

### Example Component Test

```typescript
// Component to test
function AffiliateStatusBadge({ status }: { status: AffiliateStatus }) {
  const getStatusClasses = (status: AffiliateStatus) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'inactive': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <span 
      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusClasses(status)}`}
      aria-label={`Affiliate status: ${status}`}
    >
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
}

// Test
import { render, screen } from '@testing-library/react';

describe('AffiliateStatusBadge', () => {
  it('should render with the correct text based on status', () => {
    render(<AffiliateStatusBadge status="active" />);
    expect(screen.getByText('Active')).toBeInTheDocument();
  });

  it('should apply the correct styling for active status', () => {
    render(<AffiliateStatusBadge status="active" />);
    const badge = screen.getByText('Active');
    expect(badge).toHaveClass('bg-green-100');
    expect(badge).toHaveClass('text-green-800');
  });

  it('should apply the correct styling for pending status', () => {
    render(<AffiliateStatusBadge status="pending" />);
    const badge = screen.getByText('Pending');
    expect(badge).toHaveClass('bg-yellow-100');
    expect(badge).toHaveClass('text-yellow-800');
  });

  it('should have the correct aria-label for accessibility', () => {
    render(<AffiliateStatusBadge status="active" />);
    expect(screen.getByText('Active')).toHaveAttribute('aria-label', 'Affiliate status: active');
  });
});
```

## Integration Testing Strategy

### What to Test

- **Form Submission Flows**: Test complete form submission flows with API mocks
- **Data Fetching**: Test components that fetch and display data
- **Cross-Component Interactions**: Test interactions between related components
- **API Error Handling**: Test how components handle API errors

### Integration Testing Best Practices

1. **Mock External Dependencies**:
   - Use MSW to mock API responses
   - Create realistic mock data that matches production schemas

2. **Test Complete Flows**:
   - Test end-to-end user flows within a feature
   - Include all steps in the process

3. **Test Error Handling**:
   - Test how the UI handles API errors
   - Test loading states, error states, and success states

### Example Integration Test

```typescript
// Integration test for affiliate creation flow
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { rest } from 'msw';
import { setupServer } from 'msw/node';
import { CreateAffiliateForm } from './CreateAffiliateForm';

// Mock server setup
const server = setupServer(
  rest.post('/api/v1/affiliate-config/affiliates', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({ id: 'new-affiliate-id', ...req.body })
    );
  })
);

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

describe('CreateAffiliateForm Integration', () => {
  it('should submit the form and show success message', async () => {
    const mockOnSuccess = jest.fn();
    
    render(<CreateAffiliateForm onSuccess={mockOnSuccess} />);
    
    // Fill out the form
    await userEvent.type(screen.getByLabelText(/name/i), 'Test Affiliate');
    await userEvent.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await userEvent.type(screen.getByLabelText(/phone/i), '1234567890');
    await userEvent.type(screen.getByLabelText(/street/i), '123 Test St');
    await userEvent.type(screen.getByLabelText(/city/i), 'Test City');
    await userEvent.type(screen.getByLabelText(/state/i), 'CA');
    await userEvent.type(screen.getByLabelText(/zip/i), '12345');
    
    // Submit the form
    await userEvent.click(screen.getByRole('button', { name: /create/i }));
    
    // Wait for success state
    await waitFor(() => {
      expect(screen.getByText(/affiliate created successfully/i)).toBeInTheDocument();
    });
    
    expect(mockOnSuccess).toHaveBeenCalledWith(expect.objectContaining({
      id: 'new-affiliate-id',
      name: 'Test Affiliate'
    }));
  });
  
  it('should display validation errors when form is incomplete', async () => {
    render(<CreateAffiliateForm onSuccess={jest.fn()} />);
    
    // Submit without filling required fields
    await userEvent.click(screen.getByRole('button', { name: /create/i }));
    
    // Check for validation errors
    expect(screen.getByText(/name is required/i)).toBeInTheDocument();
    expect(screen.getByText(/email is required/i)).toBeInTheDocument();
  });
  
  it('should handle API errors', async () => {
    // Override default handler for this test
    server.use(
      rest.post('/api/v1/affiliate-config/affiliates', (req, res, ctx) => {
        return res(
          ctx.status(400),
          ctx.json({ message: 'Validation failed', errors: { email: ['Email already in use'] } })
        );
      })
    );
    
    render(<CreateAffiliateForm onSuccess={jest.fn()} />);
    
    // Fill out the form with minimal valid data
    await userEvent.type(screen.getByLabelText(/name/i), 'Test Affiliate');
    await userEvent.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await userEvent.type(screen.getByLabelText(/phone/i), '1234567890');
    
    // Submit the form
    await userEvent.click(screen.getByRole('button', { name: /create/i }));
    
    // Check for API error message
    await waitFor(() => {
      expect(screen.getByText(/email already in use/i)).toBeInTheDocument();
    });
  });
});
```

## End-to-End Testing Strategy

### What to Test

- **Critical User Journeys**: Test the most important user flows end-to-end
- **Cross-Feature Interactions**: Test interactions between different features
- **Authenticated Flows**: Test flows that require authentication
- **Multiple Browser Support**: Test on different browsers

### End-to-End Testing Best Practices

1. **Focus on Critical Paths**:
   - Identify and test the most important user journeys
   - Avoid testing every possible scenario (that's for unit tests)

2. **Use Realistic Test Data**:
   - Set up test data that closely resembles production data
   - Use a test database or API environment

3. **Test on Multiple Browsers**:
   - Test on Chrome, Firefox, Safari, and Edge
   - Test mobile viewports

4. **Create Modular Test Files**:
   - Organize tests by feature or user journey
   - Create reusable helper functions for common actions

### Example End-to-End Test

```typescript
// Using Playwright
import { test, expect } from '@playwright/test';

test.describe('Affiliate Management E2E', () => {
  test.beforeEach(async ({ page }) => {
    // Log in as super admin
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'securePassword123');
    await page.click('button[type="submit"]');
    
    // Wait for login to complete
    await page.waitForURL('/super-admin/dashboard');
  });
  
  test('should create a new affiliate and verify it appears in the list', async ({ page }) => {
    // Navigate to affiliate config
    await page.click('text=Affiliate Config');
    
    // Click "Add Affiliate" button
    await page.click('button:has-text("Add Affiliate")');
    
    // Fill out the form
    await page.fill('input[name="name"]', 'E2E Test Affiliate');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="phone"]', '1234567890');
    await page.fill('input[name="address.street"]', '123 E2E Street');
    await page.fill('input[name="address.city"]', 'Test City');
    await page.fill('input[name="address.state"]', 'CA');
    await page.fill('input[name="address.zip"]', '12345');
    await page.fill('input[name="address.country"]', 'USA');
    await page.fill('input[name="businessInfo.taxId"]', 'TAX123456');
    await page.selectOption('select[name="businessInfo.businessType"]', 'llc');
    
    // Add a contact person
    await page.fill('input[name="contactPersons.0.name"]', 'John Doe');
    await page.fill('input[name="contactPersons.0.role"]', 'Manager');
    await page.fill('input[name="contactPersons.0.email"]', '<EMAIL>');
    await page.fill('input[name="contactPersons.0.phone"]', '9876543210');
    await page.check('input[name="contactPersons.0.isPrimary"]');
    
    // Submit the form
    await page.click('button:has-text("Create Affiliate")');
    
    // Wait for success message
    await page.waitForSelector('text=Affiliate created successfully');
    
    // Navigate back to affiliate list
    await page.click('button:has-text("Back to List")');
    
    // Search for the newly created affiliate
    await page.fill('input[placeholder="Search affiliates..."]', 'E2E Test Affiliate');
    
    // Verify the affiliate appears in the list
    await expect(page.locator('text=E2E Test Affiliate')).toBeVisible();
    
    // Verify status is correct
    await expect(page.locator('text=Pending')).toBeVisible();
  });
  
  test('should edit an existing affiliate', async ({ page }) => {
    // Navigate to affiliate config
    await page.click('text=Affiliate Config');
    
    // Search for an existing affiliate
    await page.fill('input[placeholder="Search affiliates..."]', 'E2E Test Affiliate');
    
    // Click the dropdown menu and select Edit
    await page.click('button[aria-label="Open menu"]');
    await page.click('text=Edit Affiliate');
    
    // Update the affiliate name
    await page.fill('input[name="name"]', 'Updated E2E Test Affiliate');
    
    // Save changes
    await page.click('button:has-text("Save Changes")');
    
    // Wait for success message
    await page.waitForSelector('text=Affiliate updated successfully');
    
    // Navigate back to affiliate list
    await page.click('button:has-text("Back to List")');
    
    // Search for the updated affiliate
    await page.fill('input[placeholder="Search affiliates..."]', 'Updated E2E Test Affiliate');
    
    // Verify the updated affiliate appears in the list
    await expect(page.locator('text=Updated E2E Test Affiliate')).toBeVisible();
  });
  
  test('should manage affiliate documents', async ({ page }) => {
    // Navigate to affiliate config
    await page.click('text=Affiliate Config');
    
    // Search for an existing affiliate
    await page.fill('input[placeholder="Search affiliates..."]', 'E2E Test Affiliate');
    
    // Click on the affiliate to view details
    await page.click('text=E2E Test Affiliate');
    
    // Navigate to Compliance & Audit tab
    await page.click('button:has-text("Compliance & Audit")');
    
    // Click "Upload Document" button
    await page.click('button:has-text("Upload Document")');
    
    // Select document type
    await page.selectOption('select[name="documentType"]', 'business_license');
    
    // Upload a test file
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles('./test-assets/test-document.pdf');
    
    // Submit the upload
    await page.click('button:has-text("Upload")');
    
    // Wait for success message
    await page.waitForSelector('text=Document uploaded successfully');
    
    // Verify document appears in the list
    await expect(page.locator('text=Business License')).toBeVisible();
    await expect(page.locator('text=Pending')).toBeVisible();
  });
});
```

## Test Coverage Requirements

We require the following minimum coverage thresholds:

- **Utility Functions**: 95% coverage
- **UI Components**: 80% coverage
- **Hooks**: 90% coverage
- **API Client Functions**: 90% coverage
- **Overall Coverage**: 85% coverage

Coverage reports should be generated as part of the CI pipeline.

## Testing in CI/CD Pipeline

1. **Pull Request Checks**:
   - All unit and component tests must pass
   - Coverage thresholds must be met
   - No TypeScript errors

2. **Deployment Checks**:
   - End-to-end tests must pass in staging environment
   - Visual regression tests must pass

3. **Production Verification**:
   - Smoke tests run against production deployment
   - Key user journeys verified

## Mocking Strategy

1. **API Mocks**:
   - Use MSW for API mocking
   - Create realistic mock responses that match production schemas
   - Include proper error responses for testing error handling

2. **Component Mocks**:
   - Mock complex child components when testing parent components
   - Use Jest manual mocks for third-party libraries

3. **Authentication Mocks**:
   - Create mock authentication context for testing protected routes
   - Test both authenticated and unauthenticated states

## Test Data Management

1. **Fixtures**:
   - Create reusable test data fixtures for common entities
   - Store fixtures in a dedicated directory
   - Use TypeScript interfaces to ensure type safety

2. **Factories**:
   - Create factory functions for generating test data with custom attributes
   - Use Faker.js for generating realistic random data

Example Test Data Factory:

```typescript
import { faker } from '@faker-js/faker';
import { Affiliate } from '../types/affiliates';

type PartialAffiliate = Partial<Affiliate>;

export function createMockAffiliate(overrides?: PartialAffiliate): Affiliate {
  return {
    id: faker.string.uuid(),
    name: faker.company.name(),
    businessName: faker.company.name(),
    status: faker.helpers.arrayElement(['active', 'pending', 'inactive']),
    verificationStatus: faker.helpers.arrayElement(['verified', 'unverified', 'in_progress']),
    email: faker.internet.email(),
    phone: faker.phone.number(),
    website: faker.internet.url(),
    address: {
      street: faker.location.streetAddress(),
      city: faker.location.city(),
      state: faker.location.state(),
      zip: faker.location.zipCode(),
      country: 'United States',
      coordinates: {
        latitude: parseFloat(faker.location.latitude()),
        longitude: parseFloat(faker.location.longitude()),
      },
    },
    businessInfo: {
      taxId: faker.string.alphanumeric(9),
      businessType: faker.helpers.arrayElement(['sole_proprietorship', 'partnership', 'corporation', 'llc']),
      yearEstablished: faker.number.int({ min: 1990, max: 2023 }),
      description: faker.company.catchPhrase(),
      licenseNumber: faker.string.alphanumeric(10),
      licenseExpiration: faker.date.future().toISOString().split('T')[0],
    },
    metrics: {
      rating: faker.number.float({ min: 1, max: 5, precision: 0.1 }),
      completionRate: faker.number.float({ min: 80, max: 100, precision: 0.1 }),
      onTimeRate: faker.number.float({ min: 80, max: 100, precision: 0.1 }),
      responseTime: faker.number.float({ min: 1, max: 10, precision: 0.1 }),
      tripCount: faker.number.int({ min: 10, max: 1000 }),
      reviewCount: faker.number.int({ min: 5, max: 500 }),
    },
    tenantId: faker.string.uuid(),
    tenants: [faker.string.uuid(), faker.string.uuid()],
    contactPersons: [
      {
        id: faker.string.uuid(),
        name: faker.person.fullName(),
        role: faker.person.jobTitle(),
        email: faker.internet.email(),
        phone: faker.phone.number(),
        isPrimary: true,
      },
    ],
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
    lastActive: faker.date.recent().toISOString(),
    ...overrides,
  };
}

export function createMockAffiliates(count: number, overrides?: PartialAffiliate): Affiliate[] {
  return Array.from({ length: count }, () => createMockAffiliate(overrides));
}
```

## Continuous Improvement

1. **Test Review Process**:
   - Code reviews should include review of tests
   - Tests should be considered as important as the implementation

2. **Update Tests with Requirements**:
   - When requirements change, tests should be updated first
   - Follow test-driven development principles when possible

3. **Regular Audit**:
   - Regularly audit test coverage and quality
   - Identify areas that need better testing

4. **Performance Monitoring**:
   - Monitor test performance as the test suite grows
   - Split large test files as needed

---

*This document should be updated as testing requirements and best practices evolve.* 