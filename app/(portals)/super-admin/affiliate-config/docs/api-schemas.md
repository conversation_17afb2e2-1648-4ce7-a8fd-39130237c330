# Affiliate Management API Schemas

This document defines the data schemas, types, and interfaces used throughout the affiliate management system. These definitions ensure type safety and consistent data handling across all components.

## Core Entities

### Affiliate

The primary entity representing a service provider in the system.

```typescript
export interface Affiliate {
  id: string;
  name: string;
  businessName?: string;
  status: 'active' | 'pending' | 'inactive' | 'suspended';
  verificationStatus: 'verified' | 'unverified' | 'in_progress';
  
  // Contact Information
  email: string;
  phone: string;
  website?: string;
  
  // Address
  address: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  
  // Business Details
  businessInfo: {
    taxId: string;
    businessType: 'sole_proprietorship' | 'partnership' | 'corporation' | 'llc';
    yearEstablished: number;
    description?: string;
    licenseNumber?: string;
    licenseExpiration?: string;
  };
  
  // Metrics
  metrics?: {
    rating: number;
    completionRate: number;
    onTimeRate: number;
    responseTime: number;
    tripCount: number;
    reviewCount: number;
  };
  
  // Relationships
  tenantId: string;
  tenants?: string[]; // IDs of tenants this affiliate serves
  
  // Contact Persons
  contactPersons: Array<{
    id: string;
    name: string;
    role: string;
    email: string;
    phone: string;
    isPrimary: boolean;
  }>;
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
  lastActive?: string;
}
```

### Affiliate Application

Represents an application from a service provider to join the platform.

```typescript
export interface AffiliateApplication {
  id: string;
  affiliateId?: string; // Only set once approved
  status: 'draft' | 'submitted' | 'in_review' | 'approved' | 'rejected';
  progress: number; // 0-100
  
  // Business Information
  businessName: string;
  dba?: string;
  taxId: string;
  businessType: 'sole_proprietorship' | 'partnership' | 'corporation' | 'llc';
  yearEstablished: number;
  website?: string;
  
  // Contact Information
  primaryContact: {
    name: string;
    email: string;
    phone: string;
    position: string;
  };
  
  // Address
  address: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  
  // Service Locations
  serviceLocations: Array<{
    city: string;
    state: string;
    isAirportService: boolean;
  }>;
  
  // Service Types
  serviceTypes: string[]; // e.g., ['point_to_point', 'hourly', 'airport']
  
  // Document IDs
  documents: Record<string, string>; // e.g., { "business_license": "doc_123", "insurance": "doc_456" }
  
  // Fleet Information
  fleetSummary?: {
    totalVehicles: number;
    vehicleTypes: string[];
  };
  
  // Review Notes
  reviewNotes?: Array<{
    id: string;
    note: string;
    createdBy: string;
    createdAt: string;
  }>;
  
  // Tenant
  tenantId: string;
  
  // Timestamps
  submittedAt?: string;
  reviewedAt?: string;
  createdAt: string;
  updatedAt: string;
}
```

### Fleet Vehicle

Represents a vehicle in an affiliate's fleet.

```typescript
export interface FleetVehicle {
  id: string;
  affiliateId: string;
  
  // Vehicle Details
  make: string;
  model: string;
  year: number;
  color?: string;
  licensePlate: string;
  vin: string;
  registrationExpiration: string;
  
  // Classification
  vehicleTypeId: string;
  capacity: number;
  features: string[]; // e.g., ['wifi', 'water', 'charging']
  accessibility: string[]; // e.g., ['wheelchair', 'car_seat']
  
  // Status
  status: 'active' | 'maintenance' | 'inactive';
  
  // Insurance
  insuranceInfo?: {
    policyNumber: string;
    provider: string;
    expirationDate: string;
    coverageAmount: number;
  };
  
  // Images
  images?: string[]; // URLs to vehicle images
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
}
```

### Vehicle Type

Defines the types of vehicles available in the system.

```typescript
export interface VehicleType {
  id: string;
  name: string;
  description?: string;
  category: 'sedan' | 'suv' | 'van' | 'stretch' | 'bus' | 'specialty';
  capacity: {
    passengers: number;
    luggage: number;
  };
  image?: string;
  isActive: boolean;
}
```

### Affiliate Rates

Defines pricing structures for an affiliate.

```typescript
export interface AffiliateRates {
  id: string;
  affiliateId: string;
  vehicleTypeId: string;
  
  // Base Rates
  baseRates: {
    pointToPoint?: number;
    hourly?: number;
    minimumHours?: number;
    distance?: {
      base: number;
      perMile: number;
      minimumMiles: number;
    };
    airport?: number;
    waitTime?: number;
    extraHour?: number;
  };
  
  // Modifiers
  modifiers?: {
    nightSurcharge?: number;
    holidaySurcharge?: number;
    peakHourSurcharge?: number;
  };
  
  // Seasonal Rates
  seasonalRates?: Array<{
    id: string;
    name: string;
    startDate: string;
    endDate: string;
    multiplier: number;
    description?: string;
  }>;
  
  // Pricing Model
  pricingModel: 'point_to_point' | 'distance_time' | 'hourly';
  
  // Cancellation Policy
  cancellationPolicy?: string;
  
  // Status
  isActive: boolean;
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
}
```

### Driver

Represents a driver working for an affiliate.

```typescript
export interface Driver {
  id: string;
  affiliateId: string;
  
  // Personal Information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  
  // Status
  status: 'active' | 'inactive' | 'pending';
  
  // License Information
  licenseInfo: {
    number: string;
    state: string;
    expirationDate: string;
    class: string;
  };
  
  // Certification
  certifications?: string[];
  
  // Background Check
  backgroundCheck?: {
    status: 'passed' | 'failed' | 'pending';
    date?: string;
    expirationDate?: string;
    provider?: string;
  };
  
  // Drug Test
  drugTest?: {
    status: 'passed' | 'failed' | 'pending';
    date?: string;
    expirationDate?: string;
    provider?: string;
  };
  
  // Profile Image
  profileImage?: string;
  
  // Metrics
  metrics?: {
    rating: number;
    completedTrips: number;
    completionRate: number;
    onTimeRate: number;
  };
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
}
```

### Document

Represents a document uploaded by an affiliate.

```typescript
export interface AffiliateDocument {
  id: string;
  affiliateId: string;
  
  // Document Type
  type: 'business_license' | 'insurance' | 'vehicle_registration' | 'driver_license' | 'background_check' | 'tax_document' | 'other';
  
  // Document Details
  filename: string;
  originalFilename: string;
  mimeType: string;
  size: number;
  url: string;
  
  // Verification
  status: 'pending' | 'verified' | 'rejected';
  verifiedBy?: string;
  verifiedAt?: string;
  rejectionReason?: string;
  
  // Metadata
  metadata?: {
    expirationDate?: string;
    issueDate?: string;
    documentNumber?: string;
    issuingAuthority?: string;
    relatedEntityId?: string; // e.g., driver or vehicle ID
  };
  
  // Timestamps
  uploadedBy: string;
  createdAt: string;
  updatedAt: string;
}
```

### Audit Log

Records all actions taken in the system for audit purposes.

```typescript
export interface AuditLog {
  id: string;
  
  // Action Details
  action: string;
  entityType: string;
  entityId: string;
  description: string;
  
  // Change Details
  previousValue?: any;
  newValue?: any;
  
  // User Details
  userId: string;
  userEmail: string;
  userRole: string;
  
  // Context
  ipAddress: string;
  userAgent?: string;
  tenantId?: string;
  
  // Timestamp
  timestamp: string;
}
```

## Operational Entities

### Live Trip

Represents a trip currently in progress or scheduled.

```typescript
export interface LiveTrip {
  id: string;
  affiliateId: string;
  driverId?: string;
  
  // Status
  status: 'scheduled' | 'assigned' | 'en_route' | 'arrived' | 'in_progress' | 'completed' | 'cancelled';
  
  // Trip Details
  pickup: {
    location: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
    address: string;
    time: string;
    instructions?: string;
  };
  
  dropoff: {
    location: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
    address: string;
    estimatedTime?: string;
    instructions?: string;
  };
  
  // Passenger Information
  passenger: {
    name: string;
    phone?: string;
    email?: string;
    passengerCount: number;
    luggageCount?: number;
  };
  
  // Vehicle Info
  vehicle?: {
    id: string;
    type: string;
    make?: string;
    model?: string;
    licensePlate?: string;
  };
  
  // Driver Info
  driver?: {
    id: string;
    name: string;
    phone: string;
    rating?: number;
  };
  
  // Tracking
  tracking?: {
    currentLocation?: {
      latitude: number;
      longitude: number;
      updatedAt: string;
    };
    estimatedArrival?: string;
    delay?: number; // in minutes
  };
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
}
```

### Operational Alert

Represents alerts related to operational issues.

```typescript
export interface OperationalAlert {
  id: string;
  affiliateId: string;
  
  // Alert Details
  type: 'late_driver' | 'sla_breach' | 'compliance' | 'vehicle_issue' | 'customer_complaint' | 'system';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  details?: string;
  
  // Related Entity
  relatedEntityType?: 'trip' | 'driver' | 'vehicle' | 'document';
  relatedEntityId?: string;
  
  // Status
  status: 'active' | 'acknowledged' | 'resolved';
  acknowledgedBy?: string;
  acknowledgedAt?: string;
  resolvedBy?: string;
  resolvedAt?: string;
  resolution?: string;
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
}
```

### Performance Metric

Represents performance metrics for an affiliate.

```typescript
export interface PerformanceMetric {
  id: string;
  affiliateId: string;
  
  // Time Period
  period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  startDate: string;
  endDate: string;
  
  // Metrics
  metrics: {
    completionRate: number;
    onTimeRate: number;
    customerRating: number;
    responseTime: number; // in minutes
    utilization: number; // percentage
    revenue?: number;
    tripCount: number;
    passengerCount: number;
    incidentCount: number;
    cancellationRate: number;
  };
  
  // SLA Compliance
  slaCompliance: {
    responseTime: boolean;
    onTime: boolean;
    customerSatisfaction: boolean;
  };
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
}
```

## API Response Types

### Paginated Response

Generic type for paginated responses.

```typescript
export interface PaginatedResponse<T> {
  items: T[];
  totalCount: number;
  pageSize: number;
  page: number;
  totalPages: number;
}
```

### API Error

Standard error response format.

```typescript
export interface ApiError {
  statusCode: number;
  message: string;
  errors?: Record<string, string[]>;
  timestamp: string;
  path?: string;
  code?: string;
}
```

## Form Schemas (Zod)

### Affiliate Form Schema

```typescript
import { z } from 'zod';

export const affiliateFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  businessName: z.string().optional(),
  email: z.string().email("Please enter a valid email"),
  phone: z.string().min(10, "Please enter a valid phone number"),
  website: z.string().url().optional().or(z.literal('')),
  
  address: z.object({
    street: z.string().min(2, "Street is required"),
    city: z.string().min(2, "City is required"),
    state: z.string().min(2, "State is required"),
    zip: z.string().min(5, "Zip code is required"),
    country: z.string().min(2, "Country is required"),
  }),
  
  businessInfo: z.object({
    taxId: z.string().min(1, "Tax ID is required"),
    businessType: z.enum(['sole_proprietorship', 'partnership', 'corporation', 'llc']),
    yearEstablished: z.number().min(1900).max(new Date().getFullYear()),
    description: z.string().optional(),
    licenseNumber: z.string().optional(),
    licenseExpiration: z.string().optional(),
  }),
  
  status: z.enum(['active', 'pending', 'inactive', 'suspended']),
  verificationStatus: z.enum(['verified', 'unverified', 'in_progress']),
  
  tenantId: z.string().min(1, "Tenant ID is required"),
  tenants: z.array(z.string()).optional(),
  
  contactPersons: z.array(z.object({
    name: z.string().min(2, "Name is required"),
    role: z.string().min(2, "Role is required"),
    email: z.string().email("Please enter a valid email"),
    phone: z.string().min(10, "Please enter a valid phone number"),
    isPrimary: z.boolean(),
  })).min(1, "At least one contact person is required"),
});

export type AffiliateFormValues = z.infer<typeof affiliateFormSchema>;
```

### Rate Form Schema

```typescript
import { z } from 'zod';

export const rateFormSchema = z.object({
  affiliateId: z.string().min(1, "Affiliate ID is required"),
  vehicleTypeId: z.string().min(1, "Vehicle type is required"),
  
  pricingModel: z.enum(['point_to_point', 'distance_time', 'hourly']),
  
  baseRates: z.object({
    pointToPoint: z.number().min(0).optional(),
    hourly: z.number().min(0).optional(),
    minimumHours: z.number().min(0).optional(),
    
    distance: z.object({
      base: z.number().min(0),
      perMile: z.number().min(0),
      minimumMiles: z.number().min(0),
    }).optional(),
    
    airport: z.number().min(0).optional(),
    waitTime: z.number().min(0).optional(),
    extraHour: z.number().min(0).optional(),
  }),
  
  modifiers: z.object({
    nightSurcharge: z.number().min(0).optional(),
    holidaySurcharge: z.number().min(0).optional(),
    peakHourSurcharge: z.number().min(0).optional(),
  }).optional(),
  
  seasonalRates: z.array(z.object({
    name: z.string().min(1, "Name is required"),
    startDate: z.string(),
    endDate: z.string(),
    multiplier: z.number().min(1),
    description: z.string().optional(),
  })).optional(),
  
  cancellationPolicy: z.string().optional(),
  isActive: z.boolean(),
});

export type RateFormValues = z.infer<typeof rateFormSchema>;
```

---

*This document should be updated whenever data schemas change. All interface types should be used consistently across the codebase to ensure type safety.* 