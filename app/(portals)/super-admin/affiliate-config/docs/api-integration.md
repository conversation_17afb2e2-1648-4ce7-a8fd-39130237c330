# Affiliate Management API Integration

This document provides guidance on integrating the frontend with the backend APIs for affiliate management functionality. It covers best practices, patterns, and implementation details for both operations and configuration features.

## API Structure

### Base Endpoints

```
# Affiliate Operations (live business)
/api/v1/affiliate-operations/
    /live-trips
    /scheduled-trips
    /quotes
    /alerts
    /metrics
    /sla
    /contact

# Affiliate Configuration (deep config)
/api/v1/affiliate-config/
    /applications
    /onboarding
    /fleet
    /rates
    /drivers
    /documents
    /compliance
    /audit
```

## Authentication

All API requests must include a valid JWT token in the Authorization header:

```typescript
const fetchAffiliateData = async (affiliateId: string) => {
  try {
    const response = await fetch(`/api/v1/affiliate-config/${affiliateId}`, {
      headers: {
        'Authorization': `Bearer ${session.accessToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch affiliate data');
    }
    
    return await response.json();
  } catch (error) {
    handleError(error);
    return null;
  }
};
```

## Data Fetching Patterns

### Using React Query (Recommended)

For data fetching, use React Query to handle caching, background updates, and stale data:

```typescript
import { useQuery, useMutation, useQueryClient } from 'react-query';

// Fetch affiliate data
export const useAffiliateData = (affiliateId: string) => {
  return useQuery(
    ['affiliate', affiliateId], 
    () => fetchAffiliateData(affiliateId),
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 3,
      onError: (error) => {
        toast({
          title: "Error fetching affiliate data",
          description: error.message,
          variant: "destructive"
        });
      }
    }
  );
};

// Update affiliate data
export const useUpdateAffiliate = () => {
  const queryClient = useQueryClient();
  return useMutation(
    (data: { id: string, updates: Partial<Affiliate> }) => 
      updateAffiliateData(data.id, data.updates),
    {
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries(['affiliate', variables.id]);
        toast({
          title: "Success",
          description: "Affiliate updated successfully",
        });
      },
      onError: (error) => {
        toast({
          title: "Error updating affiliate",
          description: error.message,
          variant: "destructive"
        });
      }
    }
  );
};
```

### API Call Debouncing

Always debounce API calls triggered by user input:

```typescript
import { useState, useCallback } from 'react';
import { debounce } from 'lodash';

export const useSearchAffiliates = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  
  const debouncedSearch = useCallback(
    debounce(async (query: string) => {
      if (!query) {
        setResults([]);
        return;
      }
      
      setLoading(true);
      try {
        const data = await searchAffiliates(query);
        setResults(data);
      } catch (error) {
        console.error('Search error:', error);
      } finally {
        setLoading(false);
      }
    }, 300),
    []
  );
  
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    debouncedSearch(query);
  }, [debouncedSearch]);
  
  return { searchQuery, results, loading, handleSearch };
};
```

## Error Handling

Implement consistent error handling across all API calls:

```typescript
const handleApiError = (error: unknown, fallbackMessage: string = "An error occurred") => {
  console.error('API Error:', error);
  
  let errorMessage = fallbackMessage;
  
  if (error instanceof Error) {
    errorMessage = error.message;
  }
  
  if (axios.isAxiosError(error) && error.response) {
    // Handle different status codes
    switch (error.response.status) {
      case 401:
        errorMessage = "Authentication required. Please log in again.";
        // Redirect to login
        router.push('/login');
        break;
      case 403:
        errorMessage = "You don't have permission to perform this action";
        break;
      case 404:
        errorMessage = "The requested resource was not found";
        break;
      case 422:
        errorMessage = "Validation error. Please check your input.";
        break;
      case 500:
        errorMessage = "Server error. Please try again later.";
        break;
      default:
        errorMessage = error.response.data?.message || fallbackMessage;
    }
  }
  
  toast({
    title: "Error",
    description: errorMessage,
    variant: "destructive"
  });
  
  return errorMessage;
};
```

## Handling Loading States

Implement loading states for all asynchronous operations:

```tsx
const AffiliateDetails = ({ affiliateId }: { affiliateId: string }) => {
  const { data, isLoading, error } = useAffiliateData(affiliateId);
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-48">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p>Loading affiliate data...</p>
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="p-4 border border-red-200 bg-red-50 rounded-md">
        <p className="text-red-700">Error loading affiliate data</p>
        <Button variant="outline" onClick={() => window.location.reload()}>
          Try Again
        </Button>
      </div>
    );
  }
  
  if (!data) {
    return <div>No affiliate data found</div>;
  }
  
  return (
    <div>
      {/* Render affiliate details */}
    </div>
  );
};
```

## Form Validation with Zod

Use Zod for form validation:

```typescript
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

const affiliateFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email"),
  phone: z.string().min(10, "Please enter a valid phone number"),
  address: z.object({
    street: z.string().min(2, "Street is required"),
    city: z.string().min(2, "City is required"),
    state: z.string().min(2, "State is required"),
    zip: z.string().min(5, "Zip code is required"),
  }),
  taxId: z.string().optional(),
});

type AffiliateFormValues = z.infer<typeof affiliateFormSchema>;

export const AffiliateForm = ({ onSubmit, defaultValues }: {
  onSubmit: (data: AffiliateFormValues) => void;
  defaultValues?: Partial<AffiliateFormValues>;
}) => {
  const form = useForm<AffiliateFormValues>({
    resolver: zodResolver(affiliateFormSchema),
    defaultValues: defaultValues || {
      name: "",
      email: "",
      phone: "",
      address: {
        street: "",
        city: "",
        state: "",
        zip: "",
      },
      taxId: "",
    },
  });
  
  return (
    <form onSubmit={form.handleSubmit(onSubmit)}>
      {/* Form fields */}
    </form>
  );
};
```

## Audit Trail Logging

All admin actions should be logged for audit purposes:

```typescript
const logAuditAction = async (action: {
  entityType: string;
  entityId: string;
  action: string;
  previousValue?: any;
  newValue?: any;
  details?: string;
}) => {
  try {
    await fetch('/api/v1/audit/log', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ...action,
        timestamp: new Date().toISOString(),
        userId: session.user.id,
        userEmail: session.user.email,
        ipAddress: window.clientIpAddress || "unknown",
      }),
    });
  } catch (error) {
    console.error('Failed to log audit action:', error);
    // Don't show UI error, but log to monitoring system
  }
};

// Usage
const updateAffiliateStatus = async (affiliateId: string, newStatus: string) => {
  const prevStatus = currentAffiliate.status;
  
  try {
    await api.updateAffiliateStatus(affiliateId, newStatus);
    
    // Log the action
    await logAuditAction({
      entityType: 'affiliate',
      entityId: affiliateId,
      action: 'status_update',
      previousValue: prevStatus,
      newValue: newStatus,
      details: `Status changed from ${prevStatus} to ${newStatus}`,
    });
    
    toast({
      title: "Success",
      description: "Affiliate status updated successfully",
    });
  } catch (error) {
    handleApiError(error, "Failed to update affiliate status");
  }
};
```

## Performance Optimization

### Pagination for Large Lists

Always implement pagination for lists that could grow large:

```typescript
import { useState } from 'react';
import { useQuery } from 'react-query';

export const useAffiliatesList = () => {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  
  const { data, isLoading, error } = useQuery(
    ['affiliates', page, pageSize],
    () => fetchAffiliates({ page, pageSize }),
    { keepPreviousData: true }
  );
  
  return {
    affiliates: data?.items || [],
    totalCount: data?.totalCount || 0,
    page,
    pageSize,
    setPage,
    setPageSize,
    isLoading,
    error,
    hasNextPage: data ? page * pageSize < data.totalCount : false,
    hasPreviousPage: page > 1,
  };
};
```

### Data Caching

Use appropriate caching strategies for different data types:

```typescript
// Static data (rarely changes)
export const useVehicleTypes = () => {
  return useQuery(
    'vehicleTypes',
    fetchVehicleTypes,
    {
      staleTime: Infinity, // Never becomes stale
      cacheTime: Infinity, // Never expires from cache
    }
  );
};

// Frequently updated data
export const useActiveTripsList = () => {
  return useQuery(
    'activeTrips',
    fetchActiveTrips,
    {
      staleTime: 30 * 1000, // 30 seconds
      refetchInterval: 30 * 1000, // Poll every 30 seconds
    }
  );
};
```

## Types and Interfaces

Define explicit types for all API responses:

```typescript
// types/affiliates.ts
export interface Affiliate {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zip: string;
    country: string;
  };
  status: 'active' | 'pending' | 'suspended' | 'inactive';
  createdAt: string;
  updatedAt: string;
  tenantId: string;
  verificationStatus: 'verified' | 'unverified' | 'in_progress';
  businessInfo: {
    taxId: string;
    businessType: string;
    yearEstablished: number;
    description: string;
  };
  rating: number;
  contactPersons: Array<{
    name: string;
    role: string;
    email: string;
    phone: string;
    isPrimary: boolean;
  }>;
}

export interface AffiliateListResponse {
  items: Affiliate[];
  totalCount: number;
  pageSize: number;
  page: number;
}
```

## Testing Strategy

### Unit Testing API Hooks

```typescript
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClientProvider, QueryClient } from 'react-query';
import { useAffiliateData } from './hooks/useAffiliateData';
import { fetchAffiliateData } from './api/affiliates';

// Mock the API function
jest.mock('./api/affiliates');

describe('useAffiliateData', () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  
  const wrapper = ({ children }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
  
  afterEach(() => {
    queryClient.clear();
    jest.clearAllMocks();
  });
  
  it('should fetch affiliate data successfully', async () => {
    const mockData = { id: '123', name: 'Test Affiliate' };
    (fetchAffiliateData as jest.Mock).mockResolvedValue(mockData);
    
    const { result } = renderHook(() => useAffiliateData('123'), { wrapper });
    
    expect(result.current.isLoading).toBeTruthy();
    
    await waitFor(() => {
      expect(result.current.isLoading).toBeFalsy();
    });
    
    expect(fetchAffiliateData).toHaveBeenCalledWith('123');
    expect(result.current.data).toEqual(mockData);
  });
  
  it('should handle errors', async () => {
    const error = new Error('Failed to fetch');
    (fetchAffiliateData as jest.Mock).mockRejectedValue(error);
    
    const { result } = renderHook(() => useAffiliateData('123'), { wrapper });
    
    await waitFor(() => {
      expect(result.current.isLoading).toBeFalsy();
    });
    
    expect(result.current.error).toBeDefined();
    expect(result.current.error.message).toBe('Failed to fetch');
  });
});
```

## Security Best Practices

- Use parameterized queries to prevent SQL injection
- Validate all input data client-side and server-side
- Implement proper error handling
- Avoid storing sensitive data in localStorage
- Debounce all search/filter API calls
- Use HTTPS for all API requests
- Implement CSRF protection for form submissions

---

*This document should be updated whenever the API structure or integration patterns change.* 