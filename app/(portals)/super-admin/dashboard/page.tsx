"use client"

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/app/components/ui/tabs"
import {
  Activity,
  Users,
  Building2,
  DollarSign,
  CreditCard,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  Server,
  Layers,
  AlertTriangle
} from "lucide-react"
import { Button } from "@/app/components/ui/button"
import { useToast } from "@/app/components/ui/use-toast"
import { useAuth } from '@/lib/auth/context'
import { Progress } from "@/app/components/ui/progress"

interface SummaryCardProps {
  title: string
  value: string | number
  description?: string
  icon: React.ReactNode
  trend?: {
    value: number
    positive: boolean
  }
}

function SummaryCard({ title, value, description, icon, trend }: SummaryCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          {title}
        </CardTitle>
        <div className="h-4 w-4 text-muted-foreground">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {trend ? (
          <p className="text-xs text-muted-foreground flex items-center mt-1">
            {trend.positive ? (
              <ArrowUpRight className="mr-1 h-3 w-3 text-emerald-500" />
            ) : (
              <ArrowDownRight className="mr-1 h-3 w-3 text-rose-500" />
            )}
            <span className={trend.positive ? "text-emerald-500" : "text-rose-500"}>
              {trend.value}%
            </span>
            {description && <span className="ml-1 text-muted-foreground">{description}</span>}
          </p>
        ) : (
          <p className="text-xs text-muted-foreground mt-1">{description}</p>
        )}
      </CardContent>
    </Card>
  )
}

function RecentActivityItem({ time, title, description }: { time: string; title: string; description: string }) {
  return (
    <div className="flex items-start space-x-4 rounded-md p-2 transition-all hover:bg-accent">
      <div className="flex-shrink-0 mt-0.5">
        <Activity className="h-5 w-5 text-muted-foreground" />
      </div>
      <div className="flex-1 space-y-1">
        <p className="text-sm font-medium leading-none">{title}</p>
        <p className="text-xs text-muted-foreground">{description}</p>
      </div>
      <div className="text-xs text-muted-foreground">{time}</div>
    </div>
  )
}

function ServerStatusItem({ name, status, load }: { name: string; status: 'healthy' | 'warning' | 'error'; load: number }) {
  const statusColors = {
    healthy: 'bg-emerald-500',
    warning: 'bg-amber-500',
    error: 'bg-rose-500'
  }

  return (
    <div className="flex items-center justify-between space-y-0 py-2">
      <div className="flex items-center space-x-2">
        <div className={`h-2 w-2 rounded-full ${statusColors[status]}`} />
        <p className="text-sm font-medium">{name}</p>
      </div>
      <div className="flex items-center space-x-4">
        <div className="w-24">
          <Progress value={load} className="h-2 w-full" />
        </div>
        <div className="text-sm">{load}%</div>
      </div>
    </div>
  )
}

export default function SuperAdminDashboard() {
  const { toast } = useToast()
  const { user } = useAuth()
  const [stats, setStats] = useState({
    tenants: { total: 24, growth: 12 },
    users: { total: 342, growth: 8 },
    revenue: { total: 42500, growth: 18 },
    subscriptions: { total: 28, growth: -3 }
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true)
        // Placeholder for actual API calls
        setTimeout(() => {
          setStats({
            tenants: { total: 24, growth: 12 },
            users: { total: 342, growth: 8 },
            revenue: { total: 42500, growth: 18 },
            subscriptions: { total: 28, growth: -3 }
          })
          setLoading(false)
        }, 1000)
      } catch (error) {
        console.error('Error fetching dashboard data:', error)
        toast({
          title: "Error fetching data",
          description: "Could not load dashboard data. Please try again later.",
          variant: "destructive"
        })
        setLoading(false)
      }
    }
    fetchDashboardData()
  }, [toast])

  return (
    <div className="space-y-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Super Admin Dashboard</h2>
          <p className="text-muted-foreground">SaaS platform metrics and system health overview</p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <SummaryCard
          title="Total Tenants"
          value={loading ? "Loading..." : stats.tenants.total}
          description="Active organizations"
          icon={<Building2 className="h-4 w-4" />}
          trend={{ value: stats.tenants.growth, positive: stats.tenants.growth > 0 }}
        />
        <SummaryCard
          title="Total Users"
          value={loading ? "Loading..." : stats.users.total}
          description="Across all tenants"
          icon={<Users className="h-4 w-4" />}
          trend={{ value: stats.users.growth, positive: stats.users.growth > 0 }}
        />
        <SummaryCard
          title="Monthly Revenue"
          value={loading ? "Loading..." : `$${stats.revenue.total.toLocaleString()}`}
          description="This month"
          icon={<DollarSign className="h-4 w-4" />}
          trend={{ value: stats.revenue.growth, positive: stats.revenue.growth > 0 }}
        />
        <SummaryCard
          title="Active Subscriptions"
          value={loading ? "Loading..." : stats.subscriptions.total}
          description="Paid plans"
          icon={<CreditCard className="h-4 w-4" />}
          trend={{ value: stats.subscriptions.growth, positive: stats.subscriptions.growth > 0 }}
        />
      </div>

      {/* Recent Activity */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Recent Activities</CardTitle>
            <CardDescription>
              Platform-wide activity in the last 24 hours
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <RecentActivityItem
                time="2h ago"
                title="New tenant: Acme Corporation"
                description="Subscription plan: Enterprise, Annual billing"
              />
              <RecentActivityItem
                time="4h ago"
                title="Payment received: $2,500"
                description="From: Global Logistics Inc. (Invoice #INV-2023-045)"
              />
              <RecentActivityItem
                time="8h ago"
                title="Subscription upgraded"
                description="Swift Transit changed from Basic to Premium plan"
              />
              <RecentActivityItem
                time="12h ago"
                title="New tenant: City Tours LLC"
                description="Subscription plan: Basic, Monthly billing"
              />
              <RecentActivityItem
                time="18h ago"
                title="Support ticket escalated"
                description="Ticket #4532: API integration issue (High priority)"
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* System Status */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>System Status</CardTitle>
            <CardDescription>
              Current system health and performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between space-y-0">
                <div className="space-y-1">
                  <p className="text-sm font-medium leading-none">Overall Health</p>
                  <p className="text-sm text-muted-foreground">
                    All systems operational
                  </p>
                </div>
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-green-100">
                  <Server className="h-5 w-5 text-green-600" />
                </div>
              </div>
              <div className="space-y-2 pt-2">
                <ServerStatusItem name="API Server" status="healthy" load={34} />
                <ServerStatusItem name="Database" status="healthy" load={42} />
                <ServerStatusItem name="Storage" status="warning" load={78} />
                <ServerStatusItem name="Authentication" status="healthy" load={12} />
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Alerts</CardTitle>
            <CardDescription>
              System warnings and notifications
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 border rounded-md p-4">
              <div className="flex items-center space-x-2 text-amber-500">
                <AlertTriangle className="h-4 w-4" />
                <p className="text-sm">Storage approaching capacity (78%)</p>
              </div>
              <div className="text-xs text-muted-foreground mt-1 pl-6">
                Recommended action: Review large files and database size
              </div>
            </div>
            <div className="flex justify-end mt-4">
              <Button size="sm">View All Alerts</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
