"use client"

import React, { <PERSON>actNode } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { cn } from "@/lib/utils"
import { UserNav } from "@/app/components/features/navigation/user-nav"
import { MobileNav } from "@/app/components/features/navigation/mobile-nav"
import { useAuth } from '@/lib/auth/context';
import { UserRole } from '@/src/types/roles'
import { GlobalFilterProvider } from '@/app/contexts/GlobalFilterContext'
import { OrgSelector } from '@/app/components/features/super-admin/filters/OrgSelector'
import { GlobalDateRangePicker } from '@/app/components/features/super-admin/filters/GlobalDateRangePicker'
import { TenantSwitcher } from '@/app/components/features/tenant/TenantSwitcher'
import { Loader2 } from "lucide-react"
import {
  LayoutDashboard,
  Users,
  Building2,
  Settings,
  Shield,
  Globe,
  CreditCard,
  BarChart3,
  Network,
  FileText,
  Briefcase,
  ClipboardList,
  CalendarDays,
  Plane,
  Map as MapIcon,
  Bell,
  UsersRound,
  Car
} from "lucide-react"
import Link from "next/link"

// Super Admin Specific Navigation
const superAdminNavigation = [
  {
    title: "Dashboard",
    href: "/super-admin/dashboard",
    icon: LayoutDashboard
  },
  {
    title: "ORGs",
    href: "/super-admin/orgs",
    icon: Building2,
    highlight: true,
    children: [
      {
        title: "Manage Organizations",
        href: "/super-admin/orgs",
      },
      {
        title: "Analytics Dashboard",
        href: "/super-admin/subscriptions/analytics",
      },
      {
        title: "Reports & Exports",
        href: "/super-admin/subscriptions/reports",
      }
    ]
  },
  {
    title: "Users",
    href: "/super-admin/users",
    icon: Users
  },
  {
    title: "Tenants",
    href: "/super-admin/tenants",
    icon: Globe
  },
  {
    title: "Affiliate Config",
    href: "/super-admin/affiliate-config",
    icon: Network
  },
  {
    title: "Coverage",
    href: "/super-admin/coverage",
    icon: MapIcon
  },
  {
    title: "Analytics",
    href: "/super-admin/analytics",
    icon: BarChart3
  },
  {
    title: "Settings",
    href: "/super-admin/settings",
    icon: Settings
  },
  {
    title: "Security",
    href: "/super-admin/security",
    icon: Shield
  },
  {
    title: "Documentation",
    href: "/super-admin/docs",
    icon: FileText
  },
];

// Operations Related Navigation - Reordered
const operationsNavigation = [
  {
    title: "OPS Snapshot",
    href: "/super-admin/tenant-dashboards",
    icon: LayoutDashboard
  },
  {
    title: "Events",
    href: "/super-admin/events",
    icon: CalendarDays
  },
  {
    title: "Quotes",
    href: "/super-admin/quotes",
    icon: FileText
  },
  {
    title: "Trips",
    href: "/super-admin/trips",
    icon: Plane
  },
  {
    title: "Passengers",
    href: "/super-admin/passengers",
    icon: UsersRound
  },
  {
    title: "Affiliate Operations",
    href: "/super-admin/affiliates",
    icon: Network
  },
];

export default function SuperAdminLayout({
  children,
}: {
  children: ReactNode
}) {
  const pathname = usePathname()
  const { user, loading, initialized } = useAuth()
  const router = useRouter()
  
  // Define paths where each global filter is relevant
  const orgFilterRelevantPaths = [
    "/super-admin/quotes",
    "/super-admin/events",
    "/super-admin/trips",
    "/super-admin/passengers",
    "/super-admin/affiliates", // Affiliate Operations
    "/super-admin/tenant-dashboards", // Operational Tenant Dashboards
  ];

  const dateRangeFilterRelevantPaths = [
    "/super-admin/quotes",
    "/super-admin/events",
    "/super-admin/trips",
    "/super-admin/passengers",
    "/super-admin/tenant-dashboards", // Operational Dashboards
    "/super-admin/dashboard",         // Platform SA Dashboard
    "/super-admin/analytics",       // Platform Analytics
    "/super-admin/orgs",            // Organizations & Subscriptions
  ];
  
  React.useEffect(() => {
    console.log('SuperAdminLayout - Auth state check:', {
      loading,
      initialized,
      userPresent: !!user,
      userEmail: user?.email,
      userRoles: user?.roles,
    });

    // Don't redirect while loading or not initialized
    if (loading || !initialized) {
      console.log('SuperAdminLayout - Still loading or not initialized, skipping redirect check');
      return;
    }

    // If we have no user after initialization, redirect
    if (!user) {
      console.log('SuperAdminLayout - No user after initialization, redirecting to login');
      router.push('/login');
      return;
    }

    // Check for SUPER_ADMIN role
    const isSuperAdmin = user.roles?.includes('SUPER_ADMIN' as UserRole);
    console.log('SuperAdminLayout - User role check:', {
      roles: user.roles,
      isSuperAdmin,
      userId: user.id,
      email: user.email
    });

    if (!isSuperAdmin) {
      console.error('SuperAdminLayout - User is not SUPER_ADMIN, redirecting to login');
      router.push('/login');
    }
  }, [user, loading, initialized, router]);
  
  if (loading || !initialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p>Loading super admin dashboard...</p>
        </div>
      </div>
    )
  }
  
  const isSuperAdminUser = user?.roles?.includes('SUPER_ADMIN' as UserRole);

  return (
    <GlobalFilterProvider>
      <>
        {/* Desktop Header (Two Rows) */}
        <div className="hidden md:flex flex-col">
          {/* Row 1: Title, Super Admin Links, UserNav & Notifications Icon */}
          <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="container flex h-16 items-center">
              <div className="mr-6 flex items-center space-x-2 shrink-0">
                <Link href="/super-admin/dashboard" className="flex flex-col items-center">
                  <div className="flex items-center space-x-2">
                    <Globe className="h-7 w-7 text-primary" />
                    <span className="text-xl font-bold">transflow</span>
                  </div>
                  {isSuperAdminUser && (
                    <span className="text-xs font-semibold text-muted-foreground mt-1">Super Admin</span>
                  )}
                </Link>
              </div>
              
              {/* Super Admin Specific Links (center area) */}
              <nav className="flex items-center space-x-1 lg:space-x-2 mx-auto overflow-x-auto whitespace-nowrap">
                {superAdminNavigation.map((item) => (
                  <Link
                    key={item.title}
                    href={item.href}
                    className={cn(
                      "group flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
                      pathname === item.href ? "bg-accent" : "transparent",
                      item.highlight && "text-primary font-semibold"
                    )}
                  >
                    <item.icon className={cn("mr-2 h-4 w-4 shrink-0", item.highlight && "text-primary")} />
                    <span>{item.title}</span>
                  </Link>
                ))}
              </nav>

              {/* Right side: Tenant Switcher, Notifications Icon and UserNav */}
              <div className="ml-auto flex items-center space-x-2 pl-4 shrink-0">
                <TenantSwitcher />
                <Link href="/super-admin/notifications" className="p-2 rounded-full hover:bg-accent">
                  <Bell className="h-5 w-5" />
                  <span className="sr-only">Notifications</span>
                </Link>
                <UserNav />
              </div>
            </div>
          </header>

          {/* Row 2: Global Filters & Tenant Data Related Links */}
          <nav className="sticky top-16 z-40 w-full border-b bg-background/90 backdrop-blur supports-[backdrop-filter]:bg-background/50 shadow-sm">
            <div className="container flex h-14 items-center justify-between px-4">
              {/* Global Filters - Left Aligned */}
              <div className="flex items-center space-x-3">
                {orgFilterRelevantPaths.includes(pathname) && <OrgSelector />}
                {dateRangeFilterRelevantPaths.includes(pathname) && <GlobalDateRangePicker />}
              </div>
              {/* Tenant Data Nav Links - Right Aligned */}
              <div className="flex items-center space-x-1 lg:space-x-2 overflow-x-auto whitespace-nowrap">
                {operationsNavigation.map((item) => (
                  <Link
                    key={item.title}
                    href={item.href}
                    className={cn(
                      "group flex items-center rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground",
                      pathname === item.href ? "bg-accent" : "transparent"
                    )}
                  >
                    <item.icon className="mr-2 h-4 w-4 shrink-0" />
                    <span>{item.title}</span>
                  </Link>
                ))}
              </div>
            </div>
          </nav>
        </div>

        {/* Mobile Header */}
        <div className="md:hidden">
          <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="container flex h-16 items-center justify-between px-4">
              <Link href="/super-admin/dashboard" className="flex flex-col items-center">
                <div className="flex items-center space-x-2">
                  <Globe className="h-6 w-6 text-primary" />
                  <span className="text-lg font-bold">transflow</span>
                </div>
                {isSuperAdminUser && (
                  <span className="text-xs font-semibold text-muted-foreground">Super Admin</span>
                )}
              </Link>
              <div className="flex items-center space-x-1">
                <TenantSwitcher className="hidden sm:flex" />
                <Link href="/super-admin/notifications" className="p-2 rounded-full hover:bg-accent">
                  <Bell className="h-5 w-5" />
                  <span className="sr-only">Notifications</span>
                </Link>
                <UserNav />
                <MobileNav
                  superAdminItems={superAdminNavigation}
                  tenantDataItems={operationsNavigation}
                />
              </div>
            </div>
          </header>
        </div>
        
        <main className="flex-1 space-y-4 p-8 pt-6">
          {children}
        </main>
      </>
    </GlobalFilterProvider>
  )
} 