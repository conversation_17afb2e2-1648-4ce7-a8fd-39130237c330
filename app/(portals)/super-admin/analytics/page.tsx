"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/app/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from "@/app/components/ui/tabs"
import { DateRangePicker } from "@/app/components/ui/date-range-picker"
import { But<PERSON> } from "@/app/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table"
import { Badge } from "@/app/components/ui/badge"
import { Progress } from "@/app/components/ui/progress"
import { 
  ArrowUpRight, 
  Download, 
  Users, 
  Building2, 
  CreditCard, 
  BarChart3, 
  ArrowRight, 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  Activity,
  CalendarDays,
  Layers,
  SearchX,
  ChevronRight,
  ChevronUp,
  Car,
  Map as MapIcon
} from "lucide-react"
import { subDays } from 'date-fns'
import type { DateRange } from 'react-day-picker'

export default function PlatformAnalyticsPage() {
  const [date, setDate] = useState<DateRange | undefined>({
    from: subDays(new Date(), 29),
    to: new Date(),
  })
  const [selectedTab, setSelectedTab] = useState("overview")
  const [comparisonPeriod, setComparisonPeriod] = useState("previous_period")
  
  const handleDateChange = (newDateRange: DateRange | undefined) => {
    if (newDateRange) {
       setDate(newDateRange)
    } else {
        setDate(undefined)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
    <div>
          <h2 className="text-3xl font-bold tracking-tight">Platform Analytics</h2>
          <p className="text-muted-foreground">Comprehensive metrics and performance insights across the platform</p>
        </div>
        <div className="flex items-center gap-2">
          <DateRangePicker 
            date={date}
            onDateChange={handleDateChange}
          />
          <Select value={comparisonPeriod} onValueChange={setComparisonPeriod}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Comparison period" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="previous_period">Previous Period</SelectItem>
              <SelectItem value="previous_year">Previous Year</SelectItem>
              <SelectItem value="custom">Custom Period</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon" title="Download Report">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid grid-cols-4 md:grid-cols-6 w-full md:w-fit">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="tenants">Tenants</TabsTrigger>
          <TabsTrigger value="usage">Platform Usage</TabsTrigger>
          <TabsTrigger value="operations">Operations</TabsTrigger>
        </TabsList>
        
        {/* Overview Tab Content */}
        <TabsContent value="overview" className="space-y-6">
          {/* KPI Cards */}
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
            {/* MRR */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Monthly Recurring Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$243,000</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+12.5% from last month</span>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Total recurring revenue across all subscriptions
                </p>
              </CardContent>
            </Card>
            
            {/* Active Tenants */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Tenants</CardTitle>
                <Building2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">128</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+6 new this month</span>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Organizations with active subscriptions
                </p>
              </CardContent>
            </Card>
            
            {/* Active Users */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3,287</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+8.3% from last month</span>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  Users who logged in at least once this month
                </p>
              </CardContent>
            </Card>
            
            {/* Platform Uptime */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Platform Uptime</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">99.98%</div>
                <div className="flex items-center pt-1 text-xs text-amber-500">
                  <TrendingDown className="mr-1 h-3.5 w-3.5" />
                  <span>-0.01% from last month</span>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  System availability over the past 30 days
                </p>
              </CardContent>
            </Card>
          </div>
          
          {/* Charts and tables */}
          <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
            {/* Revenue Trend Chart */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Revenue Trend</CardTitle>
                <CardDescription>
                  Monthly revenue over the last 6 months
                </CardDescription>
              </CardHeader>
              <CardContent className="px-2">
                <div className="h-[300px] w-full">
                  {/* Chart component would go here - using placeholder */}
                  <div className="h-full w-full rounded-md bg-muted/50 flex flex-col items-center justify-center text-muted-foreground">
                    <BarChart3 className="h-8 w-8 mb-2" />
                    <div className="text-sm font-medium">Revenue Trend Chart</div>
                    <div className="text-xs">(Chart component implementation required)</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* User Growth Chart */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>User Growth</CardTitle>
                <CardDescription>
                  New user registrations by tenant type
                </CardDescription>
              </CardHeader>
              <CardContent className="px-2">
                <div className="h-[300px] w-full">
                  {/* Chart component would go here - using placeholder */}
                  <div className="h-full w-full rounded-md bg-muted/50 flex flex-col items-center justify-center text-muted-foreground">
                    <Users className="h-8 w-8 mb-2" />
                    <div className="text-sm font-medium">User Growth Chart</div>
                    <div className="text-xs">(Chart component implementation required)</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Top Performing Tenants */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Top Performing Tenants</CardTitle>
                <CardDescription>
                  Ranked by revenue contribution and growth
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Tenant</TableHead>
                      <TableHead>MRR</TableHead>
                      <TableHead>Growth</TableHead>
                      <TableHead>Users</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell className="font-medium">Acme Transportation</TableCell>
                      <TableCell>$24,500</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <ChevronUp className="h-4 w-4 text-green-500 mr-1" />
                          15.3%
                        </div>
                      </TableCell>
                      <TableCell>348</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Global Logistics</TableCell>
                      <TableCell>$18,750</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <ChevronUp className="h-4 w-4 text-green-500 mr-1" />
                          12.8%
                        </div>
                      </TableCell>
                      <TableCell>276</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">City Tours LLC</TableCell>
                      <TableCell>$15,200</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <ChevronUp className="h-4 w-4 text-green-500 mr-1" />
                          9.5%
                        </div>
                      </TableCell>
                      <TableCell>187</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Metro Shuttle Services</TableCell>
                      <TableCell>$12,800</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <ChevronUp className="h-4 w-4 text-green-500 mr-1" />
                          7.2%
                        </div>
                      </TableCell>
                      <TableCell>152</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Swift Transit</TableCell>
                      <TableCell>$10,500</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                          2.1%
                        </div>
                      </TableCell>
                      <TableCell>98</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </CardContent>
              <CardFooter className="border-t px-6 py-3">
                <Button variant="ghost" className="w-full justify-center" size="sm">
                  View All Tenants <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
            
            {/* System Health */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>System Health</CardTitle>
                <CardDescription>
                  Current platform performance metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">API Response Time</div>
                      <div className="text-sm font-medium text-green-600">85ms avg</div>
                    </div>
                    <Progress value={17} className="h-2" />
                    <div className="text-xs text-muted-foreground">
                      Healthy (target: &lt;500ms)
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Error Rate</div>
                      <div className="text-sm font-medium text-green-600">0.12%</div>
                    </div>
                    <Progress value={2} className="h-2" />
                    <div className="text-xs text-muted-foreground">
                      Healthy (target: &lt;1%)
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Database Load</div>
                      <div className="text-sm font-medium text-amber-600">68%</div>
                    </div>
                    <Progress value={68} className="h-2" />
                    <div className="text-xs text-muted-foreground">
                      Moderate (target: &lt;70%)
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Cache Hit Rate</div>
                      <div className="text-sm font-medium text-green-600">92.4%</div>
                    </div>
                    <Progress value={92} className="h-2" />
                    <div className="text-xs text-muted-foreground">
                      Excellent (target: &gt;85%)
                    </div>
                  </div>
                  
                  <div className="pt-2">
                    <div className="text-sm font-medium mb-2">Recent Incidents</div>
                    <div className="text-sm rounded-md border p-3">
                      <div className="flex justify-between items-start mb-1">
                        <div className="font-medium">API Latency Spike</div>
                        <Badge variant="outline">Resolved</Badge>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        June 28, 2024 - 15:42 UTC (Duration: 18 min)
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="border-t px-6 py-3">
                <Button variant="ghost" className="w-full justify-center" size="sm">
                  View System Status <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
        
        {/* Revenue Tab Content */}
        <TabsContent value="revenue" className="space-y-6">
          {/* Revenue KPI Cards */}
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
            {/* MRR */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Monthly Recurring Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$243,000</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+12.5% from last month</span>
                </div>
              </CardContent>
            </Card>
            
            {/* ARR */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Annual Recurring Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$2.92M</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+14.2% from last year</span>
                </div>
              </CardContent>
            </Card>
            
            {/* Average Revenue Per Tenant */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg. Revenue Per Tenant</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$1,898</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+5.3% from last month</span>
                </div>
              </CardContent>
            </Card>
            
            {/* Customer Acquisition Cost */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Customer Acquisition Cost</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$1,250</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingDown className="mr-1 h-3.5 w-3.5" />
                  <span>-8.1% from last quarter</span>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Revenue Analysis */}
          <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
            {/* Revenue by Subscription Type */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Revenue by Subscription Type</CardTitle>
                <CardDescription>
                  Distribution across subscription tiers
                </CardDescription>
              </CardHeader>
              <CardContent className="px-2">
                <div className="h-[300px] w-full">
                  {/* Chart component would go here - using placeholder */}
                  <div className="h-full w-full rounded-md bg-muted/50 flex flex-col items-center justify-center text-muted-foreground">
                    <BarChart3 className="h-8 w-8 mb-2" />
                    <div className="text-sm font-medium">Subscription Revenue Chart</div>
                    <div className="text-xs">(Chart component implementation required)</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Monthly Revenue Trend */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Monthly Revenue Trend</CardTitle>
                <CardDescription>
                  Revenue growth over the past 12 months
                </CardDescription>
              </CardHeader>
              <CardContent className="px-2">
                <div className="h-[300px] w-full">
                  {/* Chart component would go here - using placeholder */}
                  <div className="h-full w-full rounded-md bg-muted/50 flex flex-col items-center justify-center text-muted-foreground">
                    <TrendingUp className="h-8 w-8 mb-2" />
                    <div className="text-sm font-medium">Revenue Trend Chart</div>
                    <div className="text-xs">(Chart component implementation required)</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Subscription Plans */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Subscription Plans</CardTitle>
                <CardDescription>
                  Performance by subscription tier
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Plan</TableHead>
                      <TableHead>Subscribers</TableHead>
                      <TableHead>Monthly Revenue</TableHead>
                      <TableHead>Growth</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell className="font-medium">Enterprise</TableCell>
                      <TableCell>24</TableCell>
                      <TableCell>$96,000</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <ChevronUp className="h-4 w-4 text-green-500 mr-1" />
                          18.5%
                        </div>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Premium</TableCell>
                      <TableCell>56</TableCell>
                      <TableCell>$84,000</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <ChevronUp className="h-4 w-4 text-green-500 mr-1" />
                          12.2%
                        </div>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Standard</TableCell>
                      <TableCell>42</TableCell>
                      <TableCell>$42,000</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <ChevronUp className="h-4 w-4 text-green-500 mr-1" />
                          8.7%
                        </div>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Basic</TableCell>
                      <TableCell>35</TableCell>
                      <TableCell>$21,000</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                          3.5%
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
            
            {/* Revenue Metrics */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Revenue Metrics</CardTitle>
                <CardDescription>
                  Key financial performance indicators
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Customer Lifetime Value</div>
                      <div className="text-sm font-medium">$24,500</div>
                    </div>
                    <Progress value={70} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Previous: $22,800</span>
                      <span>+7.5%</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">LTV to CAC Ratio</div>
                      <div className="text-sm font-medium">3.4x</div>
                    </div>
                    <Progress value={75} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Target: &gt;3.0x</span>
                      <span>+0.2x from last quarter</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Net Revenue Retention</div>
                      <div className="text-sm font-medium">112%</div>
                    </div>
                    <Progress value={85} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Target: &gt;110%</span>
                      <span>+2% from last quarter</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Average Contract Value</div>
                      <div className="text-sm font-medium">$22,750</div>
                    </div>
                    <Progress value={65} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Previous: $20,500</span>
                      <span>+11% year-over-year</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="users" className="space-y-6">
          {/* User KPI Cards */}
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
            {/* Total Users */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4,586</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+12.8% from last quarter</span>
                </div>
              </CardContent>
            </Card>
            
            {/* Active Users */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Users (30d)</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3,287</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+8.3% from last month</span>
                </div>
              </CardContent>
            </Card>
            
            {/* New Users */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">New Users (30d)</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">342</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+24.5% from last month</span>
                </div>
              </CardContent>
            </Card>
            
            {/* User Retention */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">User Retention</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">94.8%</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+1.2% from last month</span>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* User Analysis */}
          <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
            {/* User Growth Trend */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>User Growth Trend</CardTitle>
                <CardDescription>
                  New users per month over the past year
                </CardDescription>
              </CardHeader>
              <CardContent className="px-2">
                <div className="h-[300px] w-full">
                  {/* Chart component would go here - using placeholder */}
                  <div className="h-full w-full rounded-md bg-muted/50 flex flex-col items-center justify-center text-muted-foreground">
                    <TrendingUp className="h-8 w-8 mb-2" />
                    <div className="text-sm font-medium">User Growth Chart</div>
                    <div className="text-xs">(Chart component implementation required)</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Users by Role */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Users by Role</CardTitle>
                <CardDescription>
                  Distribution of users across different roles
                </CardDescription>
              </CardHeader>
              <CardContent className="px-2">
                <div className="h-[300px] w-full">
                  {/* Chart component would go here - using placeholder */}
                  <div className="h-full w-full rounded-md bg-muted/50 flex flex-col items-center justify-center text-muted-foreground">
                    <Layers className="h-8 w-8 mb-2" />
                    <div className="text-sm font-medium">Users by Role Chart</div>
                    <div className="text-xs">(Chart component implementation required)</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Top User Activity */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Top User Activity</CardTitle>
                <CardDescription>
                  Most frequent user actions in the platform
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Action</TableHead>
                      <TableHead>Count</TableHead>
                      <TableHead>% of Total</TableHead>
                      <TableHead>Trend</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell className="font-medium">Create Quote</TableCell>
                      <TableCell>15,428</TableCell>
                      <TableCell>28.4%</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <ChevronUp className="h-4 w-4 text-green-500 mr-1" />
                          12.5%
                        </div>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">View Trip</TableCell>
                      <TableCell>12,845</TableCell>
                      <TableCell>23.6%</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <ChevronUp className="h-4 w-4 text-green-500 mr-1" />
                          8.7%
                        </div>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Search Passengers</TableCell>
                      <TableCell>8,934</TableCell>
                      <TableCell>16.4%</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <ChevronUp className="h-4 w-4 text-green-500 mr-1" />
                          5.2%
                        </div>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Edit Event</TableCell>
                      <TableCell>6,248</TableCell>
                      <TableCell>11.5%</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                          2.3%
                        </div>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Generate Report</TableCell>
                      <TableCell>4,582</TableCell>
                      <TableCell>8.4%</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <ChevronUp className="h-4 w-4 text-green-500 mr-1" />
                          14.8%
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </CardContent>
              <CardFooter className="border-t px-6 py-3">
                <Button variant="ghost" className="w-full justify-center" size="sm">
                  View All Activities <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
            
            {/* User Engagement Metrics */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>User Engagement Metrics</CardTitle>
                <CardDescription>
                  Key measures of platform engagement
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Average Session Duration</div>
                      <div className="text-sm font-medium">18:42 min</div>
                    </div>
                    <Progress value={75} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Previous: 16:57 min</span>
                      <span>+10.4%</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Sessions Per User (Monthly)</div>
                      <div className="text-sm font-medium">24.3</div>
                    </div>
                    <Progress value={65} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Previous: 22.8</span>
                      <span>+6.6%</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Feature Adoption Rate</div>
                      <div className="text-sm font-medium">78.5%</div>
                    </div>
                    <Progress value={78} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Target: 80%</span>
                      <span>+3.2% from last quarter</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Daily Active Users/Monthly Active Users</div>
                      <div className="text-sm font-medium">0.68</div>
                    </div>
                    <Progress value={68} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Industry Avg: 0.55</span>
                      <span>+0.03 from last month</span>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="border-t px-6 py-3">
                <Button variant="ghost" className="w-full justify-center" size="sm">
                  View Detailed Engagement Report <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="tenants" className="space-y-6">
          {/* Tenant KPI Cards */}
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
            {/* Total Tenants */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Tenants</CardTitle>
                <Building2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">128</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+6 new this month</span>
                </div>
              </CardContent>
            </Card>
            
            {/* Active Tenants */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Tenants</CardTitle>
                <Building2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">124</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+4 from last month</span>
                </div>
              </CardContent>
            </Card>
            
            {/* Tenant Retention */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Tenant Retention</CardTitle>
                <Building2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">98.4%</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+0.6% from last quarter</span>
                </div>
              </CardContent>
            </Card>
            
            {/* Avg. Tenant Size */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg. Tenant Size</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">36</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+8.3% from last quarter</span>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Tenant Analysis */}
          <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
            {/* Tenant Growth */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Tenant Growth</CardTitle>
                <CardDescription>
                  New tenant acquisition over time
                </CardDescription>
              </CardHeader>
              <CardContent className="px-2">
                <div className="h-[300px] w-full">
                  {/* Chart component would go here - using placeholder */}
                  <div className="h-full w-full rounded-md bg-muted/50 flex flex-col items-center justify-center text-muted-foreground">
                    <Building2 className="h-8 w-8 mb-2" />
                    <div className="text-sm font-medium">Tenant Growth Chart</div>
                    <div className="text-xs">(Chart component implementation required)</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Tenant Distribution */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Tenant Distribution</CardTitle>
                <CardDescription>
                  By industry and subscription tier
                </CardDescription>
              </CardHeader>
              <CardContent className="px-2">
                <div className="h-[300px] w-full">
                  {/* Chart component would go here - using placeholder */}
                  <div className="h-full w-full rounded-md bg-muted/50 flex flex-col items-center justify-center text-muted-foreground">
                    <Layers className="h-8 w-8 mb-2" />
                    <div className="text-sm font-medium">Tenant Distribution Chart</div>
                    <div className="text-xs">(Chart component implementation required)</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Top Tenants by Activity */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Top Tenants by Activity</CardTitle>
                <CardDescription>
                  Ranked by user activity and engagement
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Tenant</TableHead>
                      <TableHead>Users</TableHead>
                      <TableHead>Sessions (30d)</TableHead>
                      <TableHead>Avg. Engagement</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell className="font-medium">Acme Transportation</TableCell>
                      <TableCell>348</TableCell>
                      <TableCell>12,458</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <ChevronUp className="h-4 w-4 text-green-500 mr-1" />
                          High
                        </div>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Global Logistics</TableCell>
                      <TableCell>276</TableCell>
                      <TableCell>10,872</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <ChevronUp className="h-4 w-4 text-green-500 mr-1" />
                          High
                        </div>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">City Tours LLC</TableCell>
                      <TableCell>187</TableCell>
                      <TableCell>8,345</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <ChevronUp className="h-4 w-4 text-amber-500 mr-1" />
                          Medium
                        </div>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Metro Shuttle Services</TableCell>
                      <TableCell>152</TableCell>
                      <TableCell>5,267</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <ChevronUp className="h-4 w-4 text-amber-500 mr-1" />
                          Medium
                        </div>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Swift Transit</TableCell>
                      <TableCell>98</TableCell>
                      <TableCell>2,845</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                          Low
                        </div>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </CardContent>
              <CardFooter className="border-t px-6 py-3">
                <Button variant="ghost" className="w-full justify-center" size="sm">
                  View All Tenants <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
            
            {/* Tenant Health */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Tenant Health Metrics</CardTitle>
                <CardDescription>
                  Key measures of tenant satisfaction and usage
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Avg. Feature Adoption</div>
                      <div className="text-sm font-medium">72.5%</div>
                    </div>
                    <Progress value={72} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Target: 80%</span>
                      <span>+5.8% from last quarter</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Support Ticket Rate</div>
                      <div className="text-sm font-medium">2.4 per tenant</div>
                    </div>
                    <Progress value={40} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Previous: 3.1</span>
                      <span>-22.6% (improvement)</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Avg. NPS Score</div>
                      <div className="text-sm font-medium">48</div>
                    </div>
                    <Progress value={65} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Industry Avg: 32</span>
                      <span>+4 points from last quarter</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Subscription Renewals</div>
                      <div className="text-sm font-medium">96.8%</div>
                    </div>
                    <Progress value={96} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Target: 95%</span>
                      <span>+1.2% from last year</span>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="border-t px-6 py-3">
                <Button variant="ghost" className="w-full justify-center" size="sm">
                  View Detailed Health Report <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="usage" className="space-y-6">
          {/* Usage KPI Cards */}
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
            {/* Daily Active Users */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Daily Active Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2,248</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+5.8% from yesterday</span>
                </div>
              </CardContent>
            </Card>
            
            {/* API Requests */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">API Requests (24h)</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4.8M</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+12.4% from last week</span>
                </div>
              </CardContent>
            </Card>
            
            {/* Avg Response Time */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">85ms</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingDown className="mr-1 h-3.5 w-3.5" />
                  <span>-12.5ms from last week</span>
                </div>
              </CardContent>
            </Card>
            
            {/* Error Rate */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0.12%</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingDown className="mr-1 h-3.5 w-3.5" />
                  <span>-0.08% from last month</span>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Usage Analysis */}
          <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
            {/* API Traffic Trend */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>API Traffic Trend</CardTitle>
                <CardDescription>
                  Request volume over time
                </CardDescription>
              </CardHeader>
              <CardContent className="px-2">
                <div className="h-[300px] w-full">
                  {/* Chart component would go here - using placeholder */}
                  <div className="h-full w-full rounded-md bg-muted/50 flex flex-col items-center justify-center text-muted-foreground">
                    <Activity className="h-8 w-8 mb-2" />
                    <div className="text-sm font-medium">API Traffic Chart</div>
                    <div className="text-xs">(Chart component implementation required)</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Response Time Distribution */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Response Time Distribution</CardTitle>
                <CardDescription>
                  Performance analysis across endpoints
                </CardDescription>
              </CardHeader>
              <CardContent className="px-2">
                <div className="h-[300px] w-full">
                  {/* Chart component would go here - using placeholder */}
                  <div className="h-full w-full rounded-md bg-muted/50 flex flex-col items-center justify-center text-muted-foreground">
                    <Activity className="h-8 w-8 mb-2" />
                    <div className="text-sm font-medium">Response Time Chart</div>
                    <div className="text-xs">(Chart component implementation required)</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Top API Endpoints */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Top API Endpoints</CardTitle>
                <CardDescription>
                  Most frequently accessed endpoints
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Endpoint</TableHead>
                      <TableHead>Requests (24h)</TableHead>
                      <TableHead>Avg. Response</TableHead>
                      <TableHead>Error Rate</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell className="font-medium">/api/v1/trips</TableCell>
                      <TableCell>842,156</TableCell>
                      <TableCell>78ms</TableCell>
                      <TableCell>0.05%</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">/api/v1/quotes</TableCell>
                      <TableCell>624,782</TableCell>
                      <TableCell>92ms</TableCell>
                      <TableCell>0.11%</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">/api/v1/users</TableCell>
                      <TableCell>418,945</TableCell>
                      <TableCell>64ms</TableCell>
                      <TableCell>0.08%</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">/api/v1/events</TableCell>
                      <TableCell>386,127</TableCell>
                      <TableCell>105ms</TableCell>
                      <TableCell>0.18%</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">/api/v1/passengers</TableCell>
                      <TableCell>298,534</TableCell>
                      <TableCell>72ms</TableCell>
                      <TableCell>0.09%</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </CardContent>
              <CardFooter className="border-t px-6 py-3">
                <Button variant="ghost" className="w-full justify-center" size="sm">
                  View All Endpoints <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
            
            {/* System Performance */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>System Performance</CardTitle>
                <CardDescription>
                  Key platform health metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">API Availability</div>
                      <div className="text-sm font-medium">99.98%</div>
                    </div>
                    <Progress value={99.98} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>SLA Target: 99.95%</span>
                      <span>+0.01% from last month</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Database Load</div>
                      <div className="text-sm font-medium">68%</div>
                    </div>
                    <Progress value={68} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Peak: 82%</span>
                      <span>Threshold: 85%</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Cache Hit Rate</div>
                      <div className="text-sm font-medium">92.4%</div>
                    </div>
                    <Progress value={92} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Previous: 88.7%</span>
                      <span>+3.7% improvement</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Search Performance</div>
                      <div className="text-sm font-medium">112ms avg</div>
                    </div>
                    <Progress value={78} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Previous: 145ms</span>
                      <span>-22.8% (improvement)</span>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="border-t px-6 py-3">
                <Button variant="ghost" className="w-full justify-center" size="sm">
                  View Performance Dashboard <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="operations" className="space-y-6">
          {/* Operations KPI Cards */}
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
            {/* Total Trips */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Trips (30d)</CardTitle>
                <Car className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">28,456</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+18.3% from last month</span>
                </div>
              </CardContent>
            </Card>
            
            {/* Active Events */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Events</CardTitle>
                <CalendarDays className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">342</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+24.2% from last month</span>
                </div>
              </CardContent>
            </Card>
            
            {/* On-Time Performance */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">On-Time Performance</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">94.8%</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+1.2% from last month</span>
                </div>
              </CardContent>
            </Card>
            
            {/* Quote Conversion */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Quote Conversion</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">68.5%</div>
                <div className="flex items-center pt-1 text-xs text-green-600">
                  <TrendingUp className="mr-1 h-3.5 w-3.5" />
                  <span>+3.2% from last month</span>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Operations Analysis */}
          <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
            {/* Trip Volume Trend */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Trip Volume Trend</CardTitle>
                <CardDescription>
                  Trip counts over time by type
                </CardDescription>
              </CardHeader>
              <CardContent className="px-2">
                <div className="h-[300px] w-full">
                  {/* Chart component would go here - using placeholder */}
                  <div className="h-full w-full rounded-md bg-muted/50 flex flex-col items-center justify-center text-muted-foreground">
                    <Car className="h-8 w-8 mb-2" />
                    <div className="text-sm font-medium">Trip Volume Chart</div>
                    <div className="text-xs">(Chart component implementation required)</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Geographic Distribution */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Geographic Distribution</CardTitle>
                <CardDescription>
                  Operations by location
                </CardDescription>
              </CardHeader>
              <CardContent className="px-2">
                <div className="h-[300px] w-full">
                  {/* Chart component would go here - using placeholder */}
                  <div className="h-full w-full rounded-md bg-muted/50 flex flex-col items-center justify-center text-muted-foreground">
                    <MapIcon className="h-8 w-8 mb-2" />
                    <div className="text-sm font-medium">Geographic Distribution Map</div>
                    <div className="text-xs">(Chart component implementation required)</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Service Type Performance */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Service Type Performance</CardTitle>
                <CardDescription>
                  Metrics by service category
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Service Type</TableHead>
                      <TableHead>Trip Volume</TableHead>
                      <TableHead>On-Time %</TableHead>
                      <TableHead>Avg. Rating</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell className="font-medium">Airport Transfer</TableCell>
                      <TableCell>12,845</TableCell>
                      <TableCell>96.2%</TableCell>
                      <TableCell>4.82</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Point-to-Point</TableCell>
                      <TableCell>8,672</TableCell>
                      <TableCell>94.8%</TableCell>
                      <TableCell>4.75</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Hourly As Directed</TableCell>
                      <TableCell>3,984</TableCell>
                      <TableCell>92.5%</TableCell>
                      <TableCell>4.91</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Event Transportation</TableCell>
                      <TableCell>2,584</TableCell>
                      <TableCell>91.8%</TableCell>
                      <TableCell>4.86</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell className="font-medium">Employee Shuttle</TableCell>
                      <TableCell>1,958</TableCell>
                      <TableCell>97.2%</TableCell>
                      <TableCell>4.72</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </CardContent>
              <CardFooter className="border-t px-6 py-3">
                <Button variant="ghost" className="w-full justify-center" size="sm">
                  View Detailed Service Metrics <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
            
            {/* Operational Metrics */}
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Operational Metrics</CardTitle>
                <CardDescription>
                  Key indicators of operational efficiency
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Average Trip Completion Time</div>
                      <div className="text-sm font-medium">42 min</div>
                    </div>
                    <Progress value={70} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Previous: 48 min</span>
                      <span>-12.5% (improvement)</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Driver Assignment Success</div>
                      <div className="text-sm font-medium">98.4%</div>
                    </div>
                    <Progress value={98} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Target: 98%</span>
                      <span>+0.8% from last month</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Passenger No-Show Rate</div>
                      <div className="text-sm font-medium">2.8%</div>
                    </div>
                    <Progress value={65} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Previous: 3.4%</span>
                      <span>-17.6% (improvement)</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-medium">Affiliate Response Time</div>
                      <div className="text-sm font-medium">4.2 min</div>
                    </div>
                    <Progress value={82} className="h-2" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Target: 5 min</span>
                      <span>-0.6 min from last quarter</span>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="border-t px-6 py-3">
                <Button variant="ghost" className="w-full justify-center" size="sm">
                  View Operations Dashboard <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
} 