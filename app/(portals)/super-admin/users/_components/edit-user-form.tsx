'use client';

import { <PERSON><PERSON> } from '@/app/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/app/components/ui/form';
import { Input } from '@/app/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select';
import { toast } from '@/app/components/ui/use-toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

const formSchema = z.object({
  email: z.string().email('Invalid email address'),
  role: z.string().min(1, 'Role is required'),
  tenantIds: z.array(z.string()).optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface EditUserFormProps {
  user: {
    id: string;
    email: string;
    role: string;
    tenantIds: string[];
  };
  tenants: Array<{ id: string; name: string }>;
}

export function EditUserForm({ user, tenants }: EditUserFormProps) {
  const router = useRouter();
  
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: user.email,
      role: user.role,
      tenantIds: user.tenantIds || [],
    },
  });

  const onSubmit = async (values: FormValues) => {
    try {
      const response = await fetch(`/api/super-admin/users/${user.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        throw new Error('Failed to update user');
      }

      toast({
        title: 'Success',
        description: 'User updated successfully',
      });

      router.push('/super-admin/users');
      router.refresh();
    } catch (error) {
      console.error('Error updating user:', error);
      toast({
        title: 'Error',
        description: 'Failed to update user',
        variant: 'destructive',
      });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 max-w-2xl">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input {...field} type="email" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="role"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Role</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="SUPER_ADMIN">Super Admin</SelectItem>
                  <SelectItem value="ADMIN">Admin</SelectItem>
                  <SelectItem value="MANAGER">Manager</SelectItem>
                  <SelectItem value="USER">User</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="tenantIds"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tenants</FormLabel>
              <div className="space-y-2">
                {tenants.map((tenant) => (
                  <div key={tenant.id} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`tenant-${tenant.id}`}
                      value={tenant.id}
                      checked={field.value?.includes(tenant.id)}
                      onChange={(e) => {
                        const currentTenants = field.value || [];
                        if (e.target.checked) {
                          field.onChange([...currentTenants, tenant.id]);
                        } else {
                          field.onChange(
                            currentTenants.filter((id) => id !== tenant.id)
                          );
                        }
                      }}
                      className="h-4 w-4 rounded border-gray-300"
                    />
                    <label
                      htmlFor={`tenant-${tenant.id}`}
                      className="text-sm font-medium leading-none"
                    >
                      {tenant.name}
                    </label>
                  </div>
                ))}
              </div>
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/super-admin/users')}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={form.formState.isSubmitting}>
            {form.formState.isSubmitting ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
