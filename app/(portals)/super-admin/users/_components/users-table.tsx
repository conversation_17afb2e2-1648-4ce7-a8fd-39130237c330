'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { format } from 'date-fns';
import { Pencil, Trash2, Eye } from 'lucide-react';
import Link from 'next/link';
import { toast } from '@/components/ui/use-toast';

export function UsersTable({ users }: { users: any[] }) {
  const handleDelete = async (userId: string) => {
    if (confirm('Are you sure you want to delete this user?')) {
      try {
        const response = await fetch(`/api/super-admin/users/${userId}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error('Failed to delete user');
        }

        toast({
          title: 'Success',
          description: 'User deleted successfully',
        });

        // Refresh the page to show updated user list
        window.location.reload();
      } catch (error) {
        console.error('Error deleting user:', error);
        toast({
          title: 'Error',
          description: 'Failed to delete user',
          variant: 'destructive',
        });
      }
    }
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Email</TableHead>
            <TableHead>Role</TableHead>
            <TableHead>Tenants</TableHead>
            <TableHead>Created</TableHead>
            <TableHead>Last Sign In</TableHead>
            <TableHead className="w-[100px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.map((user) => (
            <TableRow key={user.id}>
              <TableCell className="font-medium">{user.email}</TableCell>
              <TableCell>
                {user.role || (user.roles && user.roles[0]) || 'No role'}
              </TableCell>
              <TableCell>
                {user.tenant_users?.map((tu: any) => tu.tenant.name).join(', ') || 'No tenants'}
              </TableCell>
              <TableCell>
                {format(new Date(user.created_at), 'MMM d, yyyy')}
              </TableCell>
              <TableCell>
                {user.last_sign_in_at 
                  ? format(new Date(user.last_sign_in_at), 'MMM d, yyyy HH:mm') 
                  : 'Never'}
              </TableCell>
              <TableCell>
                <div className="flex space-x-1">
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    asChild
                    className="h-8 w-8 p-0 group relative"
                    title="View Details"
                  >
                    <Link href={`/super-admin/users/${user.id}`}>
                      <Eye className="h-4 w-4" />
                      <span className="sr-only">View Details</span>
                      <span className="invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-opacity absolute -top-8 left-1/2 -translate-x-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 whitespace-nowrap">
                        View Details
                      </span>
                    </Link>
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    asChild
                    className="h-8 w-8 p-0 group relative"
                    title="Edit User"
                  >
                    <Link href={`/super-admin/users/${user.id}?mode=edit`}>
                      <Pencil className="h-4 w-4" />
                      <span className="sr-only">Edit</span>
                      <span className="invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-opacity absolute -top-8 left-1/2 -translate-x-1/2 bg-gray-800 text-white text-xs rounded py-1 px-2 whitespace-nowrap">
                        Edit User
                      </span>
                    </Link>
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="icon"
                    className="h-8 w-8 p-0 text-destructive hover:text-destructive/80"
                    onClick={() => handleDelete(user.id)}
                    title="Delete User"
                  >
                    <Trash2 className="h-4 w-4" />
                    <span className="sr-only">Delete</span>
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
