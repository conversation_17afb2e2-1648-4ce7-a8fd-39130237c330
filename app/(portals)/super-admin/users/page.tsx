// ---
// SYSTEM ENTITY SUMMARY
//
// Tenants: Isolated workspaces for each client/affiliate/network. All business data is scoped by tenant_id.
// Users: Authenticated individuals. Linked to tenants via tenant_users (can belong to multiple tenants).
// Orgs/Companies: Business entities (e.g., affiliate companies, client companies). May be linked to tenants or used for grouping.
//
// Typical Flow:
// - Super Admin creates a tenant (<PERSON>lient, Affiliate, TNC, White Label, etc.).
// - Super Admin or Tenant Admin invites/creates users and assigns them to the tenant.
// - Users may be further associated with organizations/companies for business logic, reporting, or permissions.
// ---

"use client";

import React, { useState, useMemo, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table";
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Badge } from "@/app/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { Plus, Search, User as UserIconLucide, Mail, Shield, MoreVertical, Loader2, Users as UsersIcon, UserCheck, UserPlus, ShieldCheck, MailWarning } from "lucide-react";
import { DataTable } from "@/app/components/ui/data-table";
import { columns, User } from './components/columns';

// Real user interface that matches our database
interface DatabaseUser {
  id: string;
  email: string;
  full_name: string | null;
  role: string | null;
  roles: string[];
  company_name: string | null;
  phone_number: string | null;
  created_at: string;
  updated_at: string;
  organizations: Array<{
    id: string;
    name: string;
    slug: string;
    role: string;
  }>;
}

// Helper function to transform database user to UI user format
const transformDatabaseUser = (dbUser: DatabaseUser): User => {
  const primaryOrg = dbUser.organizations?.[0];

  return {
    id: dbUser.id, // Keep as UUID string
    name: dbUser.full_name || dbUser.email.split('@')[0],
    email: dbUser.email,
    role: dbUser.role || 'USER',
    status: 'Active', // For now, assume all users are active
    lastLogin: dbUser.updated_at,
    org: primaryOrg?.name || dbUser.company_name || 'No Organization'
  };
};

export default function UsersPage() {
  const [search, setSearch] = useState("");
  const [roleFilter, setRoleFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [totalUsers, setTotalUsers] = useState(0);

  // Filter logic (client-side for now)
  const filteredUsers = useMemo(() => {
    let result = users;

    if (search) {
      result = result.filter((user) =>
        user.name.toLowerCase().includes(search.toLowerCase()) ||
        user.email.toLowerCase().includes(search.toLowerCase())
      );
    }
    if (roleFilter !== "all") {
      result = result.filter((user) => user.role === roleFilter);
    }
    if (statusFilter !== "all") {
      result = result.filter((user) => user.status === statusFilter);
    }
    return result;
  }, [users, search, roleFilter, statusFilter]);

  // Fetch users from API
  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (search) params.append('search', search);
      if (roleFilter !== 'all') params.append('role', roleFilter);

      const response = await fetch(`/api/super-admin/users?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }

      const data = await response.json();

      if (data.success) {
        const transformedUsers = data.users.map(transformDatabaseUser);
        setUsers(transformedUsers);
        setTotalUsers(data.total);
      } else {
        throw new Error(data.error || 'Failed to fetch users');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load users');
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [search, roleFilter]); // Re-fetch when search or role filter changes

  // --- Mock Stats Data ---
  // In a real app, fetch this aggregate data separately or calculate from fetched users if feasible
  const userStats = useMemo(() => ({
      totalUsers: users.length,
      activeUsers: users.filter(u => u.status === 'Active').length,
      pendingInvites: users.filter(u => u.status === 'Pending').length,
      superAdmins: users.filter(u => u.role === 'Super Admin').length
  }), [users]);

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">User Management</h2>
        <div className="flex items-center space-x-2">
           <Button><MailWarning className="mr-2 h-4 w-4" /> Invite New User</Button>
        </div>
      </div>

      {/* --- Stats Section --- */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <UsersIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userStats.totalUsers}</div>
            {/* <p className="text-xs text-muted-foreground">Across all organizations</p> */}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userStats.activeUsers}</div>
            {/* <p className="text-xs text-muted-foreground">Logged in recently</p> */}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Invites</CardTitle>
            <UserPlus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userStats.pendingInvites}</div>
            {/* <p className="text-xs text-muted-foreground">Waiting for activation</p> */}
          </CardContent>
        </Card>
         <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Super Admins</CardTitle>
            <ShieldCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userStats.superAdmins}</div>
            {/* <p className="text-xs text-muted-foreground">Platform administrators</p> */}
          </CardContent>
        </Card>
      </div>
      {/* --- End Stats Section --- */}

      <Card>
        <CardHeader>
          <CardTitle>All Users</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row md:items-center gap-2 mb-4">
            <div className="relative w-full max-w-xs">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search users..."
                className="pl-8"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="Super Admin">Super Admin</SelectItem>
                <SelectItem value="Admin">Admin</SelectItem>
                <SelectItem value="User">User</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="Active">Active</SelectItem>
                <SelectItem value="Suspended">Suspended</SelectItem>
                <SelectItem value="Deactivated">Deactivated</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {loading ? (
            <div className="flex justify-center items-center py-10">
              <Loader2 className="animate-spin h-6 w-6 text-muted-foreground" />
            </div>
          ) : error ? (
            <div className="text-center text-red-500 py-10">{error}</div>
          ) : (
            <div className="overflow-x-auto">
              {/* DataTable renders only when loading is false and error is null */}
              <DataTable columns={columns} data={filteredUsers} />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
