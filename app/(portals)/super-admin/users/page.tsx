"use client";

import React, { useState, use<PERSON>emo, useEffect } from "react";
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON>eader, Card<PERSON>itle } from "@/app/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Badge } from "@/app/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { Plus, Search, User as UserIconLucide, Mail, Shield, MoreVertical, Loader2, Users as <PERSON>Icon, <PERSON>r<PERSON>heck, UserPlus, ShieldCheck, MailWarning } from "lucide-react";
import { DataTable } from "@/app/components/ui/data-table";
import { columns, User } from './components/columns';

const mockUsers: User[] = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Super Admin",
    status: "Active",
    lastLogin: "2024-06-28T09:43:00Z",
    org: "Acme Transportation",
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Admin",
    status: "Active",
    lastLogin: "2024-06-28T08:12:00Z",
    org: "Global Logistics",
  },
  {
    id: 3,
    name: "James Lee",
    email: "<EMAIL>",
    role: "User",
    status: "Suspended",
    lastLogin: "2024-06-27T17:22:00Z",
    org: "City Tours LLC",
  },
  {
    id: 4,
    name: "Maria Garcia",
    email: "<EMAIL>",
    role: "User",
    status: "Active",
    lastLogin: "2024-06-28T07:55:00Z",
    org: "Metro Shuttle Services",
  },
  {
    id: 5,
    name: "John Doe",
    email: "<EMAIL>",
    role: "Admin",
    status: "Deactivated",
    lastLogin: "2024-06-20T12:10:00Z",
    org: "Swift Transit",
  },
];

export default function UsersPage() {
  const [search, setSearch] = useState("");
  const [roleFilter, setRoleFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [users, setUsers] = useState<User[]>([]);

  // Filter logic (client-side for now)
  const filteredUsers = useMemo(() => {
    let result = users;

    if (search) {
      result = result.filter((user) =>
        user.name.toLowerCase().includes(search.toLowerCase()) ||
        user.email.toLowerCase().includes(search.toLowerCase())
      );
    }
    if (roleFilter !== "all") {
      result = result.filter((user) => user.role === roleFilter);
    }
    if (statusFilter !== "all") {
      result = result.filter((user) => user.status === statusFilter);
    }
    return result;
  }, [users, search, roleFilter, statusFilter]);

  useEffect(() => {
    // Simulate fetching data
    setLoading(true);
    setError(null);
    setTimeout(() => {
      try {
        // Simulate filtering based on some context (not implemented here)
        setUsers(mockUsers);
      } catch (err) {
        setError("Failed to load users.");
        console.error(err);
      } finally {
        setLoading(false);
      }
    }, 1000);
  }, []);

  // --- Mock Stats Data ---
  // In a real app, fetch this aggregate data separately or calculate from fetched users if feasible
  const userStats = useMemo(() => ({
      totalUsers: users.length,
      activeUsers: users.filter(u => u.status === 'Active').length,
      pendingInvites: users.filter(u => u.status === 'Pending').length,
      superAdmins: users.filter(u => u.role === 'Super Admin').length
  }), [users]);

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">User Management</h2>
        <div className="flex items-center space-x-2">
           <Button><MailWarning className="mr-2 h-4 w-4" /> Invite New User</Button> 
        </div>
      </div>

      {/* --- Stats Section --- */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <UsersIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userStats.totalUsers}</div>
            {/* <p className="text-xs text-muted-foreground">Across all organizations</p> */}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userStats.activeUsers}</div>
            {/* <p className="text-xs text-muted-foreground">Logged in recently</p> */}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Invites</CardTitle>
            <UserPlus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userStats.pendingInvites}</div>
            {/* <p className="text-xs text-muted-foreground">Waiting for activation</p> */}
          </CardContent>
        </Card>
         <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Super Admins</CardTitle>
            <ShieldCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userStats.superAdmins}</div>
            {/* <p className="text-xs text-muted-foreground">Platform administrators</p> */}
          </CardContent>
        </Card>
      </div>
      {/* --- End Stats Section --- */}

      <Card>
        <CardHeader>
          <CardTitle>All Users</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row md:items-center gap-2 mb-4">
            <div className="relative w-full max-w-xs">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search users..."
                className="pl-8"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="Super Admin">Super Admin</SelectItem>
                <SelectItem value="Admin">Admin</SelectItem>
                <SelectItem value="User">User</SelectItem>
              </SelectContent>
            </Select>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="Active">Active</SelectItem>
                <SelectItem value="Suspended">Suspended</SelectItem>
                <SelectItem value="Deactivated">Deactivated</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {loading ? (
            <div className="flex justify-center items-center py-10">
              <Loader2 className="animate-spin h-6 w-6 text-muted-foreground" />
            </div>
          ) : error ? (
            <div className="text-center text-red-500 py-10">{error}</div>
          ) : (
            <div className="overflow-x-auto">
              {/* DataTable renders only when loading is false and error is null */}
              <DataTable columns={columns} data={filteredUsers} />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 