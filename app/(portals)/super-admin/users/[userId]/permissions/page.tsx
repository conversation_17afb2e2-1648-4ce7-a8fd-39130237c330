"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRout<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { But<PERSON> } from "@/app/components/ui/button"
import { Badge } from "@/app/components/ui/badge"
import { Separator } from "@/app/components/ui/separator"
import { Alert, AlertDescription } from "@/app/components/ui/alert"
import { 
  ArrowLeft, 
  Save, 
  RotateCcw, 
  AlertTriangle, 
  CheckCircle2, 
  User,
  Building,
  Network,
  Shield,
  PlusCircle,
  XCircle,
  Pencil,
  Trash2,
  ListFilter
} from "lucide-react"
import Link from "next/link"
import { PermissionTemplateSelector } from "@/app/components/super-admin/permissions/PermissionTemplateSelector"
import { GranularPermissionsEditor } from "@/app/components/super-admin/permissions/GranularPermissionsEditor"
import { useToast } from "@/app/components/ui/use-toast"
import {
  <PERSON>,
  SelectContent,
  SelectItem,
  Select<PERSON>rigger,
  SelectV<PERSON>ue,
} from "@/app/components/ui/select"
import { Input } from "@/app/components/ui/input"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from "@/app/components/ui/dialog"
import { Label } from "@/app/components/ui/label"
import { Tenant } from "@/lib/types/tenant"

interface UserProfile {
  id: string
  email: string
  full_name: string
  roles: string[]
  created_at: string
}

interface Organization {
  id: string
  name: string
  slug: string
}

interface UserOrganization {
  role: string
  organizations: Organization
}

interface PermissionOverride {
  id: string
  tenant_id: string | null
  organization_id: string | null
  feature_permissions: Record<string, boolean>
  ui_customizations: Record<string, 'hidden' | 'visible' | 'restricted'>
  access_controls: Record<string, any>
  template_id: string | null
  notes: string | null
  permission_templates?: {
    id: string
    name: string
    description: string
  }
  tenants?: {
    id: string
    name: string
    tenant_type: string
  }
  organizations?: {
    id: string
    name: string
    slug: string
  }
}

interface PermissionTemplate {
  id: string
  name: string
  description: string
  template_type: string
  permissions: Record<string, any>
  ui_customizations: Record<string, any>
}

export default function UserPermissionsPage() {
  // NOTE: Only base64-encoded session cookies are supported. JWT hacks are no longer needed.

  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const userId = params.userId as string

  const [user, setUser] = useState<UserProfile | null>(null)
  const [userOrganizations, setUserOrganizations] = useState<UserOrganization[]>([])
  const [permissions, setPermissions] = useState<PermissionOverride[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [tenants, setTenants] = useState<Tenant[]>([]);

  // Form state for new/active permission override
  const [activePermissionOverride, setActivePermissionOverride] = useState<PermissionOverride | null>(null)
  const [isNewPermission, setIsNewPermission] = useState(false)
  const [showNewPermissionDialog, setShowNewPermissionDialog] = useState(false)

  // Initial state for new permission form
  const NEW_PERMISSION_INITIAL_STATE: PermissionOverride = {
    id: `new-${Date.now()}`,
    tenant_id: null,
    organization_id: null,
    feature_permissions: {},
    ui_customizations: {},
    access_controls: {},
    template_id: null,
    notes: ""
  }

  useEffect(() => {
    // Debug: log cookie and localStorage before fetch
    if (typeof window !== 'undefined') {
      const cookieValue = document.cookie.split('; ').find(row => row.startsWith('sb-127-auth-token='));
      const localStorageValue = localStorage.getItem('sb-127-auth-token');
      console.log('[PERMISSIONS PAGE DEBUG] BEFORE FETCH: sb-127-auth-token cookie:', cookieValue);
      console.log('[PERMISSIONS PAGE DEBUG] BEFORE FETCH: sb-127-auth-token localStorage:', localStorageValue);
    }
  }, [userId]);

  useEffect(() => {
    const fetchUserPermissions = async () => {
      try {
        setLoading(true)
        console.log('Fetching permissions for user:', userId)
        // Use credentials: 'include' to send cookies
        const response = await fetch(`/api/super-admin/users/${userId}/permissions`, {
          credentials: 'include',
        });
        
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || 'Failed to fetch user permissions');
        }
        
        const data = await response.json();
        
        console.log('API Response:', { status: response.status, data });
        
        if (!response.ok) {
          const errorMessage = data?.error || 'Failed to fetch user permissions';
          const errorDetails = data?.details ? ` (${data.details})` : '';
          throw new Error(`${errorMessage}${errorDetails}`);
        }
        
        setUser(data.user)
        setUserOrganizations(data.organizations || [])
        setPermissions(data.permissions || [])
        setTenants(data.tenants || []);

        // Set the first permission as active by default, or initialize a new one
        if (data.permissions && data.permissions.length > 0) {
          setActivePermissionOverride(data.permissions[0]);
          setIsNewPermission(false);
        } else {
          setActivePermissionOverride(NEW_PERMISSION_INITIAL_STATE);
          setIsNewPermission(true);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load user permissions';
        console.error('Error fetching user permissions:', errorMessage, err);
        setError(errorMessage);
        
        // Show error toast
        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive',
        });
      } finally {
        setLoading(false)
        // Debug: log cookie and localStorage after fetch
        if (typeof window !== 'undefined') {
          const cookieValue = document.cookie.split('; ').find(row => row.startsWith('sb-127-auth-token='));
          const localStorageValue = localStorage.getItem('sb-127-auth-token');
          console.log('[PERMISSIONS PAGE DEBUG] AFTER FETCH: sb-127-auth-token cookie:', cookieValue);
          console.log('[PERMISSIONS PAGE DEBUG] AFTER FETCH: sb-127-auth-token localStorage:', localStorageValue);
        }
      }
    }

    if (userId) {
      fetchUserPermissions()
    }
  }, [userId])

  const handleTemplateSelect = (template: PermissionTemplate | null) => {
    if (activePermissionOverride) {
      setActivePermissionOverride(prev => ({
        ...(prev || NEW_PERMISSION_INITIAL_STATE),
        template_id: template?.id || null,
        feature_permissions: template?.permissions || {},
        ui_customizations: template?.ui_customizations || {},
        access_controls: {}, // Templates don't directly manage access_controls usually
      }));
    }
  }

  const handlePermissionConfigChange = (newConfig: { feature_permissions: Record<string, boolean>; ui_customizations: Record<string, 'hidden' | 'visible' | 'restricted'>; access_controls: Record<string, any>; }) => {
    if (activePermissionOverride) {
      setActivePermissionOverride(prev => ({
        ...(prev || NEW_PERMISSION_INITIAL_STATE),
        feature_permissions: newConfig.feature_permissions,
        ui_customizations: newConfig.ui_customizations,
        access_controls: newConfig.access_controls,
      }));
    }
  }

  const handleNotesChange = (newNotes: string) => {
    if (activePermissionOverride) {
      setActivePermissionOverride(prev => ({
        ...(prev || NEW_PERMISSION_INITIAL_STATE),
        notes: newNotes,
      }));
    }
  }

  const handleOrganizationChange = (value: string) => {
    if (activePermissionOverride) {
      setActivePermissionOverride(prev => ({
        ...(prev || NEW_PERMISSION_INITIAL_STATE),
        organization_id: value === 'all' ? null : value,
      }));
    }
  }

  const handleTenantChange = (value: string) => {
    if (activePermissionOverride) {
      setActivePermissionOverride(prev => ({
        ...(prev || NEW_PERMISSION_INITIAL_STATE),
        tenant_id: value === 'all' ? null : value,
        organization_id: null, // Reset organization when tenant changes
      }));
    }
  }

  const handleSavePermissions = async () => {
    if (!activePermissionOverride) return;

    try {
      setSaving(true)
      
      const payload = {
        tenant_id: activePermissionOverride.tenant_id,
        organization_id: activePermissionOverride.organization_id,
        feature_permissions: activePermissionOverride.feature_permissions,
        ui_customizations: activePermissionOverride.ui_customizations,
        access_controls: activePermissionOverride.access_controls,
        template_id: activePermissionOverride.template_id,
        notes: activePermissionOverride.notes,
      }

      const method = isNewPermission ? 'POST' : 'PUT';
      const url = isNewPermission ?
        `/api/super-admin/users/${userId}/permissions?granted_by=${user?.id}` :
        `/api/super-admin/users/${userId}/permissions/${activePermissionOverride.id}?granted_by=${user?.id}`;

      const response = await fetch(url,
        {
          method: method,
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload)
        }
      )

      if (!response.ok) {
        throw new Error('Failed to save permissions')
      }

      toast({
        title: "Permissions Saved",
        description: "User permissions have been successfully updated.",
      })

      // Refresh the data
      window.location.reload()
    } catch (err) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to save permissions",
        variant: "destructive"
      })
    } finally {
      setSaving(false)
    }
  }

  const handleResetPermissions = () => {
    if (activePermissionOverride && !isNewPermission) {
      // Reset to the original state of the active override if it's not new
      const originalPermission = permissions.find(p => p.id === activePermissionOverride.id);
      if (originalPermission) {
        setActivePermissionOverride(originalPermission);
        return;
      }
    }
    // Otherwise, reset to initial empty state
    setActivePermissionOverride(NEW_PERMISSION_INITIAL_STATE);
    setIsNewPermission(true);
  }

  const handleNewPermission = () => {
    setActivePermissionOverride(NEW_PERMISSION_INITIAL_STATE);
    setIsNewPermission(true);
    setShowNewPermissionDialog(true);
  }

  const handleSelectExistingPermission = (permissionId: string) => {
    const selected = permissions.find(p => p.id === permissionId);
    if (selected) {
      setActivePermissionOverride(selected);
      setIsNewPermission(false);
      setShowNewPermissionDialog(false);
    }
  }

  const handleAddNewPermissionSave = async () => {
    // This is handled by handleSavePermissions, just close dialog
    setShowNewPermissionDialog(false);
  }

  const renderPermissionIdentifier = (permission: PermissionOverride) => {
    let identifier = '';
    if (permission.tenant_id && permission.tenants?.name) {
      identifier += `Tenant: ${permission.tenants.name}`;
    }
    if (permission.organization_id && permission.organizations?.name) {
      identifier += `${identifier ? ' / ' : ''}Org: ${permission.organizations.name}`;
    }
    if (!identifier) {
      identifier = 'Global';
    }
    return identifier;
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Permission for {userId}</CardTitle>
          <CardDescription>Loading user profile and permissions...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Error Loading Permissions</CardTitle>
          <CardDescription>Error loading user permissions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-4 w-4" />
            <span className="text-sm">{error}</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Manage User Permissions</CardTitle>
        <CardDescription>
          Configure granular permissions for {user?.full_name || user?.email}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {user && (
          <div className="flex items-center gap-4">
            <User className="h-10 w-10 text-primary" />
            <div>
              <h3 className="text-xl font-semibold">{user.full_name || user.email}</h3>
              <p className="text-sm text-muted-foreground">{user.email}</p>
              <div className="flex gap-2 mt-1">
                {user.roles.map(role => (
                  <Badge key={role} variant="secondary">{role.replace('_', ' ')}</Badge>
                ))}
              </div>
            </div>
          </div>
        )}
        <Separator />
        
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Permission Override Selector */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <ListFilter className="h-5 w-5" />
            Permission Overrides
          </h3>
          <div className="flex items-center gap-2">
            <Select onValueChange={handleSelectExistingPermission} value={activePermissionOverride?.id || ''}>
              <SelectTrigger className="w-[300px]">
                <SelectValue placeholder="Select an existing override" />
              </SelectTrigger>
              <SelectContent>
                {permissions.length === 0 && <SelectItem value="no-overrides" disabled>No existing overrides</SelectItem>}
                {permissions.map(p => (
                  <SelectItem key={p.id} value={p.id}>
                    {renderPermissionIdentifier(p)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Dialog open={showNewPermissionDialog} onOpenChange={setShowNewPermissionDialog}>
              <DialogTrigger asChild>
                <Button variant="outline" onClick={handleNewPermission}>
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Add New Override
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Create New Permission Override</DialogTitle>
                  <DialogDescription>
                    Select the scope for this new permission override.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="tenant-select" className="text-right">Tenant</Label>
                    <Select onValueChange={handleTenantChange} value={activePermissionOverride?.tenant_id || 'all'}>
                      <SelectTrigger className="col-span-3">
                        <SelectValue placeholder="Select a tenant" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Global (No Tenant)</SelectItem>
                        {tenants.map(tenant => (
                          <SelectItem key={tenant.id} value={tenant.id}>
                            {tenant.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  {activePermissionOverride?.tenant_id && (
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="organization-select" className="text-right">Organization</Label>
                      <Select onValueChange={handleOrganizationChange} value={activePermissionOverride?.organization_id || 'all'} disabled={!activePermissionOverride?.tenant_id}>
                        <SelectTrigger className="col-span-3">
                          <SelectValue placeholder="Select an organization" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Organizations in Tenant</SelectItem>
                          {userOrganizations.filter(uo => uo.organizations?.name).map(uo => (
                            <SelectItem key={uo.organizations.id} value={uo.organizations.id}>
                              {uo.organizations.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>
                <DialogFooter>
                  <Button type="submit" onClick={handleAddNewPermissionSave} disabled={saving}>
                    {saving ? "Creating..." : "Create Override"}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {activePermissionOverride && (
          <>
            <Separator />
            <div className="flex items-center gap-4">
              {isNewPermission ? (
                <Badge variant="outline" className="bg-yellow-100 text-yellow-800">New Override</Badge>
              ) : (
                <Badge variant="outline" className="bg-blue-100 text-blue-800">Editing Existing Override</Badge>
              )}
              <p className="text-sm text-muted-foreground">Scope: {renderPermissionIdentifier(activePermissionOverride)}</p>
            </div>
            <Separator />

            <PermissionTemplateSelector
              selectedTemplateId={activePermissionOverride.template_id || undefined}
              onTemplateSelect={handleTemplateSelect}
              onNotesChange={handleNotesChange}
              notes={activePermissionOverride.notes || ""}
            />

            <GranularPermissionsEditor
              permissions={{
                feature_permissions: activePermissionOverride.feature_permissions || {},
                ui_customizations: activePermissionOverride.ui_customizations || {},
                access_controls: activePermissionOverride.access_controls || {},
              }}
              onChange={handlePermissionConfigChange}
            />
          </>
        )}

        <div className="flex justify-end gap-2 mt-6">
          <Button variant="outline" onClick={handleResetPermissions} disabled={saving}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button onClick={handleSavePermissions} disabled={saving}>
            <Save className="h-4 w-4 mr-2" />
            {saving ? "Saving..." : "Save Permissions"}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
