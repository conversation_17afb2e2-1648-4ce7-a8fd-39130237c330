'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { format } from 'date-fns';
import { Pencil, Loader2, AlertCircle } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
// Using shadcn/ui Alert component
const Alert = ({ variant, className, children, ...props }: { variant?: 'default' | 'destructive', className?: string, children: React.ReactNode }) => (
  <div 
    className={`p-4 rounded-md ${
      variant === 'destructive' 
        ? 'bg-destructive/15 text-destructive' 
        : 'bg-primary/10 text-primary'
    } ${className}`}
    {...props}
  >
    {children}
  </div>
);

const AlertTitle = ({ className, children, ...props }: { className?: string, children: React.ReactNode }) => (
  <h4 className={`font-medium leading-none tracking-tight mb-1 ${className}`} {...props}>
    {children}
  </h4>
);

const AlertDescription = ({ className, children, ...props }: { className?: string, children: React.ReactNode }) => (
  <div className={`text-sm [&_p]:leading-relaxed ${className}`} {...props}>
    {children}
  </div>
);

// Types
type Organization = {
  id: string;
  name: string;
  slug: string;
};

type UserRole = {
  role: string;
};

type UserOrganization = {
  organization: Organization;
};

type UserData = {
  id: string;
  email: string;
  created_at: string;
  role: string | null;
  roles: string[] | null;
  user_organizations: UserOrganization[];
};

interface UserDetailsProps {
  user: UserData;
}

export function UserDetails({ user: initialUser }: UserDetailsProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState<UserData>(initialUser);

  // Refresh user data
  const refreshUserData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await fetch(`/api/super-admin/users/${user.id}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch user data');
      }
      
      const data = await response.json();
      setUser(data);
    } catch (err) {
      console.error('Error refreshing user data:', err);
      setError('Failed to refresh user data');
      toast({
        title: 'Error',
        description: 'Failed to refresh user data',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle edit button click
  const handleEdit = () => {
    router.push(`?mode=edit`, { scroll: false });
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-10 w-64" />
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-4 w-48" />
            </CardHeader>
            <CardContent className="space-y-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-6 w-full" />
                </div>
              ))}
            </CardContent>
          </Card>
          <div className="space-y-6">
            {[...Array(2)].map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-6 w-32" />
                  <Skeleton className="h-4 w-48" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-20 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          {error}.{' '}
          <Button variant="link" onClick={refreshUserData} className="p-0 h-auto">
            Try again
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight">User Details</h1>
          <p className="text-muted-foreground">View and manage user information</p>
        </div>
        <Button onClick={handleEdit} className="w-full sm:w-auto">
          <Pencil className="mr-2 h-4 w-4" />
          Edit User
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>User Information</CardTitle>
          <CardDescription>Basic details about this user</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Email</p>
              <p className="font-medium break-all">{user.email}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">User ID</p>
              <p className="font-mono text-sm break-all">{user.id}</p>
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Account Created</p>
              <p>
                {user.created_at 
                  ? format(new Date(user.created_at), 'PPpp') 
                  : 'N/A'}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Last Sign In</p>
              <p>
                {user.last_sign_in_at 
                  ? format(new Date(user.last_sign_in_at), 'PPpp') 
                  : 'Never'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Roles</CardTitle>
            <CardDescription>User's assigned roles</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {user.roles?.length > 0 ? (
                user.roles.map((role, index) => (
                  <Badge
                    key={index}
                    variant={role === 'SUPER_ADMIN' ? 'default' : 'outline'}
                    className="text-sm"
                  >
                    {role}
                  </Badge>
                ))
              ) : user.role ? (
                <Badge
                  variant={user.role === 'SUPER_ADMIN' ? 'default' : 'outline'}
                  className="text-sm"
                >
                  {user.role}
                </Badge>
              ) : (
                <p className="text-sm text-muted-foreground">No roles assigned</p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Organization Access</CardTitle>
            <CardDescription>Organizations this user has access to</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {user.user_organizations?.length > 0 ? (
                user.user_organizations.map(({ organization }) => (
                  <div
                    key={organization.id}
                    className="flex items-center justify-between p-3 rounded-md border hover:bg-muted/50 transition-colors"
                  >
                    <div>
                      <p className="font-medium">{organization.name}</p>
                      <p className="text-sm text-muted-foreground">{organization.slug}</p>
                    </div>
                    <Badge variant="secondary" className="font-mono text-xs">
                      {organization.id.substring(0, 8)}...
                    </Badge>
                  </div>
                ))
              ) : (
                <p className="text-sm text-muted-foreground">No organization access</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
