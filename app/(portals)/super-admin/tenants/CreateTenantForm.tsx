"use client"

import { useState } from "react"
import { useToast } from "@/app/components/ui/use-toast"
import { <PERSON>alog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/app/components/ui/dialog"
import { Input } from "@/app/components/ui/input"
import { Button } from "@/app/components/ui/button"
import { Label } from "@/app/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/app/components/ui/tabs"
import { Separator } from "@/app/components/ui/separator"
import { Building2, Globe, CreditCard, User, ArrowRight } from "lucide-react"
import { Badge } from "@/app/components/ui/badge"

interface CreateTenantFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: (tenantData: any) => void
}

export function CreateTenantForm({ open, onOpenChange, onSuccess }: CreateTenantFormProps) {
  const { toast } = useToast()
  const [step, setStep] = useState<'details' | 'plan' | 'admin' | 'confirmation'>('details')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    domain: '',
    industry: 'logistics',
    plan: 'essential',
    adminEmail: '',
    adminFirstName: '',
    adminLastName: '',
    adminPassword: '',
  })

  const updateFormData = (key: string, value: string) => {
    setFormData(prev => ({ ...prev, [key]: value }))
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    
    try {
      // In a real app, this would be an API call to create a tenant
      // const response = await fetch('/api/tenants', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(formData)
      // })
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      toast({
        title: "Tenant Created Successfully",
        description: `${formData.name} has been provisioned with a ${formData.plan} plan.`,
      })
      
      if (onSuccess) {
        onSuccess({
          id: `t_${Math.floor(Math.random() * 10000)}`,
          name: formData.name,
          domain: `${formData.domain.toLowerCase()}.transflow.app`,
          status: 'active',
          plan: formData.plan,
          created_at: new Date().toISOString(),
          users_count: 1,
          industry: formData.industry,
          last_active: new Date().toISOString(),
        })
      }
      
      setFormData({
        name: '',
        domain: '',
        industry: 'logistics',
        plan: 'essential',
        adminEmail: '',
        adminFirstName: '',
        adminLastName: '',
        adminPassword: '',
      })
      
      setStep('details')
      onOpenChange(false)
    } catch (error) {
      console.error('Error creating tenant:', error)
      toast({
        title: "Error Creating Tenant",
        description: "There was a problem creating the tenant. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }
  
  const validateStep = () => {
    if (step === 'details') {
      return formData.name.trim() && formData.domain.trim()
    } else if (step === 'plan') {
      return formData.plan
    } else if (step === 'admin') {
      return formData.adminEmail.trim() && formData.adminFirstName.trim() && formData.adminLastName.trim() && formData.adminPassword.trim()
    }
    return true
  }
  
  const nextStep = () => {
    if (step === 'details') setStep('plan')
    else if (step === 'plan') setStep('admin')
    else if (step === 'admin') setStep('confirmation')
  }
  
  const prevStep = () => {
    if (step === 'plan') setStep('details')
    else if (step === 'admin') setStep('plan')
    else if (step === 'confirmation') setStep('admin')
  }
  
  const PlansTab = () => (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div 
        className={`border rounded-lg p-4 hover:border-primary cursor-pointer transition-all ${formData.plan === 'essential' ? 'border-primary ring-1 ring-primary bg-primary/5' : ''}`}
        onClick={() => updateFormData('plan', 'essential')}
      >
        <div className="text-lg font-bold">Essential</div>
        <div className="text-2xl font-bold mt-2 mb-4">$199<span className="text-sm font-normal text-muted-foreground">/mo</span></div>
        <div className="text-sm text-muted-foreground space-y-1">
          <p>• Full platform access</p>
          <p>• Up to 2 team members</p>
          <p>• Access to vetted affiliate network</p>
          <p>• Basic booking management</p>
          <p>• Standard reporting</p>
          <p>• Document exchange</p>
          <p>• Access to VIP services booking</p>
          <p>• Email support</p>
        </div>
      </div>
      
      <div 
        className={`border rounded-lg p-4 hover:border-primary cursor-pointer transition-all ${formData.plan === 'professional' ? 'border-primary ring-1 ring-primary bg-primary/5' : ''}`}
        onClick={() => updateFormData('plan', 'professional')}
      >
        <div className="flex justify-between items-center">
            <div className="text-lg font-bold">Professional</div>
            <Badge variant="default">Most Popular</Badge>
        </div>
        <div className="text-2xl font-bold mt-2 mb-4">$499<span className="text-sm font-normal text-muted-foreground">/mo</span></div>
        <div className="text-sm text-muted-foreground space-y-1">
          <p>• Everything in Essential, plus:</p>
          <p>• Up to 5 team members</p>
          <p>• Advanced booking management</p>
          <p>• Custom booking forms</p>
          <p>• Advanced analytics</p>
          <p>• Bulk booking tools</p>
          <p>• Priority support</p>
        </div>
      </div>
      
      <div 
        className={`border rounded-lg p-4 hover:border-primary cursor-pointer transition-all ${formData.plan === 'business' ? 'border-primary ring-1 ring-primary bg-primary/5' : ''}`}
        onClick={() => updateFormData('plan', 'business')}
      >
        <div className="text-lg font-bold">Business</div>
        <div className="text-2xl font-bold mt-2 mb-4">$999<span className="text-sm font-normal text-muted-foreground">/mo</span></div>
        <div className="text-sm text-muted-foreground space-y-1">
          <p>• Everything in Professional, plus:</p>
          <p>• Up to 10 team members</p>
          <p>• White-label options</p>
          <p>• Advanced integrations</p>
          <p>• Custom analytics dashboard</p>
          <p>• Volume-based incentives</p>
          <p>• Dedicated account manager</p>
          <p>• 24/7 priority support</p>
        </div>
      </div>
    </div>
  )
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Create New Tenant</DialogTitle>
          <DialogDescription>
            Add a new tenant to your SaaS platform. They'll be provisioned with their own isolated environment.
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <div className="flex items-center justify-between mb-6">
            <div className="flex gap-2">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step === 'details' || step === 'plan' || step === 'admin' || step === 'confirmation' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'}`}>
                <Building2 className="h-4 w-4" />
              </div>
              <div className="h-0.5 w-6 self-center bg-muted">
                {(step === 'plan' || step === 'admin' || step === 'confirmation') && <div className="h-full bg-primary"></div>}
              </div>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step === 'plan' || step === 'admin' || step === 'confirmation' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'}`}>
                <CreditCard className="h-4 w-4" />
              </div>
              <div className="h-0.5 w-6 self-center bg-muted">
                {(step === 'admin' || step === 'confirmation') && <div className="h-full bg-primary"></div>}
              </div>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step === 'admin' || step === 'confirmation' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'}`}>
                <User className="h-4 w-4" />
              </div>
            </div>
            <div className="text-sm text-muted-foreground">
              Step {step === 'details' ? '1' : step === 'plan' ? '2' : step === 'admin' ? '3' : '4'} of 4
            </div>
          </div>
          
          {step === 'details' && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="tenant-name">Tenant Name</Label>
                <Input 
                  id="tenant-name" 
                  placeholder="Acme Transportation Co." 
                  value={formData.name}
                  onChange={(e) => updateFormData('name', e.target.value)}
                />
                <p className="text-xs text-muted-foreground">The organization or company name</p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="tenant-domain">Domain Prefix</Label>
                <div className="flex">
                  <Input 
                    id="tenant-domain" 
                    placeholder="acme" 
                    value={formData.domain}
                    onChange={(e) => updateFormData('domain', e.target.value)}
                    className="rounded-r-none"
                  />
                  <div className="flex items-center px-3 border border-l-0 rounded-r-md bg-muted">
                    .transflow.app
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">Will be used for tenant URL: {formData.domain ? formData.domain.toLowerCase() : 'yourcompany'}.transflow.app</p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="tenant-industry">Industry</Label>
                <Select value={formData.industry} onValueChange={(value) => updateFormData('industry', value)}>
                  <SelectTrigger id="tenant-industry">
                    <SelectValue placeholder="Select industry" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="logistics">Logistics</SelectItem>
                    <SelectItem value="transportation">Transportation</SelectItem>
                    <SelectItem value="tourism">Tourism</SelectItem>
                    <SelectItem value="corporate">Corporate</SelectItem>
                    <SelectItem value="events">Events Management</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          
          {step === 'plan' && (
            <div className="space-y-4">
              <div className="mb-4">
                <h3 className="text-lg font-medium">Select a Plan</h3>
                <p className="text-sm text-muted-foreground">Choose the appropriate subscription plan for this tenant.</p>
              </div>
              
              <PlansTab />
            </div>
          )}
          
          {step === 'admin' && (
            <div className="space-y-4">
              <div className="mb-4">
                <h3 className="text-lg font-medium">Tenant Admin User</h3>
                <p className="text-sm text-muted-foreground">Create the initial admin user for this tenant.</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="admin-first-name">First Name</Label>
                  <Input 
                    id="admin-first-name" 
                    placeholder="John" 
                    value={formData.adminFirstName}
                    onChange={(e) => updateFormData('adminFirstName', e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="admin-last-name">Last Name</Label>
                  <Input 
                    id="admin-last-name" 
                    placeholder="Doe" 
                    value={formData.adminLastName}
                    onChange={(e) => updateFormData('adminLastName', e.target.value)}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="admin-email">Email Address</Label>
                <Input 
                  id="admin-email" 
                  type="email"
                  placeholder="<EMAIL>" 
                  value={formData.adminEmail}
                  onChange={(e) => updateFormData('adminEmail', e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="admin-password">Initial Password</Label>
                <Input 
                  id="admin-password" 
                  type="password"
                  placeholder="••••••••" 
                  value={formData.adminPassword}
                  onChange={(e) => updateFormData('adminPassword', e.target.value)}
                />
                <p className="text-xs text-muted-foreground">The admin will be prompted to change this password on first login.</p>
              </div>
            </div>
          )}
          
          {step === 'confirmation' && (
            <div className="space-y-6">
              <div className="mb-4">
                <h3 className="text-lg font-medium">Confirm Tenant Details</h3>
                <p className="text-sm text-muted-foreground">Please review the information before creating the tenant.</p>
              </div>
              
              <div className="space-y-4">
                <div className="bg-muted p-4 rounded-md">
                  <h4 className="font-medium mb-2">Tenant Information</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="text-muted-foreground">Name:</div>
                    <div>{formData.name}</div>
                    
                    <div className="text-muted-foreground">Domain:</div>
                    <div>{formData.domain.toLowerCase()}.transflow.app</div>
                    
                    <div className="text-muted-foreground">Industry:</div>
                    <div className="capitalize">{formData.industry}</div>
                    
                    <div className="text-muted-foreground">Plan:</div>
                    <div className="capitalize">{formData.plan}</div>
                  </div>
                </div>
                
                <div className="bg-muted p-4 rounded-md">
                  <h4 className="font-medium mb-2">Admin User</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="text-muted-foreground">Name:</div>
                    <div>{formData.adminFirstName} {formData.adminLastName}</div>
                    
                    <div className="text-muted-foreground">Email:</div>
                    <div>{formData.adminEmail}</div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
        
        <DialogFooter className="flex justify-between items-center">
          {step !== 'details' ? (
            <Button type="button" variant="ghost" onClick={prevStep}>
              Back
            </Button>
          ) : (
            <div></div>
          )}
          
          {step !== 'confirmation' ? (
            <Button type="button" onClick={nextStep} disabled={!validateStep()}>
              Continue <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          ) : (
            <Button type="button" onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting ? "Creating..." : "Create Tenant"}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 