'use client'

import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Button } from '@/app/components/ui/button'
import { Badge } from '@/app/components/ui/badge'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select'
import { useToast } from '@/app/components/ui/use-toast'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/app/components/ui/dialog'
import { Globe, Building2, Crown, Plus, Edit, Trash2 } from 'lucide-react'

interface Tenant {
  id: string
  name: string
  slug: string
  domain?: string
  tenant_type: 'shared' | 'segregated' | 'white_label'
  status: 'active' | 'inactive' | 'suspended'
  branding: Record<string, any>
  settings: Record<string, any>
  created_at: string
  updated_at: string
}

const fetchTenants = async (): Promise<{ tenants: Tenant[] }> => {
  const response = await fetch('/api/super-admin/tenants')
  if (!response.ok) {
    throw new Error('Failed to fetch tenants')
  }
  return response.json()
}

const createTenant = async (tenantData: Partial<Tenant>) => {
  const response = await fetch('/api/super-admin/tenants', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(tenantData),
  })
  if (!response.ok) {
    throw new Error('Failed to create tenant')
  }
  return response.json()
}

export default function TenantsPage() {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [newTenant, setNewTenant] = useState({
    name: '',
    slug: '',
    domain: '',
    tenant_type: 'shared' as const,
  })
  
  const { toast } = useToast()
  const queryClient = useQueryClient()

  const { data, isLoading, error } = useQuery({
    queryKey: ['tenants'],
    queryFn: fetchTenants,
  })

  const createMutation = useMutation({
    mutationFn: createTenant,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tenants'] })
      setIsCreateDialogOpen(false)
      setNewTenant({ name: '', slug: '', domain: '', tenant_type: 'shared' })
      toast({
        title: 'Success',
        description: 'Tenant created successfully',
      })
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create tenant',
        variant: 'destructive',
      })
    },
  })

  const getTenantIcon = (tenantType: string) => {
    switch (tenantType) {
      case 'shared':
        return <Globe className="h-5 w-5 text-blue-600" />
      case 'segregated':
        return <Building2 className="h-5 w-5 text-green-600" />
      case 'white_label':
        return <Crown className="h-5 w-5 text-purple-600" />
      default:
        return <Building2 className="h-5 w-5" />
    }
  }

  const getTenantTypeLabel = (tenantType: string) => {
    switch (tenantType) {
      case 'shared':
        return 'Shared SaaS'
      case 'segregated':
        return 'TNC Network'
      case 'white_label':
        return 'White Label'
      default:
        return 'Unknown'
    }
  }

  const getTenantTypeBadgeVariant = (tenantType: string) => {
    switch (tenantType) {
      case 'shared':
        return 'default'
      case 'segregated':
        return 'secondary'
      case 'white_label':
        return 'outline'
      default:
        return 'outline'
    }
  }

  const handleCreateTenant = () => {
    if (!newTenant.name || !newTenant.slug) {
      toast({
        title: 'Error',
        description: 'Name and slug are required',
        variant: 'destructive',
      })
      return
    }

    createMutation.mutate(newTenant)
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading tenants...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-4">Failed to load tenants</p>
          <Button onClick={() => queryClient.invalidateQueries({ queryKey: ['tenants'] })}>
            Retry
          </Button>
        </div>
      </div>
    )
  }

  const tenants = data?.tenants || []

  return (
    <div className="container p-6 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Tenant Management</h1>
          <p className="text-muted-foreground">
            Manage multi-tenant architecture and network switching
          </p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Tenant
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Tenant</DialogTitle>
              <DialogDescription>
                Add a new tenant to the multi-tenant architecture
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={newTenant.name}
                  onChange={(e) => setNewTenant({ ...newTenant, name: e.target.value })}
                  placeholder="e.g., LIMO123 Network"
                />
              </div>
              <div>
                <Label htmlFor="slug">Slug</Label>
                <Input
                  id="slug"
                  value={newTenant.slug}
                  onChange={(e) => setNewTenant({ ...newTenant, slug: e.target.value })}
                  placeholder="e.g., limo123"
                />
              </div>
              <div>
                <Label htmlFor="domain">Domain (Optional)</Label>
                <Input
                  id="domain"
                  value={newTenant.domain}
                  onChange={(e) => setNewTenant({ ...newTenant, domain: e.target.value })}
                  placeholder="e.g., app.limo123.com"
                />
              </div>
              <div>
                <Label htmlFor="tenant_type">Tenant Type</Label>
                <Select
                  value={newTenant.tenant_type}
                  onValueChange={(value) => setNewTenant({ ...newTenant, tenant_type: value as any })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="shared">Shared SaaS</SelectItem>
                    <SelectItem value="segregated">TNC Network</SelectItem>
                    <SelectItem value="white_label">White Label</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateTenant} disabled={createMutation.isLoading}>
                {createMutation.isLoading ? 'Creating...' : 'Create Tenant'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {tenants.map((tenant) => (
          <Card key={tenant.id} className="relative">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getTenantIcon(tenant.tenant_type)}
                  <div>
                    <CardTitle className="text-lg">{tenant.name}</CardTitle>
                    <CardDescription>{tenant.slug}</CardDescription>
                  </div>
                </div>
                <Badge variant={getTenantTypeBadgeVariant(tenant.tenant_type)}>
                  {getTenantTypeLabel(tenant.tenant_type)}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                {tenant.domain && (
                  <div>
                    <span className="font-medium">Domain:</span> {tenant.domain}
                  </div>
                )}
                <div>
                  <span className="font-medium">Status:</span>{' '}
                  <Badge variant={tenant.status === 'active' ? 'default' : 'secondary'}>
                    {tenant.status}
                  </Badge>
                </div>
                <div>
                  <span className="font-medium">Created:</span>{' '}
                  {new Date(tenant.created_at).toLocaleDateString()}
                </div>
              </div>
              
              <div className="flex items-center gap-2 mt-4">
                <Button variant="outline" size="sm">
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Button>
                <Button variant="outline" size="sm" className="text-red-600">
                  <Trash2 className="h-4 w-4 mr-1" />
                  Delete
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {tenants.length === 0 && (
        <div className="text-center py-12">
          <Globe className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No tenants found</h3>
          <p className="text-muted-foreground mb-4">
            Create your first tenant to get started with multi-tenancy
          </p>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create First Tenant
          </Button>
        </div>
      )}
    </div>
  )
}
