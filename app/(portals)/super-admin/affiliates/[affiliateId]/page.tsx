"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/app/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from "@/app/components/ui/tabs";
import { useToast } from "@/app/components/ui/use-toast";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  Building2,
  Car,
  Users,
  MapPin,
  FileText,
  CheckCircle2,
  XCircle,
  ArrowLeft,
  Shield,
  DollarSign,
  ClipboardCheck,
  AlertTriangle,
  Mail,
  Phone,
  Globe,
  FileCheck,
  Clock,
  Calendar,
} from "lucide-react";
import Link from "next/link";
import { Badge } from "@/app/components/ui/badge";
import { Progress } from "@/app/components/ui/progress";
import { Separator } from "@/app/components/ui/separator";

// API functions
const fetchAffiliateDetails = async (id: string) => {
  const response = await fetch(`/api/super-admin/affiliates/${id}`);
  if (!response.ok) {
    throw new Error("Failed to fetch affiliate details");
  }
  return response.json();
};

const updateAffiliateStatus = async ({
  id,
  status,
  verificationStatus,
}: {
  id: string;
  status?: string;
  verificationStatus?: string;
}) => {
  const response = await fetch(`/api/super-admin/affiliates/${id}`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ status, verificationStatus }),
  });

  if (!response.ok) {
    throw new Error("Failed to update affiliate status");
  }

  return response.json();
};

export default function AffiliateDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const affiliateId = params.affiliateId as string;

  // Fetch affiliate details
  const {
    data: affiliate,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["affiliate", affiliateId],
    queryFn: () => fetchAffiliateDetails(affiliateId),
    enabled: !!affiliateId,
  });

  // Status update mutation
  const statusMutation = useMutation({
    mutationFn: updateAffiliateStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["affiliate", affiliateId] });
      queryClient.invalidateQueries({ queryKey: ["affiliate-live-data"] });
      toast({
        title: "Success",
        description: "Affiliate status updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description:
          (error instanceof Error ? error.message : String(error)) ||
          "Failed to update affiliate status",
        variant: "destructive",
      });
    },
  });

  const handleApprove = () => {
    statusMutation.mutate({
      id: affiliateId,
      status: "active",
      verificationStatus: "approved",
    });
  };

  const handleReject = () => {
    statusMutation.mutate({
      id: affiliateId,
      status: "inactive",
      verificationStatus: "rejected",
    });
  };

  const handleRequestUpdates = () => {
    statusMutation.mutate({
      id: affiliateId,
      verificationStatus: "pending",
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading affiliate details...</p>
        </div>
      </div>
    );
  }

  if (error || !affiliate) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-8 w-8 text-red-600 mx-auto mb-4" />
          <p className="text-muted-foreground">
            Failed to load affiliate details
          </p>
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="mt-2"
          >
            Go Back
          </Button>
        </div>
      </div>
    );
  }
  return (
    <div className="container p-6 space-y-8">
      {/* Header with navigation and primary actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/super-admin/affiliates">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Affiliates
            </Link>
          </Button>
          <div className="flex items-center gap-2">
            <Badge
              variant={
                affiliate.status === "active"
                  ? "default"
                  : affiliate.status === "pending"
                  ? "secondary"
                  : "destructive"
              }
            >
              {affiliate.status === "active"
                ? "Active"
                : affiliate.status === "pending"
                ? "Pending Review"
                : "Inactive"}
            </Badge>
            <Badge variant="outline">
              {affiliate.verificationStatus === "verified"
                ? "Verified"
                : affiliate.verificationStatus === "in_progress"
                ? "In Progress"
                : affiliate.verificationStatus === "rejected"
                ? "Rejected"
                : "Pending Verification"}
            </Badge>
          </div>
        </div>

        {/* Prominent Action Buttons */}
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={handleRequestUpdates}
            disabled={statusMutation.isLoading}
            className="h-10 px-6"
          >
            <FileText className="h-4 w-4 mr-2" />
            Request Updates
          </Button>
          <Button
            variant="destructive"
            onClick={handleReject}
            disabled={
              statusMutation.isLoading || affiliate.status === "inactive"
            }
            className="h-10 px-6"
          >
            <XCircle className="h-4 w-4 mr-2" />
            {statusMutation.isLoading ? "Processing..." : "Reject Application"}
          </Button>
          <Button
            onClick={handleApprove}
            disabled={statusMutation.isLoading || affiliate.status === "active"}
            className="h-10 px-6 bg-green-600 hover:bg-green-700"
          >
            <CheckCircle2 className="h-4 w-4 mr-2" />
            {statusMutation.isLoading ? "Processing..." : "Approve & Activate"}
          </Button>
        </div>
      </div>

      {/* Key Metrics Dashboard - Moved to Top */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-600" />
              Affiliate Distribution
            </CardTitle>
            <p className="text-sm text-muted-foreground">By tenant usage</p>
          </CardHeader>
          <CardContent>
            <div className="h-[120px] bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">85%</div>
                <p className="text-sm text-blue-700">Active Usage</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <MapPin className="h-5 w-5 text-green-600" />
              Service Coverage
            </CardTitle>
            <p className="text-sm text-muted-foreground">By location</p>
          </CardHeader>
          <CardContent>
            <div className="h-[120px] bg-gradient-to-br from-green-50 to-green-100 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">12</div>
                <p className="text-sm text-green-700">Cities Covered</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Shield className="h-5 w-5 text-purple-600" />
              Performance
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Top rated affiliates
            </p>
          </CardHeader>
          <CardContent>
            <div className="h-[120px] bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">4.8</div>
                <p className="text-sm text-purple-700">Rating Score</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Comprehensive Affiliate Management */}
      <Tabs defaultValue="approval" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5 lg:grid-cols-9">
          <TabsTrigger value="approval">Approval</TabsTrigger>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="contact">Contact</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="rates">Rates</TabsTrigger>
          <TabsTrigger value="fleet">Fleet</TabsTrigger>
          <TabsTrigger value="service-area">Service Area</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="audit">Audit Log</TabsTrigger>
        </TabsList>

        {/* Approval Tab - Current Content */}
        <TabsContent value="approval" className="space-y-6">
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
            {/* Left Column - Company Overview & Contact */}
            <div className="xl:col-span-1 space-y-6">
              <Card>
                <CardHeader>
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl flex items-center justify-center">
                      <Building2 className="h-8 w-8 text-gray-600" />
                    </div>
                    <div>
                      <CardTitle className="text-xl">
                        {affiliate.name || affiliate.businessName}
                      </CardTitle>
                      <p className="text-sm text-muted-foreground">
                        Applied{" "}
                        {new Date(affiliate.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm text-muted-foreground">Email</p>
                        <p className="font-medium">
                          {affiliate.email || "Not provided"}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm text-muted-foreground">Phone</p>
                        <p className="font-medium">
                          {affiliate.phone || "Not provided"}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm text-muted-foreground">Address</p>
                        <p className="font-medium">
                          {affiliate.address?.street
                            ? `${affiliate.address.street}, ${affiliate.address.city}, ${affiliate.address.state} ${affiliate.address.zip}`
                            : "Not provided"}
                        </p>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Business License
                      </p>
                      <p className="font-medium text-sm">
                        {affiliate.businessInfo?.taxId || "Not provided"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Established
                      </p>
                      <p className="font-medium text-sm">
                        {affiliate.businessInfo?.yearEstablished || "N/A"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Rating</p>
                      <p className="font-medium text-sm">
                        {affiliate.metrics?.rating?.toFixed(1) || "N/A"}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Last Active
                      </p>
                      <p className="font-medium text-sm">
                        {affiliate.lastActive
                          ? new Date(affiliate.lastActive).toLocaleDateString()
                          : "Never"}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Application Progress */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <ClipboardCheck className="h-5 w-5" />
                    Application Progress
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Progress value={65} className="w-full" />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Documents</span>
                      <span>Verification</span>
                      <span>Review</span>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <CheckCircle2 className="h-4 w-4 text-green-500" />
                          <span className="text-sm">Business Registration</span>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          Verified
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-yellow-500" />
                          <span className="text-sm">
                            Insurance Documentation
                          </span>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          In Review
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <AlertTriangle className="h-4 w-4 text-red-500" />
                          <span className="text-sm">Fleet Inspection</span>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          Pending
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Right Column - Fleet & Rates, Compliance & Audit */}
            <div className="xl:col-span-2 space-y-6">
              {/* Fleet & Rates Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Car className="h-5 w-5" />
                    Fleet & Rates
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Fleet Overview */}
                    <div className="space-y-4">
                      <h4 className="font-medium">Fleet Overview</h4>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="p-4 bg-blue-50 rounded-lg text-center">
                          <div className="text-2xl font-bold text-blue-600">
                            12
                          </div>
                          <p className="text-sm text-blue-700">
                            Total Vehicles
                          </p>
                        </div>
                        <div className="p-4 bg-green-50 rounded-lg text-center">
                          <div className="text-2xl font-bold text-green-600">
                            8
                          </div>
                          <p className="text-sm text-green-700">
                            Active Drivers
                          </p>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                          <span className="text-sm">Sedans</span>
                          <Badge variant="outline">6 vehicles</Badge>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                          <span className="text-sm">SUVs</span>
                          <Badge variant="outline">4 vehicles</Badge>
                        </div>
                        <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                          <span className="text-sm">Luxury</span>
                          <Badge variant="outline">2 vehicles</Badge>
                        </div>
                      </div>
                    </div>

                    {/* Rate Structure */}
                    <div className="space-y-4">
                      <h4 className="font-medium">Rate Structure</h4>
                      <div className="space-y-3">
                        <div className="p-4 border rounded-lg">
                          <div className="flex justify-between items-center mb-2">
                            <span className="font-medium">Sedan - Airport</span>
                            <span className="text-lg font-bold">$85</span>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Base rate to/from major airports
                          </p>
                        </div>
                        <div className="p-4 border rounded-lg">
                          <div className="flex justify-between items-center mb-2">
                            <span className="font-medium">SUV - Hourly</span>
                            <span className="text-lg font-bold">$120/hr</span>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            3-hour minimum
                          </p>
                        </div>
                        <div className="p-4 border rounded-lg">
                          <div className="flex justify-between items-center mb-2">
                            <span className="font-medium">
                              Luxury - Point-to-Point
                            </span>
                            <span className="text-lg font-bold">$3.50/mi</span>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            $50 minimum
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Compliance & Audit Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    Compliance & Audit
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Documents Status */}
                    <div className="space-y-4">
                      <h4 className="font-medium">Required Documents</h4>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                          <div className="flex items-center gap-2">
                            <CheckCircle2 className="h-4 w-4 text-green-500" />
                            <span className="text-sm">Business License</span>
                          </div>
                          <Badge
                            variant="outline"
                            className="bg-green-100 text-green-700"
                          >
                            Verified
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                          <div className="flex items-center gap-2">
                            <CheckCircle2 className="h-4 w-4 text-green-500" />
                            <span className="text-sm">
                              Commercial Insurance
                            </span>
                          </div>
                          <Badge
                            variant="outline"
                            className="bg-green-100 text-green-700"
                          >
                            Verified
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-yellow-500" />
                            <span className="text-sm">Vehicle Inspections</span>
                          </div>
                          <Badge variant="secondary">In Progress</Badge>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                          <div className="flex items-center gap-2">
                            <AlertTriangle className="h-4 w-4 text-red-500" />
                            <span className="text-sm">
                              Driver Background Checks
                            </span>
                          </div>
                          <Badge
                            variant="outline"
                            className="bg-red-100 text-red-700"
                          >
                            Missing
                          </Badge>
                        </div>
                      </div>
                    </div>

                    {/* Operational Compliance */}
                    <div className="space-y-4">
                      <h4 className="font-medium">Operational Compliance</h4>
                      <div className="space-y-3">
                        <div className="p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">
                              Dispatch Integration
                            </span>
                            <CheckCircle2 className="h-4 w-4 text-green-500" />
                          </div>
                          <p className="text-xs text-muted-foreground">
                            LimoAnywhere connected
                          </p>
                        </div>
                        <div className="p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">
                              24/7 Support
                            </span>
                            <CheckCircle2 className="h-4 w-4 text-green-500" />
                          </div>
                          <p className="text-xs text-muted-foreground">
                            Confirmed availability
                          </p>
                        </div>
                        <div className="p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium">
                              Service Standards
                            </span>
                            <Clock className="h-4 w-4 text-yellow-500" />
                          </div>
                          <p className="text-xs text-muted-foreground">
                            Training in progress
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Service Coverage */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    Service Coverage
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="font-medium">Coverage Areas</h4>
                      <div className="h-[200px] bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg flex items-center justify-center">
                        <div className="text-center">
                          <MapPin className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                          <p className="text-sm text-gray-600">
                            Interactive coverage map
                          </p>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-muted-foreground">
                            Primary Hub
                          </p>
                          <p className="font-medium">Manhattan, NY</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">
                            Coverage Radius
                          </p>
                          <p className="font-medium">50 miles</p>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h4 className="font-medium">Service Capabilities</h4>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                          <span className="text-sm">Airport Transfers</span>
                          <CheckCircle2 className="h-4 w-4 text-green-500" />
                        </div>
                        <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                          <span className="text-sm">Corporate Events</span>
                          <CheckCircle2 className="h-4 w-4 text-green-500" />
                        </div>
                        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <span className="text-sm">Wedding Services</span>
                          <XCircle className="h-4 w-4 text-gray-400" />
                        </div>
                        <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                          <span className="text-sm">24/7 Availability</span>
                          <CheckCircle2 className="h-4 w-4 text-green-500" />
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Total Quotes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">247</div>
                <p className="text-xs text-muted-foreground">
                  +12% from last month
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Completed Trips</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">189</div>
                <p className="text-xs text-muted-foreground">
                  76% completion rate
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Revenue Generated</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$45,230</div>
                <p className="text-xs text-muted-foreground">This month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Average Rating</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4.8</div>
                <p className="text-xs text-muted-foreground">
                  Based on 156 reviews
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Contact Tab */}
        <TabsContent value="contact" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Primary Contact</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Full Name</label>
                  <p className="text-sm">
                    {affiliate.contactPerson || "Not provided"}
                  </p>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Email</label>
                  <p className="text-sm">{affiliate.email || "Not provided"}</p>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Phone</label>
                  <p className="text-sm">{affiliate.phone || "Not provided"}</p>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Position</label>
                  <p className="text-sm">
                    {affiliate.position || "Not provided"}
                  </p>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Business Address</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Street Address</label>
                  <p className="text-sm">
                    {affiliate.address?.street || "Not provided"}
                  </p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">City</label>
                    <p className="text-sm">
                      {affiliate.address?.city || "Not provided"}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">State</label>
                    <p className="text-sm">
                      {affiliate.address?.state || "Not provided"}
                    </p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">ZIP Code</label>
                    <p className="text-sm">
                      {affiliate.address?.zip || "Not provided"}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Country</label>
                    <p className="text-sm">
                      {affiliate.address?.country || "United States"}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Response Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm">Average Response Time</span>
                  <span className="font-medium">12 minutes</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Quote Acceptance Rate</span>
                  <span className="font-medium">78%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">On-Time Performance</span>
                  <span className="font-medium">94%</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Customer Satisfaction</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm">Overall Rating</span>
                  <span className="font-medium">4.8/5.0</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Total Reviews</span>
                  <span className="font-medium">156</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Repeat Customers</span>
                  <span className="font-medium">67%</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Financial Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm">Monthly Revenue</span>
                  <span className="font-medium">$45,230</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Average Trip Value</span>
                  <span className="font-medium">$239</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Growth Rate</span>
                  <span className="font-medium text-green-600">+12%</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Additional tabs would continue here... */}
        <TabsContent value="rates" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Rate Management</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Detailed rate management interface coming soon...
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="fleet" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Fleet Management</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Comprehensive fleet management interface coming soon...
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="service-area" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Service Area Coverage</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Interactive service area management coming soon...
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Document Management</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Document upload and verification interface coming soon...
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audit" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Audit Log</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Comprehensive audit trail coming soon...
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
