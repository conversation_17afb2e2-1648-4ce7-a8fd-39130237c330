"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle, CardDescription, CardFooter } from "@/app/components/ui/card"
import { DateRangePicker } from "@/app/components/ui/date-range-picker"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table"
import { Button } from "@/app/components/ui/button"
import { Download, FileText, CalendarDays } from "lucide-react"
import { subDays } from 'date-fns'
import type { DateRange } from 'react-day-picker'

const mockReportTypes = [
  { value: "active_orgs", label: "Active Organizations" },
  { value: "churned_orgs", label: "Churned Organizations" },
  { value: "revenue_by_org", label: "Revenue by Organization" },
  { value: "subscription_events", label: "Subscription Events" },
]

const mockReports = [
  {
    id: 1,
    type: "Active Organizations",
    generated: "2024-06-28 10:12",
    range: "2024-06-01 to 2024-06-28",
    status: "Ready",
    url: "/downloads/active_orgs_june.csv"
  },
  {
    id: 2,
    type: "Revenue by Organization",
    generated: "2024-06-27 09:45",
    range: "2024-05-01 to 2024-05-31",
    status: "Ready",
    url: "/downloads/revenue_by_org_may.csv"
  },
  {
    id: 3,
    type: "Subscription Events",
    generated: "2024-06-26 14:22",
    range: "2024-06-01 to 2024-06-26",
    status: "Ready",
    url: "/downloads/subscription_events_june.csv"
  },
]

export default function SubscriptionsReportsPage() {
  const [date, setDate] = useState<DateRange | undefined>({
    from: subDays(new Date(), 29),
    to: new Date(),
  })
  const [reportType, setReportType] = useState("active_orgs")

  // TODO: Replace with Supabase data fetching and report generation

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Reports & Exports</h2>
          <p className="text-muted-foreground">Generate and download reports for organizations and subscriptions</p>
        </div>
        <div className="flex items-center gap-2">
          <DateRangePicker date={date} onDateChange={setDate} />
          <Select value={reportType} onValueChange={setReportType}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Report Type" />
            </SelectTrigger>
            <SelectContent>
              {mockReportTypes.map(rt => (
                <SelectItem key={rt.value} value={rt.value}>{rt.label}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon" title="Generate Report">
            <FileText className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Generated Reports</CardTitle>
          <CardDescription>Download completed reports below</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Type</TableHead>
                <TableHead>Date Generated</TableHead>
                <TableHead>Date Range</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Download</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockReports.map(report => (
                <TableRow key={report.id}>
                  <TableCell>{report.type}</TableCell>
                  <TableCell>{report.generated}</TableCell>
                  <TableCell>{report.range}</TableCell>
                  <TableCell>{report.status}</TableCell>
                  <TableCell>
                    <Button asChild variant="ghost" size="icon" title="Download">
                      <a href={report.url} download>
                        <Download className="h-4 w-4" />
                      </a>
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter>
          <Button variant="outline" className="gap-1">
            <Download className="h-4 w-4" />
            Export All
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
} 