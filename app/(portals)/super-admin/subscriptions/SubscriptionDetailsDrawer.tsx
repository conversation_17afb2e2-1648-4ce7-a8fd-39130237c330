"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Sheet<PERSON><PERSON> } from "@/app/components/ui/sheet"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/app/components/ui/tabs"
import { But<PERSON> } from "@/app/components/ui/button"
import { CreditCard, Receipt, History, Settings, User, Building2, CalendarClock, CheckCircle, AlertTriangle, RefreshCw, Download, FileText, Clock, LogIn } from "lucide-react"
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from "@/app/components/ui/card"
import { Badge } from "@/app/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/app/components/ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table"
import { useToast } from "@/app/components/ui/use-toast"
import { Switch } from "@/app/components/ui/switch"
import { Label } from "@/app/components/ui/label"
import { Input } from "@/app/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"

interface SubscriptionDetailsDrawerProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  subscription: {
    id: string
    tenant_id: string
    tenant_name: string
    plan: string
    status: string
    billing_cycle: string
    amount: number
    start_date: string
    next_billing_date: string
    payment_method: string
  } | null
}

export function SubscriptionDetailsDrawer({ open, onOpenChange, subscription }: SubscriptionDetailsDrawerProps) {
  const { toast } = useToast()
  
  if (!subscription) return null
  
  const handleCancelSubscription = () => {
    toast({
      title: "Subscription Cancellation",
      description: `Subscription for ${subscription.tenant_name} will be canceled at the end of the billing period.`,
    })
  }
  
  const handleChangePlan = () => {
    toast({
      title: "Plan Change",
      description: `Plan change for ${subscription.tenant_name} initiated.`,
    })
  }
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date)
  }
  
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount)
  }
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>
      case 'past_due':
        return <Badge className="bg-amber-100 text-amber-800">Past Due</Badge>
      case 'canceled':
        return <Badge className="bg-red-100 text-red-800">Canceled</Badge>
      case 'trialing':
        return <Badge className="bg-blue-100 text-blue-800">Trial</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }
  
  const OverviewTab = () => (
    <TabsContent value="overview" className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Subscription Information</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="space-y-2 text-sm">
              <div className="flex justify-between">
                <dt className="text-muted-foreground">Subscription ID:</dt>
                <dd className="font-medium">{subscription.id}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-muted-foreground">Plan:</dt>
                <dd className="font-medium capitalize">{subscription.plan}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-muted-foreground">Status:</dt>
                <dd>{getStatusBadge(subscription.status)}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-muted-foreground">Billing Cycle:</dt>
                <dd className="font-medium capitalize">{subscription.billing_cycle}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-muted-foreground">Amount:</dt>
                <dd className="font-medium">{formatCurrency(subscription.amount)}/{subscription.billing_cycle === 'annual' ? 'year' : 'month'}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Important Dates</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="space-y-2 text-sm">
              <div className="flex justify-between">
                <dt className="text-muted-foreground">Started On:</dt>
                <dd className="font-medium">{formatDate(subscription.start_date)}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-muted-foreground">Next Billing Date:</dt>
                <dd className="font-medium">{formatDate(subscription.next_billing_date)}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-muted-foreground">Current Period:</dt>
                <dd className="font-medium">
                  {subscription.billing_cycle === 'annual' ? 'Annual' : 'Monthly'}
                </dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-muted-foreground">Auto-Renew:</dt>
                <dd>
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    Enabled
                  </Badge>
                </dd>
              </div>
            </dl>
          </CardContent>
        </Card>
      </div>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Tenant Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Avatar className="h-12 w-12">
              <AvatarImage src={`https://avatars.dicebear.com/api/initials/${subscription.tenant_name.split(' ').map(n => n[0]).join('')}.svg`} alt={subscription.tenant_name} />
              <AvatarFallback><Building2 /></AvatarFallback>
            </Avatar>
            <div className="space-y-1">
              <h4 className="text-base font-medium">{subscription.tenant_name}</h4>
              <p className="text-sm text-muted-foreground">ID: {subscription.tenant_id}</p>
              <div className="flex gap-2 mt-2">
                <Button size="sm" variant="outline" className="h-8">
                  <Building2 className="h-3.5 w-3.5 mr-1" />
                  View Tenant
                </Button>
                <Button size="sm" variant="outline" className="h-8">
                  <User className="h-3.5 w-3.5 mr-1" />
                  Contact Admin
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-2 flex flex-row items-center justify-between">
          <CardTitle className="text-sm font-medium">Billing Summary</CardTitle>
          <Button variant="outline" size="sm">
            <Receipt className="h-4 w-4 mr-1" />
            View All Invoices
          </Button>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-2 grid-cols-3">
              <div className="bg-muted p-3 rounded-md">
                <div className="text-sm text-muted-foreground">Current Period</div>
                <div className="font-medium mt-1">{formatCurrency(subscription.amount)}</div>
              </div>
              <div className="bg-muted p-3 rounded-md">
                <div className="text-sm text-muted-foreground">Payment Status</div>
                <div className="font-medium mt-1">Current</div>
              </div>
              <div className="bg-muted p-3 rounded-md">
                <div className="text-sm text-muted-foreground">Payment Method</div>
                <div className="font-medium mt-1 capitalize">
                  {subscription.payment_method.replace('_', ' ')}
                </div>
              </div>
            </div>
            
            <div>
              <div className="text-sm font-medium mb-2">Recent Invoices</div>
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell>{formatDate(new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString())}</TableCell>
                      <TableCell>{formatCurrency(subscription.amount)}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          Paid
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <FileText className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell>{formatDate(new Date(new Date().setMonth(new Date().getMonth() - 2)).toISOString())}</TableCell>
                      <TableCell>{formatCurrency(subscription.amount)}</TableCell>
                      <TableCell>
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                          Paid
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm">
                          <FileText className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </TabsContent>
  )
  
  const BillingTab = () => (
    <TabsContent value="billing" className="space-y-6">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Payment Method</CardTitle>
          <CardDescription>Current payment information</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-4 p-4 border rounded-md">
              <div className="bg-muted rounded-md p-2">
                <CreditCard className="h-6 w-6" />
              </div>
              <div>
                <div className="font-medium">{subscription.payment_method === 'credit_card' ? 'Credit Card' : subscription.payment_method === 'bank_transfer' ? 'Bank Transfer' : 'Invoice'}</div>
                {subscription.payment_method === 'credit_card' && (
                  <div className="text-sm text-muted-foreground">•••• •••• •••• 4242 | Expires 04/2024</div>
                )}
                {subscription.payment_method === 'bank_transfer' && (
                  <div className="text-sm text-muted-foreground">Last four: 1234 | Example Bank</div>
                )}
              </div>
              <div className="ml-auto">
                <Button variant="outline" size="sm">Update</Button>
              </div>
            </div>
            
            <div className="flex flex-col gap-2">
              <div className="text-sm font-medium">Contact Information</div>
              <div className="p-4 border rounded-md space-y-2">
                <div className="flex justify-between">
                  <div className="text-sm text-muted-foreground">Billing Email</div>
                  <div className="text-sm font-medium">billing@{subscription.tenant_name.toLowerCase().replace(/ /g, '')}.com</div>
                </div>
                <div className="flex justify-between">
                  <div className="text-sm text-muted-foreground">Billing Address</div>
                  <div className="text-sm font-medium">123 Business St, New York, NY 10001</div>
                </div>
                <div className="flex justify-between">
                  <div className="text-sm text-muted-foreground">Tax ID</div>
                  <div className="text-sm font-medium">***********</div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Billing History</CardTitle>
          <CardDescription>Past invoices and transactions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Invoice #</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {Array.from({ length: 5 }).map((_, i) => (
                  <TableRow key={i}>
                    <TableCell>INV-{2023100 + i}</TableCell>
                    <TableCell>{formatDate(new Date(new Date().setMonth(new Date().getMonth() - i)).toISOString())}</TableCell>
                    <TableCell>{formatCurrency(subscription.amount)}</TableCell>
                    <TableCell>
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        Paid
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm">
                        <Download className="h-4 w-4 mr-1" />
                        PDF
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          
          <div className="flex justify-end mt-4">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-1" />
              Download All
            </Button>
          </div>
        </CardContent>
      </Card>
    </TabsContent>
  )
  
  const HistoryTab = () => (
    <TabsContent value="history" className="space-y-6">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Subscription Changes</CardTitle>
          <CardDescription>History of subscription updates</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="border-l-2 border-border pl-4 space-y-6">
              <div className="relative pb-6">
                <div className="absolute -left-[21px] h-6 w-6 rounded-full bg-muted flex items-center justify-center">
                  <CheckCircle className="h-4 w-4 text-primary" />
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between items-start">
                    <div className="font-medium">Subscription Created</div>
                    <div className="text-sm text-muted-foreground">{formatDate(subscription.start_date)}</div>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Initial {subscription.plan} plan subscription created.
                  </div>
                </div>
              </div>
              
              <div className="relative pb-6">
                <div className="absolute -left-[21px] h-6 w-6 rounded-full bg-muted flex items-center justify-center">
                  <RefreshCw className="h-4 w-4 text-primary" />
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between items-start">
                    <div className="font-medium">Subscription Renewed</div>
                    <div className="text-sm text-muted-foreground">{formatDate(new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString())}</div>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Subscription automatically renewed for another {subscription.billing_cycle === 'annual' ? 'year' : 'month'}.
                  </div>
                </div>
              </div>
              
              <div className="relative pb-6">
                <div className="absolute -left-[21px] h-6 w-6 rounded-full bg-muted flex items-center justify-center">
                  <Clock className="h-4 w-4 text-amber-500" />
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between items-start">
                    <div className="font-medium">Upcoming Renewal</div>
                    <div className="text-sm text-muted-foreground">{formatDate(subscription.next_billing_date)}</div>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Next automatic renewal scheduled.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </TabsContent>
  )
  
  const SettingsTab = () => (
    <TabsContent value="settings" className="space-y-6">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Subscription Settings</CardTitle>
          <CardDescription>Manage subscription preferences</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium">Auto-Renew</div>
                <div className="text-sm text-muted-foreground">Automatically renew subscription</div>
              </div>
              <Switch defaultChecked={true} />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium">Email Notifications</div>
                <div className="text-sm text-muted-foreground">Send email reminders for billing events</div>
              </div>
              <Switch defaultChecked={true} />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="billing-cycle">Billing Cycle</Label>
              <Select defaultValue={subscription.billing_cycle}>
                <SelectTrigger id="billing-cycle">
                  <SelectValue placeholder="Select billing cycle" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="annual">Annual</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                {subscription.billing_cycle === 'annual' 
                  ? 'Save 10% with annual billing' 
                  : 'Switch to annual billing to save 10%'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Plan Options</CardTitle>
          <CardDescription>Update subscription plan</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="plan">Current Plan</Label>
              <Select defaultValue={subscription.plan}>
                <SelectTrigger id="plan">
                  <SelectValue placeholder="Select plan" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="essential">Essential ($199/month)</SelectItem>
                  <SelectItem value="professional">Professional ($499/month)</SelectItem>
                  <SelectItem value="business">Business ($999/month)</SelectItem>
                  <SelectItem value="enterprise">Enterprise (Custom)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <Button onClick={handleChangePlan} className="w-full">
              Update Plan
            </Button>
          </div>
        </CardContent>
      </Card>
      
      <Card className="border-red-200">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-red-600">Danger Zone</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium">Cancel Subscription</div>
                <div className="text-sm text-red-600">This will end the subscription at the current billing period</div>
              </div>
              <Button variant="destructive" size="sm" onClick={handleCancelSubscription}>
                Cancel
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </TabsContent>
  )
  
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="!fixed !right-0 !top-0 !h-full !w-[95vw] !max-w-[95vw] !p-0 !m-0 !rounded-none !border-l-4 !border-l-blue-500 !bg-background !shadow-2xl !flex !flex-col !animate-in !data-[state=open]:slide-in-from-right-80">
        <SheetHeader className="flex flex-row items-center justify-between px-6 pt-6 pb-2 border-b">
          <div>
            <SheetTitle className="text-2xl font-bold">Subscription Details</SheetTitle>
            <div className="text-sm text-muted-foreground">{subscription.tenant_name}</div>
            <div className="flex gap-2 mt-2">
              {getStatusBadge(subscription.status)}
              <span className="px-2 py-0.5 rounded bg-secondary/10 text-secondary text-xs font-medium capitalize">{subscription.plan}</span>
            </div>
          </div>
          <div className="flex gap-2 items-center">
            <Button size="sm" variant="outline" className="gap-1" onClick={handleChangePlan}>
              <CreditCard className="h-4 w-4" /> Change Plan
            </Button>
            <SheetClose asChild>
              <Button size="icon" variant="ghost" className="ml-2">
                <span className="sr-only">Close</span>
                ×
              </Button>
            </SheetClose>
          </div>
        </SheetHeader>
        <div className="flex-1 flex flex-col overflow-hidden">
          <Tabs defaultValue="overview" className="flex-1 flex flex-col">
            <TabsList className="flex gap-2 px-6 pt-4 pb-2 border-b bg-background sticky top-0 z-10">
              <TabsTrigger value="overview" className="flex gap-1 items-center"><CreditCard className="h-4 w-4" /> Overview</TabsTrigger>
              <TabsTrigger value="billing" className="flex gap-1 items-center"><Receipt className="h-4 w-4" /> Billing</TabsTrigger>
              <TabsTrigger value="history" className="flex gap-1 items-center"><History className="h-4 w-4" /> History</TabsTrigger>
              <TabsTrigger value="settings" className="flex gap-1 items-center"><Settings className="h-4 w-4" /> Settings</TabsTrigger>
            </TabsList>
            <div className="flex-1 overflow-y-auto">
              <div className="p-6">
                <OverviewTab />
                <BillingTab />
                <HistoryTab />
                <SettingsTab />
              </div>
            </div>
          </Tabs>
        </div>
      </SheetContent>
    </Sheet>
  )
} 