"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Input } from "@/app/components/ui/input";
import { Button } from "@/app/components/ui/button";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/app/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/app/components/ui/dropdown-menu";
import { Badge } from "@/app/components/ui/badge";
import { Plus, MoreHorizontal, Building2, Edit, EyeIcon, UserPlus, Shield, CreditCard, BarChart3, FileText, Clock, CheckCircle, AlertTriangle, DollarSign, ArrowUpCircle, ArrowDownCircle, Building, Users, PlusCircle, Briefcase } from "lucide-react";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/app/components/ui/dialog";
import { TrialManagementCard } from "@/app/(portals)/super-admin/subscriptions/TrialManagementCard";
import { useToast } from "@/app/components/ui/use-toast";
import { OrgDetailsDrawer } from "./OrgDetailsDrawer";
import { CreateOrgForm } from "./CreateOrgForm";

// Mock data for orgs
const mockOrgs = [
  { id: "t_01", name: "Acme Transportation Co.", created_at: "2023-06-12T10:00:00Z", status: "active", plan: "enterprise", users_count: 42, industry: "Logistics", last_active: "2023-10-15T14:32:00Z" },
  { id: "t_02", name: "City Tours LLC", created_at: "2023-07-23T15:30:00Z", status: "active", plan: "professional", users_count: 18, industry: "Tourism", last_active: "2023-10-14T09:15:00Z" },
  { id: "t_03", name: "Swift Transit", created_at: "2023-08-05T08:45:00Z", status: "active", plan: "basic", users_count: 7, industry: "Public Transport", last_active: "2023-10-15T11:20:00Z" },
  { id: "t_04", name: "Global Logistics Inc.", created_at: "2023-04-18T12:20:00Z", status: "active", plan: "enterprise", users_count: 76, industry: "Freight", last_active: "2023-10-15T16:05:00Z" },
  { id: "t_05", name: "Metro Shuttle Services", created_at: "2023-09-30T14:10:00Z", status: "pending", plan: "professional", users_count: 3, industry: "Airport Transfers", last_active: "2023-10-13T10:45:00Z" },
  { id: "t_06", name: "Corporate Travel Solutions", created_at: "2023-02-08T09:15:00Z", status: "suspended", plan: "enterprise", users_count: 29, industry: "Corporate", last_active: "2023-09-28T15:30:00Z" }
];

// Mock data for subscriptions
const mockSubscriptions = [
  { id: "sub_01", org_id: "t_01", org_name: "Acme Transportation Co.", plan: "enterprise", status: "active", billing_cycle: "annual", amount: 999, start_date: "2023-06-15T00:00:00Z", next_billing_date: "2024-06-15T00:00:00Z", payment_method: "credit_card" },
  { id: "sub_02", org_id: "t_02", org_name: "City Tours LLC", plan: "professional", status: "active", billing_cycle: "monthly", amount: 199, start_date: "2023-07-25T00:00:00Z", next_billing_date: "2023-11-25T00:00:00Z", payment_method: "credit_card" },
  { id: "sub_03", org_id: "t_03", org_name: "Swift Transit", plan: "basic", status: "trialing", billing_cycle: "monthly", amount: 49, start_date: "2023-10-10T00:00:00Z", next_billing_date: "2023-11-10T00:00:00Z", payment_method: "credit_card" },
  { id: "sub_04", org_id: "t_04", org_name: "Global Logistics Inc.", plan: "enterprise", status: "active", billing_cycle: "annual", amount: 999, start_date: "2023-05-01T00:00:00Z", next_billing_date: "2024-05-01T00:00:00Z", payment_method: "bank_transfer" },
  { id: "sub_05", org_id: "t_05", org_name: "Metro Shuttle Services", plan: "professional", status: "past_due", billing_cycle: "monthly", amount: 199, start_date: "2023-09-30T00:00:00Z", next_billing_date: "2023-10-30T00:00:00Z", payment_method: "credit_card" },
  { id: "sub_06", org_id: "t_06", org_name: "Corporate Travel Solutions", plan: "enterprise", status: "canceled", billing_cycle: "annual", amount: 999, start_date: "2023-02-10T00:00:00Z", next_billing_date: "2023-10-01T00:00:00Z", payment_method: "invoice" }
];

export default function OrgsAndSubscriptionsPage() {
  const { toast } = useToast();
  const [tab, setTab] = useState("orgs");
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredOrgs, setFilteredOrgs] = useState(mockOrgs);
  const [filteredSubscriptions, setFilteredSubscriptions] = useState(mockSubscriptions);
  const [showCreateOrgDialog, setShowCreateOrgDialog] = useState(false);
  const [showCreateSubscriptionDialog, setShowCreateSubscriptionDialog] = useState(false);
  const [selectedOrg, setSelectedOrg] = useState<typeof mockOrgs[0] | null>(null);
  const [orgDetailsOpen, setOrgDetailsOpen] = useState(false);

  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredOrgs(mockOrgs);
      setFilteredSubscriptions(mockSubscriptions);
    } else {
      setFilteredOrgs(mockOrgs.filter(org => org.name.toLowerCase().includes(searchQuery.toLowerCase()) || org.industry.toLowerCase().includes(searchQuery.toLowerCase())));
      setFilteredSubscriptions(mockSubscriptions.filter(sub => sub.org_name.toLowerCase().includes(searchQuery.toLowerCase()) || sub.plan.toLowerCase().includes(searchQuery.toLowerCase()) || sub.status.toLowerCase().includes(searchQuery.toLowerCase())));
    }
  }, [searchQuery]);

  const formatDate = (dateString: string) => new Intl.DateTimeFormat('en-US', { year: 'numeric', month: 'short', day: 'numeric' }).format(new Date(dateString));
  const formatCurrency = (amount: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

  const handleOrgCreated = (newOrg: typeof mockOrgs[0]) => {
    setFilteredOrgs((prev) => [newOrg, ...prev]);
    toast({
      title: "Organization Created",
      description: `${newOrg.name} has been successfully created.`,
    });
  };

  const handleViewOrgDetails = (org: typeof mockOrgs[0]) => {
    setSelectedOrg(org);
    setOrgDetailsOpen(true);
  };

  // --- Mock Stats Data ---
  const orgStats = {
    totalOrgs: mockOrgs.length,
    activeSubscriptions: mockSubscriptions.filter(sub => sub.status === 'active').length,
    trialOrgs: mockOrgs.filter(org => org.status === 'active').length,
    avgSubValue: 1250 // Example static value
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">ORGs</h2>
          <p className="text-muted-foreground">Manage organizations, subscriptions, and trial settings</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setShowCreateOrgDialog(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add Org
          </Button>
          <Button onClick={() => setShowCreateSubscriptionDialog(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Subscription
          </Button>
          <Button variant="outline" onClick={() => window.location.href = "/super-admin/subscriptions/analytics"}>
            <BarChart3 className="mr-2 h-4 w-4" />
            Analytics Dashboard
          </Button>
          <Button variant="outline" onClick={() => window.location.href = "/super-admin/subscriptions/reports"}>
            <FileText className="mr-2 h-4 w-4" />
            Reports
          </Button>
        </div>
      </div>

      <div className="w-full flex flex-col md:flex-row gap-4">
        <Input
          type="search"
          placeholder="Search orgs or subscriptions..."
          className="pl-8 w-full md:w-[350px]"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      {/* --- Stats Section --- */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Organizations</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orgStats.totalOrgs}</div>
            {/* <p className="text-xs text-muted-foreground">+2 this month</p> */}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orgStats.activeSubscriptions}</div>
            {/* <p className="text-xs text-muted-foreground">+10 active</p> */}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Organizations in Trial</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orgStats.trialOrgs}</div>
            {/* <p className="text-xs text-muted-foreground">3 ending soon</p> */}
          </CardContent>
        </Card>
         <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Subscription Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${orgStats.avgSubValue.toLocaleString()}</div>
            {/* <p className="text-xs text-muted-foreground">Per month</p> */}
          </CardContent>
        </Card>
      </div>
      {/* --- End Stats Section --- */}

      <Tabs value={tab} onValueChange={setTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="orgs">Organizations</TabsTrigger>
          <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
          <TabsTrigger value="trial">Trial Management</TabsTrigger>
        </TabsList>

        <TabsContent value="orgs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>All Organizations</CardTitle>
              <CardDescription>Manage organizations and their details</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Org</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Plan</TableHead>
                    <TableHead>Users</TableHead>
                    <TableHead>Industry</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Last Active</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredOrgs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">No organizations found</TableCell>
                    </TableRow>
                  ) : (
                    filteredOrgs.map(org => (
                      <TableRow key={org.id}>
                        <TableCell>{org.name}</TableCell>
                        <TableCell>{org.status}</TableCell>
                        <TableCell>{org.plan}</TableCell>
                        <TableCell>{org.users_count}</TableCell>
                        <TableCell>{org.industry}</TableCell>
                        <TableCell>{formatDate(org.created_at)}</TableCell>
                        <TableCell>{formatDate(org.last_active)}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleViewOrgDetails(org)}>
                                <EyeIcon className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Org
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <UserPlus className="mr-2 h-4 w-4" />
                                Manage Users
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <Shield className="mr-2 h-4 w-4" />
                                {org.status === 'suspended' ? 'Activate' : 'Suspend'}
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subscriptions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>All Subscriptions</CardTitle>
              <CardDescription>Manage subscription plans and billing cycles</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Org</TableHead>
                    <TableHead>Plan</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Billing Cycle</TableHead>
                    <TableHead>Next Billing</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredSubscriptions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">No subscriptions found</TableCell>
                    </TableRow>
                  ) : (
                    filteredSubscriptions.map(sub => (
                      <TableRow key={sub.id}>
                        <TableCell>{sub.org_name}</TableCell>
                        <TableCell>{sub.plan}</TableCell>
                        <TableCell>{sub.status}</TableCell>
                        <TableCell>{formatCurrency(sub.amount)}</TableCell>
                        <TableCell>{sub.billing_cycle}</TableCell>
                        <TableCell>{formatDate(sub.next_billing_date)}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>
                                <EyeIcon className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Subscription
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <CreditCard className="mr-2 h-4 w-4" />
                                View Payment History
                              </DropdownMenuItem>
                              {sub.status === 'active' && (
                                <DropdownMenuItem className="text-amber-600">
                                  <Clock className="mr-2 h-4 w-4" />
                                  Change Plan
                                </DropdownMenuItem>
                              )}
                              {sub.status !== 'canceled' && (
                                <DropdownMenuItem className="text-red-600">
                                  <AlertTriangle className="mr-2 h-4 w-4" />
                                  Cancel Subscription
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trial" className="space-y-4">
          <TrialManagementCard />
        </TabsContent>
      </Tabs>

      {/* Drawer and Dialog Components */}
      <OrgDetailsDrawer 
        open={orgDetailsOpen}
        onOpenChange={setOrgDetailsOpen}
        org={selectedOrg}
      />
      
      <CreateOrgForm 
        open={showCreateOrgDialog}
        onOpenChange={setShowCreateOrgDialog}
        onSuccess={handleOrgCreated}
      />
    </div>
  );
} 