"use client"

import { She<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>lose } from "@/app/components/ui/sheet"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/app/components/ui/tabs"
import { Button } from "@/app/components/ui/button"
import { User, Users, CreditCard, Settings, LogIn, UserPlus, MoreHorizontal, ArrowRight, Check, Calendar, Clock, AlertTriangle, DollarSign, Download } from "lucide-react"
import { Card, CardHeader, CardTitle, CardContent } from "@/app/components/ui/card"
import { Badge } from "@/app/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/app/components/ui/avatar"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/app/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownM<PERSON>uTrigger } from "@/app/components/ui/dropdown-menu"
import { useToast } from "@/app/components/ui/use-toast"
import { ScrollArea } from "@/app/components/ui/scroll-area"
import { Switch } from "@/app/components/ui/switch"
import { Label } from "@/app/components/ui/label"
import { Textarea } from "@/app/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/app/components/ui/radio-group"
import { useRouter } from "next/navigation"
import { Input } from "@/app/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"

interface OrgDetailsDrawerProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  org: {
    id: string
    name: string
    domain?: string
    status: string
    plan: string
    created_at: string
    updated_at?: string
    branding?: any
    settings?: any
    users_count?: number
  } | null
}

export function OrgDetailsDrawer({ open, onOpenChange, org }: OrgDetailsDrawerProps) {
  const { toast } = useToast()
  const router = useRouter()
  
  if (!org) return null
  
  const handleImpersonate = () => {
    toast({
      title: "Impersonation Started",
      description: `You are now viewing as an admin of ${org.name}.`,
    })
    
    console.log(`Impersonating org: ${org.id}`, org)
  }
  
  const OverviewTab = () => (
    <TabsContent value="overview" className="space-y-6">
      <div className="p-4 bg-green-100 text-green-800 rounded-md mb-4">
        This drawer has been updated! The width is now set to max-w-7xl.
      </div>
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Org Details</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="space-y-2 text-sm">
              <div className="flex justify-between">
                <dt className="text-muted-foreground">Industry:</dt>
                <dd className="font-medium">{(org as any).industry || "Not specified"}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-muted-foreground">Total Users:</dt>
                <dd className="font-medium">{org.users_count || 27}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-muted-foreground">Subdomain:</dt>
                <dd className="font-medium flex items-center">
                  {org.domain || "Not set"} 
                  {org.domain && (
                    <Button variant="ghost" size="icon" className="h-4 w-4 ml-1">
                      <ArrowRight className="h-3 w-3" />
                    </Button>
                  )}
                </dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-muted-foreground">Created:</dt>
                <dd className="font-medium">{new Date(org.created_at).toLocaleDateString()}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-muted-foreground">Last Updated:</dt>
                <dd className="font-medium">{org.updated_at ? new Date(org.updated_at).toLocaleDateString() : "N/A"}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Subscription</CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="space-y-2 text-sm">
              <div className="flex justify-between">
                <dt className="text-muted-foreground">Plan:</dt>
                <dd>
                  <Badge 
                    className={
                      org.plan === 'enterprise'
                        ? 'bg-purple-100 text-purple-800'
                        : org.plan === 'professional'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800'
                    }
                  >
                    {org.plan.charAt(0).toUpperCase() + org.plan.slice(1)}
                  </Badge>
                </dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-muted-foreground">Status:</dt>
                <dd className="font-medium text-green-600">Active</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-muted-foreground">Billing Cycle:</dt>
                <dd className="font-medium">Annual</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-muted-foreground">Next Billing:</dt>
                <dd className="font-medium">May 15, 2024</dd>
              </div>
              <div>
                <Button variant="outline" size="sm" className="mt-2 w-full">
                  <CreditCard className="h-4 w-4 mr-2" />
                  View Full Subscription
                </Button>
              </div>
            </dl>
          </CardContent>
        </Card>
      </div>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Activity & Usage</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-4 grid-cols-3">
              <div className="bg-muted p-3 rounded-md">
                <div className="text-2xl font-bold">{org.users_count || 27}</div>
                <div className="text-xs text-muted-foreground">Active Users</div>
              </div>
              <div className="bg-muted p-3 rounded-md">
                <div className="text-2xl font-bold">142</div>
                <div className="text-xs text-muted-foreground">Quotes This Month</div>
              </div>
              <div className="bg-muted p-3 rounded-md">
                <div className="text-2xl font-bold">83</div>
                <div className="text-xs text-muted-foreground">Trips Completed</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-2 flex flex-row items-center justify-between">
          <CardTitle className="text-sm font-medium">Branding & Customization</CardTitle>
          <Button variant="outline" size="sm">Edit</Button>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-4 grid-cols-2">
              <div>
                <div className="text-sm font-medium mb-2">Logo</div>
                <div className="h-24 bg-muted rounded-md flex items-center justify-center">
                  {org.branding?.logo ? (
                    <img src={org.branding.logo} alt="Logo" className="max-h-20" />
                  ) : (
                    <div className="text-xs text-muted-foreground">No logo set</div>
                  )}
                </div>
              </div>
              <div>
                <div className="text-sm font-medium mb-2">Colors</div>
                <div className="flex gap-2">
                  <div className="w-8 h-8 rounded-full bg-primary"></div>
                  <div className="w-8 h-8 rounded-full bg-secondary"></div>
                  <div className="w-8 h-8 rounded-full bg-muted"></div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </TabsContent>
  )
  
  const UsersTab = () => (
    <TabsContent value="users" className="space-y-6">
      <div className="flex justify-between mb-4">
        <h3 className="text-lg font-medium">User Management</h3>
        <Button size="sm">
          <UserPlus className="mr-2 h-4 w-4" />
          Add User
        </Button>
      </div>
      
      <Card className="overflow-hidden">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Org Users</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Login</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[
                { id: 1, name: "Alice Smith", email: "<EMAIL>", role: "Admin", status: "Active", lastLogin: "2023-10-14T09:15:00Z" },
                { id: 2, name: "Bob Johnson", email: "<EMAIL>", role: "User", status: "Active", lastLogin: "2023-10-13T16:42:00Z" },
                { id: 3, name: "Charlie Brown", email: "<EMAIL>", role: "Editor", status: "Active", lastLogin: "2023-10-15T11:20:00Z" },
                { id: 4, name: "David Williams", email: "<EMAIL>", role: "Viewer", status: "Inactive", lastLogin: "2023-09-28T15:30:00Z" },
              ].map(user => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={`https://avatars.dicebear.com/api/initials/${user.name.split(' ').map(n => n[0]).join('')}.svg`} />
                        <AvatarFallback>{user.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-muted-foreground">{user.email}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="font-normal">
                      {user.role}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={user.status === "Active" ? "default" : "secondary"} className="font-normal">
                      {user.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {new Date(user.lastLogin).toLocaleDateString()}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem>Edit Role</DropdownMenuItem>
                        <DropdownMenuItem>Reset Password</DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className={user.status === "Active" ? "text-destructive" : ""}>
                          {user.status === "Active" ? "Deactivate" : "Activate"}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">User Roles & Permissions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="bg-muted p-4 rounded-lg">
                <h4 className="font-medium mb-2">Admin</h4>
                <p className="text-sm text-muted-foreground">Full access to all org settings, users, and data.</p>
              </div>
              <div className="bg-muted p-4 rounded-lg">
                <h4 className="font-medium mb-2">Editor</h4>
                <p className="text-sm text-muted-foreground">Can manage trips, quotes, and events but cannot change org settings.</p>
              </div>
              <div className="bg-muted p-4 rounded-lg">
                <h4 className="font-medium mb-2">User</h4>
                <p className="text-sm text-muted-foreground">Standard access to create and manage their own resources.</p>
              </div>
              <div className="bg-muted p-4 rounded-lg">
                <h4 className="font-medium mb-2">Viewer</h4>
                <p className="text-sm text-muted-foreground">Read-only access to view reports and dashboards.</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </TabsContent>
  )
  
  const SubscriptionTab = () => (
    <TabsContent value="subscription" className="space-y-6">
      <div className="flex justify-between mb-4">
        <h3 className="text-lg font-medium">Subscription Management</h3>
        <Button size="sm">
          <DollarSign className="mr-2 h-4 w-4" />
          Change Plan
        </Button>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Current Plan</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="p-4 rounded-lg border bg-card text-card-foreground">
              <div className="mb-4">
                <Badge className={`capitalize ${
                  org.plan === 'enterprise' ? 'bg-purple-100 text-purple-800' : 
                  org.plan === 'business' ? 'bg-purple-100 text-purple-800' : 
                  org.plan === 'professional' ? 'bg-blue-100 text-blue-800' : 
                  'bg-gray-100 text-gray-800'
                }`}>
                  {org.plan}
                </Badge>
              </div>
              <div className="mb-6">
                <div className="text-3xl font-bold mb-1">
                  ${org.plan === 'enterprise' ? 'Custom' : 
                     org.plan === 'business' ? '999' : 
                     org.plan === 'professional' ? '499' : '199'}
                  {org.plan !== 'enterprise' && <span className="text-sm font-normal text-muted-foreground"> / month</span>}
                </div>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  <Check className="mr-2 h-4 w-4 text-green-500" />
                  {org.plan === 'enterprise' ? 'Unlimited team members' : 
                   org.plan === 'business' ? 'Up to 10 team members' : 
                   org.plan === 'professional' ? 'Up to 5 team members' : 
                   'Up to 2 team members'}
                </div>
                <div className="flex items-center">
                  <Check className="mr-2 h-4 w-4 text-green-500" />
                  {org.plan === 'enterprise' ? 'Enterprise-grade booking management' : 
                   org.plan === 'business' ? 'Advanced booking management' : 
                   org.plan === 'professional' ? 'Advanced booking management' : 
                   'Basic booking management'}
                </div>
                <div className="flex items-center">
                  <Check className="mr-2 h-4 w-4 text-green-500" />
                  {org.plan === 'enterprise' ? 'Custom reporting suite' : 
                   org.plan === 'business' ? 'Custom dashboard' : 
                   org.plan === 'professional' ? 'Advanced analytics' : 
                   'Standard reporting'}
                </div>
                <div className="flex items-center">
                  <Check className="mr-2 h-4 w-4 text-green-500" />
                  {org.plan === 'enterprise' ? 'Dedicated support team' : 
                   org.plan === 'business' ? '24/7 priority support' : 
                   org.plan === 'professional' ? 'Priority support' : 
                   'Email support'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Billing Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <div className="text-sm font-medium">Payment Method</div>
                  <Button variant="link" size="sm" className="h-auto p-0">Update</Button>
                </div>
                <div className="flex items-center gap-2 p-3 border rounded-md">
                  <CreditCard className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">•••• •••• •••• 4242</div>
                    <div className="text-xs text-muted-foreground">Expires 12/2024</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Usage & Limits</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-1">
                <div className="text-sm font-medium">Team Members</div>
                <div className="text-sm text-muted-foreground">
                  {org.users_count || 27} / {
                    org.plan === 'enterprise' ? 'Unlimited' : 
                    org.plan === 'business' ? '10' : 
                    org.plan === 'professional' ? '5' : '2'
                  }
                </div>
              </div>
              <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                <div className={`h-2 ${
                  org.plan === 'enterprise' ? 'w-[10%] bg-green-500' : 
                  org.plan === 'business' && (org.users_count || 27) > 9 ? 'w-[100%] bg-red-500' :
                  org.plan === 'business' ? `w-[${Math.min(Math.round(((org.users_count || 27) / 10) * 100), 95)}%] bg-green-500` :
                  org.plan === 'professional' && (org.users_count || 27) > 4 ? 'w-[100%] bg-red-500' :
                  org.plan === 'professional' ? `w-[${Math.min(Math.round(((org.users_count || 27) / 5) * 100), 95)}%] bg-green-500` :
                  (org.users_count || 27) > 2 ? 'w-[100%] bg-red-500' :
                  `w-[${Math.min(Math.round(((org.users_count || 27) / 2) * 100), 95)}%] bg-green-500`
                }`}></div>
              </div>
              {((org.plan === 'essential' && (org.users_count || 27) > 2) ||
                (org.plan === 'professional' && (org.users_count || 27) > 5) ||
                (org.plan === 'business' && (org.users_count || 27) > 10)) && (
                <div className="flex items-center gap-1 mt-1 text-xs text-red-600">
                  <AlertTriangle className="h-3 w-3" />
                  Over limit. Consider upgrading.
                </div>
              )}
            </div>
            
            <div>
              <div className="flex justify-between mb-1">
                <div className="text-sm font-medium">Features</div>
                <div className="text-sm text-muted-foreground">{
                  org.plan === 'enterprise' ? 'All features + Custom development' : 
                  org.plan === 'business' ? 'Advanced features' : 
                  org.plan === 'professional' ? 'Enhanced features' : 
                  'Basic features'
                }</div>
              </div>
              <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                <div className={`h-2 ${
                  org.plan === 'enterprise' ? 'w-[100%] bg-green-500' : 
                  org.plan === 'business' ? 'w-[75%] bg-green-500' : 
                  org.plan === 'professional' ? 'w-[50%] bg-green-500' : 
                  'w-[25%] bg-green-500'
                }`}></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </TabsContent>
  )
  
  const SettingsTab = () => (
    <TabsContent value="settings" className="space-y-6">
      <div className="flex justify-between mb-4">
        <h3 className="text-lg font-medium">Org Settings</h3>
        <Button size="sm">Save Changes</Button>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Basic Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="org-name">Org Name</Label>
                <Input id="org-name" defaultValue={org.name} />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="org-domain">Domain</Label>
                <div className="flex">
                  <Input id="org-domain" defaultValue={org.domain?.split('.')[0]} className="rounded-r-none" />
                  <div className="flex items-center px-3 border border-l-0 rounded-r-md bg-muted">
                    .transflow.app
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Status & Security</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Org Status</div>
                  <div className="text-sm text-muted-foreground">Enable or disable this org</div>
                </div>
                <div>
                  <RadioGroup defaultValue={org.status} className="flex gap-4">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="active" id="status-active" />
                      <Label htmlFor="status-active">Active</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="suspended" id="status-suspended" />
                      <Label htmlFor="status-suspended">Suspended</Label>
                    </div>
                  </RadioGroup>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium">Require 2FA</div>
                  <div className="text-sm text-muted-foreground">Enforce two-factor authentication</div>
                </div>
                <Switch defaultChecked={org.settings?.require2FA} />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Card className="border-red-200">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-red-600">Danger Zone</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium">Delete Org</div>
                <div className="text-sm text-red-600">This action cannot be undone</div>
              </div>
              <Button variant="destructive" size="sm">
                Delete Org
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </TabsContent>
  )
  
  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="!fixed !right-0 !top-0 !h-full !w-[95vw] !max-w-[95vw] !p-0 !m-0 !rounded-none !border-l-4 !border-l-blue-500 !bg-background !shadow-2xl !flex !flex-col !animate-in !data-[state=open]:slide-in-from-right-80">
        <SheetHeader className="flex flex-row items-center justify-between px-6 pt-6 pb-2 border-b">
          <div>
            <SheetTitle className="text-2xl font-bold">{org.name}</SheetTitle>
            <div className="text-sm text-muted-foreground">{org.domain || "No domain set"}</div>
            <div className="flex gap-2 mt-2">
              <span className="px-2 py-0.5 rounded bg-primary/10 text-primary text-xs font-medium">{org.status}</span>
              <span className="px-2 py-0.5 rounded bg-secondary/10 text-secondary text-xs font-medium">{org.plan}</span>
            </div>
          </div>
          <div className="flex gap-2 items-center">
            <Button size="sm" variant="outline" className="gap-1" title="Impersonate Org" onClick={handleImpersonate}>
              <LogIn className="h-4 w-4" /> Impersonate
            </Button>
            <SheetClose asChild>
              <Button size="icon" variant="ghost" className="ml-2">
                <span className="sr-only">Close</span>
                ×
              </Button>
            </SheetClose>
          </div>
        </SheetHeader>
        <div className="flex-1 flex flex-col overflow-hidden">
          <Tabs defaultValue="overview" className="flex-1 flex flex-col">
            <TabsList className="flex gap-2 px-6 pt-4 pb-2 border-b bg-background sticky top-0 z-10">
              <TabsTrigger value="overview" className="flex gap-1 items-center"><User className="h-4 w-4" /> Overview</TabsTrigger>
              <TabsTrigger value="users" className="flex gap-1 items-center"><Users className="h-4 w-4" /> Users</TabsTrigger>
              <TabsTrigger value="subscription" className="flex gap-1 items-center"><CreditCard className="h-4 w-4" /> Subscription</TabsTrigger>
              <TabsTrigger value="settings" className="flex gap-1 items-center"><Settings className="h-4 w-4" /> Settings</TabsTrigger>
            </TabsList>
            <div className="flex-1 overflow-y-auto">
              <div className="p-6">
                <OverviewTab />
                <UsersTab />
                <SubscriptionTab />
                <SettingsTab />
              </div>
            </div>
          </Tabs>
        </div>
      </SheetContent>
    </Sheet>
  )
} 