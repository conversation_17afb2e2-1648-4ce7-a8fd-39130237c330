"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle, CardDescription, CardFooter } from "@/app/components/ui/card"
import { But<PERSON> } from "@/app/components/ui/button"
import { Switch } from "@/app/components/ui/switch"
import { RefreshCw, Server, ListOrdered, CalendarClock, Wrench } from "lucide-react"

export default function SuperAdminOperationsPage() {
  // Mock state for operations
  const [maintenanceMode, setMaintenanceMode] = useState(false)
  const [jobQueue, setJobQueue] = useState([
    { id: 1, name: "Send Weekly Reports", status: "pending", scheduled: "2024-06-30 02:00" },
    { id: 2, name: "Purge Old Logs", status: "completed", scheduled: "2024-06-28 01:00" },
    { id: 3, name: "Sync Billing Data", status: "running", scheduled: "2024-06-28 03:00" },
  ])
  const [scheduledTasks, setScheduledTasks] = useState([
    { id: 1, name: "Nightly Backup", nextRun: "2024-06-29 02:00" },
    { id: 2, name: "Subscription Renewal", nextRun: "2024-06-29 04:00" },
  ])
  const [systemHealth, setSystemHealth] = useState({
    api: "healthy",
    db: "healthy",
    storage: "degraded",
    queue: "healthy",
  })

  // TODO: Replace with Supabase integration and validation

  return (
    <div className="space-y-8 max-w-3xl mx-auto p-6">
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold tracking-tight">Operations</h1>
        <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
          Monitor system health, manage jobs, and control maintenance mode for your platform.
        </p>
      </div>

      {/* System Health */}
      <Card>
        <CardHeader className="flex flex-row items-center gap-3 pb-2">
          <Server className="h-5 w-5 text-primary" />
          <CardTitle className="text-lg font-semibold">System Health</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex items-center justify-between">
            <span>API</span>
            <span className={systemHealth.api === "healthy" ? "text-green-600" : "text-red-600"}>{systemHealth.api}</span>
          </div>
          <div className="flex items-center justify-between">
            <span>Database</span>
            <span className={systemHealth.db === "healthy" ? "text-green-600" : "text-red-600"}>{systemHealth.db}</span>
          </div>
          <div className="flex items-center justify-between">
            <span>Storage</span>
            <span className={systemHealth.storage === "healthy" ? "text-green-600" : "text-amber-600"}>{systemHealth.storage}</span>
          </div>
          <div className="flex items-center justify-between">
            <span>Job Queue</span>
            <span className={systemHealth.queue === "healthy" ? "text-green-600" : "text-red-600"}>{systemHealth.queue}</span>
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline" size="sm" onClick={() => { /* TODO: refresh health */ }}>
            <RefreshCw className="h-4 w-4 mr-1" /> Refresh
          </Button>
        </CardFooter>
      </Card>

      {/* Job Queue */}
      <Card>
        <CardHeader className="flex flex-row items-center gap-3 pb-2">
          <ListOrdered className="h-5 w-5 text-primary" />
          <CardTitle className="text-lg font-semibold">Job Queue</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <table className="w-full text-sm">
            <thead>
              <tr className="text-muted-foreground">
                <th className="text-left font-medium">Job</th>
                <th className="text-left font-medium">Status</th>
                <th className="text-left font-medium">Scheduled</th>
              </tr>
            </thead>
            <tbody>
              {jobQueue.map(job => (
                <tr key={job.id}>
                  <td className="py-2 font-semibold">{job.name}</td>
                  <td className="py-2 capitalize">{job.status}</td>
                  <td className="py-2">{job.scheduled}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </CardContent>
        <CardFooter>
          <Button variant="outline" size="sm" onClick={() => { /* TODO: refresh jobs */ }}>
            <RefreshCw className="h-4 w-4 mr-1" /> Refresh
          </Button>
        </CardFooter>
      </Card>

      {/* Scheduled Tasks */}
      <Card>
        <CardHeader className="flex flex-row items-center gap-3 pb-2">
          <CalendarClock className="h-5 w-5 text-primary" />
          <CardTitle className="text-lg font-semibold">Scheduled Tasks</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <table className="w-full text-sm">
            <thead>
              <tr className="text-muted-foreground">
                <th className="text-left font-medium">Task</th>
                <th className="text-left font-medium">Next Run</th>
              </tr>
            </thead>
            <tbody>
              {scheduledTasks.map(task => (
                <tr key={task.id}>
                  <td className="py-2 font-semibold">{task.name}</td>
                  <td className="py-2">{task.nextRun}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </CardContent>
        <CardFooter>
          <Button variant="outline" size="sm" onClick={() => { /* TODO: refresh tasks */ }}>
            <RefreshCw className="h-4 w-4 mr-1" /> Refresh
          </Button>
        </CardFooter>
      </Card>

      {/* Maintenance Mode */}
      <Card>
        <CardHeader className="flex flex-row items-center gap-3 pb-2">
          <Wrench className="h-5 w-5 text-primary" />
          <CardTitle className="text-lg font-semibold">Maintenance Mode</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm">Enable Maintenance Mode</span>
            <Switch checked={maintenanceMode} onCheckedChange={setMaintenanceMode} />
          </div>
          <div className="text-xs text-muted-foreground">
            When enabled, only super admins can access the platform. All other users will see a maintenance notice.
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="default">Update Maintenance Mode</Button>
        </CardFooter>
      </Card>
      {/* TODO: Add Supabase integration, validation, and error handling */}
    </div>
  )
} 