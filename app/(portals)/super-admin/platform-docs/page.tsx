"use client"

import { useAuth } from '@/lib/auth/context'
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import Link from "next/link"
import { FileText } from "lucide-react"

const docs = [
  { name: "SaaS User Roles", file: "Saas-User-Roles.md" },
  { name: "SaaS DB Structure", file: "Saas-DB-doc.md" },
  { name: "Transflow SaaS Database", file: "transflow-saas-database.md" },
  { name: "SaaS Implementation Guide", file: "saas-implementation-guide.md" },
  { name: "WWMS SaaS Master Documentation", file: "WWMS-SaaS-Master-Documentation.md" },
  { name: "API Standards", file: "API-STANDARDS.md" },
  { name: "API Reference", file: "api.md" },
  { name: "Auth Lessons Learned", file: "auth-lessons-learned.md" },
  { name: "Dual Model Strategy", file: "dual-model-strategy.md" },
  { name: "WWMS SaaS Platform Model", file: "wwms-saas-platform-model.md" },
]

export default function PlatformDocsPage() {
  const { user, loading, initialized } = useAuth()

  if (loading || !initialized) {
    return <div className="p-8">Loading...</div>
  }
  if (!user || !user.roles?.includes('SUPER_ADMIN')) {
    return <div className="p-8 text-red-600">Access denied. Super Admins only.</div>
  }

  return (
    <div className="container py-8 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" /> Platform Documentation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2">
            {docs.map(doc => (
              <li key={doc.file}>
                <Link href={`/super-admin/platform-docs/view?file=${encodeURIComponent(doc.file)}`} className="text-primary hover:underline flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  {doc.name}
                </Link>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>
    </div>
  )
} 