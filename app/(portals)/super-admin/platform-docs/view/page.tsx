"use client"

import { useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useAuth } from '@/lib/auth/context'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { FileText } from "lucide-react"
import Link from "next/link"
import ReactMarkdown from 'react-markdown'

const allowedDocs = [
  "Saas-User-Roles.md",
  "Saas-DB-doc.md",
  "transflow-saas-database.md",
  "saas-implementation-guide.md",
  "WWMS-SaaS-Master-Documentation.md",
  "API-STANDARDS.md",
  "api.md",
  "auth-lessons-learned.md",
  "dual-model-strategy.md",
  "wwms-saas-platform-model.md"
]

export default function PlatformDocViewPage() {
  const { user, loading, initialized } = useAuth()
  const params = useSearchParams()
  const file = params.get('file')
  const [content, setContent] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!file || !allowedDocs.includes(file)) {
      setError('Invalid or unauthorized document.')
      return
    }
    fetch(`/docs/${file}`)
      .then(res => {
        if (!res.ok) throw new Error('File not found')
        return res.text()
      })
      .then(setContent)
      .catch(() => setError('Could not load document.'))
  }, [file])

  if (loading || !initialized) {
    return <div className="p-8">Loading...</div>
  }
  if (!user || !user.roles?.includes('SUPER_ADMIN')) {
    return <div className="p-8 text-red-600">Access denied. Super Admins only.</div>
  }
  if (error) {
    return <div className="p-8 text-red-600">{error}</div>
  }
  if (!content) {
    return <div className="p-8">Loading document...</div>
  }

  return (
    <div className="container py-8 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            <span>{file}</span>
          </CardTitle>
          <Link href="/super-admin/platform-docs" className="text-sm text-primary hover:underline mt-2">Back to Documentation</Link>
        </CardHeader>
        <CardContent>
          <div className="prose max-w-none">
            <ReactMarkdown>{content}</ReactMarkdown>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 