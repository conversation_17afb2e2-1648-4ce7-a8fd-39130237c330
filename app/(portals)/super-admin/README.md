# Super Admin User Management

This directory contains the super admin user management features for the application. These features are only accessible to users with the `SUPER_ADMIN` role.

## Features

### User Management
- List all users in the system
- View user details
- Edit user information
- Assign/remove users from tenants
- Update user roles
- Delete users

### Tenant Management
- List all tenants
- View tenant details
- Create/edit tenants
- Manage tenant users

## API Endpoints

### Users
- `GET /api/super-admin/users` - List all users
- `GET /api/super-admin/users/[userId]` - Get user details
- `PATCH /api/super-admin/users/[userId]` - Update user
- `DELETE /api/super-admin/users/[userId]` - Delete user

### Health Check
- `GET /api/super-admin/health` - Check system health

## Security

All super admin routes are protected by middleware that verifies the user has the `SUPER_ADMIN` role. The middleware also ensures proper CORS headers and rate limiting are in place.

## Development

When developing super admin features:

1. Make sure to test with a user that has the `SUPER_ADMIN` role
2. Follow the principle of least privilege
3. Always validate and sanitize user input
4. Use the Supabase client with the appropriate permissions
5. Follow the existing patterns for API routes and components

## Testing

To test the super admin features:

1. Log in as a user with the `SUPER_ADMIN` role
2. Navigate to `/super-admin`
3. Use the UI to manage users and tenants
4. Test the API endpoints using a tool like Postman or curl

## Error Handling

All API endpoints return appropriate HTTP status codes and error messages. The UI displays these errors to the user in a user-friendly way.

## Logging

All super admin actions are logged for auditing purposes. The logs include:
- The user who performed the action
- The action that was performed
- The timestamp of the action
- Any relevant data or context
