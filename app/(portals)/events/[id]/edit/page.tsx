"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { Textarea } from "@/app/components/ui/textarea"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { useParams, useRouter } from "next/navigation"
import { Label } from "@/app/components/ui/label"
import { DatePicker } from "@/app/components/ui/date-picker"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"

export default function EditEventPage() {
  const params = useParams()
  const router = useRouter()
  const eventId = params.id as string

  // Mock data - replace with API call
  const event = {
    id: eventId,
    title: "Annual Tech Conference 2024",
    description: "Annual technology conference bringing together industry leaders and innovators.",
    startDate: new Date("2024-06-15"),
    endDate: new Date("2024-06-17"),
    eventType: "conference",
    expectedAttendees: 500,
    location: "Convention Center, San Francisco",
    status: "planning",
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    // TODO: Implement form submission
    router.push(`/events/${eventId}`)
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-x-4">
          <Link href={`/customer/events/${eventId}`}>
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h2 className="text-2xl font-bold tracking-tight">Edit Event</h2>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card className="p-6">
          <div className="grid gap-6">
            <div className="grid gap-2">
              <Label htmlFor="title">Event Title</Label>
              <Input
                id="title"
                defaultValue={event.title}
                placeholder="Enter event title"
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                defaultValue={event.description}
                placeholder="Enter event description"
              />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="grid gap-2">
                <Label>Start Date</Label>
                <DatePicker
                  date={event.startDate}
                  onSelect={(date) => {
                    // TODO: Implement date change handler
                    console.log('Start date changed:', date)
                  }}
                />
              </div>
              <div className="grid gap-2">
                <Label>End Date</Label>
                <DatePicker
                  date={event.endDate}
                  onSelect={(date) => {
                    // TODO: Implement date change handler
                    console.log('End date changed:', date)
                  }}
                />
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="grid gap-2">
                <Label htmlFor="type">Event Type</Label>
                <Select defaultValue={event.eventType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select event type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="conference">Conference</SelectItem>
                    <SelectItem value="meeting">Meeting</SelectItem>
                    <SelectItem value="workshop">Workshop</SelectItem>
                    <SelectItem value="seminar">Seminar</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="status">Status</Label>
                <Select defaultValue={event.status}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="planning">Planning</SelectItem>
                    <SelectItem value="confirmed">Confirmed</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="grid gap-2">
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  defaultValue={event.location}
                  placeholder="Enter event location"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="attendees">Expected Attendees</Label>
                <Input
                  id="attendees"
                  type="number"
                  defaultValue={event.expectedAttendees}
                  placeholder="Enter expected number of attendees"
                />
              </div>
            </div>
          </div>
        </Card>

        <div className="flex justify-end gap-x-2">
          <Link href={`/events/${eventId}`}>
            <Button variant="outline">Cancel</Button>
          </Link>
          <Button type="submit">Save Changes</Button>
        </div>
      </form>
    </div>
  )
} 