'use client';

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/app/components/ui/tabs';
import { Input } from '@/app/components/ui/input';
import { Label } from '@/app/components/ui/label';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader,
  TableRow 
} from '@/app/components/ui/table';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/app/components/ui/select';
import { AlertCircle, CheckCircle, RefreshCw } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/app/components/ui/alert';
import { toast } from 'sonner';

// Add stub functions that will be implemented
const getAuthStatus = async () => {
  const response = await fetch('/api/debug/auth-status');
  if (!response.ok) {
    throw new Error('Failed to fetch auth status');
  }
  return response.json();
};

const sendFixRoleRequest = async (email: string, password?: string) => {
  const response = await fetch('/api/auth/fix-role-errors', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, password })
  });
  return response.json();
};

// Stub functions for other panels
const getQuotes = async () => [];
const getQuoteById = async (id: string) => ({});
const getAffiliates = async () => [];
const assignAffiliateToQuote = async (quoteId: string, affiliateId: string) => ({ success: true });
const createTestQuoteOffer = async () => ({ success: true });
const assignBostonAffiliate = async () => ({ success: true });

// Function to fix roles for all users
const fixRoleMismatches = async () => {
  try {
    const response = await fetch('/api/auth/fix-roles', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Failed to fix roles: ${error.message || response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fixing roles:', error);
    throw error;
  }
};

function AuthDebugPanel() {
  const [authStatus, setAuthStatus] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [role, setRole] = useState('AFFILIATE');
  const [isFixing, setIsFixing] = useState(false);
  
  const fetchAuthStatus = async () => {
    setIsLoading(true);
    try {
      const data = await getAuthStatus();
      setAuthStatus(data);
    } catch (error) {
      console.error('Error fetching auth status:', error);
      toast.error('Failed to fetch auth status');
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleFixRoleErrors = async () => {
    if (!email) {
      toast.error('Email is required');
      return;
    }
    
    setIsFixing(true);
    try {
      const result = await sendFixRoleRequest(email, password);
      if (result.success) {
        toast.success('Successfully fixed role errors for ' + email);
        fetchAuthStatus(); // Refresh data
      } else {
        toast.error(result.error || 'Failed to fix role errors');
      }
    } catch (error: any) {
      toast.error('Error: ' + (error.message || 'Unknown error'));
    } finally {
      setIsFixing(false);
    }
  };
  
  // Fetch auth status on component mount
  useEffect(() => {
    fetchAuthStatus();
  }, []);
  
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-xl font-bold">Auth Status & Registration Debug</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col space-y-6">
          {/* Fix Registration Issues */}
          <div className="space-y-4">
            <div className="grid gap-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input 
                    id="email" 
                    value={email} 
                    onChange={(e) => setEmail(e.target.value)} 
                    placeholder="<EMAIL>" 
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Password (optional)</Label>
                  <Input 
                    id="password" 
                    type="password" 
                    value={password} 
                    onChange={(e) => setPassword(e.target.value)} 
                    placeholder="Only needed for new accounts" 
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="role">Role</Label>
                  <Select value={role} onValueChange={setRole}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="AFFILIATE">Affiliate</SelectItem>
                      <SelectItem value="CLIENT">Client</SelectItem>
                      <SelectItem value="SUPER_ADMIN">Super Admin</SelectItem>
                      <SelectItem value="ADMIN">Admin</SelectItem>
                      <SelectItem value="EVENT_MANAGER">Event Manager</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-end">
                  <Button 
                    onClick={handleFixRoleErrors} 
                    disabled={isFixing || !email}
                    className="w-full"
                  >
                    {isFixing ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : null}
                    Fix Registration Issues
                  </Button>
                </div>
              </div>
            </div>
          </div>
          
          {/* Refresh Auth Status */}
          <div className="flex justify-between">
            <h3 className="text-lg font-semibold">Auth Diagnostics</h3>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={fetchAuthStatus} 
              disabled={isLoading}
            >
              {isLoading ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <RefreshCw className="h-4 w-4 mr-2" />}
              Refresh
            </Button>
          </div>
          
          {/* Role Mismatch Stats */}
          {authStatus?.roleMismatches && (
            <div className="grid grid-cols-3 gap-4">
              <Card className="bg-muted">
                <CardContent className="pt-6">
                  <div className="text-center">
                    <p className="text-sm font-medium">Total Users</p>
                    <p className="text-3xl font-bold">{authStatus.roleMismatches.total_users}</p>
                  </div>
                </CardContent>
              </Card>
              <Card className={authStatus.roleMismatches.missing_profiles > 0 ? "bg-destructive/10" : "bg-muted"}>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <p className="text-sm font-medium">Missing Profiles</p>
                    <p className="text-3xl font-bold">{authStatus.roleMismatches.missing_profiles}</p>
                  </div>
                </CardContent>
              </Card>
              <Card className={authStatus.roleMismatches.role_mismatches > 0 ? "bg-destructive/10" : "bg-muted"}>
                <CardContent className="pt-6">
                  <div className="text-center">
                    <p className="text-sm font-medium">Role Mismatches</p>
                    <p className="text-3xl font-bold">{authStatus.roleMismatches.role_mismatches}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
          
          {/* Recent Users */}
          {authStatus?.recentUsers?.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-2">Recent Users</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Email</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Role (App Metadata)</TableHead>
                    <TableHead>Roles (User Metadata)</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {authStatus.recentUsers.map((user: any) => (
                    <TableRow key={user.id}>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>{new Date(user.createdAt).toLocaleString()}</TableCell>
                      <TableCell>{user.appMetadata?.role || 'Not set'}</TableCell>
                      <TableCell>
                        {user.userMetadata?.roles 
                          ? (Array.isArray(user.userMetadata.roles) 
                              ? user.userMetadata.roles.join(', ') 
                              : user.userMetadata.roles) 
                          : 'Not set'}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
          
          {/* Recent Logs */}
          {authStatus?.logs?.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-2">Recent Logs</h3>
              <div className="max-h-80 overflow-y-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Level</TableHead>
                      <TableHead>Message</TableHead>
                      <TableHead>Created</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {authStatus.logs.map((log: any) => (
                      <TableRow key={log.id}>
                        <TableCell className={
                          log.level === 'error' ? 'text-destructive font-medium' : 
                          log.level === 'warning' ? 'text-yellow-600 font-medium' : ''
                        }>
                          {log.level}
                        </TableCell>
                        <TableCell>
                          <div className="max-w-md truncate">
                            {log.message}
                          </div>
                        </TableCell>
                        <TableCell>{new Date(log.created_at).toLocaleString()}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

function QuotesDebugPanel() {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-xl font-bold">Quotes Debug</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col space-y-4">
          <Alert>
            <AlertTitle>Quotes Debug Panel</AlertTitle>
            <AlertDescription>
              This panel will contain tools for managing and debugging quotes. Coming soon.
            </AlertDescription>
          </Alert>
        </div>
      </CardContent>
    </Card>
  );
}

function AffiliatesDebugPanel() {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-xl font-bold">Affiliates Debug</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col space-y-4">
          <Alert>
            <AlertTitle>Affiliates Debug Panel</AlertTitle>
            <AlertDescription>
              This panel will contain tools for managing and debugging affiliates. Coming soon.
            </AlertDescription>
          </Alert>
        </div>
      </CardContent>
    </Card>
  );
}

export default function Debug() {
  return (
    <div className="container mx-auto py-10">
      <h1 className="text-2xl font-bold mb-6">Admin Debug Tools</h1>
      
      <Tabs defaultValue="auth">
        <TabsList className="mb-4">
          <TabsTrigger value="auth">Auth & Registration</TabsTrigger>
          <TabsTrigger value="quotes">Quotes</TabsTrigger>
          <TabsTrigger value="affiliates">Affiliates</TabsTrigger>
        </TabsList>
        
        <TabsContent value="auth">
          <AuthDebugPanel />
        </TabsContent>
        
        <TabsContent value="quotes">
          <QuotesDebugPanel />
        </TabsContent>
        
        <TabsContent value="affiliates">
          <AffiliatesDebugPanel />
        </TabsContent>
      </Tabs>
    </div>
  );
} 