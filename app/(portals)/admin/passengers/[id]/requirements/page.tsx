"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { ArrowLeft, Save } from "lucide-react"
import Link from "next/link"
import { useParams, useRouter } from "next/navigation"
import { useState, useEffect } from "react"
import { Checkbox } from "@/app/components/ui/checkbox"
import { Label } from "@/app/components/ui/label"
import { toast } from "sonner"

type PassengerData = {
  id: string
  name: string
  requirements: {
    accessibility?: string[]
  }
}

// Mock function - replace with actual API call
const getPassenger = async (id: string): Promise<PassengerData> => {
  return {
    id,
    name: "<PERSON>",
    requirements: {
      accessibility: ["wheelchair"],
    },
  }
}

const accessibilityOptions = [
  { id: "wheelchair", label: "Wheelchair Access" },
  { id: "assistance", label: "Personal Assistance Required" },
  { id: "mobility_aid", label: "Mobility Aid" },
  { id: "easy_access", label: "Easy Access Seating" },
  { id: "service_animal", label: "Service Animal Accommodation" },
]

export default function ManageRequirementsPage() {
  const params = useParams()
  const router = useRouter()
  const passengerId = params.id as string
  const [passenger, setPassenger] = useState<PassengerData | null>(null)
  const [selectedRequirements, setSelectedRequirements] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)

  useEffect(() => {
    const fetchPassenger = async () => {
      try {
        const data = await getPassenger(passengerId)
        setPassenger(data)
        setSelectedRequirements(data.requirements.accessibility || [])
      } catch (error) {
        console.error("Failed to fetch passenger:", error)
        toast.error("Failed to load passenger requirements")
      } finally {
        setIsLoading(false)
      }
    }

    fetchPassenger()
  }, [passengerId])

  const handleSave = async () => {
    setIsSaving(true)
    try {
      // Mock API call - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      toast.success("Requirements updated successfully")
      router.push(`/customer/passengers/${passengerId}`)
    } catch (error) {
      console.error("Failed to update requirements:", error)
      toast.error("Failed to update requirements")
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="container space-y-6 p-6">
        <div className="flex items-center gap-x-3">
          <Link href={`/customer/passengers/${passengerId}`}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <h2 className="text-2xl font-bold tracking-tight">Manage Requirements</h2>
        </div>
        <div className="h-[400px] flex items-center justify-center">
          <p className="text-muted-foreground">Loading requirements...</p>
        </div>
      </div>
    )
  }

  if (!passenger) {
    return (
      <div className="container space-y-6 p-6">
        <div className="flex items-center gap-x-3">
          <Link href="/customer/passengers">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <h2 className="text-2xl font-bold tracking-tight">Manage Requirements</h2>
        </div>
        <Card>
          <CardContent className="p-6">
            <p className="text-muted-foreground">Passenger not found.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-x-3">
          <Link href={`/customer/passengers/${passengerId}`}>
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Manage Requirements</h2>
            <p className="text-muted-foreground">Update accessibility requirements for {passenger.name}</p>
          </div>
        </div>
        <Button onClick={handleSave} disabled={isSaving}>
          {isSaving ? (
            "Saving..."
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </>
          )}
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Accessibility Requirements</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-4">
            {accessibilityOptions.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <Checkbox
                  id={option.id}
                  checked={selectedRequirements.includes(option.id)}
                  onCheckedChange={(checked) => {
                    setSelectedRequirements(prev =>
                      checked
                        ? [...prev, option.id]
                        : prev.filter(id => id !== option.id)
                    )
                  }}
                />
                <Label htmlFor={option.id}>{option.label}</Label>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 