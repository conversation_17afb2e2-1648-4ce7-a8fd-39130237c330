"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Checkbox } from "@/app/components/ui/checkbox"
import { Input } from "@/app/components/ui/input"
import { Label } from "@/app/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { 
  Card, 
  CardContent,
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/app/components/ui/card"
import { useToast } from "@/app/components/ui/use-toast"
import { updateQuoteStatus } from "@/app/lib/enhanced-quote-api"

interface QuoteStatusDebuggerProps {
  onRefresh?: () => Promise<void>
}

export const QuoteStatusDebugger = ({ onRefresh }: QuoteStatusDebuggerProps) => {
  const [quoteId, setQuoteId] = useState("")
  const [status, setStatus] = useState("pending")
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()

  const handleUpdateStatus = async () => {
    if (!quoteId) {
      toast({
        title: "Error",
        description: "Please enter a quote ID",
        variant: "destructive"
      })
      return
    }

    try {
      setLoading(true)
      const response = await fetch(`/api/admin/quotes/${quoteId}/status`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ status })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Failed to update quote status")
      }

      toast({
        title: "Success",
        description: `Quote status updated to ${status}`
      })

      // Refresh quotes list if callback is provided
      if (onRefresh) {
        await onRefresh()
      }
    } catch (error) {
      console.error("Error updating quote status:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="border-red-300 bg-red-50 mt-6">
      <CardHeader>
        <CardTitle className="text-red-800">Quote Status Debugger</CardTitle>
        <CardDescription className="text-red-700">
          Tool for manually updating quote statuses (admin only)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="quote-id" className="text-red-700">Quote ID</Label>
            <Input
              id="quote-id"
              placeholder="Enter quote ID"
              value={quoteId}
              onChange={(e) => setQuoteId(e.target.value)}
              className="border-red-300"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="quote-status" className="text-red-700">Status</Label>
            <Select 
              defaultValue={status} 
              onValueChange={setStatus}
            >
              <SelectTrigger id="quote-status" className="border-red-300">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="rate_requested">Rate Requested</SelectItem>
                <SelectItem value="fixed_offer">Fixed Offer</SelectItem>
                <SelectItem value="quote_assigned">Assigned</SelectItem>
                <SelectItem value="accepted">Accepted</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <div className="flex items-center space-x-2 pt-4">
          <Checkbox id="force-update" />
          <Label htmlFor="force-update" className="text-red-700">
            Force update (bypass workflow checks)
          </Label>
        </div>
      </CardContent>
      <CardFooter>
        <Button 
          variant="destructive" 
          onClick={handleUpdateStatus} 
          disabled={loading}
          className="w-full"
        >
          {loading ? "Updating..." : "Update Status"}
        </Button>
      </CardFooter>
    </Card>
  )
}

/**
 * Component to fix quotes stuck in pending state
 */
export const QuoteStatusFixerTool = ({ onRefresh }: QuoteStatusDebuggerProps) => {
  const [stuckQuotes, setStuckQuotes] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [scanning, setScanning] = useState(false)
  const [selectedIds, setSelectedIds] = useState<string[]>([])
  const [dbStatus, setDbStatus] = useState<string>("")
  const { toast } = useToast()

  const handleScanForStuckQuotes = async () => {
    try {
      setScanning(true)
      const response = await fetch('/api/admin/quotes')
      
      if (!response.ok) {
        throw new Error(`Failed to fetch quotes: ${response.statusText}`)
      }
      
      const data = await response.json()
      const quotes = data.quotes || []
      
      // Log some stats about the quotes
      console.log(`Total quotes: ${quotes.length}`);
      
      // Count by status
      const statusCounts = quotes.reduce((acc: Record<string, number>, quote: any) => {
        const status = quote.status || 'unknown';
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {});
      
      console.log('Status counts from API:', statusCounts);
      
      // Check for quotes with affiliate_responses
      const quotesWithResponses = quotes.filter((q: any) => 
        q.affiliate_responses && q.affiliate_responses.length > 0
      );
      
      console.log(`Quotes with affiliate responses: ${quotesWithResponses.length}`);
      
      // Look for quotes that might be stuck
      // We're checking quotes that:
      // 1. Have affiliate_responses but are still in pending status
      // 2. Have timeline entries suggesting they were sent to affiliates but status is still pending
      
      const potentiallyStuckQuotes = quotes.filter((q: any) => {
        // Check if the quote has affiliate_responses but is still in pending status
        const hasMismatchedStatus = 
          (q.status.toLowerCase() === 'pending' || q.status.toLowerCase() === 'pending_quote') && 
          q.affiliate_responses && 
          q.affiliate_responses.length > 0
          
        // Check if the quote has a timeline entry suggesting it was sent to affiliates
        const hasTimelineIndication = 
          q.timeline && 
          q.timeline.some((t: any) => 
            t.action && 
            (t.action.includes('sent_to_affiliates') || 
             t.action.includes('quote_ready') || 
             t.action.includes('rate_requested'))
          )
          
        return hasMismatchedStatus || hasTimelineIndication
      })
      
      setStuckQuotes(potentiallyStuckQuotes)
      
      if (potentiallyStuckQuotes.length === 0) {
        toast({
          title: "No stuck quotes found",
          description: "No quotes appear to be stuck in an incorrect status."
        })
      } else {
        toast({
          title: "Potentially stuck quotes found",
          description: `Found ${potentiallyStuckQuotes.length} quotes that may need status correction.`
        })
      }
      
      // Also check direct database status
      await checkDatabaseStatus();
      
    } catch (error) {
      console.error("Error scanning for stuck quotes:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      })
    } finally {
      setScanning(false)
    }
  }

  const checkDatabaseStatus = async () => {
    try {
      // Try to directly check the database for quote statuses to verify what's happening
      const response = await fetch('/api/admin/db-check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          table: 'quotes',
          query: 'SELECT status, COUNT(*) as count FROM quotes GROUP BY status'
        })
      });
      
      if (!response.ok) {
        console.error('Failed to check database status:', response.statusText);
        return;
      }
      
      const data = await response.json();
      console.log('Direct DB check result:', data);
      
      if (data.error) {
        console.error('DB check error:', data.error);
        setDbStatus(`Error: ${data.error}`);
        return;
      }
      
      // Format the result
      const formattedResult = data.result.map((row: any) => 
        `${row.status}: ${row.count}`
      ).join(', ');
      
      setDbStatus(formattedResult);
      
    } catch (error) {
      console.error('Error checking database status:', error);
      setDbStatus(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };
  
  const handleFixSelectedQuotes = async () => {
    if (selectedIds.length === 0) {
      toast({
        title: "No quotes selected",
        description: "Please select at least one quote to fix",
        variant: "destructive"
      })
      return
    }
    
    setLoading(true)
    const results = []
    
    for (const id of selectedIds) {
      try {
        // Find the quote to determine the correct status
        const quote = stuckQuotes.find(q => q.id === id)
        
        // Determine the appropriate status based on affiliate responses and timeline
        let targetStatus = 'pending' // default
        
        if (quote.affiliate_responses && quote.affiliate_responses.length > 0) {
          // If it has responses, it was probably meant to be rate_requested or fixed_offer
          // Check responses to see if they suggest a fixed offer
          const fixedOfferIndication = quote.affiliate_responses.some((r: any) => 
            r.type === 'fixed_offer' || r.status === 'fixed_offer'
          )
          
          targetStatus = fixedOfferIndication ? 'fixed_offer' : 'rate_requested'
        } else if (quote.timeline) {
          // Check timeline to determine status
          const timelineEntry = quote.timeline.find((t: any) => 
            t.action && 
            (t.action.includes('sent_to_affiliates') || 
             t.action.includes('quote_ready') || 
             t.action.includes('rate_requested'))
          )
          
          if (timelineEntry) {
            if (timelineEntry.action.includes('quote_ready') || timelineEntry.action.includes('fixed_offer')) {
              targetStatus = 'fixed_offer'
            } else {
              targetStatus = 'rate_requested'
            }
          }
        }
        
        console.log(`Updating quote ${id} from "${quote.status}" to "${targetStatus}"`);
        
        // Use the direct API endpoint for more reliable updates
        const response = await fetch(`/api/admin/quotes/${id}/status`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ status: targetStatus })
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'API returned error status');
        }
        
        const responseData = await response.json();
        console.log(`Quote update response:`, responseData);
        
        results.push({ id, status: 'success', message: `Status updated to ${targetStatus}` });
      } catch (error) {
        console.error(`Error fixing quote ${id}:`, error)
        results.push({ id, status: 'error', message: error instanceof Error ? error.message : 'Unknown error' })
      }
    }
    
    // Display results
    const successCount = results.filter(r => r.status === 'success').length
    const errorCount = results.filter(r => r.status === 'error').length
    
    if (successCount > 0) {
      toast({
        title: `Fixed ${successCount} quotes`,
        description: `Successfully updated status for ${successCount} quotes.`
      })
    }
    
    if (errorCount > 0) {
      toast({
        title: `Failed to fix ${errorCount} quotes`,
        description: "Check console for details",
        variant: "destructive"
      })
    }
    
    // Clear selection
    setSelectedIds([])
    
    // Refresh quotes list
    if (onRefresh) {
      await onRefresh()
    }
    
    // Rescan for stuck quotes
    await handleScanForStuckQuotes()
    
    setLoading(false)
  }
  
  const handleSetAllToRateRequested = async () => {
    try {
      setLoading(true);
      
      // Select all stuck quotes
      setSelectedIds(stuckQuotes.map(q => q.id));
      
      // Submit the request
      await handleFixSelectedQuotes();
      
      toast({
        title: "Quotes updated",
        description: "All stuck quotes have been updated to their correct statuses."
      });
    } catch (error) {
      console.error("Error updating all quotes:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <Card className="border-orange-300 bg-orange-50 mt-6">
      <CardHeader>
        <CardTitle className="text-orange-800">Quote Status Fixer</CardTitle>
        <CardDescription className="text-orange-700">
          Find and fix quotes stuck in the wrong status (especially pending)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={handleScanForStuckQuotes} 
            disabled={scanning}
            className="flex-1 border-orange-300 text-orange-800"
          >
            {scanning ? "Scanning..." : "Scan for Stuck Quotes"}
          </Button>
          
          <Button
            variant="outline"
            onClick={checkDatabaseStatus}
            disabled={scanning || loading}
            className="border-orange-300 text-orange-800"
          >
            Refresh DB Info
          </Button>
        </div>
        
        {dbStatus && (
          <div className="bg-white p-3 rounded-md border border-orange-200 text-sm">
            <div className="font-semibold text-orange-800 mb-1">Database Status:</div>
            <div className="text-gray-700">{dbStatus}</div>
          </div>
        )}
        
        {stuckQuotes.length > 0 && (
          <>
            <div className="text-sm font-medium text-orange-800 mb-2">
              Found {stuckQuotes.length} potentially stuck quotes:
            </div>
            
            <div className="max-h-60 overflow-y-auto border rounded-md border-orange-200">
              {stuckQuotes.map((quote) => (
                <div 
                  key={quote.id} 
                  className="flex items-center p-2 border-b border-orange-200 hover:bg-orange-100/50"
                >
                  <Checkbox 
                    id={`quote-${quote.id}`}
                    checked={selectedIds.includes(quote.id)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        setSelectedIds([...selectedIds, quote.id])
                      } else {
                        setSelectedIds(selectedIds.filter(id => id !== quote.id))
                      }
                    }}
                    className="mr-2"
                  />
                  <Label htmlFor={`quote-${quote.id}`} className="flex-1 text-sm cursor-pointer">
                    <div className="font-medium">{quote.reference_number || quote.id.substring(0, 8)}</div>
                    <div className="text-xs text-orange-700">
                      Status: {quote.status} • 
                      {quote.affiliate_responses && ` ${quote.affiliate_responses.length} responses •`}
                      {quote.pickup_location && ` ${quote.pickup_location}`}
                    </div>
                  </Label>
                </div>
              ))}
            </div>
            
            <div className="flex items-center justify-between pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  if (selectedIds.length === stuckQuotes.length) {
                    setSelectedIds([])
                  } else {
                    setSelectedIds(stuckQuotes.map(q => q.id))
                  }
                }}
                className="text-xs border-orange-300 text-orange-800"
              >
                {selectedIds.length === stuckQuotes.length ? 'Deselect All' : 'Select All'}
              </Button>
              
              <div className="text-sm text-orange-700">
                {selectedIds.length} of {stuckQuotes.length} selected
              </div>
            </div>
          </>
        )}
      </CardContent>
      
      {stuckQuotes.length > 0 && (
        <CardFooter className="flex gap-2">
          <Button 
            variant="destructive" 
            onClick={handleFixSelectedQuotes} 
            disabled={loading || selectedIds.length === 0}
            className="flex-1 bg-orange-600 hover:bg-orange-700"
          >
            {loading ? "Fixing..." : `Fix ${selectedIds.length} Selected Quotes`}
          </Button>
          
          <Button
            variant="secondary"
            onClick={handleSetAllToRateRequested}
            disabled={loading || stuckQuotes.length === 0}
            className="flex-1"
          >
            Fix All Quotes
          </Button>
        </CardFooter>
      )}
    </Card>
  )
} 