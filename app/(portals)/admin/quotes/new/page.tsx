"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card } from "@/app/components/ui/card"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { QuoteForm } from "@/app/components/features/quotes/quote-form"

export default function NewQuotePage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-x-4">
          <Link href="/customer/quotes">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h2 className="text-2xl font-bold tracking-tight">New Quote</h2>
        </div>
      </div>
      <Card className="p-6">
        <QuoteForm userRole="system_manager" />
      </Card>
    </div>
  )
} 