'use client'

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Plus } from "lucide-react"
import { useRouter } from "next/navigation"
import { useQuery } from "@tanstack/react-query"
import { toast } from "sonner"
import { Badge } from "@/app/components/ui/badge"
import { PostgrestError } from "@supabase/supabase-js"
import { DataTable } from "@/app/components/ui/data-table"
import { ColumnDef } from "@tanstack/react-table"

interface RateCard {
  id: string
  name: string
  serviceType: string
  baseRate: number
  hourlyRate: number
  minimumHours: number
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
}

const serviceTypeLabels: Record<string, string> = {
  airport_transfer: "Airport Transfer",
  venue_transfer: "Venue Transfer",
  city_tour: "City Tour",
  vip_service: "VIP Service",
  group_shuttle: "Group Shuttle",
}

async function getRateCards(): Promise<RateCard[]> {
  // TODO: Implement rate cards fetching
  return [
    {
      id: "1",
      name: "Standard Airport Transfer",
      serviceType: "airport_transfer",
      baseRate: 100,
      hourlyRate: 75,
      minimumHours: 3,
      status: "active",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: "2",
      name: "VIP City Tour",
      serviceType: "city_tour",
      baseRate: 200,
      hourlyRate: 150,
      minimumHours: 4,
      status: "active",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ]
}

export default function RatesPage() {
  const router = useRouter()

  const { data: rateCards, isLoading, error } = useQuery<RateCard[], PostgrestError>({
    queryKey: ["rateCards"],
    queryFn: getRateCards,
  })

  const columns: ColumnDef<RateCard>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => {
        const name = row.getValue("name") as string
        return (
          <Button
            variant="link"
            className="p-0 h-auto font-medium text-left"
            onClick={() => router.push(`/admin/coverage/rates/${row.original.id}`)}
          >
            {name}
          </Button>
        )
      },
    },
    {
      accessorKey: "serviceType",
      header: "Service Type",
      cell: ({ row }) => {
        const type = row.getValue("serviceType") as string
        return serviceTypeLabels[type]
      },
    },
    {
      accessorKey: "baseRate",
      header: "Base Rate",
      cell: ({ row }) => {
        const amount = row.getValue("baseRate") as number
        const formatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
        }).format(amount)
        return formatted
      },
    },
    {
      accessorKey: "hourlyRate",
      header: "Hourly Rate",
      cell: ({ row }) => {
        const amount = row.getValue("hourlyRate") as number
        const formatted = new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
        }).format(amount)
        return formatted
      },
    },
    {
      accessorKey: "minimumHours",
      header: "Min. Hours",
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string
        return (
          <Badge variant={status === "active" ? "default" : "secondary"}>
            {status === "active" ? "Active" : "Inactive"}
          </Badge>
        )
      },
    },
    {
      accessorKey: "updatedAt",
      header: "Last Updated",
      cell: ({ row }) => {
        const date = row.getValue("updatedAt") as string
        return new Date(date).toLocaleDateString()
      },
    },
  ]

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
      </div>
    )
  }

  if (error) {
    toast.error(error.message || "Failed to load rate cards")
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <p className="text-red-500">Error loading rate cards</p>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-bold">Rate Cards</h1>
        <Button onClick={() => router.push("/admin/coverage/rates/new")}>
          <Plus className="mr-2 h-4 w-4" />
          New Rate Card
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Rate Cards</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={rateCards || []}
          />
        </CardContent>
      </Card>
    </div>
  )
} 