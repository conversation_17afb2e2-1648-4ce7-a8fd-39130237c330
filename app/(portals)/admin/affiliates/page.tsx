"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/app/components/ui/tabs"
import { Download, Plus, Search, Filter } from "lucide-react"
import Link from "next/link"
import { Separator } from "@/app/components/ui/separator"
import { Badge } from "@/app/components/ui/badge"
import { Switch } from "@/app/components/ui/switch"
import {
  Building2,
  Car,
  Users,
  MapPin,
  FileText,
  CheckCircle2,
  XCircle,
  ArrowLeft,
  Shield,
  DollarSign,
  ClipboardCheck,
  AlertTriangle,
  Mail,
  Phone,
  Globe,
  FileCheck,
  Clock,
  Calendar
} from "lucide-react"

export default function AffiliatesPage() {
  return (
    <div className="container p-6 space-y-6">
      {/* Header with title and actions */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold">Affiliate Management</h1>
          <p className="text-sm text-muted-foreground">
            Review applications, verify credentials, and manage affiliate partnerships
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export List
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Affiliate
          </Button>
        </div>
      </div>

      {/* Quick stats */}
      <div className="grid grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Building2 className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <div className="text-2xl font-bold">0</div>
              <div className="text-sm text-muted-foreground">Active Affiliates</div>
            </div>
          </div>
          <div className="mt-2 text-xs text-muted-foreground">Currently operating partners</div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-green-100 rounded-lg">
              <FileCheck className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <div className="text-2xl font-bold">0</div>
              <div className="text-sm text-muted-foreground">New Applications</div>
            </div>
          </div>
          <div className="mt-2 text-xs text-muted-foreground">Pending initial review</div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Clock className="h-5 w-5 text-yellow-600" />
            </div>
            <div>
              <div className="text-2xl font-bold">0</div>
              <div className="text-sm text-muted-foreground">In Verification</div>
            </div>
          </div>
          <div className="mt-2 text-xs text-muted-foreground">Document review in progress</div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-red-100 rounded-lg">
              <AlertTriangle className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <div className="text-2xl font-bold">0</div>
              <div className="text-sm text-muted-foreground">Action Required</div>
            </div>
          </div>
          <div className="mt-2 text-xs text-muted-foreground">Needs immediate attention</div>
        </Card>
      </div>

      {/* Main content */}
      <Card>
        <CardHeader className="pb-3">
          <Tabs defaultValue="all" className="w-full">
            <div className="flex items-center justify-between">
              <TabsList>
                <TabsTrigger value="all">All Affiliates</TabsTrigger>
                <TabsTrigger value="applications">New Applications</TabsTrigger>
                <TabsTrigger value="verification">In Verification</TabsTrigger>
                <TabsTrigger value="active">Active</TabsTrigger>
              </TabsList>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search affiliates..."
                    className="w-[250px] pl-9"
                  />
                </div>
                <Button variant="outline" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <TabsContent value="all" className="mt-4">
              <div className="rounded-md border">
                <div className="p-4">
                  <div className="space-y-4">
                    {/* Sample affiliate entry - This will be mapped over real data */}
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="p-2 bg-gray-100 rounded-full">
                          <Building2 className="h-6 w-6" />
                        </div>
                        <div>
                          <h3 className="font-medium">Premium Fleet Services</h3>
                          <p className="text-sm text-muted-foreground">San Francisco, CA</p>
                        </div>
                        <div className="flex gap-2">
                          <Badge variant="outline">Active</Badge>
                          <Badge variant="secondary">Verified</Badge>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-sm text-right">
                          <p>Last Active: 2 hours ago</p>
                          <p className="text-muted-foreground">10 vehicles in fleet</p>
                        </div>
                        <Button variant="outline" size="sm" asChild>
                          <Link href="/admin/affiliates/1">
                            View Details
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="applications" className="mt-4">
              <div className="rounded-md border">
                <div className="p-4">
                  <div className="space-y-4">
                    {/* Sample new application - This will be mapped over real data */}
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="p-2 bg-gray-100 rounded-full">
                          <Building2 className="h-6 w-6" />
                        </div>
                        <div>
                          <h3 className="font-medium">Elite Transportation Co.</h3>
                          <p className="text-sm text-muted-foreground">New York, NY</p>
                        </div>
                        <Badge variant="secondary">New Application</Badge>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-sm text-right">
                          <p>Applied: 1 day ago</p>
                          <p className="text-muted-foreground">Initial review pending</p>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            Review
                          </Button>
                          <Button variant="secondary" size="sm">
                            Documents
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardHeader>
      </Card>

      {/* Quick Actions Panel */}
      <div className="grid grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <ClipboardCheck className="h-5 w-5" />
              Verification Queue
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between py-2">
                <div>
                  <p className="font-medium">Business Documents</p>
                  <p className="text-sm text-muted-foreground">0 pending reviews</p>
                </div>
                <Button variant="outline" size="sm">
                  Review
                </Button>
              </div>
              <Separator />
              <div className="flex items-center justify-between py-2">
                <div>
                  <p className="font-medium">Insurance Verification</p>
                  <p className="text-sm text-muted-foreground">0 pending verifications</p>
                </div>
                <Button variant="outline" size="sm">
                  Verify
                </Button>
              </div>
              <Separator />
              <div className="flex items-center justify-between py-2">
                <div>
                  <p className="font-medium">Fleet Inspections</p>
                  <p className="text-sm text-muted-foreground">0 scheduled inspections</p>
                </div>
                <Button variant="outline" size="sm">
                  Schedule
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Service Coverage
              </CardTitle>
              <div className="flex items-center gap-2">
                <Switch id="coverage-mode" />
                <label htmlFor="coverage-mode" className="text-sm text-muted-foreground">
                  Show gaps
                </label>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="h-[200px] bg-muted rounded-lg flex items-center justify-center">
                <p className="text-sm text-muted-foreground">Coverage map visualization</p>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Active Service Areas</p>
                  <p className="text-sm text-muted-foreground">0 regions covered</p>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/admin/coverage">
                    View Full Map
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 