"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON>Trigger } from "@/app/components/ui/tabs"
import {
  Building2,
  Car,
  Users,
  MapPin,
  FileText,
  CheckCircle2,
  XCircle,
  ArrowLeft,
  Shield,
  DollarSign,
  ClipboardCheck,
  AlertTriangle,
  Mail,
  Phone,
  Globe,
  FileCheck,
  Clock,
  Calendar
} from "lucide-react"
import Link from "next/link"
import { Badge } from "@/app/components/ui/badge"
import { Progress } from "@/app/components/ui/progress"
import { Separator } from "@/app/components/ui/separator"

export default function AffiliateDetailsPage() {
  return (
    <div className="container p-6 space-y-6">
      {/* Header with navigation and actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/affiliates">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Affiliates
            </Link>
          </Button>
          <Badge variant="outline">Application Review</Badge>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            Request Updates
          </Button>
          <Button variant="destructive" size="sm">
            Reject
          </Button>
          <Button size="sm">
            Approve & Activate
          </Button>
        </div>
      </div>

      {/* Company Overview */}
      <div className="flex items-start gap-6">
        <div className="w-[200px] h-[200px] bg-muted rounded-lg flex items-center justify-center">
          <Building2 className="h-20 w-20 text-muted-foreground" />
        </div>
        <div className="flex-1 space-y-4">
          <div>
            <h1 className="text-2xl font-semibold">Elite Transportation Co.</h1>
            <p className="text-muted-foreground">Application submitted on March 20, 2024</p>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Reservations Email
              </p>
              <p className="font-medium"><EMAIL></p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Billing Email
              </p>
              <p className="font-medium"><EMAIL></p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground flex items-center gap-2">
                <Phone className="h-4 w-4" />
                Dispatch Phone
              </p>
              <p className="font-medium">+****************</p>
            </div>
          </div>

          <div className="grid grid-cols-4 gap-4">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Federal Tax ID</p>
              <p className="font-medium">XX-XXXXXXX</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Year Established</p>
              <p className="font-medium">2015</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Owner Name</p>
              <p className="font-medium">John Smith</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">DBA</p>
              <p className="font-medium">Elite Transport Services</p>
            </div>
          </div>

          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">Business Address</p>
            <div className="grid grid-cols-4 gap-4">
              <div className="col-span-4">
                <p className="font-medium">123 Transportation Ave</p>
              </div>
              <div>
                <p className="font-medium">New York</p>
              </div>
              <div>
                <p className="font-medium">NY</p>
              </div>
              <div>
                <p className="font-medium">10001</p>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">Application Progress</p>
            <Progress value={33} className="w-full" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Documents Submitted</span>
              <span>Verification</span>
              <span>Final Review</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main content tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="fleet-rates">Fleet & Rates</TabsTrigger>
          <TabsTrigger value="coverage">Coverage</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <FileCheck className="h-4 w-4" />
                  Document Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4/6</div>
                <p className="text-xs text-muted-foreground">Required documents submitted</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Car className="h-4 w-4" />
                  Fleet Size
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">12</div>
                <p className="text-xs text-muted-foreground">Vehicles declared</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Drivers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">8</div>
                <p className="text-xs text-muted-foreground">Registered drivers</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Insurance Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">Pending</div>
                <p className="text-xs text-muted-foreground">Verification required</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <ClipboardCheck className="h-5 w-5" />
                  Operations
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <p className="text-sm font-medium mb-2">Dispatch Software</p>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                      <span className="text-sm">LimoAnywhere</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Ground Alliance</span>
                    </div>
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium mb-2">Status Update Methods</p>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Drivers use DriverAnywhere</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Automated email notifications</span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div className="p-3 bg-muted rounded-lg flex items-center gap-2">
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                    <span className="text-sm">24/7 Support</span>
                  </div>
                  <div className="p-3 bg-muted rounded-lg flex items-center gap-2">
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Gridd Gnet</span>
                  </div>
                  <div className="p-3 bg-muted rounded-lg flex items-center gap-2">
                    <XCircle className="h-4 w-4 text-gray-300" />
                    <span className="text-sm">Addons LA</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  Service Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <p className="text-sm font-medium mb-2">Airports Served</p>
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm">JFK International Airport, LaGuardia Airport, Newark Liberty International Airport</p>
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium mb-2">Languages Spoken</p>
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm">English, Spanish, French</p>
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium mb-2">Website</p>
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm">www.elitetransport.com</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <ClipboardCheck className="h-5 w-5" />
                Verification Checklist
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                    <span>Business Registration</span>
                  </div>
                  <Badge variant="outline">Verified</Badge>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-yellow-500" />
                    <span>Insurance Documentation</span>
                  </div>
                  <Badge variant="secondary">In Review</Badge>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-red-500" />
                    <span>Fleet Inspection</span>
                  </div>
                  <Badge variant="outline">Not Started</Badge>
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-yellow-500" />
                    <span>Rate Agreement</span>
                  </div>
                  <Badge variant="secondary">Pending</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Proposed Service Areas
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="h-[200px] bg-muted rounded-lg flex items-center justify-center">
                  <p className="text-sm text-muted-foreground">Service area map</p>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Primary Region</p>
                    <p className="text-sm text-muted-foreground">New York Metropolitan Area</p>
                  </div>
                  <Button variant="outline" size="sm">
                    Review Coverage
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Required Documents
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                    <div className="flex items-center gap-3">
                      <CheckCircle2 className="h-5 w-5 text-green-500" />
                      <div>
                        <p className="font-medium">Business License</p>
                        <p className="text-sm text-muted-foreground">Uploaded on Mar 15, 2024</p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">View</Button>
                  </div>
                  <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                    <div className="flex items-center gap-3">
                      <Clock className="h-5 w-5 text-yellow-500" />
                      <div>
                        <p className="font-medium">Insurance Certificate</p>
                        <p className="text-sm text-muted-foreground">Under review</p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">View</Button>
                  </div>
                  <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                    <div className="flex items-center gap-3">
                      <CheckCircle2 className="h-5 w-5 text-green-500" />
                      <div>
                        <p className="font-medium">DOT Number</p>
                        <p className="text-sm text-muted-foreground">Verified on Mar 16, 2024</p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">View</Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Insurance Details
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Policy Number</p>
                      <p className="font-medium">INS-2024-789456</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Expiration Date</p>
                      <p className="font-medium">Dec 31, 2024</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Coverage Amount</p>
                      <p className="font-medium">$2,000,000</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Provider</p>
                      <p className="font-medium">SafeGuard Insurance</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 p-4 bg-yellow-50 rounded-lg">
                    <AlertTriangle className="h-5 w-5 text-yellow-500" />
                    <p className="text-sm">Pending verification of coverage limits</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="fleet-rates" className="space-y-4">
          <div className="grid grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Vehicle Types</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">2/14</div>
                <p className="text-xs text-muted-foreground">Types configured</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Fleet Size</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4</div>
                <p className="text-xs text-muted-foreground">Vehicles registered</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Rate Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">Pending</div>
                <p className="text-xs text-muted-foreground">Rate review</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Last Updated</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">Mar 20</div>
                <p className="text-xs text-muted-foreground">2024</p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Car className="h-5 w-5" />
                Vehicle Types & Rates
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* LUXURY SEDAN */}
                <div className="p-4 bg-muted rounded-lg space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div>
                        <p className="font-medium">LUXURY SEDAN</p>
                        <p className="text-sm text-muted-foreground">4-passenger luxury sedan</p>
                      </div>
                      <Badge variant="outline" className="bg-white">Active</Badge>
                    </div>
                  </div>
                  <Separator />
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-medium">Vehicle Details</p>
                        <div className="grid grid-cols-3 gap-2 mt-2">
                          <div>
                            <p className="text-xs text-muted-foreground">Make & Model</p>
                            <p className="text-sm">Mercedes-Benz S580</p>
                          </div>
                          <div>
                            <p className="text-xs text-muted-foreground">Year</p>
                            <p className="text-sm">2024</p>
                          </div>
                          <div>
                            <p className="text-xs text-muted-foreground">Capacity</p>
                            <p className="text-sm">4</p>
                          </div>
                        </div>
                      </div>
                      <div>
                        <Badge>Point to Point</Badge>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-xs text-muted-foreground">Point to Point Rate</p>
                        <p className="text-sm font-medium">$4.50/mile</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Extra Hour</p>
                        <p className="text-sm font-medium">$75.00</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Airport Rate</p>
                        <p className="text-sm font-medium">$25.00</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Min Hours</p>
                        <p className="text-sm font-medium">2</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* STRETCH LIMOUSINE */}
                <div className="p-4 bg-muted rounded-lg space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div>
                        <p className="font-medium">STRETCH LIMOUSINE</p>
                        <p className="text-sm text-muted-foreground">6-8 passenger stretch limousine</p>
                      </div>
                      <Badge variant="outline" className="bg-white">Active</Badge>
                    </div>
                  </div>
                  <Separator />
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-medium">Vehicle Details</p>
                        <div className="grid grid-cols-3 gap-2 mt-2">
                          <div>
                            <p className="text-xs text-muted-foreground">Make & Model</p>
                            <p className="text-sm">Lincoln MKT</p>
                          </div>
                          <div>
                            <p className="text-xs text-muted-foreground">Year</p>
                            <p className="text-sm">2023</p>
                          </div>
                          <div>
                            <p className="text-xs text-muted-foreground">Capacity</p>
                            <p className="text-sm">8</p>
                          </div>
                        </div>
                      </div>
                      <div>
                        <Badge>Distance + Time</Badge>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-xs text-muted-foreground">Base Rate</p>
                        <p className="text-sm font-medium">$150.00</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Per Mile</p>
                        <p className="text-sm font-medium">$4.50</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Per Hour</p>
                        <p className="text-sm font-medium">$85.00</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Min Miles</p>
                        <p className="text-sm font-medium">25</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Airport Rate</p>
                        <p className="text-sm font-medium">$35.00</p>
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Min Hours</p>
                        <p className="text-sm font-medium">4</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Disabled Vehicle Types */}
                <div className="p-4 border border-dashed rounded-lg">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <XCircle className="h-4 w-4" />
                      <span className="text-sm">SUV</span>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <XCircle className="h-4 w-4" />
                      <span className="text-sm">MERCEDES SPRINTER</span>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <XCircle className="h-4 w-4" />
                      <span className="text-sm">MINI COACH</span>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <XCircle className="h-4 w-4" />
                      <span className="text-sm">MOTOR COACH</span>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <XCircle className="h-4 w-4" />
                      <span className="text-sm">EXECUTIVE MINI COACH</span>
                    </div>
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <XCircle className="h-4 w-4" />
                      <span className="text-sm">SHUTTLE VAN</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Seasonal Rates
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                  <div>
                    <p className="font-medium">Holiday Season 2024</p>
                    <p className="text-sm text-muted-foreground">Dec 15, 2024 - Jan 5, 2025</p>
                  </div>
                  <div className="text-right">
                    <Badge variant="secondary">+25% Surcharge</Badge>
                  </div>
                </div>
                <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                  <div>
                    <p className="font-medium">Summer Peak</p>
                    <p className="text-sm text-muted-foreground">Jun 1, 2024 - Aug 31, 2024</p>
                  </div>
                  <div className="text-right">
                    <Badge variant="secondary">+15% Surcharge</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Additional Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-muted rounded-lg">
                  <p className="font-medium">Cancellation Policy</p>
                  <p className="text-sm text-muted-foreground mt-1">24 hours notice required for cancellation. Full charge applies for late cancellations.</p>
                </div>
                <div className="p-4 bg-yellow-50 rounded-lg">
                  <p className="font-medium">Rate Review Notes</p>
                  <p className="text-sm mt-1">Base rates are competitive for the market. Seasonal surcharges need review.</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="coverage" className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Service Areas
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="h-[300px] bg-muted rounded-lg flex items-center justify-center">
                    <p className="text-sm text-muted-foreground">Interactive coverage map</p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Primary Hub</p>
                      <p className="font-medium">Manhattan, NY</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Coverage Radius</p>
                      <p className="font-medium">50 miles</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  Service Capabilities
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <span className="font-medium">Airport Transfers</span>
                    <CheckCircle2 className="h-5 w-5 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <span className="font-medium">Corporate Events</span>
                    <CheckCircle2 className="h-5 w-5 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <span className="font-medium">Wedding Services</span>
                    <XCircle className="h-5 w-5 text-gray-300" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
} 