[Log] Initializing Supabase client with URL: – "http://127.0.0.1:54321"
[Log] Syncing auth token from localStorage to cookies
[Log] Auth state changed: – "SIGNED_IN" – "session exists"
[Log] User ID: – "2b2a2488-a406-49d1-ae51-1d1c35a2a813"
[Log] User email: – "<EMAIL>"
[Log] Syncing auth token from localStorage to cookies
[Log] Auth state changed: – "INITIAL_SESSION" – "session exists"
[Log] User ID: – "2b2a2488-a406-49d1-ae51-1d1c35a2a813"
[Log] User email: – "<EMAIL>"
[Log] Syncing auth token from localStorage to cookies
[Log] Auth token synced to cookies (x2)
[Log] [AuthProvider] – "Initializing auth state"
[Log] Auth token synced to cookies
[Log] AuthSync - Attempting to sync auth state
[Log] AuthSync - Attempting to sync auth state
[Log] Syncing auth token from localStorage to cookies (x3)
[Log] [AuthProvider] – "Session found during initialization"
[Log] [AuthProvider] – "Refreshing auth state"
[Log] [AuthProvider] – "Auth state changed:" – "INITIAL_SESSION" – "session exists"
[Log] Auth token synced to cookies
[Log] Auth state synced with server
[Log] Found active session in Supabase client, syncing to cookies
[Log] Syncing auth token from localStorage to cookies
[Log] Auth token synced to cookies
[Log] Auth state synced with server
[Log] Found active session in Supabase client, syncing to cookies
[Log] Syncing auth token from localStorage to cookies
[Log] Auth token synced to cookies
[Log] Auth state synced with server
[Log] Auth token synced to cookies
[Log] Syncing auth token from localStorage to cookies
[Log] Auth token synced to cookies
[Log] Syncing auth token from localStorage to cookies
[Log] Auth token synced to cookies (x2)
[Log] Ensuring roles for user: 2b2a2488-a406-49d1-ae51-1d1c35a2a813 (<EMAIL>) (x2)
[Log] [AuthProvider] – "Roles fetched from profiles table:" – ["ADMIN"] (1)
[Log] [AuthProvider] – "User refreshed:" – "<EMAIL>" – "with roles:" – ["ADMIN"] (1)
[Log] User already has correct roles: – ["ADMIN"] (1)
[Log] AuthSync - Auth state synced: – "tokens found"
[Log] User already has correct roles: – ["ADMIN"] (1)
[Log] AuthSync - Auth state synced: – "tokens found"
[Log] Syncing auth token from localStorage to cookies
[Log] [AuthProvider] – "Auth state changed:" – "INITIAL_SESSION" – "session exists"
[Log] Auth token synced to cookies
[Log] Auth state synced with server
[Log] Quotes API response: – [Object, Object, Object, …] (21)
[Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, …]Array (21)
[Log] Pending quotes: – [Object, Object, Object, …] (17)
[Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, …]Array (17)
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status: pending
[Log] Mapped quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e status from pending to pending
[Log] Mapping quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 with status: pending
[Log] Mapped quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 status from pending to pending
[Log] Mapping quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 with status: pending
[Log] Mapped quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 status from pending to pending
[Log] Mapping quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a with status: pending
[Log] Mapped quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a status from pending to pending
[Log] Mapping quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c with status: pending
[Log] Mapped quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c status from pending to pending
[Log] Mapping quote 0e967316-89e6-4e00-b9a3-fdb01d511157 with status: pending
[Log] Mapped quote 0e967316-89e6-4e00-b9a3-fdb01d511157 status from pending to pending
[Log] Mapping quote 11111111-1111-1111-1111-111111111111 with status: pending
[Log] Mapped quote 11111111-1111-1111-1111-111111111111 status from pending to pending
[Log] Mapping quote 33333333-3333-3333-3333-333333333333 with status: accepted
[Log] Mapped quote 33333333-3333-3333-3333-333333333333 status from accepted to accepted
[Log] Mapping quote 44444444-4444-4444-4444-444444444444 with status: rejected
[Log] Mapped quote 44444444-4444-4444-4444-444444444444 status from rejected to rejected
[Log] Mapping quote 55555555-5555-5555-5555-555555555555 with status: pending
[Log] Mapped quote 55555555-5555-5555-5555-555555555555 status from pending to pending
[Log] Mapping quote 22222222-2222-2222-2222-222222222222 with status: pending
[Log] Mapped quote 22222222-2222-2222-2222-222222222222 status from pending to pending
[Log] Mapping quote b8c9d0e1-2345-ef67-8901-23abcdef4567 with status: pending
[Log] Mapped quote b8c9d0e1-2345-ef67-8901-23abcdef4567 status from pending to pending
[Log] Mapping quote e5f6a7b8-9012-cdef-3456-789012abcdef with status: pending
[Log] Mapped quote e5f6a7b8-9012-cdef-3456-789012abcdef status from pending to pending
[Log] Mapping quote d4e5f6a7-8901-bcde-f234-56789012abcd with status: pending
[Log] Mapped quote d4e5f6a7-8901-bcde-f234-56789012abcd status from pending to pending
[Log] Mapping quote c3d4e5f6-7890-abcd-ef12-3456789012ab with status: pending
[Log] Mapped quote c3d4e5f6-7890-abcd-ef12-3456789012ab status from pending to pending
[Log] Mapping quote a7b8c9d0-1234-ef56-7890-12abcdef3456 with status: pending
[Log] Mapped quote a7b8c9d0-1234-ef56-7890-12abcdef3456 status from pending to pending
[Log] Mapping quote f6a7b8c9-0123-def4-5678-9012abcdef34 with status: pending
[Log] Mapped quote f6a7b8c9-0123-def4-5678-9012abcdef34 status from pending to pending
[Log] Mapping quote b2a493af-6aba-44c0-a632-4c9710b22aca with status: pending
[Log] Mapped quote b2a493af-6aba-44c0-a632-4c9710b22aca status from pending to pending
[Log] Mapping quote 986c5191-a5d3-425d-a488-31f332d7d7a8 with status: pending
[Log] Mapped quote 986c5191-a5d3-425d-a488-31f332d7d7a8 status from pending to pending
[Log] Mapping quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 with status: accepted
[Log] Mapped quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 status from accepted to accepted
[Log] Mapping quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 with status: rejected
[Log] Mapped quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 status from rejected to rejected
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status: pending
[Log] Mapped quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e status from pending to pending
[Log] Mapping quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 with status: pending
[Log] Mapped quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 status from pending to pending
[Log] Mapping quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 with status: pending
[Log] Mapped quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 status from pending to pending
[Log] Mapping quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a with status: pending
[Log] Mapped quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a status from pending to pending
[Log] Mapping quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c with status: pending
[Log] Mapped quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c status from pending to pending
[Log] Mapping quote 0e967316-89e6-4e00-b9a3-fdb01d511157 with status: pending
[Log] Mapped quote 0e967316-89e6-4e00-b9a3-fdb01d511157 status from pending to pending
[Log] Mapping quote 11111111-1111-1111-1111-111111111111 with status: pending
[Log] Mapped quote 11111111-1111-1111-1111-111111111111 status from pending to pending
[Log] Mapping quote 33333333-3333-3333-3333-333333333333 with status: accepted
[Log] Mapped quote 33333333-3333-3333-3333-333333333333 status from accepted to accepted
[Log] Mapping quote 44444444-4444-4444-4444-444444444444 with status: rejected
[Log] Mapped quote 44444444-4444-4444-4444-444444444444 status from rejected to rejected
[Log] Mapping quote 55555555-5555-5555-5555-555555555555 with status: pending
[Log] Mapped quote 55555555-5555-5555-5555-555555555555 status from pending to pending
[Log] Mapping quote 22222222-2222-2222-2222-222222222222 with status: pending
[Log] Mapped quote 22222222-2222-2222-2222-222222222222 status from pending to pending
[Log] Mapping quote b8c9d0e1-2345-ef67-8901-23abcdef4567 with status: pending
[Log] Mapped quote b8c9d0e1-2345-ef67-8901-23abcdef4567 status from pending to pending
[Log] Mapping quote e5f6a7b8-9012-cdef-3456-789012abcdef with status: pending
[Log] Mapped quote e5f6a7b8-9012-cdef-3456-789012abcdef status from pending to pending
[Log] Mapping quote d4e5f6a7-8901-bcde-f234-56789012abcd with status: pending
[Log] Mapped quote d4e5f6a7-8901-bcde-f234-56789012abcd status from pending to pending
[Log] Mapping quote c3d4e5f6-7890-abcd-ef12-3456789012ab with status: pending
[Log] Mapped quote c3d4e5f6-7890-abcd-ef12-3456789012ab status from pending to pending
[Log] Mapping quote a7b8c9d0-1234-ef56-7890-12abcdef3456 with status: pending
[Log] Mapped quote a7b8c9d0-1234-ef56-7890-12abcdef3456 status from pending to pending
[Log] Mapping quote f6a7b8c9-0123-def4-5678-9012abcdef34 with status: pending
[Log] Mapped quote f6a7b8c9-0123-def4-5678-9012abcdef34 status from pending to pending
[Log] Mapping quote b2a493af-6aba-44c0-a632-4c9710b22aca with status: pending
[Log] Mapped quote b2a493af-6aba-44c0-a632-4c9710b22aca status from pending to pending
[Log] Mapping quote 986c5191-a5d3-425d-a488-31f332d7d7a8 with status: pending
[Log] Mapped quote 986c5191-a5d3-425d-a488-31f332d7d7a8 status from pending to pending
[Log] Mapping quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 with status: accepted
[Log] Mapped quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 status from accepted to accepted
[Log] Mapping quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 with status: rejected
[Log] Mapped quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 status from rejected to rejected
[Log] Rendering quotes page with – 21 – "quotes"
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status pending to row format
[Log] Mapping quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 with status pending to row format
[Log] Mapping quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 with status pending to row format
[Log] Mapping quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a with status pending to row format
[Log] Mapping quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c with status pending to row format
[Log] Mapping quote 0e967316-89e6-4e00-b9a3-fdb01d511157 with status pending to row format
[Log] Mapping quote 11111111-1111-1111-1111-111111111111 with status pending to row format
[Log] Mapping quote 33333333-3333-3333-3333-333333333333 with status accepted to row format
[Log] Mapping quote 44444444-4444-4444-4444-444444444444 with status rejected to row format
[Log] Mapping quote 55555555-5555-5555-5555-555555555555 with status pending to row format
[Log] Mapping quote 22222222-2222-2222-2222-222222222222 with status pending to row format
[Log] Mapping quote b8c9d0e1-2345-ef67-8901-23abcdef4567 with status pending to row format
[Log] Mapping quote e5f6a7b8-9012-cdef-3456-789012abcdef with status pending to row format
[Log] Mapping quote d4e5f6a7-8901-bcde-f234-56789012abcd with status pending to row format
[Log] Mapping quote c3d4e5f6-7890-abcd-ef12-3456789012ab with status pending to row format
[Log] Mapping quote a7b8c9d0-1234-ef56-7890-12abcdef3456 with status pending to row format
[Log] Mapping quote f6a7b8c9-0123-def4-5678-9012abcdef34 with status pending to row format
[Log] Mapping quote b2a493af-6aba-44c0-a632-4c9710b22aca with status pending to row format
[Log] Mapping quote 986c5191-a5d3-425d-a488-31f332d7d7a8 with status pending to row format
[Log] Mapping quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 with status accepted to row format
[Log] Mapping quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 with status rejected to row format
[Log] Rendering quotes page with – 21 – "quotes"
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status pending to row format
[Log] Mapping quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 with status pending to row format
[Log] Mapping quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 with status pending to row format
[Log] Mapping quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a with status pending to row format
[Log] Mapping quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c with status pending to row format
[Log] Mapping quote 0e967316-89e6-4e00-b9a3-fdb01d511157 with status pending to row format
[Log] Mapping quote 11111111-1111-1111-1111-111111111111 with status pending to row format
[Log] Mapping quote 33333333-3333-3333-3333-333333333333 with status accepted to row format
[Log] Mapping quote 44444444-4444-4444-4444-444444444444 with status rejected to row format
[Log] Mapping quote 55555555-5555-5555-5555-555555555555 with status pending to row format
[Log] Mapping quote 22222222-2222-2222-2222-222222222222 with status pending to row format
[Log] Mapping quote b8c9d0e1-2345-ef67-8901-23abcdef4567 with status pending to row format
[Log] Mapping quote e5f6a7b8-9012-cdef-3456-789012abcdef with status pending to row format
[Log] Mapping quote d4e5f6a7-8901-bcde-f234-56789012abcd with status pending to row format
[Log] Mapping quote c3d4e5f6-7890-abcd-ef12-3456789012ab with status pending to row format
[Log] Mapping quote a7b8c9d0-1234-ef56-7890-12abcdef3456 with status pending to row format
[Log] Mapping quote f6a7b8c9-0123-def4-5678-9012abcdef34 with status pending to row format
[Log] Mapping quote b2a493af-6aba-44c0-a632-4c9710b22aca with status pending to row format
[Log] Mapping quote 986c5191-a5d3-425d-a488-31f332d7d7a8 with status pending to row format
[Log] Mapping quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 with status accepted to row format
[Log] Mapping quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 with status rejected to row format
[Log] Syncing auth token from localStorage to cookies
[Log] Quotes API response: – [Object, Object, Object, …] (21)
[Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, …]Array (21)
[Log] Pending quotes: – [Object, Object, Object, …] (17)
[Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, …]Array (17)
[Log] Quotes loaded after auth sync
[Log] Quotes API response: – [Object, Object, Object, …] (21)
[Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, …]Array (21)
[Log] Pending quotes: – [Object, Object, Object, …] (17)
[Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, …]Array (17)
[Log] Quotes loaded after auth sync
[Log] Quotes API response: – [Object, Object, Object, …] (21)
[Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, …]Array (21)
[Log] Pending quotes: – [Object, Object, Object, …] (17)
[Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, …]Array (17)
[Log] Quotes loaded after auth sync
[Log] Quotes API response: – [Object, Object, Object, …] (21)
[Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, …]Array (21)
[Log] Pending quotes: – [Object, Object, Object, …] (17)
[Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, …]Array (17)
[Log] Quotes loaded after auth sync
[Log] Auth token synced to cookies
[Log] Auth state synced with server
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status: pending
[Log] Mapped quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e status from pending to pending
[Log] Mapping quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 with status: pending
[Log] Mapped quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 status from pending to pending
[Log] Mapping quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 with status: pending
[Log] Mapped quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 status from pending to pending
[Log] Mapping quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a with status: pending
[Log] Mapped quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a status from pending to pending
[Log] Mapping quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c with status: pending
[Log] Mapped quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c status from pending to pending
[Log] Mapping quote 0e967316-89e6-4e00-b9a3-fdb01d511157 with status: pending
[Log] Mapped quote 0e967316-89e6-4e00-b9a3-fdb01d511157 status from pending to pending
[Log] Mapping quote 11111111-1111-1111-1111-111111111111 with status: pending
[Log] Mapped quote 11111111-1111-1111-1111-111111111111 status from pending to pending
[Log] Mapping quote 33333333-3333-3333-3333-333333333333 with status: accepted
[Log] Mapped quote 33333333-3333-3333-3333-333333333333 status from accepted to accepted
[Log] Mapping quote 44444444-4444-4444-4444-444444444444 with status: rejected
[Log] Mapped quote 44444444-4444-4444-4444-444444444444 status from rejected to rejected
[Log] Mapping quote 55555555-5555-5555-5555-555555555555 with status: pending
[Log] Mapped quote 55555555-5555-5555-5555-555555555555 status from pending to pending
[Log] Mapping quote 22222222-2222-2222-2222-222222222222 with status: pending
[Log] Mapped quote 22222222-2222-2222-2222-222222222222 status from pending to pending
[Log] Mapping quote b8c9d0e1-2345-ef67-8901-23abcdef4567 with status: pending
[Log] Mapped quote b8c9d0e1-2345-ef67-8901-23abcdef4567 status from pending to pending
[Log] Mapping quote e5f6a7b8-9012-cdef-3456-789012abcdef with status: pending
[Log] Mapped quote e5f6a7b8-9012-cdef-3456-789012abcdef status from pending to pending
[Log] Mapping quote d4e5f6a7-8901-bcde-f234-56789012abcd with status: pending
[Log] Mapped quote d4e5f6a7-8901-bcde-f234-56789012abcd status from pending to pending
[Log] Mapping quote c3d4e5f6-7890-abcd-ef12-3456789012ab with status: pending
[Log] Mapped quote c3d4e5f6-7890-abcd-ef12-3456789012ab status from pending to pending
[Log] Mapping quote a7b8c9d0-1234-ef56-7890-12abcdef3456 with status: pending
[Log] Mapped quote a7b8c9d0-1234-ef56-7890-12abcdef3456 status from pending to pending
[Log] Mapping quote f6a7b8c9-0123-def4-5678-9012abcdef34 with status: pending
[Log] Mapped quote f6a7b8c9-0123-def4-5678-9012abcdef34 status from pending to pending
[Log] Mapping quote b2a493af-6aba-44c0-a632-4c9710b22aca with status: pending
[Log] Mapped quote b2a493af-6aba-44c0-a632-4c9710b22aca status from pending to pending
[Log] Mapping quote 986c5191-a5d3-425d-a488-31f332d7d7a8 with status: pending
[Log] Mapped quote 986c5191-a5d3-425d-a488-31f332d7d7a8 status from pending to pending
[Log] Mapping quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 with status: accepted
[Log] Mapped quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 status from accepted to accepted
[Log] Mapping quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 with status: rejected
[Log] Mapped quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 status from rejected to rejected
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status: pending
[Log] Mapped quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e status from pending to pending
[Log] Mapping quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 with status: pending
[Log] Mapped quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 status from pending to pending
[Log] Mapping quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 with status: pending
[Log] Mapped quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 status from pending to pending
[Log] Mapping quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a with status: pending
[Log] Mapped quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a status from pending to pending
[Log] Mapping quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c with status: pending
[Log] Mapped quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c status from pending to pending
[Log] Mapping quote 0e967316-89e6-4e00-b9a3-fdb01d511157 with status: pending
[Log] Mapped quote 0e967316-89e6-4e00-b9a3-fdb01d511157 status from pending to pending
[Log] Mapping quote 11111111-1111-1111-1111-111111111111 with status: pending
[Log] Mapped quote 11111111-1111-1111-1111-111111111111 status from pending to pending
[Log] Mapping quote 33333333-3333-3333-3333-333333333333 with status: accepted
[Log] Mapped quote 33333333-3333-3333-3333-333333333333 status from accepted to accepted
[Log] Mapping quote 44444444-4444-4444-4444-444444444444 with status: rejected
[Log] Mapped quote 44444444-4444-4444-4444-444444444444 status from rejected to rejected
[Log] Mapping quote 55555555-5555-5555-5555-555555555555 with status: pending
[Log] Mapped quote 55555555-5555-5555-5555-555555555555 status from pending to pending
[Log] Mapping quote 22222222-2222-2222-2222-222222222222 with status: pending
[Log] Mapped quote 22222222-2222-2222-2222-222222222222 status from pending to pending
[Log] Mapping quote b8c9d0e1-2345-ef67-8901-23abcdef4567 with status: pending
[Log] Mapped quote b8c9d0e1-2345-ef67-8901-23abcdef4567 status from pending to pending
[Log] Mapping quote e5f6a7b8-9012-cdef-3456-789012abcdef with status: pending
[Log] Mapped quote e5f6a7b8-9012-cdef-3456-789012abcdef status from pending to pending
[Log] Mapping quote d4e5f6a7-8901-bcde-f234-56789012abcd with status: pending
[Log] Mapped quote d4e5f6a7-8901-bcde-f234-56789012abcd status from pending to pending
[Log] Mapping quote c3d4e5f6-7890-abcd-ef12-3456789012ab with status: pending
[Log] Mapped quote c3d4e5f6-7890-abcd-ef12-3456789012ab status from pending to pending
[Log] Mapping quote a7b8c9d0-1234-ef56-7890-12abcdef3456 with status: pending
[Log] Mapped quote a7b8c9d0-1234-ef56-7890-12abcdef3456 status from pending to pending
[Log] Mapping quote f6a7b8c9-0123-def4-5678-9012abcdef34 with status: pending
[Log] Mapped quote f6a7b8c9-0123-def4-5678-9012abcdef34 status from pending to pending
[Log] Mapping quote b2a493af-6aba-44c0-a632-4c9710b22aca with status: pending
[Log] Mapped quote b2a493af-6aba-44c0-a632-4c9710b22aca status from pending to pending
[Log] Mapping quote 986c5191-a5d3-425d-a488-31f332d7d7a8 with status: pending
[Log] Mapped quote 986c5191-a5d3-425d-a488-31f332d7d7a8 status from pending to pending
[Log] Mapping quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 with status: accepted
[Log] Mapped quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 status from accepted to accepted
[Log] Mapping quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 with status: rejected
[Log] Mapped quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 status from rejected to rejected
[Log] Rendering quotes page with – 21 – "quotes"
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status pending to row format
[Log] Mapping quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 with status pending to row format
[Log] Mapping quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 with status pending to row format
[Log] Mapping quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a with status pending to row format
[Log] Mapping quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c with status pending to row format
[Log] Mapping quote 0e967316-89e6-4e00-b9a3-fdb01d511157 with status pending to row format
[Log] Mapping quote 11111111-1111-1111-1111-111111111111 with status pending to row format
[Log] Mapping quote 33333333-3333-3333-3333-333333333333 with status accepted to row format
[Log] Mapping quote 44444444-4444-4444-4444-444444444444 with status rejected to row format
[Log] Mapping quote 55555555-5555-5555-5555-555555555555 with status pending to row format
[Log] Mapping quote 22222222-2222-2222-2222-222222222222 with status pending to row format
[Log] Mapping quote b8c9d0e1-2345-ef67-8901-23abcdef4567 with status pending to row format
[Log] Mapping quote e5f6a7b8-9012-cdef-3456-789012abcdef with status pending to row format
[Log] Mapping quote d4e5f6a7-8901-bcde-f234-56789012abcd with status pending to row format
[Log] Mapping quote c3d4e5f6-7890-abcd-ef12-3456789012ab with status pending to row format
[Log] Mapping quote a7b8c9d0-1234-ef56-7890-12abcdef3456 with status pending to row format
[Log] Mapping quote f6a7b8c9-0123-def4-5678-9012abcdef34 with status pending to row format
[Log] Mapping quote b2a493af-6aba-44c0-a632-4c9710b22aca with status pending to row format
[Log] Mapping quote 986c5191-a5d3-425d-a488-31f332d7d7a8 with status pending to row format
[Log] Mapping quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 with status accepted to row format
[Log] Mapping quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 with status rejected to row format
[Log] Rendering quotes page with – 21 – "quotes"
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status pending to row format
[Log] Mapping quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 with status pending to row format
[Log] Mapping quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 with status pending to row format
[Log] Mapping quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a with status pending to row format
[Log] Mapping quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c with status pending to row format
[Log] Mapping quote 0e967316-89e6-4e00-b9a3-fdb01d511157 with status pending to row format
[Log] Mapping quote 11111111-1111-1111-1111-111111111111 with status pending to row format
[Log] Mapping quote 33333333-3333-3333-3333-333333333333 with status accepted to row format
[Log] Mapping quote 44444444-4444-4444-4444-444444444444 with status rejected to row format
[Log] Mapping quote 55555555-5555-5555-5555-555555555555 with status pending to row format
[Log] Mapping quote 22222222-2222-2222-2222-222222222222 with status pending to row format
[Log] Mapping quote b8c9d0e1-2345-ef67-8901-23abcdef4567 with status pending to row format
[Log] Mapping quote e5f6a7b8-9012-cdef-3456-789012abcdef with status pending to row format
[Log] Mapping quote d4e5f6a7-8901-bcde-f234-56789012abcd with status pending to row format
[Log] Mapping quote c3d4e5f6-7890-abcd-ef12-3456789012ab with status pending to row format
[Log] Mapping quote a7b8c9d0-1234-ef56-7890-12abcdef3456 with status pending to row format
[Log] Mapping quote f6a7b8c9-0123-def4-5678-9012abcdef34 with status pending to row format
[Log] Mapping quote b2a493af-6aba-44c0-a632-4c9710b22aca with status pending to row format
[Log] Mapping quote 986c5191-a5d3-425d-a488-31f332d7d7a8 with status pending to row format
[Log] Mapping quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 with status accepted to row format
[Log] Mapping quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 with status rejected to row format
[Log] Quotes API response: – [Object, Object, Object, …] (21)
[Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, …]Array (21)
[Log] Pending quotes: – [Object, Object, Object, …] (17)
[Object, Object, Object, Object, Object, Object, Object, Object, Object, Object, …]Array (17)
[Log] Quotes loaded after auth sync
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status: pending
[Log] Mapped quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e status from pending to pending
[Log] Mapping quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 with status: pending
[Log] Mapped quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 status from pending to pending
[Log] Mapping quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 with status: pending
[Log] Mapped quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 status from pending to pending
[Log] Mapping quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a with status: pending
[Log] Mapped quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a status from pending to pending
[Log] Mapping quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c with status: pending
[Log] Mapped quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c status from pending to pending
[Log] Mapping quote 0e967316-89e6-4e00-b9a3-fdb01d511157 with status: pending
[Log] Mapped quote 0e967316-89e6-4e00-b9a3-fdb01d511157 status from pending to pending
[Log] Mapping quote 11111111-1111-1111-1111-111111111111 with status: pending
[Log] Mapped quote 11111111-1111-1111-1111-111111111111 status from pending to pending
[Log] Mapping quote 33333333-3333-3333-3333-333333333333 with status: accepted
[Log] Mapped quote 33333333-3333-3333-3333-333333333333 status from accepted to accepted
[Log] Mapping quote 44444444-4444-4444-4444-444444444444 with status: rejected
[Log] Mapped quote 44444444-4444-4444-4444-444444444444 status from rejected to rejected
[Log] Mapping quote 55555555-5555-5555-5555-555555555555 with status: pending
[Log] Mapped quote 55555555-5555-5555-5555-555555555555 status from pending to pending
[Log] Mapping quote 22222222-2222-2222-2222-222222222222 with status: pending
[Log] Mapped quote 22222222-2222-2222-2222-222222222222 status from pending to pending
[Log] Mapping quote b8c9d0e1-2345-ef67-8901-23abcdef4567 with status: pending
[Log] Mapped quote b8c9d0e1-2345-ef67-8901-23abcdef4567 status from pending to pending
[Log] Mapping quote e5f6a7b8-9012-cdef-3456-789012abcdef with status: pending
[Log] Mapped quote e5f6a7b8-9012-cdef-3456-789012abcdef status from pending to pending
[Log] Mapping quote d4e5f6a7-8901-bcde-f234-56789012abcd with status: pending
[Log] Mapped quote d4e5f6a7-8901-bcde-f234-56789012abcd status from pending to pending
[Log] Mapping quote c3d4e5f6-7890-abcd-ef12-3456789012ab with status: pending
[Log] Mapped quote c3d4e5f6-7890-abcd-ef12-3456789012ab status from pending to pending
[Log] Mapping quote a7b8c9d0-1234-ef56-7890-12abcdef3456 with status: pending
[Log] Mapped quote a7b8c9d0-1234-ef56-7890-12abcdef3456 status from pending to pending
[Log] Mapping quote f6a7b8c9-0123-def4-5678-9012abcdef34 with status: pending
[Log] Mapped quote f6a7b8c9-0123-def4-5678-9012abcdef34 status from pending to pending
[Log] Mapping quote b2a493af-6aba-44c0-a632-4c9710b22aca with status: pending
[Log] Mapped quote b2a493af-6aba-44c0-a632-4c9710b22aca status from pending to pending
[Log] Mapping quote 986c5191-a5d3-425d-a488-31f332d7d7a8 with status: pending
[Log] Mapped quote 986c5191-a5d3-425d-a488-31f332d7d7a8 status from pending to pending
[Log] Mapping quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 with status: accepted
[Log] Mapped quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 status from accepted to accepted
[Log] Mapping quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 with status: rejected
[Log] Mapped quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 status from rejected to rejected
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status: pending
[Log] Mapped quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e status from pending to pending
[Log] Mapping quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 with status: pending
[Log] Mapped quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 status from pending to pending
[Log] Mapping quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 with status: pending
[Log] Mapped quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 status from pending to pending
[Log] Mapping quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a with status: pending
[Log] Mapped quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a status from pending to pending
[Log] Mapping quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c with status: pending
[Log] Mapped quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c status from pending to pending
[Log] Mapping quote 0e967316-89e6-4e00-b9a3-fdb01d511157 with status: pending
[Log] Mapped quote 0e967316-89e6-4e00-b9a3-fdb01d511157 status from pending to pending
[Log] Mapping quote 11111111-1111-1111-1111-111111111111 with status: pending
[Log] Mapped quote 11111111-1111-1111-1111-111111111111 status from pending to pending
[Log] Mapping quote 33333333-3333-3333-3333-333333333333 with status: accepted
[Log] Mapped quote 33333333-3333-3333-3333-333333333333 status from accepted to accepted
[Log] Mapping quote 44444444-4444-4444-4444-444444444444 with status: rejected
[Log] Mapped quote 44444444-4444-4444-4444-444444444444 status from rejected to rejected
[Log] Mapping quote 55555555-5555-5555-5555-555555555555 with status: pending
[Log] Mapped quote 55555555-5555-5555-5555-555555555555 status from pending to pending
[Log] Mapping quote 22222222-2222-2222-2222-222222222222 with status: pending
[Log] Mapped quote 22222222-2222-2222-2222-222222222222 status from pending to pending
[Log] Mapping quote b8c9d0e1-2345-ef67-8901-23abcdef4567 with status: pending
[Log] Mapped quote b8c9d0e1-2345-ef67-8901-23abcdef4567 status from pending to pending
[Log] Mapping quote e5f6a7b8-9012-cdef-3456-789012abcdef with status: pending
[Log] Mapped quote e5f6a7b8-9012-cdef-3456-789012abcdef status from pending to pending
[Log] Mapping quote d4e5f6a7-8901-bcde-f234-56789012abcd with status: pending
[Log] Mapped quote d4e5f6a7-8901-bcde-f234-56789012abcd status from pending to pending
[Log] Mapping quote c3d4e5f6-7890-abcd-ef12-3456789012ab with status: pending
[Log] Mapped quote c3d4e5f6-7890-abcd-ef12-3456789012ab status from pending to pending
[Log] Mapping quote a7b8c9d0-1234-ef56-7890-12abcdef3456 with status: pending
[Log] Mapped quote a7b8c9d0-1234-ef56-7890-12abcdef3456 status from pending to pending
[Log] Mapping quote f6a7b8c9-0123-def4-5678-9012abcdef34 with status: pending
[Log] Mapped quote f6a7b8c9-0123-def4-5678-9012abcdef34 status from pending to pending
[Log] Mapping quote b2a493af-6aba-44c0-a632-4c9710b22aca with status: pending
[Log] Mapped quote b2a493af-6aba-44c0-a632-4c9710b22aca status from pending to pending
[Log] Mapping quote 986c5191-a5d3-425d-a488-31f332d7d7a8 with status: pending
[Log] Mapped quote 986c5191-a5d3-425d-a488-31f332d7d7a8 status from pending to pending
[Log] Mapping quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 with status: accepted
[Log] Mapped quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 status from accepted to accepted
[Log] Mapping quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 with status: rejected
[Log] Mapped quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 status from rejected to rejected
[Log] Rendering quotes page with – 21 – "quotes"
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status pending to row format
[Log] Mapping quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 with status pending to row format
[Log] Mapping quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 with status pending to row format
[Log] Mapping quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a with status pending to row format
[Log] Mapping quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c with status pending to row format
[Log] Mapping quote 0e967316-89e6-4e00-b9a3-fdb01d511157 with status pending to row format
[Log] Mapping quote 11111111-1111-1111-1111-111111111111 with status pending to row format
[Log] Mapping quote 33333333-3333-3333-3333-333333333333 with status accepted to row format
[Log] Mapping quote 44444444-4444-4444-4444-444444444444 with status rejected to row format
[Log] Mapping quote 55555555-5555-5555-5555-555555555555 with status pending to row format
[Log] Mapping quote 22222222-2222-2222-2222-222222222222 with status pending to row format
[Log] Mapping quote b8c9d0e1-2345-ef67-8901-23abcdef4567 with status pending to row format
[Log] Mapping quote e5f6a7b8-9012-cdef-3456-789012abcdef with status pending to row format
[Log] Mapping quote d4e5f6a7-8901-bcde-f234-56789012abcd with status pending to row format
[Log] Mapping quote c3d4e5f6-7890-abcd-ef12-3456789012ab with status pending to row format
[Log] Mapping quote a7b8c9d0-1234-ef56-7890-12abcdef3456 with status pending to row format
[Log] Mapping quote f6a7b8c9-0123-def4-5678-9012abcdef34 with status pending to row format
[Log] Mapping quote b2a493af-6aba-44c0-a632-4c9710b22aca with status pending to row format
[Log] Mapping quote 986c5191-a5d3-425d-a488-31f332d7d7a8 with status pending to row format
[Log] Mapping quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 with status accepted to row format
[Log] Mapping quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 with status rejected to row format
[Log] Rendering quotes page with – 21 – "quotes"
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status pending to row format
[Log] Mapping quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 with status pending to row format
[Log] Mapping quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 with status pending to row format
[Log] Mapping quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a with status pending to row format
[Log] Mapping quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c with status pending to row format
[Log] Mapping quote 0e967316-89e6-4e00-b9a3-fdb01d511157 with status pending to row format
[Log] Mapping quote 11111111-1111-1111-1111-111111111111 with status pending to row format
[Log] Mapping quote 33333333-3333-3333-3333-333333333333 with status accepted to row format
[Log] Mapping quote 44444444-4444-4444-4444-444444444444 with status rejected to row format
[Log] Mapping quote 55555555-5555-5555-5555-555555555555 with status pending to row format
[Log] Mapping quote 22222222-2222-2222-2222-222222222222 with status pending to row format
[Log] Mapping quote b8c9d0e1-2345-ef67-8901-23abcdef4567 with status pending to row format
[Log] Mapping quote e5f6a7b8-9012-cdef-3456-789012abcdef with status pending to row format
[Log] Mapping quote d4e5f6a7-8901-bcde-f234-56789012abcd with status pending to row format
[Log] Mapping quote c3d4e5f6-7890-abcd-ef12-3456789012ab with status pending to row format
[Log] Mapping quote a7b8c9d0-1234-ef56-7890-12abcdef3456 with status pending to row format
[Log] Mapping quote f6a7b8c9-0123-def4-5678-9012abcdef34 with status pending to row format
[Log] Mapping quote b2a493af-6aba-44c0-a632-4c9710b22aca with status pending to row format
[Log] Mapping quote 986c5191-a5d3-425d-a488-31f332d7d7a8 with status pending to row format
[Log] Mapping quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 with status accepted to row format
[Log] Mapping quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 with status rejected to row format
[Log] Rendering quotes page with – 21 – "quotes"
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status pending to row format
[Log] Mapping quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 with status pending to row format
[Log] Mapping quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 with status pending to row format
[Log] Mapping quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a with status pending to row format
[Log] Mapping quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c with status pending to row format
[Log] Mapping quote 0e967316-89e6-4e00-b9a3-fdb01d511157 with status pending to row format
[Log] Mapping quote 11111111-1111-1111-1111-111111111111 with status pending to row format
[Log] Mapping quote 33333333-3333-3333-3333-333333333333 with status accepted to row format
[Log] Mapping quote 44444444-4444-4444-4444-444444444444 with status rejected to row format
[Log] Mapping quote 55555555-5555-5555-5555-555555555555 with status pending to row format
[Log] Mapping quote 22222222-2222-2222-2222-222222222222 with status pending to row format
[Log] Mapping quote b8c9d0e1-2345-ef67-8901-23abcdef4567 with status pending to row format
[Log] Mapping quote e5f6a7b8-9012-cdef-3456-789012abcdef with status pending to row format
[Log] Mapping quote d4e5f6a7-8901-bcde-f234-56789012abcd with status pending to row format
[Log] Mapping quote c3d4e5f6-7890-abcd-ef12-3456789012ab with status pending to row format
[Log] Mapping quote a7b8c9d0-1234-ef56-7890-12abcdef3456 with status pending to row format
[Log] Mapping quote f6a7b8c9-0123-def4-5678-9012abcdef34 with status pending to row format
[Log] Mapping quote b2a493af-6aba-44c0-a632-4c9710b22aca with status pending to row format
[Log] Mapping quote 986c5191-a5d3-425d-a488-31f332d7d7a8 with status pending to row format
[Log] Mapping quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 with status accepted to row format
[Log] Mapping quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 with status rejected to row format
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status pending to row format
[Log] Rendering quotes page with – 21 – "quotes"
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status pending to row format
[Log] Mapping quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 with status pending to row format
[Log] Mapping quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 with status pending to row format
[Log] Mapping quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a with status pending to row format
[Log] Mapping quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c with status pending to row format
[Log] Mapping quote 0e967316-89e6-4e00-b9a3-fdb01d511157 with status pending to row format
[Log] Mapping quote 11111111-1111-1111-1111-111111111111 with status pending to row format
[Log] Mapping quote 33333333-3333-3333-3333-333333333333 with status accepted to row format
[Log] Mapping quote 44444444-4444-4444-4444-444444444444 with status rejected to row format
[Log] Mapping quote 55555555-5555-5555-5555-555555555555 with status pending to row format
[Log] Mapping quote 22222222-2222-2222-2222-222222222222 with status pending to row format
[Log] Mapping quote b8c9d0e1-2345-ef67-8901-23abcdef4567 with status pending to row format
[Log] Mapping quote e5f6a7b8-9012-cdef-3456-789012abcdef with status pending to row format
[Log] Mapping quote d4e5f6a7-8901-bcde-f234-56789012abcd with status pending to row format
[Log] Mapping quote c3d4e5f6-7890-abcd-ef12-3456789012ab with status pending to row format
[Log] Mapping quote a7b8c9d0-1234-ef56-7890-12abcdef3456 with status pending to row format
[Log] Mapping quote f6a7b8c9-0123-def4-5678-9012abcdef34 with status pending to row format
[Log] Mapping quote b2a493af-6aba-44c0-a632-4c9710b22aca with status pending to row format
[Log] Mapping quote 986c5191-a5d3-425d-a488-31f332d7d7a8 with status pending to row format
[Log] Mapping quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 with status accepted to row format
[Log] Mapping quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 with status rejected to row format
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status pending to row format
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 0 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] [QuoteActionPanel] Showing no-affiliates UI - city: – "Boston"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 0 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] [QuoteActionPanel] Showing no-affiliates UI - city: – "Boston"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 0 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] [QuoteActionPanel] Showing no-affiliates UI - city: – "Boston"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 0 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] [QuoteActionPanel] Showing no-affiliates UI - city: – "Boston"
[Log] [QuoteActionPanel] Processing quote: – {id: "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e", city: "Boston", pickup: "10000 FFFFFFFF", …}
{id: "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e", city: "Boston", pickup: "10000 FFFFFFFF", vehicle: "SUV", activePanel: "action"}Object
[Log] [QuoteActionPanel] Processing quote: – {id: "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e", city: "Boston", pickup: "10000 FFFFFFFF", …}
{id: "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e", city: "Boston", pickup: "10000 FFFFFFFF", vehicle: "SUV", activePanel: "action"}Object
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 0 – "loading:" – true – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 0 – "loading:" – true – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 0 – "loading:" – true – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 0 – "loading:" – true – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 0 – "loading:" – true – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 0 – "loading:" – true – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 0 – "loading:" – true – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 0 – "loading:" – true – "error:" – null – "quoteHasOffers:" – false
[Log] [QuoteActionPanel] Quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e has offers: false
[Log] [getAffiliatesForQuote] Starting for quote ID: f2d5a135-eada-440a-ba5a-e9c3a2d2c19e
[Log] [QuoteActionPanel] Quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e has offers: false
[Log] [getAffiliatesForQuote] Starting for quote ID: f2d5a135-eada-440a-ba5a-e9c3a2d2c19e
[Log] [getAffiliatesForQuote] Quote details: – {id: "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e", city: "Boston", pickup_location: "10000 FFFFFFFF", …}
{id: "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e", city: "Boston", pickup_location: "10000 FFFFFFFF", status: "pending"}Object
[Log] [getAffiliatesForQuote] Fetching affiliates for city: Boston
[Log] [getAffiliatesForQuote] Quote details: – {id: "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e", city: "Boston", pickup_location: "10000 FFFFFFFF", …}
{id: "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e", city: "Boston", pickup_location: "10000 FFFFFFFF", status: "pending"}Object
[Log] [getAffiliatesForQuote] Fetching affiliates for city: Boston
[Error] Failed to load resource: the server responded with a status of 400 (Bad Request) (quote_offers, line 0)
[Error] Failed to load resource: the server responded with a status of 400 (Bad Request) (quote_offers, line 0)
[Error] Error fetching quote offers: – {code: "42703", details: null, hint: null, …}
{code: "42703", details: null, hint: null, message: "column quote_offers.affiliate_id does not exist"}Object
	(anonymous function) (app-index.js:33)
	(anonymous function) (hydration-error-info.js:63)
	(anonymous function) (quote-responses.ts:54)
[Error] Error fetching quote offers: – {code: "42703", details: null, hint: null, …}
{code: "42703", details: null, hint: null, message: "column quote_offers.affiliate_id does not exist"}Object
	(anonymous function) (app-index.js:33)
	(anonymous function) (hydration-error-info.js:63)
	(anonymous function) (quote-responses.ts:54)
[Error] Failed to load resource: the server responded with a status of 400 (Bad Request) (rate_proposals, line 0)
[Error] Error fetching rate proposals: – {code: "42703", details: null, hint: null, …}
{code: "42703", details: null, hint: null, message: "column rate_proposals.affiliate_id does not exist"}Object
	(anonymous function) (app-index.js:33)
	(anonymous function) (hydration-error-info.js:63)
	(anonymous function) (quote-responses.ts:63)
[Error] Failed to load resource: the server responded with a status of 400 (Bad Request) (rate_proposals, line 0)
[Error] Error fetching rate proposals: – {code: "42703", details: null, hint: null, …}
{code: "42703", details: null, hint: null, message: "column rate_proposals.affiliate_id does not exist"}Object
	(anonymous function) (app-index.js:33)
	(anonymous function) (hydration-error-info.js:63)
	(anonymous function) (quote-responses.ts:63)
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 0 – "loading:" – true – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 0 – "loading:" – true – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 0 – "loading:" – true – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 0 – "loading:" – true – "error:" – null – "quoteHasOffers:" – false
[Log] [getAffiliatesForQuote] API returned 1 affiliates
[Log] [getAffiliatesForQuote] Found 1 affiliates for city: Boston
[Log] [QuoteActionPanel] Found 1 affiliates for city: Boston
[Log] [getAffiliatesForQuote] API returned 1 affiliates
[Log] [getAffiliatesForQuote] Found 1 affiliates for city: Boston
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] Quote action: – "update_sequence" – [Object] (1)
[Object]Array (1)
[Warning] Unknown quote action: – "update_sequence"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] Quote action: – "update_sequence" – [Object] (1)
[Object]Array (1)
[Warning] Unknown quote action: – "update_sequence"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] Quote action: – "update_sequence" – [Object] (1)
[Object]Array (1)
[Warning] Unknown quote action: – "update_sequence"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
> Selected Element
< <div role="dialog" id="radix-:rc:" aria-describedby="radix-:re:" aria-labelledby="radix-:rd:" data-state="open" class="fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500 inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-md md:max-w-lg lg:max-w-xl overflow-y-auto" tabindex="-1" style="pointer-events: auto;">…</div>
[Log] Auth state changed: – "TOKEN_REFRESHED" – "session exists"
[Log] User ID: – "2b2a2488-a406-49d1-ae51-1d1c35a2a813"
[Log] User email: – "<EMAIL>"
[Log] Syncing auth token from localStorage to cookies
[Log] [AuthProvider] – "Auth state changed:" – "TOKEN_REFRESHED" – "session exists"
[Log] Auth token synced to cookies
[Log] [Fast Refresh] rebuilding
[Log] [Fast Refresh] done in 1041ms
[Log] Rendering quotes page with – 21 – "quotes"
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status pending to row format
[Log] Mapping quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 with status pending to row format
[Log] Mapping quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 with status pending to row format
[Log] Mapping quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a with status pending to row format
[Log] Mapping quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c with status pending to row format
[Log] Mapping quote 0e967316-89e6-4e00-b9a3-fdb01d511157 with status pending to row format
[Log] Mapping quote 11111111-1111-1111-1111-111111111111 with status pending to row format
[Log] Mapping quote 33333333-3333-3333-3333-333333333333 with status accepted to row format
[Log] Mapping quote 44444444-4444-4444-4444-444444444444 with status rejected to row format
[Log] Mapping quote 55555555-5555-5555-5555-555555555555 with status pending to row format
[Log] Mapping quote 22222222-2222-2222-2222-222222222222 with status pending to row format
[Log] Mapping quote b8c9d0e1-2345-ef67-8901-23abcdef4567 with status pending to row format
[Log] Mapping quote e5f6a7b8-9012-cdef-3456-789012abcdef with status pending to row format
[Log] Mapping quote d4e5f6a7-8901-bcde-f234-56789012abcd with status pending to row format
[Log] Mapping quote c3d4e5f6-7890-abcd-ef12-3456789012ab with status pending to row format
[Log] Mapping quote a7b8c9d0-1234-ef56-7890-12abcdef3456 with status pending to row format
[Log] Mapping quote f6a7b8c9-0123-def4-5678-9012abcdef34 with status pending to row format
[Log] Mapping quote b2a493af-6aba-44c0-a632-4c9710b22aca with status pending to row format
[Log] Mapping quote 986c5191-a5d3-425d-a488-31f332d7d7a8 with status pending to row format
[Log] Mapping quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 with status accepted to row format
[Log] Mapping quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 with status rejected to row format
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status pending to row format
[Log] Rendering quotes page with – 21 – "quotes"
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status pending to row format
[Log] Mapping quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 with status pending to row format
[Log] Mapping quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 with status pending to row format
[Log] Mapping quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a with status pending to row format
[Log] Mapping quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c with status pending to row format
[Log] Mapping quote 0e967316-89e6-4e00-b9a3-fdb01d511157 with status pending to row format
[Log] Mapping quote 11111111-1111-1111-1111-111111111111 with status pending to row format
[Log] Mapping quote 33333333-3333-3333-3333-333333333333 with status accepted to row format
[Log] Mapping quote 44444444-4444-4444-4444-444444444444 with status rejected to row format
[Log] Mapping quote 55555555-5555-5555-5555-555555555555 with status pending to row format
[Log] Mapping quote 22222222-2222-2222-2222-222222222222 with status pending to row format
[Log] Mapping quote b8c9d0e1-2345-ef67-8901-23abcdef4567 with status pending to row format
[Log] Mapping quote e5f6a7b8-9012-cdef-3456-789012abcdef with status pending to row format
[Log] Mapping quote d4e5f6a7-8901-bcde-f234-56789012abcd with status pending to row format
[Log] Mapping quote c3d4e5f6-7890-abcd-ef12-3456789012ab with status pending to row format
[Log] Mapping quote a7b8c9d0-1234-ef56-7890-12abcdef3456 with status pending to row format
[Log] Mapping quote f6a7b8c9-0123-def4-5678-9012abcdef34 with status pending to row format
[Log] Mapping quote b2a493af-6aba-44c0-a632-4c9710b22aca with status pending to row format
[Log] Mapping quote 986c5191-a5d3-425d-a488-31f332d7d7a8 with status pending to row format
[Log] Mapping quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 with status accepted to row format
[Log] Mapping quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 with status rejected to row format
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status pending to row format
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] [Fast Refresh] rebuilding
[Log] [Fast Refresh] done in 507ms
[Log] Rendering quotes page with – 21 – "quotes"
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status pending to row format
[Log] Mapping quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 with status pending to row format
[Log] Mapping quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 with status pending to row format
[Log] Mapping quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a with status pending to row format
[Log] Mapping quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c with status pending to row format
[Log] Mapping quote 0e967316-89e6-4e00-b9a3-fdb01d511157 with status pending to row format
[Log] Mapping quote 11111111-1111-1111-1111-111111111111 with status pending to row format
[Log] Mapping quote 33333333-3333-3333-3333-333333333333 with status accepted to row format
[Log] Mapping quote 44444444-4444-4444-4444-444444444444 with status rejected to row format
[Log] Mapping quote 55555555-5555-5555-5555-555555555555 with status pending to row format
[Log] Mapping quote 22222222-2222-2222-2222-222222222222 with status pending to row format
[Log] Mapping quote b8c9d0e1-2345-ef67-8901-23abcdef4567 with status pending to row format
[Log] Mapping quote e5f6a7b8-9012-cdef-3456-789012abcdef with status pending to row format
[Log] Mapping quote d4e5f6a7-8901-bcde-f234-56789012abcd with status pending to row format
[Log] Mapping quote c3d4e5f6-7890-abcd-ef12-3456789012ab with status pending to row format
[Log] Mapping quote a7b8c9d0-1234-ef56-7890-12abcdef3456 with status pending to row format
[Log] Mapping quote f6a7b8c9-0123-def4-5678-9012abcdef34 with status pending to row format
[Log] Mapping quote b2a493af-6aba-44c0-a632-4c9710b22aca with status pending to row format
[Log] Mapping quote 986c5191-a5d3-425d-a488-31f332d7d7a8 with status pending to row format
[Log] Mapping quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 with status accepted to row format
[Log] Mapping quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 with status rejected to row format
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status pending to row format
[Log] Rendering quotes page with – 21 – "quotes"
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status pending to row format
[Log] Mapping quote 6bdc538a-0f61-4622-a1d3-e9cda0511d98 with status pending to row format
[Log] Mapping quote 783215f2-4888-4759-8be4-84cf4d2ae7a1 with status pending to row format
[Log] Mapping quote dd981cb7-c9f9-4c5e-9463-ef1ee1e3af6a with status pending to row format
[Log] Mapping quote fdcbacd4-6a1c-4ea4-a7f5-a305449d202c with status pending to row format
[Log] Mapping quote 0e967316-89e6-4e00-b9a3-fdb01d511157 with status pending to row format
[Log] Mapping quote 11111111-1111-1111-1111-111111111111 with status pending to row format
[Log] Mapping quote 33333333-3333-3333-3333-333333333333 with status accepted to row format
[Log] Mapping quote 44444444-4444-4444-4444-444444444444 with status rejected to row format
[Log] Mapping quote 55555555-5555-5555-5555-555555555555 with status pending to row format
[Log] Mapping quote 22222222-2222-2222-2222-222222222222 with status pending to row format
[Log] Mapping quote b8c9d0e1-2345-ef67-8901-23abcdef4567 with status pending to row format
[Log] Mapping quote e5f6a7b8-9012-cdef-3456-789012abcdef with status pending to row format
[Log] Mapping quote d4e5f6a7-8901-bcde-f234-56789012abcd with status pending to row format
[Log] Mapping quote c3d4e5f6-7890-abcd-ef12-3456789012ab with status pending to row format
[Log] Mapping quote a7b8c9d0-1234-ef56-7890-12abcdef3456 with status pending to row format
[Log] Mapping quote f6a7b8c9-0123-def4-5678-9012abcdef34 with status pending to row format
[Log] Mapping quote b2a493af-6aba-44c0-a632-4c9710b22aca with status pending to row format
[Log] Mapping quote 986c5191-a5d3-425d-a488-31f332d7d7a8 with status pending to row format
[Log] Mapping quote c83d4446-97cc-4dc5-bc10-7a7eef16d1a9 with status accepted to row format
[Log] Mapping quote db451c0f-0331-4ae7-bd90-ca9ed125d5a9 with status rejected to row format
[Log] Mapping quote f2d5a135-eada-440a-ba5a-e9c3a2d2c19e with status pending to row format
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderContent - activePanel: – "action" – "quote.status:" – "pending" – "quote.date:" – "2025-03-16"
[Log] isNewQuote called with status: pending, lowercase: pending
[Log] isNewQuote result: true
[Log] [QuoteActionPanel] renderNewQuoteContent - quote ID: – "f2d5a135-eada-440a-ba5a-e9c3a2d2c19e" – "city:" – "Boston" – "affiliates:" – 1 – "loading:" – false – "error:" – null – "quoteHasOffers:" – false