# Portals Directory

## Overview
The `(portals)` directory contains role-specific sections of the application. Each subdirectory represents a different user role with its own layout, navigation, and features.

## Structure
- `admin/` - Admin portal for system administrators
- `affiliate/` - Affiliate portal for transportation providers
- `customer/` - Customer portal for event organizers
- `event-manager/` - Event Manager portal for managing events and transportation
- `company/` - Company portal for company administrators
- `components/` - Shared components specific to portals
- `events/` - General event-related pages
- `manager/` - General management pages
- `passengers/` - Passenger-related pages

## Naming Conventions
- Directory names should use camelCase (not kebab-case)
- Component directories should be placed at the feature level when specific to a feature
- Shared components should be placed in the global components directory
- Component files should use PascalCase (e.g., `Button.tsx`, `NotificationBanner.tsx`)

## Routing
Each portal follows the Next.js App Router conventions:
- `layout.tsx` - Defines the layout for the entire portal
- `page.tsx` - Defines the main page for a route
- Dynamic routes use `[paramName]` syntax

## Authentication and Authorization
Each portal has its own access control based on user roles. The middleware ensures users can only access portals they have permission for.

## Shared Components
Many UI components are shared across portals. These are located in the global `/app/components` directory.

## Testing
Each portal should have corresponding tests in the `/tests` directory that mirror the portal structure. 