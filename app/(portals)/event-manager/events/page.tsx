'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/auth/context'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Button } from '@/app/components/ui/button'
import { Badge } from '@/app/components/ui/badge'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs'
import Link from 'next/link'
import {
  CalendarIcon,
  PlusIcon,
  SearchIcon,
  Users,
  MapPin,
  Clock,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  Calendar,
  BarChart3,
  Filter,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'
import { Input } from '@/app/components/ui/input'

export default function EventManagerEvents() {
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [events, setEvents] = useState([])
  const [loading, setLoading] = useState(true)

  // Enhanced mock data with more details
  const mockEvents = [
    {
      id: '1',
      name: 'Annual Tech Conference',
      date: '2025-06-15',
      time: '09:00',
      location: 'San Francisco, CA',
      status: 'confirmed',
      attendees: 120,
      transportation_requests: 8,
      budget: 25000,
      description: 'Annual technology conference with keynote speakers',
      venue: 'Moscone Center',
      created_at: '2025-05-01T10:00:00Z'
    },
    {
      id: '2',
      name: 'Corporate Retreat',
      date: '2025-06-22',
      time: '08:00',
      location: 'Lake Tahoe, NV',
      status: 'planning',
      attendees: 45,
      transportation_requests: 3,
      budget: 15000,
      description: 'Team building and strategic planning retreat',
      venue: 'Tahoe Resort',
      created_at: '2025-05-10T14:30:00Z'
    },
    {
      id: '3',
      name: 'Product Launch',
      date: '2025-07-05',
      time: '18:00',
      location: 'New York, NY',
      status: 'pending',
      attendees: 75,
      transportation_requests: 5,
      budget: 35000,
      description: 'Launch event for our new product line',
      venue: 'Madison Square Garden',
      created_at: '2025-05-15T09:15:00Z'
    },
    {
      id: '4',
      name: 'Annual Shareholders Meeting',
      date: '2025-07-15',
      time: '10:00',
      location: 'Chicago, IL',
      status: 'confirmed',
      attendees: 50,
      transportation_requests: 12,
      budget: 20000,
      description: 'Annual meeting with shareholders and board members',
      venue: 'Chicago Convention Center',
      created_at: '2025-04-20T16:45:00Z'
    },
    {
      id: '5',
      name: 'Team Building Workshop',
      date: '2025-07-28',
      time: '13:00',
      location: 'Austin, TX',
      status: 'planning',
      attendees: 30,
      transportation_requests: 2,
      budget: 8000,
      description: 'Interactive team building activities and workshops',
      venue: 'Austin Convention Center',
      created_at: '2025-05-25T11:20:00Z'
    }
  ]

  useEffect(() => {
    // Simulate API call
    const fetchEvents = async () => {
      setLoading(true)
      // In real app, this would be: const response = await fetch('/api/event-manager/events')
      setTimeout(() => {
        setEvents(mockEvents)
        setLoading(false)
      }, 1000)
    }

    fetchEvents()
  }, [])

  // Calculate metrics
  const stats = {
    total: events.length,
    confirmed: events.filter(e => e.status === 'confirmed').length,
    planning: events.filter(e => e.status === 'planning').length,
    pending: events.filter(e => e.status === 'pending').length,
    totalAttendees: events.reduce((sum, e) => sum + e.attendees, 0),
    totalBudget: events.reduce((sum, e) => sum + e.budget, 0),
    totalTransportRequests: events.reduce((sum, e) => sum + e.transportation_requests, 0)
  }

  // Filter events based on search and tab
  const filteredEvents = events.filter(event => {
    const matchesSearch = event.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         event.location.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesTab = activeTab === 'all' || event.status === activeTab
    return matchesSearch && matchesTab
  })

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800 border-green-200'
      case 'planning': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'confirmed': return <CheckCircle className="h-4 w-4" />
      case 'planning': return <Clock className="h-4 w-4" />
      case 'pending': return <AlertCircle className="h-4 w-4" />
      default: return <Calendar className="h-4 w-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Event Management</h1>
          <p className="text-muted-foreground">Organize and manage your events with transportation coordination</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/event-manager/quotes">
              <BarChart3 className="mr-2 h-4 w-4" />
              View Quotes
            </Link>
          </Button>
          <Button asChild>
            <Link href="/event-manager/events/new">
              <PlusIcon className="mr-2 h-4 w-4" />
              Create Event
            </Link>
          </Button>
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-2xl font-bold">{stats.total}</p>
                <p className="text-xs text-muted-foreground">Total Events</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <div>
                <p className="text-2xl font-bold">{stats.confirmed}</p>
                <p className="text-xs text-muted-foreground">Confirmed</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-2xl font-bold">{stats.planning}</p>
                <p className="text-xs text-muted-foreground">Planning</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-4 w-4 text-yellow-500" />
              <div>
                <p className="text-2xl font-bold">{stats.pending}</p>
                <p className="text-xs text-muted-foreground">Pending</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-purple-500" />
              <div>
                <p className="text-2xl font-bold">{stats.totalAttendees}</p>
                <p className="text-xs text-muted-foreground">Attendees</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-2xl font-bold">${(stats.totalBudget / 1000).toFixed(0)}K</p>
                <p className="text-xs text-muted-foreground">Total Budget</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CalendarIcon className="h-4 w-4 text-orange-500" />
              <div>
                <p className="text-2xl font-bold">{stats.totalTransportRequests}</p>
                <p className="text-xs text-muted-foreground">Transport Requests</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search events by name or location..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button variant="outline">
          <Filter className="mr-2 h-4 w-4" />
          Advanced Filter
        </Button>
      </div>

      {/* Events List with Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all">All Events ({stats.total})</TabsTrigger>
          <TabsTrigger value="confirmed">Confirmed ({stats.confirmed})</TabsTrigger>
          <TabsTrigger value="planning">Planning ({stats.planning})</TabsTrigger>
          <TabsTrigger value="pending">Pending ({stats.pending})</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {loading ? (
            <Card>
              <CardContent className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading events...</p>
              </CardContent>
            </Card>
          ) : filteredEvents.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No Events Found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery ? `No events match "${searchQuery}"` : `No ${activeTab} events at the moment.`}
                </p>
                <Button asChild>
                  <Link href="/event-manager/events/new">
                    <PlusIcon className="mr-2 h-4 w-4" />
                    Create Your First Event
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4">
              {filteredEvents.map((event) => (
                <Card key={event.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold">{event.name}</h3>
                          <Badge className={`${getStatusColor(event.status)} border`}>
                            {getStatusIcon(event.status)}
                            <span className="ml-1 capitalize">{event.status}</span>
                          </Badge>
                        </div>

                        <p className="text-muted-foreground text-sm mb-3">{event.description}</p>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span>{new Date(event.date).toLocaleDateString()} at {event.time}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-muted-foreground" />
                            <span>{event.location}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4 text-muted-foreground" />
                            <span>{event.attendees} attendees</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                            <span>{event.transportation_requests} transport requests</span>
                          </div>
                        </div>

                        <div className="flex items-center gap-4 mt-3 text-sm text-muted-foreground">
                          <span>Budget: ${event.budget.toLocaleString()}</span>
                          <span>Venue: {event.venue}</span>
                        </div>
                      </div>

                      <div className="flex items-center gap-2 ml-4">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/event-manager/events/${event.id}`}>
                            <Eye className="h-4 w-4 mr-1" />
                            View
                          </Link>
                        </Button>
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/event-manager/events/${event.id}/edit`}>
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Link>
                        </Button>
                        <Button variant="outline" size="sm">
                          <Link href={`/event-manager/quotes/new?event=${event.id}`}>
                            <PlusIcon className="h-4 w-4 mr-1" />
                            Request Transport
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}