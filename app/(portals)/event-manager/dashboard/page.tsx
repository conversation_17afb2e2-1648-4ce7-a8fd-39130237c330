'use client'

import { useAuth } from '@/lib/auth/context'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/app/components/ui/tabs'
import { CalendarIcon, CarIcon, ClipboardListIcon, UsersIcon, Plus, ArrowRight, Clock, CheckCircle, DollarSign } from 'lucide-react'
import { Button } from '@/app/components/ui/button'
import Link from 'next/link'
import { OriginalBookingForm } from '@/app/components/original-booking/NextJSWrapper'

export default function EventManagerDashboard() {
  const { user } = useAuth()

  const stats = [
    {
      title: 'Active Events',
      value: '12',
      description: 'Events in progress',
      icon: <CalendarIcon className="h-5 w-5 text-muted-foreground" />
    },
    {
      title: 'Pending Quotes',
      value: '24',
      description: 'Awaiting approval',
      icon: <ClipboardListIcon className="h-5 w-5 text-muted-foreground" />
    },
    {
      title: 'Assigned Vehicles',
      value: '36',
      description: 'Currently in service',
      icon: <CarIcon className="h-5 w-5 text-muted-foreground" />
    },
    {
      title: 'Total Passengers',
      value: '248',
      description: 'Across all events',
      icon: <UsersIcon className="h-5 w-5 text-muted-foreground" />
    }
  ]

  const upcomingEvents = [
    { id: '1', name: 'Annual Tech Conference', date: 'Mar 15, 2023', location: 'San Francisco, CA', status: 'Confirmed' },
    { id: '2', name: 'Corporate Retreat', date: 'Mar 22, 2023', location: 'Lake Tahoe, NV', status: 'Planning' },
    { id: '3', name: 'Product Launch', date: 'Apr 5, 2023', location: 'New York, NY', status: 'Pending' }
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Transportation Management</h1>
          <p className="text-muted-foreground">Request quotes, manage events, and track your transportation needs</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/event-manager/quotes">
              <ClipboardListIcon className="h-4 w-4 mr-2" />
              View All Quotes
            </Link>
          </Button>
          <Button asChild>
            <Link href="/event-manager/events/new">
              <Plus className="h-4 w-4 mr-2" />
              Create Event
            </Link>
          </Button>
        </div>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              {stat.icon}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">{stat.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Quote Request Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quote Request Form - Main CTA */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Request Transportation Quote
              </CardTitle>
              <CardDescription>
                Get instant quotes from our network of transportation providers
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <OriginalBookingForm
                formType="point-to-point"
              />
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions & Recent Activity */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button asChild className="w-full justify-start">
                <Link href="/event-manager/quotes">
                  <ClipboardListIcon className="h-4 w-4 mr-2" />
                  View My Quotes
                </Link>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <Link href="/event-manager/events">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  Manage Events
                </Link>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <Link href="/event-manager/trips">
                  <CarIcon className="h-4 w-4 mr-2" />
                  Track Trips
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* Recent Quote Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Quote Q123456 accepted</span>
                  <span className="text-muted-foreground ml-auto">2h ago</span>
                </div>
                <div className="flex items-center gap-3 text-sm">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <span>Quote Q789012 pending</span>
                  <span className="text-muted-foreground ml-auto">4h ago</span>
                </div>
                <div className="flex items-center gap-3 text-sm">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>New event created</span>
                  <span className="text-muted-foreground ml-auto">1d ago</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Tabs defaultValue="upcoming">
        <TabsList>
          <TabsTrigger value="upcoming">Upcoming Events</TabsTrigger>
          <TabsTrigger value="active">Active Events</TabsTrigger>
          <TabsTrigger value="past">Past Events</TabsTrigger>
        </TabsList>
        <TabsContent value="upcoming" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Events</CardTitle>
              <CardDescription>
                Events scheduled in the next 30 days
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingEvents.map((event) => (
                  <div key={event.id} className="flex items-center justify-between border-b pb-4">
                    <div>
                      <h3 className="font-medium">{event.name}</h3>
                      <div className="text-sm text-muted-foreground">
                        {event.date} • {event.location}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className={`text-sm px-2 py-1 rounded-full ${
                        event.status === 'Confirmed' ? 'bg-green-100 text-green-800' :
                        event.status === 'Planning' ? 'bg-blue-100 text-blue-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {event.status}
                      </span>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/event-manager/events/${event.id}`}>View</Link>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="active">
          <Card>
            <CardHeader>
              <CardTitle>Active Events</CardTitle>
              <CardDescription>
                Events currently in progress
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">No active events at the moment.</p>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="past">
          <Card>
            <CardHeader>
              <CardTitle>Past Events</CardTitle>
              <CardDescription>
                Completed events
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">No past events to display.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 