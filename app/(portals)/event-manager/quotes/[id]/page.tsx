import { Suspense } from 'react'
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card } from "@/app/components/ui/card"
import { ArrowLeft, Pencil, Send, MessageSquare } from "lucide-react"
import Link from "next/link"
import { Badge } from "@/app/components/ui/badge"
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { Textarea } from "@/app/components/ui/textarea"

async function QuoteDetail({ quoteId }: { quoteId: string }) {
  // Create server-side Supabase client
  const cookieStore = cookies()
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
      },
    }
  )

  // Fetch quote data directly
  const { data: quote, error } = await supabase
    .from('quotes')
    .select(`
      *,
      customer:profiles(id, email, full_name, phone_number)
    `)
    .eq('id', quoteId)
    .single()

  if (error) {
    return <div>Error loading quote: {error?.message || 'Unknown error occurred'}</div>
  }

  if (!quote) {
    return <div>Quote not found</div>
  }

  return (
    <Card className="p-6">
      <div className="flex justify-between items-center mb-6">
        <Link href="/quotes" className="flex items-center text-sm text-gray-500 hover:text-gray-700">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Quotes
        </Link>
        <Badge variant={quote.status === 'accepted' ? 'default' : 'secondary'}>
          {quote.status}
        </Badge>
      </div>

      <h1 className="text-2xl font-bold mb-4">Quote #{quote.reference_number}</h1>
      <p className="text-gray-600 mb-6">{quote.special_requests || 'No special requests'}</p>

      <div className="grid gap-4">
        <div className="flex justify-between items-center">
          <span className="text-gray-500">Amount:</span>
          <span className="font-semibold">${quote.total_amount || 0}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-gray-500">Service Type:</span>
          <span>{quote.service_type}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-gray-500">Created At:</span>
          <span>{new Date(quote.created_at).toLocaleDateString()}</span>
        </div>
      </div>
    </Card>
  )
}

export default function QuoteDetailPage({ params }: { params: { id: string } }) {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <QuoteDetail quoteId={params.id} />
    </Suspense>
  )
}