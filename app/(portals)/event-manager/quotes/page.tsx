"use client"

import { useState } from "react"
import { But<PERSON> } from "@/app/components/ui/button"
import { Card } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/app/components/ui/tabs"
import {
  Download,
  Plus,
  BarChart3,
  Users,
  Calendar,
  Search,
  Clock,
  CheckCircle,
  DollarSign,
  Car
} from "lucide-react"
import { useRouter } from "next/navigation"
import { ClientQuoteAnalyticsDashboard } from "./components/ClientQuoteAnalyticsDashboard"
import { ClientQuoteManagement } from "./components/ClientQuoteManagement"
export default function QuotesPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("quotes")
  const [globalSearch, setGlobalSearch] = useState("")

  // Mock stats - should come from API
  const globalStats = {
    totalQuotes: 22,
    pendingQuotes: 3,
    acceptedQuotes: 15,
    completedQuotes: 4,
    totalValue: 12400,
    activeEvents: 2,
    upcomingTrips: 6
  }

  return (
    <div className="container p-6 space-y-6">
      {/* Header with title and actions */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold">Transportation Management</h1>
          <p className="text-sm text-muted-foreground">Manage quotes, events, and track your transportation needs</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={() => router.push('/event-manager/quotes/new')}>
            <Plus className="h-4 w-4 mr-2" />
            New Request
          </Button>
        </div>
      </div>

      {/* Global Search */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search quotes, events, or trips..."
            value={globalSearch}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setGlobalSearch(e.target.value)}
            className="pl-9"
          />
        </div>
        <div className="text-sm text-muted-foreground">
          {globalStats.totalQuotes} quotes • {globalStats.activeEvents} active events
        </div>
      </div>

      {/* Global Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <BarChart3 className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <div className="text-xl font-bold">{globalStats.totalQuotes}</div>
              <div className="text-xs text-muted-foreground">Total Quotes</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Clock className="h-4 w-4 text-yellow-600" />
            </div>
            <div>
              <div className="text-xl font-bold">{globalStats.pendingQuotes}</div>
              <div className="text-xs text-muted-foreground">Pending</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="h-4 w-4 text-green-600" />
            </div>
            <div>
              <div className="text-xl font-bold">{globalStats.acceptedQuotes}</div>
              <div className="text-xs text-muted-foreground">Accepted</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <CheckCircle className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <div className="text-xl font-bold">{globalStats.completedQuotes}</div>
              <div className="text-xs text-muted-foreground">Completed</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <DollarSign className="h-4 w-4 text-green-600" />
            </div>
            <div>
              <div className="text-xl font-bold">${(globalStats.totalValue / 1000).toFixed(1)}k</div>
              <div className="text-xs text-muted-foreground">Total Value</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Calendar className="h-4 w-4 text-purple-600" />
            </div>
            <div>
              <div className="text-xl font-bold">{globalStats.activeEvents}</div>
              <div className="text-xs text-muted-foreground">Active Events</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-indigo-100 rounded-lg">
              <Car className="h-4 w-4 text-indigo-600" />
            </div>
            <div>
              <div className="text-xl font-bold">{globalStats.upcomingTrips}</div>
              <div className="text-xs text-muted-foreground">Upcoming Trips</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Main Quote Management Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="quotes" className="flex items-center space-x-2">
            <Users className="h-4 w-4" />
            <span>My Quotes</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Analytics</span>
          </TabsTrigger>
          <TabsTrigger value="events" className="flex items-center space-x-2">
            <Calendar className="h-4 w-4" />
            <span>Event Planning</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="quotes" className="space-y-6">
          <ClientQuoteManagement globalSearch={globalSearch} />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <ClientQuoteAnalyticsDashboard />
        </TabsContent>

        <TabsContent value="events" className="space-y-6">
          {/* Event-based transportation planning */}
          <Card className="p-6">
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">Event Transportation Planning</h3>
              <p className="text-muted-foreground mb-4">
                Plan transportation for your events with bulk booking and coordination tools
              </p>
              <div className="flex justify-center gap-2">
                <Button onClick={() => router.push('/event-manager/events')}>
                  View Events
                </Button>
                <Button variant="outline" onClick={() => router.push('/event-manager/events/new')}>
                  Create Event
                </Button>
              </div>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}