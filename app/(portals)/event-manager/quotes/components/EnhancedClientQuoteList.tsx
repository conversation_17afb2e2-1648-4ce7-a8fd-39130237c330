"use client"

import { useState, useEffect } from "react"
import { QuoteRow, QuoteRowData } from "@/app/components/shared/rows/QuoteRow"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/app/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Badge } from "@/app/components/ui/badge"
import { Button } from "@/app/components/ui/button"
import { 
  Clock, 
  CheckCircle, 
  X, 
  AlertCircle, 
  DollarSign,
  Users,
  Car,
  Calendar,
  MapPin,
  Eye
} from "lucide-react"

interface Quote {
  id: string
  reference_number: string
  pickup_location: string
  dropoff_location: string
  city: string
  pickup_date: string
  pickup_time: string
  passenger_count: number
  vehicle_type_preference?: string
  service_type: string
  status: string
  created_at: string
  client_name?: string
  total_price?: number
  affiliate_responses?: Array<{
    affiliate_name: string
    status: string
    price: number
    tier: string
  }>
}

interface QuoteStats {
  total: number
  pending: number
  accepted: number
  rejected: number
  completed: number
  totalValue: number
}

interface EnhancedClientQuoteListProps {
  statusFilter?: string
}

export function EnhancedClientQuoteList({ statusFilter }: EnhancedClientQuoteListProps) {
  const [quotes, setQuotes] = useState<Quote[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("all")
  const [selectedQuote, setSelectedQuote] = useState<string | null>(null)

  useEffect(() => {
    fetchQuotes()
  }, [statusFilter])

  const fetchQuotes = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (statusFilter && statusFilter !== 'all') {
        params.append('status', statusFilter)
      }

      const response = await fetch(`/api/event-manager/quotes?${params}`)
      console.log('EnhancedClientQuoteList - API Response Status:', response.status)

      if (response.ok) {
        const data = await response.json()
        console.log('EnhancedClientQuoteList - API Response Data:', data)
        setQuotes(data.quotes || [])
      } else {
        console.error('EnhancedClientQuoteList - API Error:', response.status, response.statusText)
        setQuotes([])
      }
    } catch (error) {
      console.error('Error fetching quotes:', error)
      setQuotes([])
    } finally {
      setLoading(false)
    }
  }

  // Transform quotes to QuoteRowData format
  const transformedQuotes: QuoteRowData[] = quotes.map(quote => ({
    id: quote.id,
    reference_number: quote.reference_number,
    pickup_location: quote.pickup_location,
    dropoff_location: quote.dropoff_location,
    city: quote.city,
    pickup_date: quote.pickup_date,
    pickup_time: quote.pickup_time,
    passenger_count: quote.passenger_count,
    vehicle_type: quote.vehicle_type_preference || 'Any Vehicle',
    service_type: quote.service_type,
    status: quote.status,
    created_at: quote.created_at,
    client_name: quote.client_name || 'Unknown Customer',
    total_amount: quote.total_price,
    affiliate_responses: quote.affiliate_responses || []
  }))

  // Calculate stats
  const stats: QuoteStats = {
    total: quotes.length,
    pending: quotes.filter(q => q.status === 'pending').length,
    accepted: quotes.filter(q => q.status === 'accepted').length,
    rejected: quotes.filter(q => q.status === 'rejected').length,
    completed: quotes.filter(q => q.status === 'completed').length,
    totalValue: quotes.reduce((sum, q) => sum + (q.total_price || 0), 0)
  }

  // Filter quotes based on active tab
  const filteredQuotes = transformedQuotes.filter(quote => {
    if (activeTab === 'all') return true
    if (activeTab === 'pending') return quote.status === 'pending'
    if (activeTab === 'accepted') return quote.status === 'accepted'
    if (activeTab === 'rejected') return quote.status === 'rejected'
    if (activeTab === 'completed') return quote.status === 'completed'
    return true
  })

  // Tab configuration
  const tabConfig = [
    { id: 'all', label: 'All Quotes', count: stats.total, icon: Users },
    { id: 'pending', label: 'Pending', count: stats.pending, icon: Clock },
    { id: 'accepted', label: 'Accepted', count: stats.accepted, icon: CheckCircle },
    { id: 'rejected', label: 'Rejected', count: stats.rejected, icon: X },
    { id: 'completed', label: 'Completed', count: stats.completed, icon: CheckCircle }
  ]

  const handleQuoteClick = (quote: QuoteRowData) => {
    setSelectedQuote(selectedQuote === quote.id ? null : quote.id)
  }

  const handleViewDetails = (quote: QuoteRowData) => {
    window.location.href = `/event-manager/quotes/${quote.id}`
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading your quotes...</p>
        </CardContent>
      </Card>
    )
  }

  if (quotes.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Car className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No Quotes Found</h3>
          <p className="text-muted-foreground mb-4">
            {statusFilter && statusFilter !== 'all' 
              ? `No ${statusFilter} quotes at the moment.`
              : 'You haven\'t created any transportation quotes yet.'
            }
          </p>
          <Button onClick={() => window.location.href = '/event-manager/quotes/new'}>
            Create Your First Quote
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-2xl font-bold">{stats.total}</p>
                <p className="text-xs text-muted-foreground">Total Quotes</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-yellow-500" />
              <div>
                <p className="text-2xl font-bold">{stats.pending}</p>
                <p className="text-xs text-muted-foreground">Pending</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <div>
                <p className="text-2xl font-bold">{stats.accepted}</p>
                <p className="text-xs text-muted-foreground">Accepted</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <X className="h-4 w-4 text-red-500" />
              <div>
                <p className="text-2xl font-bold">{stats.rejected}</p>
                <p className="text-xs text-muted-foreground">Rejected</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-2xl font-bold">${stats.totalValue.toLocaleString()}</p>
                <p className="text-xs text-muted-foreground">Total Value</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs Navigation */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          {tabConfig.map((tab) => (
            <TabsTrigger key={tab.id} value={tab.id} className="flex items-center gap-2">
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
              <Badge variant="secondary" className="ml-1">{tab.count}</Badge>
            </TabsTrigger>
          ))}
        </TabsList>

        {tabConfig.map((tab) => (
          <TabsContent key={tab.id} value={tab.id} className="space-y-2">
            {filteredQuotes.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <AlertCircle className="h-8 w-8 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">No {tab.label.toLowerCase()} found</p>
                </CardContent>
              </Card>
            ) : (
              filteredQuotes.map((quote) => (
                <QuoteRow
                  key={quote.id}
                  quote={quote}
                  isSelected={selectedQuote === quote.id}
                  onClick={() => handleQuoteClick(quote)}
                  userType="customer"
                  showActions={true}
                  expandable={true}
                />
              ))
            )}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  )
}
