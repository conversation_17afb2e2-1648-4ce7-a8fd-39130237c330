"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Badge } from "@/app/components/ui/badge"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import { Label } from "@/app/components/ui/label"
import { Textarea } from "@/app/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import {
  Plus,
  MapPin,
  Users,
  Clock,
  Calendar,
  Car,
  ArrowRight,
  CheckCircle,
  AlertTriangle,
  Loader2,
  Star,
  Shield,
  Crown
} from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/app/components/ui/tabs"

interface QuoteRequest {
  pickupLocation: string
  dropoffLocation: string
  city: string
  date: string
  time: string
  passengers: number
  vehicleType: string
  serviceType: 'point_to_point' | 'hourly' | 'airport'
  specialRequirements: string
  eventId?: string
}

interface AffiliateOffer {
  id: string
  affiliateId: string
  affiliateName: string
  affiliateTier: 'Elite' | 'Premium' | 'Standard'
  vehicleType: string
  vehicleModel: string
  capacity: number
  baseRate: number
  hourlyRate?: number
  totalPrice: number
  responseTime: string
  rating: number
  features: string[]
  availability: 'confirmed' | 'pending' | 'unavailable'
  estimatedArrival: string
  notes?: string
}

export function QuoteRequestFlow() {
  const [step, setStep] = useState<'request' | 'matching' | 'offers'>('request')
  const [loading, setLoading] = useState(false)
  const [quoteRequest, setQuoteRequest] = useState<QuoteRequest>({
    pickupLocation: '',
    dropoffLocation: '',
    city: '',
    date: '',
    time: '',
    passengers: 1,
    vehicleType: 'any',
    serviceType: 'point_to_point',
    specialRequirements: '',
    eventId: undefined
  })
  const [offers, setOffers] = useState<AffiliateOffer[]>([])

  const handleSubmitRequest = async () => {
    setLoading(true)
    setStep('matching')

    try {
      // Fetch real affiliate data based on quote request
      const response = await fetch('/api/event-manager/quotes/match-affiliates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          city: quoteRequest.city,
          vehicleType: quoteRequest.vehicleType === 'any' ? null : quoteRequest.vehicleType,
          serviceType: quoteRequest.serviceType,
          date: quoteRequest.date,
          time: quoteRequest.time,
          passengers: quoteRequest.passengers
        })
      })

      if (!response.ok) {
        throw new Error('Failed to fetch affiliate matches')
      }

      const data = await response.json()

      // Transform real affiliate data to offer format
      const realOffers: AffiliateOffer[] = data.affiliates.map((affiliate: any) => ({
        id: `offer-${affiliate.id}`,
        affiliateId: affiliate.id,
        affiliateName: affiliate.company_name,
        affiliateTier: affiliate.tier || 'Standard',
        vehicleType: affiliate.vehicle_type,
        vehicleModel: affiliate.vehicle_model,
        capacity: affiliate.capacity,
        baseRate: affiliate.base_rate,
        totalPrice: affiliate.total_price,
        responseTime: affiliate.avg_response_time || '< 15 min',
        rating: affiliate.rating || 4.0,
        features: affiliate.features || ['Standard Service'],
        availability: affiliate.availability || 'confirmed',
        estimatedArrival: affiliate.estimated_arrival || '20 minutes',
        notes: affiliate.notes
      }))

      setOffers(realOffers)
      setStep('offers')
    } catch (error) {
      console.error('Error fetching affiliate matches:', error)
      // Fallback to empty offers if API fails
      setOffers([])
      setStep('offers')
    } finally {
      setLoading(false)
    }
  }

  const [selectedAffiliates, setSelectedAffiliates] = useState<Array<{id: string, order: number}>>([])
  const [showAffiliateSelection, setShowAffiliateSelection] = useState(false)

  const handleSelectOffer = async (offerId: string) => {
    // Submit selection to affiliate for approval
    console.log('Selecting offer:', offerId)
    // Navigate to quote details or show confirmation
  }

  const handleAffiliateSelect = (affiliateId: string) => {
    setSelectedAffiliates(prev => {
      const existing = prev.find(a => a.id === affiliateId)
      if (existing) {
        // Remove if already selected
        return prev.filter(a => a.id !== affiliateId)
      } else {
        // Add with next order number
        const nextOrder = Math.max(0, ...prev.map(a => a.order)) + 1
        return [...prev, { id: affiliateId, order: nextOrder }]
      }
    })
  }

  const handleSubmitAffiliateSelection = async () => {
    if (selectedAffiliates.length === 0) {
      alert('Please select at least one affiliate')
      return
    }

    try {
      setLoading(true)

      // Submit quote request with selected affiliates in order
      const response = await fetch('/api/event-manager/quotes/submit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          quoteRequest,
          selectedAffiliates: selectedAffiliates.sort((a, b) => a.order - b.order)
        })
      })

      if (!response.ok) {
        throw new Error('Failed to submit quote request')
      }

      const result = await response.json()

      // Navigate to quote tracking or show success
      window.location.href = `/event-manager/quotes/${result.quoteId}`

    } catch (error) {
      console.error('Error submitting quote:', error)
      alert('Failed to submit quote request')
    } finally {
      setLoading(false)
    }
  }

  const getTierBadgeVariant = (tier: string) => {
    switch (tier) {
      case 'Elite': return 'default'
      case 'Premium': return 'secondary'
      case 'Standard': return 'outline'
      default: return 'outline'
    }
  }

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'Elite': return <Crown className="h-4 w-4" />
      case 'Premium': return <Shield className="h-4 w-4" />
      case 'Standard': return <Star className="h-4 w-4" />
      default: return null
    }
  }

  if (step === 'matching') {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-12">
            <Loader2 className="h-12 w-12 mx-auto text-blue-500 animate-spin mb-4" />
            <h3 className="text-lg font-medium mb-2">Finding Available Transportation</h3>
            <p className="text-muted-foreground mb-4">
              We're matching your request with our affiliate network...
            </p>
            <div className="space-y-2 text-sm text-muted-foreground">
              <div>✓ Checking vehicle availability</div>
              <div>✓ Calculating pricing</div>
              <div>✓ Verifying affiliate capacity</div>
              <div className="text-blue-600">→ Preparing offers...</div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (step === 'offers') {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Available Transportation Options</CardTitle>
            <CardDescription>
              Based on your request: {quoteRequest.pickupLocation} → {quoteRequest.dropoffLocation}
              on {quoteRequest.date} at {quoteRequest.time} for {quoteRequest.passengers} passengers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-800">Select Your Preferred Order</h4>
                  <p className="text-sm text-blue-700 mt-1">
                    Click on affiliates below to select them in your preferred order.
                    Quote requests will be sent to affiliates in the order you select them.
                  </p>
                  {selectedAffiliates.length > 0 && (
                    <p className="text-sm text-blue-600 mt-2 font-medium">
                      Selected: {selectedAffiliates.length} affiliate{selectedAffiliates.length !== 1 ? 's' : ''}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {offers.map((offer) => {
            const isSelected = selectedAffiliates.some(a => a.id === offer.affiliateId)
            const selectionOrder = selectedAffiliates.find(a => a.id === offer.affiliateId)?.order

            return (
            <Card
              key={offer.id}
              className={`overflow-hidden transition-all cursor-pointer ${
                isSelected
                  ? 'border-2 border-blue-500 shadow-lg'
                  : 'hover:shadow-lg border hover:border-gray-300'
              }`}
              onClick={() => handleAffiliateSelect(offer.affiliateId)}
            >
              {/* Selection Badge */}
              {isSelected && (
                <Badge className="absolute -top-2 -right-2 z-10 bg-blue-500">
                  #{selectionOrder}
                </Badge>
              )}

              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{offer.vehicleModel}</CardTitle>
                    <CardDescription>{offer.affiliateName}</CardDescription>
                  </div>
                  <div className="flex flex-col items-end gap-1">
                    <Badge variant={getTierBadgeVariant(offer.affiliateTier)} className="flex items-center gap-1">
                      {getTierIcon(offer.affiliateTier)}
                      {offer.affiliateTier}
                    </Badge>
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      <span className="text-xs font-medium">{offer.rating}</span>
                    </div>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Vehicle Details */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Vehicle Type</span>
                    <span className="font-medium">{offer.vehicleType}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Capacity</span>
                    <span className="font-medium">{offer.capacity} passengers</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Response Time</span>
                    <span className="font-medium">{offer.responseTime}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Estimated Arrival</span>
                    <span className="font-medium">{offer.estimatedArrival}</span>
                  </div>
                </div>

                {/* Features */}
                <div>
                  <div className="text-sm font-medium mb-2">Included Features</div>
                  <div className="flex flex-wrap gap-1">
                    {offer.features.map((feature, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Pricing */}
                <div className="border-t pt-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-lg font-bold">${offer.totalPrice}</div>
                      <div className="text-xs text-muted-foreground">Total price</div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">${offer.baseRate}</div>
                      <div className="text-xs text-muted-foreground">Base rate</div>
                    </div>
                  </div>
                </div>

                {/* Availability Status */}
                <div className="flex items-center gap-2">
                  {offer.availability === 'confirmed' && (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm text-green-600 font-medium">Available Now</span>
                    </>
                  )}
                  {offer.availability === 'pending' && (
                    <>
                      <Clock className="h-4 w-4 text-yellow-500" />
                      <span className="text-sm text-yellow-600 font-medium">Pending Confirmation</span>
                    </>
                  )}
                  {offer.availability === 'unavailable' && (
                    <>
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                      <span className="text-sm text-red-600 font-medium">Not Available</span>
                    </>
                  )}
                </div>

                {/* Notes */}
                {offer.notes && (
                  <div className="text-sm text-muted-foreground bg-gray-50 p-2 rounded">
                    {offer.notes}
                  </div>
                )}

                {/* Selection Indicator */}
                <div className="text-center">
                  {isSelected ? (
                    <div className="text-blue-600 font-medium">
                      ✓ Selected (Order #{selectionOrder})
                    </div>
                  ) : (
                    <div className="text-muted-foreground">
                      Click to select
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
            )
          })}
        </div>

        {/* Submit Selected Affiliates */}
        {offers.length > 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <div>
                  <h3 className="text-lg font-medium mb-2">Submit Quote Request</h3>
                  <p className="text-muted-foreground">
                    {selectedAffiliates.length === 0
                      ? 'Select at least one affiliate to send your quote request'
                      : `Send quote request to ${selectedAffiliates.length} selected affiliate${selectedAffiliates.length !== 1 ? 's' : ''} in order`
                    }
                  </p>
                </div>

                {selectedAffiliates.length > 0 && (
                  <div className="flex flex-wrap justify-center gap-2 mb-4">
                    {selectedAffiliates
                      .sort((a, b) => a.order - b.order)
                      .map((selected, index) => {
                        const affiliate = offers.find(o => o.affiliateId === selected.id)
                        return (
                          <Badge key={selected.id} variant="outline" className="text-sm">
                            #{index + 1} {affiliate?.affiliateName}
                          </Badge>
                        )
                      })
                    }
                  </div>
                )}

                <Button
                  onClick={handleSubmitAffiliateSelection}
                  disabled={selectedAffiliates.length === 0 || loading}
                  size="lg"
                  className="px-8"
                >
                  {loading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Submitting Quote...
                    </>
                  ) : (
                    <>
                      <ArrowRight className="h-4 w-4 mr-2" />
                      Submit Quote Request
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {offers.length === 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <AlertTriangle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No Options Available</h3>
                <p className="text-muted-foreground mb-4">
                  No affiliates are available for your requested time and location.
                  Try adjusting your requirements or request a custom quote.
                </p>
                <div className="flex justify-center gap-2">
                  <Button variant="outline" onClick={() => setStep('request')}>
                    Modify Request
                  </Button>
                  <Button>Request Custom Quote</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Request Transportation Quote</CardTitle>
        <CardDescription>
          Submit your transportation requirements and receive matched offers from our affiliate network
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Pickup Location */}
          <div className="space-y-2">
            <Label htmlFor="pickup">Pickup Location</Label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="pickup"
                placeholder="Enter pickup address"
                value={quoteRequest.pickupLocation}
                onChange={(e) => setQuoteRequest(prev => ({ ...prev, pickupLocation: e.target.value }))}
                className="pl-9"
              />
            </div>
          </div>

          {/* Dropoff Location */}
          <div className="space-y-2">
            <Label htmlFor="dropoff">Dropoff Location</Label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="dropoff"
                placeholder="Enter destination address"
                value={quoteRequest.dropoffLocation}
                onChange={(e) => setQuoteRequest(prev => ({ ...prev, dropoffLocation: e.target.value }))}
                className="pl-9"
              />
            </div>
          </div>

          {/* City */}
          <div className="space-y-2">
            <Label htmlFor="city">City</Label>
            <Select value={quoteRequest.city} onValueChange={(value) => setQuoteRequest(prev => ({ ...prev, city: value }))}>
              <SelectTrigger>
                <SelectValue placeholder="Select city" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="austin">Austin, TX</SelectItem>
                <SelectItem value="dallas">Dallas, TX</SelectItem>
                <SelectItem value="houston">Houston, TX</SelectItem>
                <SelectItem value="san-antonio">San Antonio, TX</SelectItem>
                <SelectItem value="fort-worth">Fort Worth, TX</SelectItem>
                <SelectItem value="el-paso">El Paso, TX</SelectItem>
                <SelectItem value="arlington">Arlington, TX</SelectItem>
                <SelectItem value="corpus-christi">Corpus Christi, TX</SelectItem>
                <SelectItem value="plano">Plano, TX</SelectItem>
                <SelectItem value="lubbock">Lubbock, TX</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Date */}
          <div className="space-y-2">
            <Label htmlFor="date">Date</Label>
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="date"
                type="date"
                value={quoteRequest.date}
                onChange={(e) => setQuoteRequest(prev => ({ ...prev, date: e.target.value }))}
                className="pl-9"
              />
            </div>
          </div>

          {/* Time */}
          <div className="space-y-2">
            <Label htmlFor="time">Time</Label>
            <div className="relative">
              <Clock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="time"
                type="time"
                value={quoteRequest.time}
                onChange={(e) => setQuoteRequest(prev => ({ ...prev, time: e.target.value }))}
                className="pl-9"
              />
            </div>
          </div>

          {/* Passengers */}
          <div className="space-y-2">
            <Label htmlFor="passengers">Number of Passengers</Label>
            <div className="relative">
              <Users className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="passengers"
                type="number"
                min="1"
                max="50"
                value={quoteRequest.passengers}
                onChange={(e) => setQuoteRequest(prev => ({ ...prev, passengers: parseInt(e.target.value) || 1 }))}
                className="pl-9"
              />
            </div>
          </div>

          {/* Service Type */}
          <div className="space-y-2">
            <Label htmlFor="serviceType">Service Type</Label>
            <Select value={quoteRequest.serviceType} onValueChange={(value) => setQuoteRequest(prev => ({ ...prev, serviceType: value as any }))}>
              <SelectTrigger>
                <SelectValue placeholder="Select service type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="point_to_point">Point to Point</SelectItem>
                <SelectItem value="hourly">Hourly Service</SelectItem>
                <SelectItem value="airport">Airport Transfer</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Vehicle Preference */}
        <div className="space-y-2">
          <Label htmlFor="vehicleType">Vehicle Preference (Optional)</Label>
          <Select value={quoteRequest.vehicleType} onValueChange={(value) => setQuoteRequest(prev => ({ ...prev, vehicleType: value }))}>
            <SelectTrigger>
              <SelectValue placeholder="Any vehicle type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="any">Any vehicle type</SelectItem>
              <SelectItem value="sedan">Sedan</SelectItem>
              <SelectItem value="suv">SUV</SelectItem>
              <SelectItem value="van">Van</SelectItem>
              <SelectItem value="luxury">Luxury Vehicle</SelectItem>
              <SelectItem value="bus">Bus/Coach</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Special Requirements */}
        <div className="space-y-2">
          <Label htmlFor="requirements">Special Requirements (Optional)</Label>
          <Textarea
            id="requirements"
            placeholder="Any special requirements, accessibility needs, or preferences..."
            value={quoteRequest.specialRequirements}
            onChange={(e) => setQuoteRequest(prev => ({ ...prev, specialRequirements: e.target.value }))}
            rows={3}
          />
        </div>

        {/* Submit Button */}
        <Button
          onClick={handleSubmitRequest}
          disabled={!quoteRequest.pickupLocation || !quoteRequest.dropoffLocation || !quoteRequest.city || !quoteRequest.date || !quoteRequest.time || loading}
          className="w-full"
        >
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Finding Options...
            </>
          ) : (
            <>
              <ArrowRight className="h-4 w-4 mr-2" />
              Get Transportation Options
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  )
}
