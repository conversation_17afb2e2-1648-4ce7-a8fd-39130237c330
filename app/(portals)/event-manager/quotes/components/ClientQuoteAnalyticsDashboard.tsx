"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Badge } from "@/app/components/ui/badge"
import { Button } from "@/app/components/ui/button"
import { 
  BarChart3, 
  Clock, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Users, 
  DollarSign,
  Target,
  Activity,
  Timer,
  Calendar,
  MapPin
} from "lucide-react"
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/app/components/ui/tabs"
import { Progress } from "@/app/components/ui/progress"

interface ClientQuoteAnalytics {
  totalQuotes: number
  pendingQuotes: number
  acceptedQuotes: number
  completedQuotes: number
  totalSpent: number
  averageQuoteValue: number
  averageResponseTime: number
  acceptanceRate: number
  upcomingTrips: number
  recentQuotes: Array<{
    id: string
    vehicleType: string
    status: string
    amount: number
    date: string
    affiliate: string
    event?: string
  }>
  spendingByCategory: Array<{
    category: string
    amount: number
    percentage: number
  }>
  monthlySpending: Array<{
    month: string
    amount: number
    quotes: number
  }>
  topAffiliates: Array<{
    id: string
    name: string
    bookings: number
    totalSpent: number
    rating: number
  }>
}

interface ClientQuoteAnalyticsDashboardProps {
  dateRange?: {
    from: Date
    to: Date
  }
}

export function ClientQuoteAnalyticsDashboard({ dateRange }: ClientQuoteAnalyticsDashboardProps) {
  const [analytics, setAnalytics] = useState<ClientQuoteAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchClientAnalytics = async () => {
      try {
        setLoading(true)
        
        // Build query parameters
        const params = new URLSearchParams()
        if (dateRange?.from) {
          params.append('from', dateRange.from.toISOString())
        }
        if (dateRange?.to) {
          params.append('to', dateRange.to.toISOString())
        }

        const response = await fetch(`/api/event-manager/analytics/quotes?${params}`)
        if (!response.ok) {
          throw new Error('Failed to fetch client analytics')
        }

        const data = await response.json()
        setAnalytics(data)
      } catch (err) {
        console.error('Error fetching client analytics:', err)
        setError(err instanceof Error ? err.message : 'Failed to load analytics')
        
        // Set mock data for development
        setAnalytics({
          totalQuotes: 24,
          pendingQuotes: 3,
          acceptedQuotes: 18,
          completedQuotes: 15,
          totalSpent: 12450,
          averageQuoteValue: 520,
          averageResponseTime: 1.8,
          acceptanceRate: 85.7,
          upcomingTrips: 6,
          recentQuotes: [
            { id: 'Q001', vehicleType: 'Luxury Sedan', status: 'accepted', amount: 450, date: '2024-01-15', affiliate: 'Elite Transport Co', event: 'Tech Conference 2024' },
            { id: 'Q002', vehicleType: 'SUV', status: 'pending', amount: 320, date: '2024-01-14', affiliate: 'Premium Rides LLC' },
            { id: 'Q003', vehicleType: 'Van', status: 'completed', amount: 680, date: '2024-01-13', affiliate: 'City Executive Cars', event: 'Corporate Retreat' }
          ],
          spendingByCategory: [
            { category: 'Airport Transfers', amount: 4200, percentage: 33.7 },
            { category: 'Event Transportation', amount: 3800, percentage: 30.5 },
            { category: 'Executive Travel', amount: 2450, percentage: 19.7 },
            { category: 'Group Shuttles', amount: 2000, percentage: 16.1 }
          ],
          monthlySpending: [
            { month: 'Oct', amount: 3200, quotes: 8 },
            { month: 'Nov', amount: 4100, quotes: 10 },
            { month: 'Dec', amount: 5150, quotes: 12 },
            { month: 'Jan', amount: 3450, quotes: 9 }
          ],
          topAffiliates: [
            { id: '1', name: 'Elite Transport Co', bookings: 8, totalSpent: 4200, rating: 4.9 },
            { id: '2', name: 'Premium Rides LLC', bookings: 6, totalSpent: 3100, rating: 4.7 },
            { id: '3', name: 'City Executive Cars', bookings: 4, totalSpent: 2150, rating: 4.5 }
          ]
        })
      } finally {
        setLoading(false)
      }
    }

    fetchClientAnalytics()
  }, [dateRange])

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="space-y-0 pb-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error || !analytics) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p>{error || 'Failed to load analytics'}</p>
            <Button variant="outline" className="mt-2" onClick={() => window.location.reload()}>
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Quotes</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalQuotes}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.pendingQuotes} pending • {analytics.acceptedQuotes} accepted
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${analytics.totalSpent.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Avg: ${analytics.averageQuoteValue} per quote
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Response Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.averageResponseTime}h</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="h-3 w-3 inline mr-1" />
              20% faster than average
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Acceptance Rate</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.acceptanceRate}%</div>
            <Progress value={analytics.acceptanceRate} className="mt-2" />
          </CardContent>
        </Card>
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Spending by Category */}
        <Card>
          <CardHeader>
            <CardTitle>Spending by Category</CardTitle>
            <CardDescription>Transportation expenses breakdown</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.spendingByCategory.map((category, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{category.category}</span>
                    <span className="text-sm text-muted-foreground">
                      ${category.amount.toLocaleString()} ({category.percentage}%)
                    </span>
                  </div>
                  <Progress value={category.percentage} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Quotes */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Quotes</CardTitle>
            <CardDescription>Latest transportation requests</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.recentQuotes.map((quote) => (
                <div key={quote.id} className="flex items-center justify-between border-b pb-3">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{quote.vehicleType}</span>
                      <Badge variant={
                        quote.status === 'accepted' ? 'default' :
                        quote.status === 'pending' ? 'secondary' :
                        quote.status === 'completed' ? 'outline' : 'destructive'
                      }>
                        {quote.status}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {quote.affiliate} • {quote.date}
                    </div>
                    {quote.event && (
                      <div className="text-xs text-blue-600">{quote.event}</div>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="font-medium">${quote.amount}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Spending Trend & Top Affiliates */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Spending */}
        <Card>
          <CardHeader>
            <CardTitle>Monthly Spending Trend</CardTitle>
            <CardDescription>Transportation expenses over time</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.monthlySpending.map((month, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="font-medium">{month.month}</div>
                      <div className="text-sm text-muted-foreground">{month.quotes} quotes</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">${month.amount.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">
                      ${Math.round(month.amount / month.quotes)} avg
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Affiliates */}
        <Card>
          <CardHeader>
            <CardTitle>Preferred Affiliates</CardTitle>
            <CardDescription>Your most used transportation partners</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.topAffiliates.map((affiliate, index) => (
                <div key={affiliate.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Badge variant="outline" className="w-6 h-6 rounded-full p-0 flex items-center justify-center">
                      {index + 1}
                    </Badge>
                    <div>
                      <p className="font-medium">{affiliate.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {affiliate.bookings} bookings • ⭐ {affiliate.rating}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">${affiliate.totalSpent.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">total spent</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Upcoming Trips Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Upcoming Transportation</CardTitle>
          <CardDescription>Your scheduled trips and bookings</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Activity className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">{analytics.upcomingTrips} Upcoming Trips</h3>
            <p className="text-muted-foreground mb-4">
              You have {analytics.upcomingTrips} confirmed transportation bookings scheduled
            </p>
            <div className="flex justify-center gap-2">
              <Button onClick={() => window.location.href = '/event-manager/trips'}>
                View All Trips
              </Button>
              <Button variant="outline" onClick={() => window.location.href = '/event-manager/events'}>
                Manage Events
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
