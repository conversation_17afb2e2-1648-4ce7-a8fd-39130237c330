"use client"


import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/app/components/ui/card"
import { But<PERSON> } from "@/app/components/ui/button"
import { Badge } from "@/app/components/ui/badge"
import {
  Calendar,
  Users,
  Car,
  Clock,
  ArrowRight,
  Zap,
  Building,
  MapPin
} from "lucide-react"

export function EnhancedRequestFlow() {

  const handleQuickRequest = (type: 'single' | 'event') => {
    if (type === 'single') {
      window.location.href = '/event-manager/quotes/new'
    } else {
      window.location.href = '/event-manager/events/new'
    }
  }

  return (
    <div className="space-y-6">
      {/* Quick Action Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Single Quote Request */}
        <Card className="hover:shadow-lg transition-shadow cursor-pointer group" onClick={() => handleQuickRequest('single')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors">
                <Car className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <div>Single Transportation Request</div>
                <div className="text-sm font-normal text-muted-foreground">
                  Quick quote for individual trips
                </div>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Zap className="h-4 w-4" />
                <span>Fast & Simple</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Clock className="h-4 w-4" />
                <span>Get quotes in minutes</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Users className="h-4 w-4" />
                <span>Perfect for individual or small group trips</span>
              </div>
              <Button className="w-full mt-4" onClick={() => handleQuickRequest('single')}>
                Request Single Quote
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Event-Based Request */}
        <Card className="hover:shadow-lg transition-shadow cursor-pointer group" onClick={() => handleQuickRequest('event')}>
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors">
                <Calendar className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <div>Event Transportation Planning</div>
                <div className="text-sm font-normal text-muted-foreground">
                  Coordinate group transportation for events
                </div>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Building className="h-4 w-4" />
                <span>Event Management</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Users className="h-4 w-4" />
                <span>Bulk coordination & scheduling</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <MapPin className="h-4 w-4" />
                <span>Multiple pickup/dropoff points</span>
              </div>
              <Button className="w-full mt-4" onClick={() => handleQuickRequest('event')}>
                Plan Event Transportation
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>Your latest transportation requests and events</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Car className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <div className="font-medium">Airport Transfer - Corporate Client</div>
                  <div className="text-sm text-muted-foreground">Quote accepted • $180</div>
                </div>
              </div>
              <Badge variant="default">Accepted</Badge>
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-yellow-100 rounded-lg">
                  <Calendar className="h-4 w-4 text-yellow-600" />
                </div>
                <div>
                  <div className="font-medium">Tech Conference 2024</div>
                  <div className="text-sm text-muted-foreground">3 quotes pending • 150 attendees</div>
                </div>
              </div>
              <Badge variant="secondary">Planning</Badge>
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Car className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <div className="font-medium">Downtown Meeting</div>
                  <div className="text-sm text-muted-foreground">Quote pending • 1 passenger</div>
                </div>
              </div>
              <Badge variant="secondary">Pending</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
