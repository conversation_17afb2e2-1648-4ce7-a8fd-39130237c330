"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Badge } from "@/app/components/ui/badge"
import { But<PERSON> } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/app/components/ui/tabs"
import {
  Clock,
  Download,
  Filter,
  Plus,
  RefreshCw,
  Search,
  AlertCircle,
  CheckCircle,
  X,
  Calendar,
  MapPin,
  Users,
  Car,
  DollarSign,
  Eye,
  Edit,
  MessageSquare
} from "lucide-react"
import { EnhancedClientQuoteList } from "./EnhancedClientQuoteList"
import { useWebSocket } from "@/lib/hooks/useWebSocket"
import { DetailedConnectionStatus } from "@/components/ui/connection-status"
import { initializeRealTimeUpdates } from "@/lib/utils/quote-realtime-updates"

interface QuoteStats {
  total: number
  pending: number
  accepted: number
  rejected: number
  completed: number
  totalValue: number
}

interface ClientQuoteManagementProps {
  globalSearch?: string
}

export function ClientQuoteManagement({ globalSearch }: ClientQuoteManagementProps) {
  const [statusFilter, setStatusFilter] = useState("all")
  const [dateFilter, setDateFilter] = useState("all")
  const [eventFilter, setEventFilter] = useState("all")

  // WebSocket integration for real-time updates
  const { isReady, connectionState, authState } = useWebSocket({
    requireAuth: true,
    permissions: ['quote_updates']
  })

  // Initialize real-time updates when WebSocket is ready
  useEffect(() => {
    if (isReady) {
      console.log('[ClientQuoteManagement] Initializing real-time updates')
      const cleanup = initializeRealTimeUpdates()
      return cleanup
    }
  }, [isReady])

  // Mock stats - replace with real data
  const stats: QuoteStats = {
    total: 24,
    pending: 3,
    accepted: 15,
    rejected: 2,
    completed: 4,
    totalValue: 12450
  }

  const handleExport = () => {
    // Implement export functionality
    console.log('Exporting quotes...')
  }

  const handleCreateQuote = () => {
    window.location.href = '/event-manager/quotes/new'
  }

  return (
    <div className="space-y-4">
      {/* Real-time Connection Status */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Quote Management</h3>
        <DetailedConnectionStatus />
      </div>

      {/* Tab-Specific Filters */}
      <Card>
        <CardContent className="pt-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="accepted">Accepted</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>

            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by date" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Dates</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="quarter">This Quarter</SelectItem>
              </SelectContent>
            </Select>

            <Select value={eventFilter} onValueChange={setEventFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by event" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Events</SelectItem>
                <SelectItem value="tech-conference">Tech Conference 2024</SelectItem>
                <SelectItem value="corporate-retreat">Corporate Retreat</SelectItem>
                <SelectItem value="board-meeting">Board Meeting</SelectItem>
                <SelectItem value="standalone">Standalone Quotes</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>



      {/* Enhanced Quote List with Professional Design */}
      <EnhancedClientQuoteList statusFilter={statusFilter} />
    </div>
  )
}
