"use client"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { But<PERSON> } from "@/app/components/ui/button"
import { Badge } from "@/app/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/app/components/ui/tabs"
import { Avatar, AvatarFallback } from "@/app/components/ui/avatar"
import GodsView from "@/app/components/shared/trips/GodsView"
import Map, {
  Marker,
  NavigationControl,
  FullscreenControl,
  Source,
  Layer
} from 'react-map-gl'
import mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'
import {
  Search,
  Calendar,
  MapPin,
  Users,
  Clock,
  AlertCircle,
  Car,
  History,
  Star,
  TrendingUp,
  Filter,
  Map as MapIcon,
  Plane,
  Crown,
  Building,
  CalendarClock,
  AlertTriangle,
  CheckCircle2,
  XCircle,
  Phone,
  MessageSquare,
  Mail,
  Edit,
  MoreVertical,
  ChevronUp,
  ChevronDown,
  Shield,
  FileText,
  UserCheck,
  Stethoscope,
  Check,
  UserX,
  Fuel,
  ThermometerSun,
  Route,
  PenLine,
  Copy,
  Info,
  Cloud,
  PlaneTakeoff,
  List,
  Plus
} from "lucide-react"
import Link from "next/link"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu"
import { Switch } from "@/app/components/ui/switch"
import { Label } from "@/app/components/ui/label"
import { cn } from "@/lib/utils"
import { ToggleGroup, ToggleGroupItem } from "@/app/components/ui/toggle-group"
import { format } from 'date-fns'
import { Activity } from "lucide-react"

// Enhanced Trip interface
interface Trip {
  id: string
  eventName: string
  date: string
  pickupLocation: string
  dropoffLocation: string
  passengerCount: number
  status: "scheduled" | "in_progress" | "completed" | "cancelled"
  startTime: string
  endTime: string
  driver?: {
    name: string
    phone: string
    rating: number
    location?: {
      lat: number
      lng: number
    }
  }
  weather?: string
  trafficAlert?: string
  tripType: "airport_arrival" | "airport_departure" | "transfer" | "vip"
  priority: "normal" | "high" | "vip"
  eventId: string
  companyId: string
  passengers: {
    name: string
    phone?: string
    email?: string
    isVIP?: boolean
  }[]
  notes?: string
  specialRequirements?: string[]
  estimatedDuration: number
  realTimeDuration?: number
  currentLocation?: {
    lat: number
    lng: number
  }
  lastUpdate: string
  alerts?: {
    type: "info" | "warning" | "error"
    message: string
    timestamp: string
  }[]
  securityLevel: "standard" | "vip" | "vvip"
  securityDetails?: {
    vehicleTracking: "active" | "inactive"
    routeSecurity: "verified" | "pending" | "not_required"
    securityTeam: "deployed" | "pending" | "not_required"
    vehicleDocuments: {
      registration: {
        status: "verified" | "pending" | "expired"
        number: string
        validUntil: string
      }
      securityCertification: {
        status: "verified" | "pending" | "expired"
        armorLevel?: string
        certNumber: string
        validUntil: string
      }
    }
    driverClearance: {
      level: 1 | 2 | 3
      status: "verified" | "pending" | "expired"
      validUntil: string
      lastVerified: string
    }
    driverMedical: {
      status: "verified" | "pending" | "expired"
      validUntil: string
      lastCheck: string
    }
  }
  welcomeService?: {
    type: "standard" | "vip" | "vvip"
    meetAndGreet: boolean
    fastTrackImmigration: boolean
    luggageAssistance: boolean
    loungeAccess: boolean
    privateSecurityEscort: boolean
  }
}

// Mock data with enhanced fields
const mockTrips: Trip[] = [
  {
    id: "1",
    eventName: "Annual Conference 2024",
    date: "2024-03-15",
    pickupLocation: "JFK Airport Terminal 4",
    dropoffLocation: "Marriott Hotel Manhattan",
    passengerCount: 3,
    status: "scheduled",
    startTime: "14:00",
    endTime: "15:30",
    driver: {
      name: "John Smith",
      phone: "+****************",
      rating: 4.8,
      location: {
        lat: 40.7128,
        lng: -74.0060
      }
    },
    weather: "Light rain expected",
    trafficAlert: "Moderate traffic on route",
    tripType: "airport_arrival",
    priority: "vip",
    eventId: "conf2024",
    companyId: "marriott",
    passengers: [
      {
        name: "Alice Johnson",
        phone: "+****************",
        email: "<EMAIL>",
        isVIP: true
      },
      {
        name: "Bob Williams",
        phone: "+****************",
        email: "<EMAIL>"
      }
    ],
    notes: "VIP guests for keynote speech",
    specialRequirements: ["Wheelchair access", "Extra luggage space"],
    estimatedDuration: 90,
    lastUpdate: "2024-03-15T13:45:00Z",
    alerts: [
      {
        type: "info",
        message: "Driver en route to pickup location",
        timestamp: "2024-03-15T13:30:00Z"
      }
    ],
    securityLevel: "vvip",
    securityDetails: {
      vehicleTracking: "active",
      routeSecurity: "verified",
      securityTeam: "deployed",
      vehicleDocuments: {
        registration: {
          status: "verified",
          number: "NYC-VIP-2023",
          validUntil: "2024-12-31"
        },
        securityCertification: {
          status: "verified",
          armorLevel: "VR9",
          certNumber: "SEC-2023-789",
          validUntil: "2024-11-30"
        }
      },
      driverClearance: {
        level: 3,
        status: "verified",
        validUntil: "2024-12-31",
        lastVerified: "2024-01-15"
      },
      driverMedical: {
        status: "verified",
        validUntil: "2024-12-31",
        lastCheck: "2024-01-15"
      }
    },
    welcomeService: {
      type: "vvip",
      meetAndGreet: true,
      fastTrackImmigration: true,
      luggageAssistance: true,
      loungeAccess: true,
      privateSecurityEscort: true
    }
  },
  {
    id: "2",
    eventName: "Tech Summit 2024",
    date: "2024-03-20",
    pickupLocation: "Grand Central Station",
    dropoffLocation: "Javits Center",
    passengerCount: 2,
    status: "in_progress",
    startTime: "09:00",
    endTime: "10:00",
    driver: {
      name: "Sarah Johnson",
      phone: "+****************",
      rating: 4.9,
      location: {
        lat: 40.7505,
        lng: -73.9934
      }
    },
    tripType: "transfer",
    priority: "normal",
    eventId: "techsummit2024",
    companyId: "javits",
    passengers: [
      {
        name: "Charlie Brown",
        phone: "+****************",
        email: "<EMAIL>"
      },
      {
        name: "Diana Prince",
        phone: "+****************",
        email: "<EMAIL>"
      }
    ],
    estimatedDuration: 45,
    lastUpdate: "2024-03-20T08:45:00Z",
    securityLevel: "standard",
    securityDetails: {
      vehicleTracking: "active",
      routeSecurity: "verified",
      securityTeam: "not_required",
      vehicleDocuments: {
        registration: {
          status: "verified",
          number: "NYC-2023-456",
          validUntil: "2024-12-31"
        },
        securityCertification: {
          status: "verified",
          certNumber: "SEC-2023-456",
          validUntil: "2024-12-31"
        }
      },
      driverClearance: {
        level: 1,
        status: "verified",
        validUntil: "2024-12-31",
        lastVerified: "2024-01-15"
      },
      driverMedical: {
        status: "verified",
        validUntil: "2024-12-31",
        lastCheck: "2024-01-15"
      }
    },
    welcomeService: {
      type: "standard",
      meetAndGreet: true,
      fastTrackImmigration: false,
      luggageAssistance: true,
      loungeAccess: false,
      privateSecurityEscort: false
    }
  }
]

// Filter interface
interface TripFilters {
  tripType: string[]
  priority: string[]
  status: string[]
  dateRange: {
    start: string
    end: string
  }
  events: string[]
  companies: string[]
}

interface SecurityOptionsProps {
  securityLevel: "standard" | "vip" | "vvip"
  onSecurityLevelChange: (level: "standard" | "vip" | "vvip") => void
  welcomeService?: {
    type: "standard" | "vip" | "vvip"
    meetAndGreet: boolean
    fastTrackImmigration: boolean
    luggageAssistance: boolean
    loungeAccess: boolean
    privateSecurityEscort: boolean
  }
  onWelcomeServiceChange?: (service: any) => void
  isDisabled?: boolean
}

function SecurityOptions({
  securityLevel,
  onSecurityLevelChange,
  welcomeService,
  onWelcomeServiceChange,
  isDisabled = false
}: SecurityOptionsProps) {
  return (
    <div className="space-y-6">
      {/* Security Level Selection */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium">Security Level</h4>
            <p className="text-sm text-muted-foreground">
              Select the required security level for this service
            </p>
          </div>
          <Select
            value={securityLevel}
            onValueChange={(value: "standard" | "vip" | "vvip") => onSecurityLevelChange(value)}
            disabled={isDisabled}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="standard">Standard</SelectItem>
              <SelectItem value="vip">VIP Security</SelectItem>
              <SelectItem value="vvip">VVIP Security</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {securityLevel !== "standard" && (
          <div className="grid gap-4 p-4 border rounded-lg">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Vehicle Requirements</Label>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={true}
                      disabled={true}
                    />
                    <span className="text-sm">Vehicle Tracking</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={securityLevel === "vvip"}
                      disabled={true}
                    />
                    <span className="text-sm">Armored Vehicle</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Security Team</Label>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={securityLevel === "vvip"}
                      disabled={true}
                    />
                    <span className="text-sm">Security Escort</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={securityLevel === "vip" || securityLevel === "vvip"}
                      disabled={true}
                    />
                    <span className="text-sm">Route Security Check</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="pt-4 border-t">
              <div className="text-sm text-muted-foreground mb-2">
                Required Security Clearance
              </div>
              <Badge variant="outline">
                Level {securityLevel === "vvip" ? "3" : "2"} Security Clearance Required
              </Badge>
            </div>
          </div>
        )}
      </div>

      {/* Welcome Service Options */}
      {onWelcomeServiceChange && (
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium">Welcome Service</h4>
            <p className="text-sm text-muted-foreground">
              Additional services available for airport arrivals
            </p>
          </div>

          <div className="grid gap-4 p-4 border rounded-lg">
            <div className="grid gap-2">
              <div className="flex items-center gap-2">
                <Switch
                  checked={welcomeService?.meetAndGreet}
                  onCheckedChange={(checked) =>
                    onWelcomeServiceChange({
                      ...welcomeService,
                      meetAndGreet: checked
                    })
                  }
                  disabled={isDisabled}
                />
                <span className="text-sm">Meet & Greet Service</span>
              </div>
              <div className="flex items-center gap-2">
                <Switch
                  checked={welcomeService?.fastTrackImmigration}
                  onCheckedChange={(checked) =>
                    onWelcomeServiceChange({
                      ...welcomeService,
                      fastTrackImmigration: checked
                    })
                  }
                  disabled={isDisabled}
                />
                <span className="text-sm">Fast Track Immigration</span>
              </div>
              <div className="flex items-center gap-2">
                <Switch
                  checked={welcomeService?.luggageAssistance}
                  onCheckedChange={(checked) =>
                    onWelcomeServiceChange({
                      ...welcomeService,
                      luggageAssistance: checked
                    })
                  }
                  disabled={isDisabled}
                />
                <span className="text-sm">Luggage Assistance</span>
              </div>
              <div className="flex items-center gap-2">
                <Switch
                  checked={welcomeService?.loungeAccess}
                  onCheckedChange={(checked) =>
                    onWelcomeServiceChange({
                      ...welcomeService,
                      loungeAccess: checked
                    })
                  }
                  disabled={isDisabled}
                />
                <span className="text-sm">Lounge Access</span>
              </div>
              {securityLevel !== "standard" && (
                <div className="flex items-center gap-2">
                  <Switch
                    checked={welcomeService?.privateSecurityEscort}
                    onCheckedChange={(checked) =>
                      onWelcomeServiceChange({
                        ...welcomeService,
                        privateSecurityEscort: checked
                      })
                    }
                    disabled={isDisabled}
                  />
                  <span className="text-sm">Private Security Escort</span>
                </div>
              )}
            </div>

            {welcomeService && Object.values(welcomeService).some(Boolean) && (
              <div className="pt-4 border-t">
                <div className="text-sm text-muted-foreground mb-2">
                  Service Level
                </div>
                <Badge variant="outline">
                  {securityLevel === "vvip" ? "VVIP" : securityLevel === "vip" ? "VIP" : "Standard"} Welcome Service
                </Badge>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

function FilterBar({ filters, setFilters }: {
  filters: TripFilters,
  setFilters: (filters: TripFilters) => void
}) {
  return (
    <div className="flex flex-wrap gap-4 mb-6">
      <div className="flex-1 min-w-[200px]">
        <Select
          onValueChange={(value) =>
            setFilters({
              ...filters,
              tripType: [...filters.tripType, value]
            })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Trip Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="airport_arrival">Airport Arrivals</SelectItem>
            <SelectItem value="airport_departure">Airport Departures</SelectItem>
            <SelectItem value="transfer">Transfers</SelectItem>
            <SelectItem value="vip">VIP Services</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex-1 min-w-[200px]">
        <Select
          onValueChange={(value) =>
            setFilters({
              ...filters,
              priority: [...filters.priority, value]
            })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Priority" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="normal">Normal</SelectItem>
            <SelectItem value="high">High Priority</SelectItem>
            <SelectItem value="vip">VIP</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex-1 min-w-[200px]">
        <Select
          onValueChange={(value) =>
            setFilters({
              ...filters,
              events: [...filters.events, value]
            })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Event" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Events</SelectItem>
            <SelectItem value="conf2024">Annual Conference 2024</SelectItem>
            <SelectItem value="summit">Tech Summit</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center gap-2">
        <Switch id="show-map" />
        <Label htmlFor="show-map">Map View</Label>
      </div>

      <Button variant="outline" size="sm" className="ml-auto">
        <Filter className="h-4 w-4 mr-2" />
        More Filters
      </Button>
    </div>
  )
}

// Enhanced TripCard component
function TripCard({ trip }: { trip: Trip }) {
  const [isExpanded, setIsExpanded] = useState(false)

  return (
    <div className="bg-background border rounded-lg overflow-hidden">
      {/* Main Content */}
      <div
        className="p-4 cursor-pointer hover:bg-muted/50 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <div className="h-10 w-10 bg-primary/10 rounded-full flex items-center justify-center">
              {trip.tripType === "airport_arrival" ? (
                <Plane className="h-5 w-5 text-primary" />
              ) : trip.tripType === "airport_departure" ? (
                <PlaneTakeoff className="h-5 w-5 text-primary" />
              ) : trip.tripType === "vip" ? (
                <Crown className="h-5 w-5 text-primary" />
              ) : (
                <Car className="h-5 w-5 text-primary" />
              )}
            </div>
            <div>
              <div className="font-medium flex items-center gap-2">
                {trip.eventName}
                {trip.priority === "vip" && (
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-0">VIP</Badge>
                )}
              </div>
              <div className="text-sm text-muted-foreground">
                {format(new Date(trip.date), "MMM d, yyyy")}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Badge
              variant={
                trip.status === "scheduled" ? "secondary" :
                  trip.status === "in_progress" ? "default" :
                    trip.status === "completed" ? "outline" :
                      "destructive"
              }
            >
              {trip.status.replace("_", " ").toUpperCase()}
            </Badge>
            <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
              <MoreVertical className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-8">
          {/* Time and Location */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <div className="text-sm">
                <span className="font-medium">{trip.startTime}</span>
                {" - "}
                <span className="font-medium">{trip.endTime}</span>
              </div>
            </div>
            <div className="flex items-start gap-2">
              <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
              <div className="text-sm space-y-1">
                <div>{trip.pickupLocation}</div>
                <div className="text-muted-foreground">{trip.dropoffLocation}</div>
              </div>
            </div>
          </div>

          {/* Passengers and Driver */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <div className="text-sm">
                <span className="font-medium">{trip.passengerCount}</span>
                {" passengers"}
              </div>
            </div>
            {trip.driver && (
              <div className="flex items-center gap-2">
                <Avatar className="h-6 w-6">
                  <AvatarFallback>
                    {trip.driver.name.split(" ").map(n => n[0]).join("")}
                  </AvatarFallback>
                </Avatar>
                <div className="text-sm">
                  <div className="font-medium">{trip.driver.name}</div>
                  <div className="text-muted-foreground">{trip.driver.phone}</div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Alerts */}
        {((trip.alerts && trip.alerts.length > 0) || trip.weather || trip.trafficAlert) && (
          <div className="mt-4 flex items-center gap-2 text-sm text-orange-600">
            <AlertTriangle className="h-4 w-4" />
            <span>
              {trip.alerts?.length || 0} alert{trip.alerts?.length !== 1 ? "s" : ""}
              {trip.weather && " • Weather alert"}
              {trip.trafficAlert && " • Traffic alert"}
            </span>
          </div>
        )}
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="border-t p-4 space-y-4">
          <div className="grid grid-cols-3 gap-4">
            {/* Timeline */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Timeline</h4>
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <div className="h-2 w-2 rounded-full bg-green-500" />
                  <span>Pickup at {trip.startTime}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <div className="h-2 w-2 rounded-full bg-blue-500" />
                  <span>Dropoff at {trip.endTime}</span>
                </div>
              </div>
            </div>

            {/* Special Requirements */}
            {trip.specialRequirements && trip.specialRequirements.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Special Requirements</h4>
                <ul className="text-sm space-y-1">
                  {trip.specialRequirements.map((req, i) => (
                    <li key={i} className="flex items-center gap-2">
                      <Check className="h-3 w-3 text-green-500" />
                      {req}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Quick Actions */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Quick Actions</h4>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" className="w-full">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Message
                </Button>
                <Button variant="outline" size="sm" className="w-full">
                  <Phone className="h-4 w-4 mr-2" />
                  Call
                </Button>
              </div>
            </div>
          </div>

          {/* Notes */}
          {trip.notes && (
            <div className="pt-4 border-t">
              <h4 className="font-medium text-sm mb-2">Notes</h4>
              <p className="text-sm text-muted-foreground">{trip.notes}</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

function StatsCard({ icon: Icon, title, value, trend }: { icon: any, title: string, value: string, trend?: string }) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
          </div>
          <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center">
            <Icon className="h-6 w-6 text-primary" />
          </div>
        </div>
        {trend && (
          <div className="mt-4 flex items-center text-sm text-green-600">
            <TrendingUp className="h-4 w-4 mr-1" />
            {trend}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

const TripRow = ({ trip }: { trip: Trip }) => {
  return (
    <div className="hover:bg-muted/50 transition-colors group relative border-b">
      <div className="flex items-center gap-4 px-4 py-2">
        {/* Checkbox column */}
        <div className="w-6">
          <input type="checkbox" className="rounded" onClick={(e) => e.stopPropagation()} />
        </div>

        {/* Trip ID and Passenger Info */}
        <div className="w-[250px] flex items-center gap-3">
          <div className="h-8 w-8 bg-primary/10 rounded-full flex items-center justify-center">
            <Car className="h-4 w-4 text-primary" />
          </div>
          <div>
            <div className="text-xs text-muted-foreground">#{trip.id}</div>
            <div className="flex items-center gap-1.5">
              <span className="font-medium">{trip.passengers[0].name}</span>
              {trip.passengers[0].isVIP && <Crown className="h-3 w-3 text-yellow-500" />}
            </div>
          </div>
        </div>

        {/* Trip Type */}
        <div className="w-[120px]">
          <Badge variant="secondary" className="bg-blue-100 text-blue-700 border-0">
            AIRPORT ARRIVAL
          </Badge>
        </div>

        {/* Time and Location */}
        <div className="flex-1 flex items-center gap-2">
          <div className="w-[80px] font-medium">{trip.startTime}</div>
          <div className="flex items-center gap-2 flex-1">
            <div className="h-6 w-6 rounded-full bg-green-100 flex items-center justify-center">
              <span className="text-green-700 text-sm font-medium">A</span>
            </div>
            <span className="text-sm text-muted-foreground truncate">{trip.pickupLocation}</span>
            <div className="w-12 h-px bg-muted-foreground/30 mx-2" />
            <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
              <span className="text-blue-700 text-sm font-medium">B</span>
            </div>
            <span className="text-sm text-muted-foreground truncate">{trip.dropoffLocation}</span>
          </div>
        </div>

        {/* Status */}
        <div className="w-[100px]">
          <Badge variant="secondary" className="bg-navy-100 text-navy-700 border-0">
            {trip.status === 'in_progress' ? 'IN PROGRESS' : 'SCHEDULED'}
          </Badge>
        </div>

        {/* Quick Actions */}
        <div className="flex items-center gap-1">
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Phone className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <MessageSquare className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <MapPin className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Alert Indicators */}
      {((trip.alerts && trip.alerts.length > 0) || trip.weather || trip.trafficAlert) && (
        <div className="absolute right-28 top-1/2 -translate-y-1/2 flex items-center gap-1.5">
          <AlertTriangle className="h-4 w-4 text-orange-500" />
          <span className="text-xs text-orange-500">{(trip.alerts && trip.alerts.length) || 1}</span>
        </div>
      )}
    </div>
  )
}

export default function TripsPage() {
  const [view, setView] = useState<"list" | "calendar" | "map">("list")
  const [searchTerm, setSearchTerm] = useState("")
  const [filters, setFilters] = useState({
    tripType: [] as string[],
    priority: [] as string[],
    status: [] as string[],
    dateRange: {
      start: "",
      end: ""
    }
  })

  // Filter trips based on status
  const upcomingTrips = mockTrips.filter(trip => trip.status === "scheduled")
  const activeTrips = mockTrips.filter(trip => trip.status === "in_progress")
  const completedTrips = mockTrips.filter(trip => trip.status === "completed")

  return (
    <div className="space-y-8 p-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold">Trips</h1>
          <p className="text-muted-foreground">
            Manage and track all transportation
          </p>
        </div>
        <div className="flex items-center gap-4">
          {/* View Toggle */}
          <ToggleGroup type="single" value={view} onValueChange={(v) => v && setView(v as any)}>
            <ToggleGroupItem value="list" aria-label="List view">
              <List className="h-4 w-4" />
            </ToggleGroupItem>
            <ToggleGroupItem value="calendar" aria-label="Calendar view">
              <Calendar className="h-4 w-4" />
            </ToggleGroupItem>
            <ToggleGroupItem value="map" aria-label="Map view">
              <MapIcon className="h-4 w-4" />
            </ToggleGroupItem>
          </ToggleGroup>

          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Trip
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Trips</CardTitle>
            <Car className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockTrips.length}</div>
            <p className="text-xs text-muted-foreground">
              +20.1% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Trips</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeTrips.length}</div>
            <p className="text-xs text-muted-foreground">
              {activeTrips.length} trips in progress
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{upcomingTrips.length}</div>
            <p className="text-xs text-muted-foreground">
              Next trip in 2 hours
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Alerts</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {mockTrips.reduce((count, trip) => count + (trip.alerts?.length || 0), 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              {mockTrips.filter(t => t.alerts?.length || 0 > 0).length} trips affected
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="relative w-[300px]">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search trips..."
              className="pl-9"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button variant="outline" className="gap-2">
            <Filter className="h-4 w-4" />
            Filters
          </Button>
        </div>
        <Select defaultValue="today">
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select date range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="today">Today</SelectItem>
            <SelectItem value="week">This Week</SelectItem>
            <SelectItem value="month">This Month</SelectItem>
            <SelectItem value="custom">Custom Range</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Trips List */}
      <Tabs defaultValue="upcoming">
        <TabsList>
          <TabsTrigger value="upcoming" className="gap-2">
            <Clock className="h-4 w-4" />
            Upcoming ({upcomingTrips.length})
          </TabsTrigger>
          <TabsTrigger value="active" className="gap-2">
            <Activity className="h-4 w-4" />
            Active ({activeTrips.length})
          </TabsTrigger>
          <TabsTrigger value="completed" className="gap-2">
            <CheckCircle2 className="h-4 w-4" />
            Completed ({completedTrips.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="upcoming" className="mt-4">
          <div className="space-y-4">
            {upcomingTrips.map((trip) => (
              <TripCard key={trip.id} trip={trip} />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="active" className="mt-4">
          <div className="space-y-4">
            {activeTrips.map((trip) => (
              <TripCard key={trip.id} trip={trip} />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="completed" className="mt-4">
          <div className="space-y-4">
            {completedTrips.map((trip) => (
              <TripCard key={trip.id} trip={trip} />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
} 