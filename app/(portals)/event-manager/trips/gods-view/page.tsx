"use client"

import { useState } from "react"
import <PERSON><PERSON><PERSON><PERSON>, { <PERSON> } from "@/app/components/shared/trips/GodsView"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/app/components/ui/tabs"
import { Input } from "@/app/components/ui/input"
import { But<PERSON> } from "@/app/components/ui/button"
import { Badge } from "@/app/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import {
  Search,
  Filter,
  Clock,
  Car,
  Users,
  Crown,
  Shield,
  AlertTriangle,
  Phone,
  MessageSquare,
  MapPin,
  ChevronUp,
  ChevronDown,
  Pin
} from "lucide-react"
import { cn } from "@/lib/utils"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/app/components/ui/tooltip"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"

// Sample data for testing
const sampleTrips: Trip[] = [
  {
    id: "1",
    eventName: "Tech Conference 2024",
    date: "2024-03-15",
    driver: { name: "<PERSON>", phone: "555-0123", rating: 4.8 },
    vehicle: "Tesla Model Y",
    pickupLocation: "SFO Airport",
    dropoffLocation: "Moscone Center",
    passengerCount: 3,
    status: "in_progress" as const,
    priority: "vip",
    tripType: "airport_arrival",
    startTime: "09:00",
    endTime: "10:00",
    eventId: "evt_123",
    passengers: [
      { name: "John Smith", isVIP: true }
    ],
    estimatedDuration: 45,
    lastUpdate: new Date().toISOString(),
    currentLocation: {
      lat: 37.7749,
      lng: -122.4194
    },
    alerts: [
      {
        type: "warning",
        message: "Heavy traffic ahead",
        timestamp: new Date().toISOString()
      }
    ]
  },
  {
    id: "2",
    eventName: "Music Festival 2024",
    date: "2024-03-15",
    driver: { name: "Sarah Wilson", phone: "555-0124", rating: 4.9 },
    vehicle: "Mercedes Sprinter",
    pickupLocation: "Oakland Airport",
    dropoffLocation: "Golden Gate Park",
    passengerCount: 8,
    status: "in_progress" as const,
    priority: "normal",
    tripType: "airport_arrival",
    startTime: "09:30",
    endTime: "10:30",
    eventId: "evt_124",
    passengers: [
      { name: "Band Members", isVIP: false }
    ],
    estimatedDuration: 50,
    lastUpdate: new Date().toISOString(),
    currentLocation: {
      lat: 37.7590,
      lng: -122.4340
    },
    alerts: []
  },
  {
    id: "3",
    eventName: "Corporate Summit 2024",
    date: "2024-03-15",
    driver: { name: "Mike Chen", phone: "555-0125", rating: 4.7 },
    vehicle: "BMW 7 Series",
    pickupLocation: "Four Seasons Hotel",
    dropoffLocation: "Salesforce Tower",
    passengerCount: 2,
    status: "in_progress" as const,
    priority: "vip",
    tripType: "transfer",
    startTime: "10:00",
    endTime: "10:30",
    eventId: "evt_125",
    passengers: [
      { name: "CEO Group", isVIP: true }
    ],
    estimatedDuration: 20,
    lastUpdate: new Date().toISOString(),
    currentLocation: {
      lat: 37.7858,
      lng: -122.4064
    },
    alerts: [
      {
        type: "info",
        message: "VIP guest onboard",
        timestamp: new Date().toISOString()
      }
    ]
  },
  {
    id: "4",
    eventName: "Wedding Ceremony",
    date: "2024-03-15",
    driver: { name: "Lisa Park", phone: "555-0126", rating: 5.0 },
    vehicle: "Rolls Royce Phantom",
    pickupLocation: "St. Mary's Cathedral",
    dropoffLocation: "Palace Hotel",
    passengerCount: 2,
    status: "in_progress" as const,
    priority: "vip",
    tripType: "vip",
    startTime: "11:00",
    endTime: "11:30",
    eventId: "evt_126",
    passengers: [
      { name: "Bride & Groom", isVIP: true }
    ],
    estimatedDuration: 25,
    lastUpdate: new Date().toISOString(),
    currentLocation: {
      lat: 37.7897,
      lng: -122.4265
    },
    alerts: []
  }
]

export default function GodsViewPage() {
  const [view, setView] = useState<"map" | "list">("map")
  const [pinnedTrips, setPinnedTrips] = useState<string[]>([])
  const [filters, setFilters] = useState({
    eventName: "",
    tripType: "all",
    priority: "all",
    status: "in_progress"
  })
  const [selectedTrip, setSelectedTrip] = useState<Trip | null>(null)

  const filteredTrips = sampleTrips.filter(trip => {
    return (
      (!filters.eventName || (trip.eventName?.toLowerCase() || '').includes(filters.eventName.toLowerCase())) &&
      (filters.tripType === "all" || trip.tripType === filters.tripType) &&
      (filters.priority === "all" || trip.priority === filters.priority) &&
      (filters.status === "all" || trip.status === filters.status)
    )
  })

  // Sort trips with pinned ones first
  const sortedTrips = [...filteredTrips].sort((a, b) => {
    const aIsPinned = pinnedTrips.includes(a.id)
    const bIsPinned = pinnedTrips.includes(b.id)
    if (aIsPinned && !bIsPinned) return -1
    if (!aIsPinned && bIsPinned) return 1
    return 0
  })

  const togglePin = (tripId: string) => {
    setPinnedTrips(prev =>
      prev.includes(tripId)
        ? prev.filter(id => id !== tripId)
        : [...prev, tripId]
    )
  }

  return (
    <div className="bg-background">
      <Tabs defaultValue="live">
        <div className="border-b px-4">
          <div className="flex items-center justify-between py-4">
            <TabsList>
              <TabsTrigger value="live">Live Trips</TabsTrigger>
              <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
            </TabsList>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                Filter
              </Button>
            </div>
          </div>
        </div>
        
        <TabsContent value="live" className="p-4">
          {/* Filters */}
          <div className="border-b p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="flex items-center gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by event name..."
                  className="pl-8"
                  value={filters.eventName}
                  onChange={(e) => setFilters(prev => ({ ...prev, eventName: e.target.value }))}
                />
              </div>
              <Select
                value={filters.tripType}
                onValueChange={(value) => setFilters(prev => ({ ...prev, tripType: value }))}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Trip Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="airport_arrival">Airport Arrival</SelectItem>
                  <SelectItem value="airport_departure">Airport Departure</SelectItem>
                  <SelectItem value="transfer">Transfer</SelectItem>
                  <SelectItem value="vip">VIP</SelectItem>
                </SelectContent>
              </Select>
              <Select
                value={filters.priority}
                onValueChange={(value) => setFilters(prev => ({ ...prev, priority: value }))}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priorities</SelectItem>
                  <SelectItem value="normal">Normal</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="vip">VIP</SelectItem>
                </SelectContent>
              </Select>
              <Select
                value={filters.status}
                onValueChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* View Toggle */}
          <Tabs value={view} onValueChange={(v) => setView(v as "map" | "list")} className="h-full">
            <div className="absolute right-4 top-20 z-10">
              <TabsList className="grid w-[200px] grid-cols-2">
                <TabsTrigger value="map">Map View</TabsTrigger>
                <TabsTrigger value="list">List View</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="map" className="h-full mt-0">
              <GodsView trips={sortedTrips} />
            </TabsContent>

            <TabsContent value="list" className="h-full mt-0 p-4">
              <div className="space-y-4">
                {sortedTrips.map(trip => (
                  <div key={trip.id} className={cn(
                    "border rounded-lg hover:bg-muted/50 transition-colors",
                    pinnedTrips.includes(trip.id) && "bg-blue-50/50",
                    trip.priority === "vip" && "bg-yellow-50/50"
                  )}>
                    {/* Main Row - Always Visible */}
                    <div className="p-3 grid grid-cols-[auto_2fr_2fr_auto] gap-4 items-center">
                      {/* Pin Button */}
                      <Button
                        variant="ghost"
                        size="sm"
                        className={cn(
                          "h-8 w-8 p-0",
                          pinnedTrips.includes(trip.id) && "text-blue-600"
                        )}
                        onClick={() => togglePin(trip.id)}
                      >
                        <Pin className="h-4 w-4" />
                      </Button>

                      {/* Left Section: Passenger Info */}
                      <div className="min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-lg font-semibold truncate">{trip.passengers?.[0]?.name || 'Unnamed Passenger'}</span>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                  <Users className="h-4 w-4" />
                                  <span>{trip.passengerCount} passengers</span>
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="space-y-1">
                                  {trip.passengers?.map((passenger, index) => (
                                    <div key={index} className="flex items-center gap-2">
                                      <span>{passenger.name}</span>
                                      {passenger.isVIP && (
                                        <Crown className="h-3 w-3 text-yellow-500" />
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          {trip.priority === "vip" && <Crown className="h-3 w-3 text-yellow-500 flex-shrink-0" />}
                          {trip.priority !== "normal" && (
                            <Badge variant="outline" className="text-xs flex-shrink-0">
                              <Shield className="h-3 w-3 mr-1" />
                              {trip.priority.toUpperCase()}
                            </Badge>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground flex items-center gap-2">
                          <span className="truncate">{trip.eventName}</span>
                        </div>
                      </div>

                      {/* Middle Section: Trip Details */}
                      <div className="min-w-0">
                        <div className="flex items-center gap-3 mb-1">
                          <span className="font-medium truncate">{trip.driver?.name}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Car className="h-4 w-4 flex-shrink-0" />
                          <span className="truncate">{trip.vehicle}</span>
                        </div>
                      </div>

                      {/* Right Section: Status & Actions */}
                      <div className="flex items-center gap-4">
                        <div className="flex flex-col items-end gap-2">
                          {/* Status Badge */}
                          <Badge variant="secondary" className={cn(
                            "capitalize h-8 flex items-center justify-center w-[120px] flex-shrink-0",
                            trip.status === "in_progress" ? "bg-blue-100 text-blue-700" :
                              trip.status === "completed" ? "bg-green-100 text-green-700" :
                                "bg-orange-100 text-orange-700"
                          )}>
                            {trip.status.replace('_', ' ')}
                          </Badge>

                          {/* Alerts */}
                          {trip.alerts && trip.alerts.length > 0 && (
                            <div className="flex items-center gap-1.5 flex-shrink-0">
                              <AlertTriangle className="h-4 w-4 text-orange-500" />
                              <span className="text-xs text-orange-500">{trip.alerts.length}</span>
                            </div>
                          )}
                        </div>

                        {/* Actions */}
                        <div className="flex items-center gap-1 flex-shrink-0">
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <Phone className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MessageSquare className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MapPin className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* Location & Time Bar */}
                    <div className={cn(
                      "px-3 py-2 border-t bg-muted/30 grid grid-cols-[200px_1fr_100px] gap-4 items-center",
                      pinnedTrips.includes(trip.id) && "bg-blue-50/50",
                      trip.priority === "vip" && "bg-yellow-50/50"
                    )}>
                      <div className="flex items-center gap-2 flex-shrink-0">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium text-sm text-muted-foreground">{trip.date}</span>
                        <span className="font-medium">{trip.startTime}</span>
                      </div>
                      <div className="grid grid-cols-[auto_1fr_auto_1fr] items-center gap-2 min-w-0">
                        <div className="h-6 w-6 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                          <span className="text-green-700 text-sm font-medium">A</span>
                        </div>
                        <span className="text-sm text-muted-foreground truncate">{trip.pickupLocation}</span>
                        <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                          <span className="text-blue-700 text-sm font-medium">B</span>
                        </div>
                        <span className="text-sm text-muted-foreground truncate">{trip.dropoffLocation}</span>
                      </div>
                      {trip.status === "in_progress" && (
                        <div className="flex items-center gap-2">
                          <div className="flex-1 h-1.5 bg-muted rounded-full overflow-hidden">
                            <div className="h-full bg-blue-500 transition-all duration-500" style={{ width: "30%" }} />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </TabsContent>
        
        <TabsContent value="upcoming" className="p-4">
          {/* Upcoming trips content */}
        </TabsContent>
        
        <TabsContent value="completed" className="p-4">
          {/* Completed trips content */}
        </TabsContent>
      </Tabs>

      {selectedTrip && (
        <div className="h-full min-h-screen p-4 md:p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-bold">{selectedTrip?.eventName ?? 'Trip Details'}</h2>
            <Button
              onClick={() => setSelectedTrip(null)}
              variant="outline"
            >
              View All Trips
            </Button>
          </div>
          <div className="mt-4 space-y-4">
            <div className="flex flex-wrap gap-3">
              <Badge variant="outline">
                {selectedTrip?.passengers?.length ?? 0} Passengers
              </Badge>
              <Badge variant="outline">
                {selectedTrip?.status}
              </Badge>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 