# Event Manager Portal

## Overview
The Event Manager Portal provides tools for event managers to oversee events, manage transportation quotes, and monitor trips. This portal is designed for users with the `EVENT_MANAGER` role.

## Features
- **Dashboard**: Overview of events, quotes, and trips
- **Events**: Create and manage events
- **Quotes**: Review and manage transportation quotes
- **Trips**: Track and manage transportation trips

## Directory Structure
- `dashboard/` - Event manager dashboard
- `events/` - Event management
  - `[id]/` - Individual event details
    - `quotes/` - Quotes for a specific event
    - `trips/` - Trips for a specific event
  - `new/` - Create new event
- `quotes/` - Quote management
  - `[id]/` - Individual quote details
  - `new/` - Create new quote
- `trips/` - Trip management
  - `components/` - Trip-specific components
  - `gods-view/` - Map view of all trips

## Key Components
- `layout.tsx` - Defines the layout for the event manager portal
- `trips/components/gods-view.tsx` - Map view component for trips
- `events/[id]/page.tsx` - Event details page
- `events/[id]/quotes/page.tsx` - Event quotes page
- `events/[id]/trips/page.tsx` - Event trips page

## API Integration
The event manager portal interacts with several API endpoints:
- `/api/events` - Event management
- `/api/quotes` - Quote management
- `/api/trips` - Trip management

## Shared Components
This portal shares many components with the Customer portal, particularly in the trips and quotes sections. The components have been copied to maintain consistency in the user experience while allowing for role-specific customizations.

## Testing
Tests for the event manager portal are located in `/tests/event-manager/` and should mirror this directory structure. 