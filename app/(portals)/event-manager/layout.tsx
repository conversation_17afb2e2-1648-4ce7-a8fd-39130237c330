"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Spinner } from '@/app/components/ui/spinner'
import { Head<PERSON> } from "@/app/components/layout/header"
import { useAuth } from '@/lib/auth/context'
import type { UserRole } from '@/src/types/roles'
import { Button } from '@/app/components/ui/button'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import { UserNav } from '@/app/components/features/navigation/user-nav'
import { MobileNav } from '@/app/components/features/navigation/mobile-nav'
import { Suspense } from 'react'
import { Toaster } from '@/app/components/ui/toaster'
import { CompactConnectionStatus } from '@/components/ui/connection-status'
import { NotificationBell } from '@/components/notifications/notification-history'

// Define debug info type
interface DebugInfo {
  timestamp: string;
  authState: { user: any; loading: boolean; roles: UserRole[] };
  location: string;
  error?: string;
  refreshAttempts: number;
  [key: string]: any;
}

export default function EventManagerLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, loading, refreshAuth } = useAuth()
  const router = useRouter()
  const [debugInfo, setDebugInfo] = useState<DebugInfo>({
    timestamp: new Date().toISOString(),
    authState: { user: null, loading: true, roles: [] },
    location: typeof window !== 'undefined' ? window.location.href : '',
    refreshAttempts: 0
  })
  const [refreshAttempts, setRefreshAttempts] = useState(0)
  const MAX_REFRESH_ATTEMPTS = 3

  const hasRequiredRole = (userRoles: UserRole[] = []) => {
    return userRoles.includes('CLIENT');
  }

  useEffect(() => {
    console.log('EventManagerLayout - Component mounted');
    let mounted = true;
    let refreshTimeout: NodeJS.Timeout;

    const updateDebugInfo = (info: Partial<DebugInfo>) => {
      if (mounted) {
        setDebugInfo(prev => ({
          ...prev,
          ...info,
          timestamp: new Date().toISOString()
        }));
      }
    };

    const attemptSessionRefresh = async () => {
      if (!mounted) {
        console.log('EventManagerLayout - Skipping refresh, component unmounted');
        return;
      }
      
      // Check if direct login is in progress
      const directLoginInProgress = localStorage.getItem('direct_login_in_progress');
      if (directLoginInProgress === 'true') {
        console.log('EventManagerLayout - Skipping refresh, direct login in progress');
        return;
      }
      
      // Skip if we're already on the refresh endpoint
      if (typeof window !== 'undefined' && window.location.pathname.includes('/api/auth/refresh')) {
        console.log('EventManagerLayout - Skipping refresh, already on refresh endpoint');
        return;
      }
      
      // Check if we've refreshed recently (within the last 10 seconds)
      const lastRefreshStr = localStorage.getItem('last_layout_refresh_time');
      if (lastRefreshStr) {
        const lastRefreshTime = parseInt(lastRefreshStr, 10);
        const now = Date.now();
        const timeSinceLastRefresh = now - lastRefreshTime;
        
        console.log(`EventManagerLayout - Last refresh was ${timeSinceLastRefresh}ms ago`);
        
        if (timeSinceLastRefresh < 10000) { // 10 seconds
          console.log(`EventManagerLayout - Skipping refresh, last refresh was ${timeSinceLastRefresh}ms ago`);
          return;
        }
      } else {
        console.log('EventManagerLayout - No last refresh time found in localStorage');
      }
      
      console.log(`EventManagerLayout - Attempting session refresh (attempt ${refreshAttempts + 1})`);
      updateDebugInfo({ refreshAttempts: refreshAttempts + 1 });
      
      // Use the auth provider's refreshAuth function
      await refreshAuth();
      
      // Store the refresh time
      const now = Date.now();
      localStorage.setItem('last_layout_refresh_time', now.toString());
      console.log(`EventManagerLayout - Stored refresh time: ${now}`);
      
      // Increment the refresh attempts counter
      if (mounted) {
        setRefreshAttempts(prev => prev + 1);
      }
    };

    const handleAuthState = async () => {
      if (!mounted) return;

      const userRoles = user?.roles || [];
      updateDebugInfo({ authState: { user, loading, roles: userRoles } });
      console.log('EventManagerLayout - Auth state:', { user, loading, roles: userRoles });

      // If still loading, wait
      if (loading) {
        console.log('EventManagerLayout - Still loading auth state');
        return;
      }

      // If no user and we haven't maxed out refresh attempts, try refreshing
      if (!user && refreshAttempts < MAX_REFRESH_ATTEMPTS) {
        console.log('EventManagerLayout - No user, attempting refresh');
        refreshTimeout = setTimeout(attemptSessionRefresh, 1000); // Wait 1 second between attempts
        return;
      }

      // If no user and we've maxed out refresh attempts, redirect to login
      if (!user && refreshAttempts >= MAX_REFRESH_ATTEMPTS) {
        console.log('EventManagerLayout - Max refresh attempts reached, redirecting to login');
        router.push('/login');
        return;
      }

      // If user exists but doesn't have required role, redirect to login
      if (user && !hasRequiredRole(userRoles)) {
        console.log('EventManagerLayout - User does not have required role:', userRoles);
        router.push('/login');
        return;
      }

      // If we get here with a valid user, they're authenticated and authorized
      if (user && hasRequiredRole(userRoles)) {
        console.log('EventManagerLayout - User authenticated and authorized');
      }
    };

    handleAuthState();

    return () => {
      mounted = false;
      if (refreshTimeout) {
        clearTimeout(refreshTimeout);
      }
    };
  }, [user, loading, refreshAttempts, router, refreshAuth]);

  // Show loading state while authentication is in progress
  if (loading || (!user && refreshAttempts < MAX_REFRESH_ATTEMPTS)) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <Spinner size="lg" />
          <p className="mt-4">Loading user data...</p>
          {refreshAttempts > 0 && (
            <p className="text-sm text-gray-500">
              Refresh attempt {refreshAttempts} of {MAX_REFRESH_ATTEMPTS}
            </p>
          )}
        </div>
      </div>
    );
  }

  // If no user after max refresh attempts, show redirect message with debug options
  if (!user) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <p className="text-xl font-bold mb-4">Unable to authenticate</p>
          <p className="mb-6">You need to be logged in to access this page.</p>
          
          <div className="flex flex-col gap-2 items-center mb-6">
            <Button onClick={() => router.push('/login')}>
              Go to Login Page
            </Button>
            <Button variant="outline" onClick={() => router.push('/debug')}>
              Go to Debug Page
            </Button>
            <Button variant="outline" onClick={() => router.push('/direct-login')}>
              Try Direct Login
            </Button>
            <Button variant="outline" onClick={() => refreshAuth()}>
              Refresh Auth State
            </Button>
          </div>
          
          <div className="mt-8 max-w-lg mx-auto">
            <p className="text-sm font-semibold mb-2">Debug Information:</p>
            <pre className="mt-4 bg-gray-100 p-2 rounded text-xs overflow-auto text-left">
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    );
  }

  // For desktop, render with standard layout
  return (
    <div className="flex min-h-screen flex-col">
      <Header user={{
        name: user?.email?.split('@')[0] || 'User',
        email: user?.email || '',
        roles: user?.roles || []
      }}>
        <div className="flex items-center gap-3">
          <NotificationBell />
          <CompactConnectionStatus />
        </div>
      </Header>
      <div className="container flex-1 py-6">
        <main className="flex w-full flex-1 flex-col overflow-hidden">
          {children}
        </main>
      </div>
    </div>
  )
} 