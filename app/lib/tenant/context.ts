/**
 * Tenant Context Management
 *
 * This module handles tenant context switching and isolation for the multi-tenant architecture.
 * It supports three levels of tenancy:
 * - Level 1 (Shared): ORG switching for client organizations
 * - Level 2 (Segregated): TNC switching between TransFlow/LIMO123/etc.
 * - Level 3 (White-label): Complete branding and data isolation
 */

import { createServerClient } from "@supabase/supabase-js";

export interface TenantContext {
  tenantId: string | null;
  tenantType: "shared" | "segregated" | "white_label";
  organizationId: string | null; // For Level 1 ORG switching
  networkId: string | null; // For Level 2 TNC switching
  domain: string | null;
  branding: TenantBranding | null;
}

export interface TenantBranding {
  logoUrl?: string;
  faviconUrl?: string;
  primaryColor?: string;
  secondaryColor?: string;
  backgroundColor?: string;
  customCss?: string;
  emailTemplates?: Record<string, any>;
}

export interface Tenant {
  id: string;
  name: string;
  slug: string;
  domain?: string;
  parent_tenant_id?: string;
  tenant_type: "shared" | "segregated" | "white_label";
  status: "active" | "inactive" | "suspended";
  branding?: TenantBranding;
  settings?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

/**
 * Determines tenant context from request headers and domain
 */
export async function getTenantContext(
  request: Request,
  supabase: ReturnType<typeof createServerClient>
): Promise<TenantContext> {
  const url = new URL(request.url);
  const domain = url.hostname;

  // Check for custom domain (Level 3 - White-label)
  if (
    domain !== "localhost" &&
    domain !== "127.0.0.1" &&
    !domain.includes("transflow.com")
  ) {
    const { data: tenant } = await supabase
      .from("tenants")
      .select("*")
      .eq("domain", domain)
      .eq("status", "active")
      .single();

    if (tenant) {
      return {
        tenantId: tenant.id,
        tenantType: tenant.tenant_type,
        organizationId: null,
        networkId: null,
        domain: tenant.domain,
        branding: tenant.branding,
      };
    }
  }

  // Check for subdomain routing (Level 2 - Segregated)
  const subdomain = domain.split(".")[0];
  if (subdomain && subdomain !== "app" && subdomain !== "www") {
    const { data: tenant } = await supabase
      .from("tenants")
      .select("*")
      .eq("slug", subdomain)
      .eq("status", "active")
      .single();

    if (tenant) {
      return {
        tenantId: tenant.id,
        tenantType: tenant.tenant_type,
        organizationId: null,
        networkId: tenant.id,
        domain: tenant.domain,
        branding: tenant.branding,
      };
    }
  }

  // Default to shared tenant (Level 1 - Shared)
  // Organization switching will be handled by session context
  return {
    tenantId: process.env.DEFAULT_TENANT_ID || null,
    tenantType: "shared",
    organizationId: null,
    networkId: null,
    domain: null,
    branding: null,
  };
}

/**
 * Sets tenant context in Supabase for RLS policies
 */
export async function setTenantContext(
  supabase: ReturnType<typeof createServerClient>,
  context: TenantContext
): Promise<void> {
  if (context.tenantId) {
    await supabase.rpc("set_tenant_config", {
      setting_name: "app.current_tenant_id",
      setting_value: context.tenantId,
      is_local: true,
    });
  }

  if (context.organizationId) {
    await supabase.rpc("set_tenant_config", {
      setting_name: "app.current_organization_id",
      setting_value: context.organizationId,
      is_local: true,
    });
  }
}

/**
 * Gets the current tenant from session storage or headers
 */
export function getCurrentTenantFromSession(request: Request): {
  tenantId?: string;
  organizationId?: string;
} {
  // Check for tenant switching headers (set by UI components)
  const tenantId = request.headers.get("x-current-tenant-id");
  const organizationId = request.headers.get("x-current-organization-id");

  return {
    tenantId: tenantId || undefined,
    organizationId: organizationId || undefined,
  };
}

/**
 * Validates if a user has access to a specific tenant/organization
 */
export async function validateTenantAccess(
  supabase: ReturnType<typeof createServerClient>,
  userId: string,
  userRoles: string[],
  tenantId: string,
  organizationId?: string
): Promise<boolean> {
  // Super admins have access to all tenants
  if (userRoles.includes("SUPER_ADMIN")) {
    return true;
  }

  // Check user's tenant access
  const { data: userTenantAccess } = await supabase
    .from("user_tenant_access")
    .select("*")
    .eq("user_id", userId)
    .eq("tenant_id", tenantId)
    .single();

  if (!userTenantAccess) {
    return false;
  }

  // If organization is specified, check organization access
  if (organizationId) {
    const { data: userOrgAccess } = await supabase
      .from("user_organization_access")
      .select("*")
      .eq("user_id", userId)
      .eq("organization_id", organizationId)
      .single();

    return !!userOrgAccess;
  }

  return true;
}

/**
 * Creates a tenant-aware Supabase client with proper RLS context
 */
export async function createTenantAwareSupabaseClient(
  request: Request,
  baseSupabase: ReturnType<typeof createServerClient>
): Promise<{
  supabase: ReturnType<typeof createServerClient>;
  context: TenantContext;
}> {
  const context = await getTenantContext(request, baseSupabase);
  await setTenantContext(baseSupabase, context);

  return {
    supabase: baseSupabase,
    context,
  };
}
