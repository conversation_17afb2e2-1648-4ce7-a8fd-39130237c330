// SERVER ONLY: Do not import this file in any React component, layout, or shared code.
import { Pool } from 'pg'

const pool = new Pool({
  user: process.env.POSTGRES_USER || 'postgres',
  host: process.env.POSTGRES_HOST || 'localhost',
  database: process.env.POSTGRES_DB || 'wwms',
  password: process.env.POSTGRES_PASSWORD || 'postgres',
  port: parseInt(process.env.POSTGRES_PORT || '5432'),
})

export async function query(text: string, params?: any[]) {
  try {
    const result = await pool.query(text, params)
    return { data: result.rows, error: null }
  } catch (error: any) {
    console.error('Database query error:', error)
    return { data: null, error: error.message }
  }
}

export async function getUser(email: string) {
  const { data, error } = await query(
    'SELECT * FROM auth.users WHERE email = $1',
    [email]
  )
  if (error) return { user: null, error }
  return { user: data?.[0] || null, error: null }
}

export async function createUser(email: string, hashedPassword: string, role: string = 'CUSTOMER') {
  const { data, error } = await query(
    'INSERT INTO auth.users (email, encrypted_password, role) VALUES ($1, $2, $3) RETURNING *',
    [email, hashedPassword, role]
  )
  if (error) return { user: null, error }
  return { user: data?.[0] || null, error: null }
}