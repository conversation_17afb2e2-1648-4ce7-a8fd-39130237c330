import { Database } from '@/src/types/supabase';
import { UserRole, toUserRoles } from '@/src/types/roles';
import { supabaseAdmin } from '@/lib/supabaseAdmin';

export async function upgradeToEventManager(userId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const { data: profile, error: fetchError } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();

    if (fetchError) throw fetchError;
    if (!profile) throw new Error('Profile not found');

    // Convert single role to array for compatibility
    const currentRoles = profile.role ? [profile.role as UserRole] : ['CUSTOMER'];
    if (currentRoles.includes('EVENT_MANAGER')) {
      return { success: false, error: 'User is already an Event Manager' };
    }

    const { error: updateError } = await supabaseAdmin
      .from('profiles')
      .update({ role: 'EVENT_MANAGER' })
      .eq('id', userId);

    if (updateError) throw updateError;

    return { success: true };
  } catch (error) {
    console.error('Error upgrading to Event Manager:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    };
  }
}

export async function getUserRoles(userId: string): Promise<UserRole[]> {
  try {
    const { data: profile, error } = await supabaseAdmin
      .from('profiles')
      .select('role, roles')
      .eq('id', userId)
      .single();

    if (error || !profile) {
      console.error('Error fetching user roles:', error);
      return ['CLIENT']; // Default to CLIENT role if there's an error
    }
    // Always use toUserRoles for normalization. Use CLIENT as default.
    return toUserRoles(profile.roles || profile.role || 'CLIENT');
  } catch (error) {
    console.error('Error fetching user roles:', error);
    return ['CLIENT']; // Default to CLIENT role if there's an error
  }
} 