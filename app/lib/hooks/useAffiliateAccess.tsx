import React, { useMemo } from "react";
import { useAffiliateCompany } from "@/app/contexts/AffiliateCompanyContext";

// TODO: Import rate fetching logic when available
// import { useRatesForCompany } from '...';

/**
 * Hook for affiliate access and onboarding logic.
 * Returns status flags, permission helpers, and onboarding state.
 */
export function useAffiliateAccess() {
  const { selectedCompany } = useAffiliateCompany();

  // Status helpers - use application_status since company_onboarding_status doesn't exist
  const isPending =
    (selectedCompany as any)?.application_status === "pending" ||
    (selectedCompany as any)?.application_status === "Draft";
  const isActive =
    (selectedCompany as any)?.application_status === "approved" ||
    (selectedCompany as any)?.status === "active";

  // TODO: Implement actual rate check logic
  const canRespondToOffer = (offer: any): boolean => {
    // Example: return true if required rate for offer.vehicleType is filled
    // const rates = useRatesForCompany(selectedCompany?.id);
    // return rates?.some(r => r.vehicleType === offer.vehicleType && r.isFilled);

    // For testing: allow if company exists and has active status OR is approved
    // This is more permissive to enable testing
    return (
      selectedCompany &&
      (isActive ||
        (selectedCompany as any)?.status === "active" ||
        (selectedCompany as any)?.application_status === "approved")
    );
  };

  // New: boolean flag for showing onboarding banner
  const showOnboardingBanner = isPending;

  return {
    isPending,
    isActive,
    canRespondToOffer,
    showOnboardingBanner,
    selectedCompany,
  };
}
