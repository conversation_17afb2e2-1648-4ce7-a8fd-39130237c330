import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import { TenantBranding } from "@/app/contexts/TenantContext";

interface NotificationTemplate {
  type: string;
  subject_template: string;
  content_template: string;
  variables: Record<string, any>;
}

/**
 * Fetches a tenant-specific email template, falling back to default if not found.
 * @param tenantId The ID of the current tenant.
 * @param templateType The type of email template to retrieve (e.g., 'approval', 'quote_confirmation').
 * @returns The email template details (subject, content, variables) or null if not found.
 */
export async function getTenantEmailTemplate(
  tenantId: string | null,
  templateType: string
): Promise<NotificationTemplate | null> {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
      },
    }
  );

  let template: NotificationTemplate | null = null;

  // 1. Try to get the template from tenant-specific branding
  if (tenantId) {
    const { data: tenantBrandingData, error: brandingError } = await supabase
      .from("tenant_branding")
      .select("email_templates")
      .eq("tenant_id", tenantId)
      .single();

    if (brandingError && brandingError.code !== "PGRST116") {
      // PGRST116 = no rows returned, which is expected if no custom branding
      console.error(
        `Error fetching tenant branding for tenant ${tenantId}:`,
        brandingError
      );
    }

    if (
      tenantBrandingData &&
      tenantBrandingData.email_templates &&
      tenantBrandingData.email_templates[templateType]
    ) {
      template = tenantBrandingData.email_templates[templateType];
      console.log(
        `[Email Templates] Found custom template for type '${templateType}' for tenant ${tenantId}.`
      );
    }
  }

  // 2. If not found in branding, fall back to public notification_templates table
  if (!template) {
    const { data: defaultTemplateData, error: defaultError } = await supabase
      .from("notification_templates")
      .select("*")
      .eq("type", templateType)
      .single();

    if (defaultError && defaultError.code !== "PGRST116") {
      console.error(
        `Error fetching default template for type '${templateType}':`,
        defaultError
      );
    }

    if (defaultTemplateData) {
      template = defaultTemplateData as NotificationTemplate;
      console.log(
        `[Email Templates] Falling back to default template for type '${templateType}'.`
      );
    }
  }

  if (!template) {
    console.warn(`[Email Templates] No template found for type '${templateType}'.`);
  }

  return template;
} 