// SERVER ONLY: Do not import this file in any React component, layout, or shared code.
import { Pool } from 'pg';
import { jwtVerify, SignJWT } from 'jose';

const pool = new Pool({
  host: process.env.POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || '5432'),
  database: process.env.POSTGRES_DB || 'wwms',
  user: process.env.POSTGRES_USER || 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'postgres'
});

const JWT_SECRET = new TextEncoder().encode(
  process.env.NEXTAUTH_SECRET || 'TEST_SECRET'
);

interface Session {
  id: string;
  email: string;
  role: 'CUSTOMER' | 'AFFILIATE' | 'EVENT_MANAGER';
  exp: number;
  iat: number;
}

export async function verifySession(token: string): Promise<Session | null> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    const result = await pool.query(
      'SELECT id, email, role FROM auth.users WHERE id = $1',
      [payload.id]
    );
    if (result.rows.length === 0) return null;
    if (!payload.id || !payload.email || !payload.role) return null;
    return payload as unknown as Session;
  } catch (error) {
    console.error('Error verifying session:', error);
    return null;
  }
}

export async function signIn(email: string, password: string) {
  try {
    console.log('[Auth] Attempting to sign in with email:', email);
    const result = await pool.query(
      'SELECT id, email, encrypted_password, role FROM auth.users WHERE email = $1',
      [email]
    );
    if (result.rows.length === 0) {
      console.error('[Auth] User not found');
      return { error: new Error('Invalid credentials') };
    }
    const user = result.rows[0];
    const validPassword = await pool.query(
      'SELECT auth.crypt($1, $2) = $2 as is_valid',
      [password, user.encrypted_password]
    );
    if (!validPassword.rows[0].is_valid) {
      console.error('[Auth] Invalid password');
      return { error: new Error('Invalid credentials') };
    }
    const iat = Math.floor(Date.now() / 1000);
    const exp = iat + 24 * 60 * 60;
    const session = {
      id: user.id,
      email: user.email,
      role: user.role,
      iat,
      exp
    };
    const token = await new SignJWT({ ...session })
      .setProtectedHeader({ alg: 'HS256', typ: 'JWT' })
      .setIssuedAt()
      .setExpirationTime('24h')
      .sign(JWT_SECRET);
    console.log('[Auth] Sign in successful for:', user.email);
    return { data: { session, token }, error: null };
  } catch (error: any) {
    console.error('[Auth] Caught error during sign in:', error.message);
    return { data: null, error };
  }
}

export async function signUp(email: string, password: string) {
  try {
    console.log('[Auth] Attempting to sign up with email:', email);
    const existingUser = await pool.query(
      'SELECT id FROM auth.users WHERE email = $1',
      [email]
    );
    if (existingUser.rows.length > 0) {
      return { error: new Error('User already exists') };
    }
    const result = await pool.query(
      'INSERT INTO auth.users (email, encrypted_password, role) VALUES ($1, auth.crypt($2, auth.gen_salt("bf")), $3) RETURNING id, email, role',
      [email, password, 'CUSTOMER']
    );
    const user = result.rows[0];
    console.log('[Auth] Sign up successful for email:', email);
    return { data: { user }, error: null };
  } catch (error: any) {
    console.error('[Auth] Caught error during sign up:', error.message);
    return { data: null, error };
  }
}

export async function signOut() {
  try {
    console.log('[Auth] Attempting to sign out');
    // For local auth, we just need to clear the session cookie on the client side
    console.log('[Auth] Sign out successful');
    return { error: null };
  } catch (error: any) {
    console.error('[Auth] Caught error during sign out:', error.message);
    return { error };
  }
} 