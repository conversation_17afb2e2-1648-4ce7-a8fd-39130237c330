// This file is safe for both client and server. Do not import server-only code here.
import { toUserRoles, UserRole } from '@/src/types/roles'

// Update the Session interface to reflect the actual structure returned by getSession
export interface Session {
  id: string | null;
  email: string | null;
  access_token: string | null;
  roles: UserRole[]; // The normalized roles array
  user: { // The raw user object from Supabase session
    id: string;
    email: string | null; // Allow email to be null or undefined for consistency
    app_metadata: { [key: string]: any; roles?: UserRole[], role?: UserRole };
    user_metadata: { [key: string]: any; roles?: UserRole[], role?: UserRole };
    [key: string]: any; // Allow other properties
  };
  [key: string]: any; // Allow other properties from the raw session object
}

export async function getSession(request: import('next/server').NextRequest) {
  // Get the Supabase session cookie
  const cookie = request.cookies.get('sb-127-auth-token')?.value;
  if (!cookie) {
    console.error('[getSession] No sb-127-auth-token cookie found. User is not authenticated.');
    return null;
  }
  // Only support Supabase v2: base64-encoded JSON session cookies
  if (!cookie.startsWith('base64-')) {
    console.error('[getSession] Session cookie is not base64-encoded. JWTs are not supported.');
    return null;
  }
  try {
    const base64 = cookie.replace('base64-', '');
    const json = Buffer.from(base64, 'base64').toString('utf-8');
    const sessionRaw = JSON.parse(json);
    // Extract roles from user_metadata, app_metadata, or top-level session
    let role = null;
    if (sessionRaw.user) {
      const um = sessionRaw.user.user_metadata || {};
      const am = sessionRaw.user.app_metadata || {};
      // Prefer 'roles' (array), fallback to 'role' (string/array)
      role = um.roles || am.roles || um.role || am.role || sessionRaw.role || [];
    } else {
      // Fallback for top-level session
      role = sessionRaw.roles || sessionRaw.role || [];
    }
    // Always normalize to array
    const roles = Array.isArray(role) ? role : role ? [role] : [];

    // Ensure id, email, and access_token are at the top-level of the session
    const userId = sessionRaw.user?.id || null;
    const userEmail = sessionRaw.user?.email || null;
    const accessToken = sessionRaw.access_token || null;

    return {
      id: userId,
      email: userEmail,
      access_token: accessToken,
      ...sessionRaw,
      roles,
    };
  } catch (e) {
    console.error('[getSession] Failed to parse base64-encoded session cookie:', e);
    return null;
  }
}

/**
 * Utility: Check if user has required role(s)
 * Always use this for role checks in API routes and middleware.
 * @param roles - The user's roles (array or undefined)
 * @param required - The required role or array of roles (UserRole or UserRole[])
 * @returns boolean
 */
export function hasRole(roles: string[] | undefined, required: UserRole | UserRole[]): boolean {
  const userRoles = toUserRoles(roles || []);
  const requiredRoles = Array.isArray(required) ? required : [required];
  return requiredRoles.some(r => userRoles.includes(r));
}
