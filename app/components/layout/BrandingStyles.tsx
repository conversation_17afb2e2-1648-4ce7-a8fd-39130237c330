"use client";

import { useTenant } from "@/app/contexts/TenantContext";
import { useEffect } from "react";

// Helper function to convert HEX to HSL
// This is a basic conversion and might need to be more robust
// depending on the exact format of the hex colors in the database.
function hexToHsl(hex: string) {
  if (!hex || hex === "" || hex.length < 7 || hex.length > 7) return null; // Invalid hex

  let r = 0,
    g = 0,
    b = 0;
  // Handle cases where hex might be 3-digit shorthand
  if (hex.length === 4) {
    r = parseInt(hex[1] + hex[1], 16);
    g = parseInt(hex[2] + hex[2], 16);
    b = parseInt(hex[3] + hex[3], 16);
  } else {
    r = parseInt(hex.substring(1, 3), 16);
    g = parseInt(hex.substring(3, 5), 16);
    b = parseInt(hex.substring(5, 7), 16);
  }

  r /= 255;
  g /= 255;
  b /= 255;

  let max = Math.max(r, g, b);
  let min = Math.min(r, g, b);
  let h = 0,
    s = 0,
    l = (max + min) / 2;

  if (max === min) {
    h = s = 0; // achromatic
  } else {
    let d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }
    h /= 6;
  }

  h = Math.round(h * 360);
  s = Math.round(s * 100);
  l = Math.round(l * 100);

  return `${h} ${s}% ${l}%`;
}

export function BrandingStyles() {
  const { currentTenant } = useTenant();

  useEffect(() => {
    if (currentTenant && currentTenant.tenant_type === "white_label" && currentTenant.branding) {
      const { primary_color, secondary_color, background_color, custom_css } = currentTenant.branding;
      const root = document.documentElement;

      if (primary_color) {
        const hsl = hexToHsl(primary_color);
        if (hsl) {
          root.style.setProperty("--primary", hsl);
          root.style.setProperty("--primary-foreground", "210 20% 98%"); // Default for dark text on primary
        }
      } else {
        root.style.removeProperty("--primary");
        root.style.removeProperty("--primary-foreground");
      }

      if (secondary_color) {
        const hsl = hexToHsl(secondary_color);
        if (hsl) {
          root.style.setProperty("--secondary", hsl);
          root.style.setProperty("--secondary-foreground", "220.9 39.3% 11%"); // Default for dark text on secondary
        }
      } else {
        root.style.removeProperty("--secondary");
        root.style.removeProperty("--secondary-foreground");
      }

      if (background_color) {
        const hsl = hexToHsl(background_color);
        if (hsl) {
          root.style.setProperty("--background", hsl);
          root.style.setProperty("--foreground", "224 71.4% 4.1%"); // Default for dark text on background
        }
      } else {
        root.style.removeProperty("--background");
        root.style.removeProperty("--foreground");
      }

      // Inject custom CSS
      let customStyleTag = document.getElementById("tenant-custom-css");
      if (custom_css) {
        if (!customStyleTag) {
          customStyleTag = document.createElement("style");
          customStyleTag.id = "tenant-custom-css";
          document.head.appendChild(customStyleTag);
        }
        customStyleTag.innerHTML = custom_css;
      } else if (customStyleTag) {
        customStyleTag.remove();
      }
    } else {
      // Remove custom styles if not a white-label tenant or no branding
      document.documentElement.style.removeProperty("--primary");
      document.documentElement.style.removeProperty("--primary-foreground");
      document.documentElement.style.removeProperty("--secondary");
      document.documentElement.style.removeProperty("--secondary-foreground");
      document.documentElement.style.removeProperty("--background");
      document.documentElement.style.removeProperty("--foreground");
      const customStyleTag = document.getElementById("tenant-custom-css");
      if (customStyleTag) {
        customStyleTag.remove();
      }
    }
  }, [currentTenant]);

  return null; // This component doesn't render anything directly to the DOM
} 