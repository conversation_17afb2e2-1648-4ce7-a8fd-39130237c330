'use client'

import { MainNav } from "@/app/components/features/navigation/main-nav"
import { UserNav } from "@/app/components/features/navigation/user-nav"
import { MobileNav } from "@/app/components/features/navigation/mobile-nav"
import { Button } from "@/app/components/ui/button"
import { Menu, LayoutGrid, MapIcon, Calendar, FileText, Clock, Car, Globe, Users } from "lucide-react"
import { useState, useEffect } from "react"
import Link from "next/link"
import { UserRole } from "@/src/types/roles"
import { hasRole } from '@/app/lib/auth'
import { useTenant } from "@/app/contexts/TenantContext";

interface HeaderProps {
  user?: {
    name: string
    email: string
    roles: UserRole[]
    image?: string
  }
  children?: React.ReactNode
}

const navigationItems = {
  customer: [
    {
      title: "Dashboard",
      href: "/customer/dashboard",
      icon: <LayoutGrid className="h-4 w-4" />
    },
    {
      title: "Quotes",
      href: "/customer/quotes",
      icon: <FileText className="h-4 w-4" />
    },
    {
      title: "Trips",
      href: "/customer/trips",
      icon: <Car className="h-4 w-4" />
    }
  ],
  eventManager: [
    {
      title: "Dashboard",
      href: "/event-manager/dashboard",
      icon: <LayoutGrid className="h-4 w-4" />
    },
    {
      title: "Events",
      href: "/event-manager/events",
      icon: <Calendar className="h-4 w-4" />
    },
    {
      title: "Quotes",
      href: "/event-manager/quotes",
      icon: <FileText className="h-4 w-4" />
    },
    {
      title: "Passengers",
      href: "/event-manager/passengers",
      icon: <Users className="h-4 w-4" />
    },
    {
      title: "Trips",
      href: "/event-manager/trips",
      icon: <Car className="h-4 w-4" />
    }
  ],
  affiliate: [
    {
      title: "Dashboard",
      href: "/affiliate/dashboard",
      icon: <LayoutGrid className="h-4 w-4" />
    },
    {
      title: "Fleet & Rates",
      href: "/affiliate/fleetRates",
      icon: <Car className="h-4 w-4" />
    },
    {
      title: "Offers",
      href: "/affiliate/offers",
      icon: <FileText className="h-4 w-4" />
    },
    {
      title: "Live Trips",
      href: "/affiliate/liveTrips",
      icon: <Clock className="h-4 w-4" />
    }
  ]
}

export function Header({ user, children }: HeaderProps) {
  const [showMobileMenu, setShowMobileMenu] = useState(false)
  const [mounted, setMounted] = useState(false)
  const { currentTenant } = useTenant();

  useEffect(() => {
    console.log('Header component mounted with user:', user)
    setMounted(true)
  }, [user])

  // Determine which navigation items to show based on user roles
  const getNavigationItems = () => {
    if (!user) {
      console.log('Header: No user found')
      return []
    }
    
    const roles = user.roles || []
    console.log('Header: User roles:', roles)
    
    // Check for affiliate role first (or email)
    if (hasRole(roles, 'AFFILIATE') || user.email.includes('affiliate')) {
      console.log('Header: Using affiliate navigation')
      return navigationItems.affiliate
    }
    
    // Then check for client (formerly event manager)
    if (hasRole(roles, 'CLIENT') || user.email.includes('event')) {
      console.log('Header: Using event manager navigation')
      return navigationItems.eventManager
    }

    // Default to customer navigation
    console.log('Header: Using customer navigation')
    return navigationItems.customer
  }

  const items = getNavigationItems()
  console.log('Header: Navigation items:', items)

  // Determine if the user is a Super Admin for MobileNav props
  const isSuperAdmin = user?.roles?.includes('SUPER_ADMIN' as UserRole) ?? false;

  if (!mounted) {
    console.log('Header: Not mounted yet')
    return null
  }

  if (!user) {
    console.log('Header: No user provided')
    return (
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-14 items-center">
          <div className="mr-4 hidden md:flex">
            <div className="flex items-center space-x-2">
              {currentTenant?.branding?.logo_url ? (
                <img src={currentTenant.branding.logo_url} alt={`${currentTenant.name || 'Transflow'} Logo`} className="h-8 w-auto" />
              ) : (
                <>
                  <Globe className="h-6 w-6 text-primary" />
                  <span className="font-bold">{currentTenant?.name || 'transflow'}</span>
                </>
              )}
            </div>
          </div>
          <div className="flex flex-1 items-center justify-end">
            <Link href="/login">
              <Button variant="default" size="sm">
                Login
              </Button>
            </Link>
          </div>
        </div>
      </header>
    )
  }

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <div className="mr-4 hidden md:flex">
          <Link
            href={
              hasRole(user.roles, 'AFFILIATE')
                ? '/affiliate/dashboard'
                : hasRole(user.roles, 'CLIENT')
                  ? '/event-manager/dashboard'
                  : '/customer/dashboard'
            }
            className="mr-6 flex items-center space-x-2"
          >
            {currentTenant?.branding?.logo_url ? (
              <img src={currentTenant.branding.logo_url} alt={`${currentTenant.name || 'Transflow'} Logo`} className="h-8 w-auto" />
            ) : (
              <>
                <Globe className="h-6 w-6 text-primary" />
                <span className="hidden font-bold sm:inline-block">
                  {currentTenant?.name || 'transflow'}
                </span>
              </>
            )}
          </Link>
          <MainNav items={items} />
        </div>
        <MobileNav 
          superAdminItems={isSuperAdmin ? [] : []} // Or actual superAdminItems if defined and relevant here, otherwise empty array
          tenantDataItems={items} 
        />
        <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
          <div className="w-full flex-1 md:w-auto md:flex-none">
            {/* Add search or other controls here if needed */}
          </div>
          <div className="flex items-center gap-2">
            {children}
            {hasRole(user.roles, 'PASSENGER') && (
              <Link href="/customer/trips/gods-view">
                <Button variant="outline" size="sm" className="gap-2">
                  <MapIcon className="h-4 w-4" />
                  Track Rides
                </Button>
              </Link>
            )}
            <UserNav />
          </div>
        </div>
      </div>
    </header>
  )
} 