'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { getSupabaseClient } from '@/lib/supabase'
import { SupabaseClient } from '@supabase/supabase-js'
import { forceRefreshAuthState } from '@/lib/auth-utils'
import { Button } from '@/app/components/ui/button'
import { RefreshCw } from 'lucide-react'
import { hasRole } from '@/app/lib/auth'
import type { UserRole } from '@/src/types/roles';
import { toUserRoles } from '@/src/types/roles';

interface AuthCheckProps {
  redirectTo?: string
  requiredRoles?: string[]
  children: React.ReactNode
}

/**
 * AuthCheck component verifies the user is authenticated and has required roles
 * If not, it provides options to fix auth issues or redirects to login
 */
export function AuthCheck({ 
  redirectTo = '/login',
  requiredRoles = [],
  children 
}: AuthCheckProps) {
  const [isChecking, setIsChecking] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [hasRequiredRoles, setHasRequiredRoles] = useState(true)
  const [authIssueDetected, setAuthIssueDetected] = useState(false)
  const router = useRouter()
  const [supabase, setSupabase] = useState<SupabaseClient | null>(null)

  useEffect(() => {
    const client = getSupabaseClient()
    setSupabase(client)
  }, [])

  useEffect(() => {
    if (!supabase) {
      return; // Do nothing until supabase is ready
    }

    const checkAuth = async () => {
      try {
        console.log('AuthCheck: Verifying authentication status')
        
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) {
          console.error('AuthCheck: Error checking session:', error)
          setAuthIssueDetected(true)
          setIsAuthenticated(false)
        } else if (!session) {
          console.log('AuthCheck: No session found')
          const hasLocalToken = localStorage.getItem('sb-access-token') || 
                                localStorage.getItem('sb-refresh-token')
          if (hasLocalToken) {
            console.log('AuthCheck: Found tokens in localStorage but no session, auth issue detected')
            setAuthIssueDetected(true)
          } 
          setIsAuthenticated(false)
        } else {
          console.log('AuthCheck: Session found')
          setIsAuthenticated(true)
          
          if (requiredRoles.length > 0) {
            const { data: profile, error: profileError } = await supabase
              .from('profiles')
              .select('roles')
              .eq('id', session.user.id)
              .single()
            
            if (profileError) {
              console.error('AuthCheck: Error fetching user profile:', profileError)
              setAuthIssueDetected(true)
              setHasRequiredRoles(false)
            } else {
              const userRoles = toUserRoles(profile?.roles || [])
              const requiredUserRoles = toUserRoles(requiredRoles)
              const hasRoles = hasRole(userRoles, requiredUserRoles)
              
              console.log(`AuthCheck: User roles: ${userRoles.join(', ')}, Required: ${requiredUserRoles.join(', ')}, Has required: ${hasRoles}`)
              setHasRequiredRoles(hasRoles)
            }
          }
        }
      } catch (error) {
        console.error('AuthCheck: Error during auth check:', error)
        setAuthIssueDetected(true)
        setIsAuthenticated(false)
      } finally {
        setIsChecking(false)
      }
    }
    
    checkAuth()
  }, [redirectTo, router, requiredRoles, supabase])

  useEffect(() => {
    if (!isChecking && !authIssueDetected) {
      if (!isAuthenticated) {
        console.log('AuthCheck (effect): Not authenticated, redirecting to', redirectTo)
        router.push(redirectTo)
      } else if (!hasRequiredRoles) {
        console.log('AuthCheck (effect): Lacks required roles, redirecting to /unauthorized')
        router.push('/unauthorized')
      }
    }
  }, [isChecking, isAuthenticated, hasRequiredRoles, authIssueDetected, redirectTo, router])
  
  const handleFixAuth = async () => {
    if (!supabase) {
      console.error('AuthCheck: Supabase client not available for handleFixAuth')
      router.push(redirectTo)
      return
    }
    setIsChecking(true)
    
    try {
      console.log('AuthCheck: Attempting to fix authentication')
      await forceRefreshAuthState()
      
      const { data: { session } } = await supabase.auth.getSession()
      
      if (session) {
        console.log('AuthCheck: Auth fixed, reloading page')
        window.location.reload()
      } else {
        console.log('AuthCheck: Auth could not be fixed, redirecting to login')
        router.push(redirectTo)
      }
    } catch (error) {
      console.error('AuthCheck: Error fixing auth:', error)
      router.push(redirectTo)
    }
  }
  
  if (isChecking) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin h-5 w-5 border-2 border-primary border-t-transparent rounded-full mr-2"></div>
        <p>Verifying authentication...</p>
      </div>
    )
  }
  
  if (authIssueDetected) {
    return (
      <div className="p-4 bg-yellow-100 rounded-md text-center">
        <p className="mb-3 text-yellow-800">
          Authentication issue detected. Your session may need to be refreshed.
        </p>
        <div className="flex justify-center space-x-3">
          <Button onClick={handleFixAuth} variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Fix Authentication
          </Button>
          <Button onClick={() => router.push(redirectTo)} variant="ghost" size="sm">
            Go to Login
          </Button>
        </div>
      </div>
    )
  }
  
  if (!isChecking && !authIssueDetected && isAuthenticated && hasRequiredRoles) {
    return <>{children}</>
  }

  return null; 
} 