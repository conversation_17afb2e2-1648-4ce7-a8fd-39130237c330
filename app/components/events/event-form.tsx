'use client'

import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/app/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/app/components/ui/form"
import { Input } from "@/app/components/ui/input"
import { Textarea } from "@/app/components/ui/textarea"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import { DatePicker } from "@/app/components/ui/date-picker"
import { useState } from "react"
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/app/components/ui/alert-dialog"
import { hasRole } from '@/app/lib/auth'

const formSchema = z.object({
  name: z.string().min(2, {
    message: "Title must be at least 2 characters.",
  }),
  description: z.string().min(10, {
    message: "Description must be at least 10 characters.",
  }),
  startDate: z.date({
    required_error: "Start date is required.",
  }),
  endDate: z.date({
    required_error: "End date is required.",
  }),
  eventType: z.enum(["conference", "corporate", "wedding", "other"], {
    required_error: "Event type is required.",
  }),
  expectedAttendees: z.string().min(1, {
    message: "Expected number of attendees is required.",
  }),
  location: z.string().min(1, {
    message: "Event location is required.",
  }),
  transportationNeeds: z.array(z.enum([
    "airport_transfers",
    "venue_transfers",
    "city_tours",
    "vip_services",
    "group_shuttles"
  ])).min(1, {
    message: "Select at least one transportation need.",
  }),
  specialRequirements: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

interface EventFormProps {
  defaultValues?: Partial<FormValues> & { id?: string }
  userRoles?: string[]
}

export function EventForm({ defaultValues, userRoles = [] }: EventFormProps) {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showRoleDialog, setShowRoleDialog] = useState(false)
  const [formValues, setFormValues] = useState<FormValues | null>(null)
  const isEventManager = hasRole(userRoles, 'CLIENT')

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      eventType: "conference",
      expectedAttendees: "",
      location: "",
      transportationNeeds: [],
      specialRequirements: "",
      ...defaultValues,
    },
  })

  async function submitEventData(values: FormValues) {
    setIsSubmitting(true)
    try {
      // Convert form values to API format
      const apiData = {
        name: values.name,
        description: values.description,
        start_date: values.startDate.toISOString(),
        end_date: values.endDate.toISOString(),
        location: values.location,
        total_passengers: parseInt(values.expectedAttendees, 10),
        status: 'draft',
        event_type: values.eventType,
        transportation_needs: values.transportationNeeds,
        special_requirements: values.specialRequirements,
      }

      // Submit to API
      const response = await fetch('/api/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create event')
      }

      const eventData = await response.json()
      
      toast.success(defaultValues?.id ? "Event updated" : "Event created")
      
      // If this was a new event and the user wasn't previously an event manager,
      // show a toast about the role change
      if (!defaultValues?.id && !isEventManager) {
        toast.info("Your account has been upgraded to Event Manager")
      }
      
      // Redirect to the event page
      router.push(`/customer/events/${eventData.id}`)
    } catch (error: any) {
      toast.error(error.message || "Something went wrong")
    } finally {
      setIsSubmitting(false)
    }
  }

  function onSubmit(values: FormValues) {
    // If user is already an event manager, or this is an edit, submit directly
    if (isEventManager || defaultValues?.id) {
      submitEventData(values)
    } else {
      // Otherwise, show the role conversion dialog
      setFormValues(values)
      setShowRoleDialog(true)
    }
  }

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Event Title</FormLabel>
                <FormControl>
                  <Input placeholder="Enter event title" {...field} />
                </FormControl>
                <FormDescription>
                  The name or title of your event.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid gap-4 md:grid-cols-2">
            <FormField
              control={form.control}
              name="startDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Start Date</FormLabel>
                  <FormControl>
                    <DatePicker
                      date={field.value}
                      onSelect={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="endDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>End Date</FormLabel>
                  <FormControl>
                    <DatePicker
                      date={field.value}
                      onSelect={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <FormField
              control={form.control}
              name="eventType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Event Type</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select event type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="conference">Conference</SelectItem>
                      <SelectItem value="corporate">Corporate Event</SelectItem>
                      <SelectItem value="wedding">Wedding</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="expectedAttendees"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Expected Attendees</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="Enter number of attendees" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="location"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Event Location</FormLabel>
                <FormControl>
                  <Input placeholder="Enter event location" {...field} />
                </FormControl>
                <FormDescription>
                  The main venue or location of your event.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter event description and details"
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Detailed description of your event.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="transportationNeeds"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Transportation Needs</FormLabel>
                <FormControl>
                  {/* TODO: Implement multi-select component */}
                  <Input placeholder="Select transportation needs" {...field} />
                </FormControl>
                <FormDescription>
                  Select all transportation services needed for this event.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="specialRequirements"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Special Requirements</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter any special requirements or notes"
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Any additional requirements or special considerations.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end gap-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push("/customer/events")}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Submitting..." : defaultValues?.id ? "Update Event" : "Create Event"}
            </Button>
          </div>
        </form>
      </Form>

      {/* Role Conversion Dialog */}
      <AlertDialog open={showRoleDialog} onOpenChange={setShowRoleDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Become an Event Manager</AlertDialogTitle>
            <AlertDialogDescription>
              Creating an event will upgrade your account to Event Manager status. This will give you access to additional features for managing multi-trip events.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={() => formValues && submitEventData(formValues)}>
              Continue
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
