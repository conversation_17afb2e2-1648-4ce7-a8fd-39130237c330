"use client"

import { ReactNode } from "react"
import { useRouter } from "next/navigation"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { useAuth } from "@/lib/auth/context"
import { Head<PERSON> } from "./layout/header"
import { useEffect } from "react"
import { UserRole } from "@/lib/auth/types"
import { hasRole } from '@/app/lib/auth'

export default function PortalLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const { user } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!user) {
      router.push('/auth/login')
    }
  }, [user, router])

  if (!user) {
    return null
  }

  const isAdmin = hasRole(user.roles, 'SUPER_ADMIN')
  const isEventManager = hasRole(user.roles, 'CLIENT')

  const headerUser = user ? {
    name: user.email || '',
    email: user.email || '',
    roles: user.roles,
    image: user.user_metadata?.avatar_url
  } : undefined

  return (
    <div className="flex min-h-screen flex-col">
      <Header user={headerUser} />
      <div className="container flex-1 items-start md:grid md:grid-cols-[220px_1fr] md:gap-6 md:pt-6 lg:grid-cols-[240px_1fr] lg:gap-10">
        {children}
      </div>
    </div>
  )
}