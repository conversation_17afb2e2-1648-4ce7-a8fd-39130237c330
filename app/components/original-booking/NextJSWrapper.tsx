"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "@/app/components/ui/use-toast";
import { MantineProvider } from "@mantine/core";

// Import the exact original booking form
import { BookingForm } from "./components/BookingForm";

// Import original styles exactly as they are
import "./styles/standalone-index.css";

interface NextJSWrapperProps {
  formType?: "point-to-point" | "hourly" | "airport" | "multi-day";
  onSubmit?: (data: any) => void;
  className?: string;
}

export function OriginalBookingForm({
  formType = "point-to-point",
  onSubmit,
  className = "",
}: NextJSWrapperProps) {
  const router = useRouter();

  useEffect(() => {
    // Set up the exact same global config as the original
    if (typeof window !== "undefined") {
      window.limoBookingConfig = {
        isWordPress: false,
        isNextJS: true,
        apiEndpoint: "/api/quotes",
        environment: "production",
      };
    }
  }, []);

  // Custom submit handler that integrates with our Next.js API
  const handleBookingSubmit = async (bookingData: any) => {
    try {
      console.log("NextJSWrapper - Received booking data:", bookingData);

      // Handle both old format and new sophisticated affiliate selection format
      const quoteData = {
        pickup_location:
          bookingData.pickup_location || bookingData.pickupLocation,
        dropoff_location:
          bookingData.dropoff_location || bookingData.dropoffLocation,
        pickup_date: bookingData.pickup_date || bookingData.pickupDate,
        pickup_time: bookingData.pickup_time || bookingData.pickupTime,
        service_type:
          bookingData.service_type || bookingData.serviceType || formType,
        passenger_count:
          bookingData.passenger_count ||
          (bookingData.adults || 1) + (bookingData.children || 0),
        adults: bookingData.adults || 1,
        children: bookingData.children || 0,
        special_requests:
          bookingData.special_requests || bookingData.specialRequests,
        vehicle_type:
          bookingData.vehicle_type || bookingData.vehicleType || "sedan",
        city:
          bookingData.city ||
          bookingData.pickupLocation?.split(",")[1]?.trim() ||
          "Unknown",

        // Coordinates for proper city extraction
        pickup_latitude:
          bookingData.pickup_latitude ||
          bookingData.pickupCoordinates?.[1] ||
          bookingData.pickupLocation?.coordinates?.[1] ||
          null,
        pickup_longitude:
          bookingData.pickup_longitude ||
          bookingData.pickupCoordinates?.[0] ||
          bookingData.pickupLocation?.coordinates?.[0] ||
          null,
        dropoff_latitude:
          bookingData.dropoff_latitude ||
          bookingData.dropoffCoordinates?.[1] ||
          bookingData.dropoffLocation?.coordinates?.[1] ||
          null,
        dropoff_longitude:
          bookingData.dropoff_longitude ||
          bookingData.dropoffCoordinates?.[0] ||
          bookingData.dropoffLocation?.coordinates?.[0] ||
          null,

        // Sophisticated affiliate selection data
        selectedAffiliates: bookingData.selectedAffiliates || [],

        // Service-specific fields
        flight_number: bookingData.flightNumber,
        airline: bookingData.airline,
        duration_hours: bookingData.duration,
        end_date: bookingData.endDate,
      };

      console.log("NextJSWrapper - Submitting quote data:", quoteData);

      // Use the correct endpoint based on whether affiliates were selected
      let response;
      if (
        quoteData.selectedAffiliates &&
        quoteData.selectedAffiliates.length > 0
      ) {
        // Pure SaaS flow with affiliate selection - use the proper endpoint
        console.log("NextJSWrapper - Using affiliate selection endpoint");
        response = await fetch("/api/event-manager/quotes/submit", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            quoteRequest: quoteData,
            selectedAffiliates: quoteData.selectedAffiliates,
          }),
        });
      } else {
        // Basic quote creation without affiliate selection
        console.log("NextJSWrapper - Using basic quote endpoint");
        response = await fetch("/api/quotes", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(quoteData),
        });
      }

      console.log("NextJSWrapper - Response status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("NextJSWrapper - API Error:", errorText);
        throw new Error(
          `Failed to submit quote request: ${response.status} ${errorText}`
        );
      }

      const result = await response.json();
      console.log("NextJSWrapper - Success result:", result);

      // Handle different response formats from different endpoints
      const quoteReference =
        result.quoteReference ||
        result.data?.quote?.reference_number ||
        "Unknown";
      const affiliateCount = result.affiliateCount || 0;

      toast({
        title: "Quote Request Submitted!",
        description:
          affiliateCount > 0
            ? `Quote ${quoteReference} submitted to ${affiliateCount} affiliate${affiliateCount > 1 ? "s" : ""} successfully!`
            : `Quote ${quoteReference} created successfully.`,
      });

      // Call the callback if provided
      if (onSubmit) {
        onSubmit(result);
      } else {
        // Default behavior - redirect to quotes page
        router.push("/event-manager/quotes");
      }

      return { success: true, data: result };
    } catch (error) {
      console.error("Quote submission error:", error);
      toast({
        title: "Error",
        description: "Failed to submit quote request. Please try again.",
        variant: "destructive",
      });
      throw error;
    }
  };

  return (
    <MantineProvider>
      <div
        className={`original-booking-form original-booking-wrapper ${className}`}
      >
        <BookingForm
          formType={formType}
          apiKeys={{
            here: process.env.NEXT_PUBLIC_HERE_API_KEY || "",
            mapbox: process.env.NEXT_PUBLIC_MAPBOX_API_KEY || "",
            resend: process.env.RESEND_API_KEY || "",
            salesmate: process.env.SALESMATE_API_KEY || "",
          }}
          vehicleImages={{
            sedan: "/images/vehicles/sedan.jpg",
            suv: "/images/vehicles/suv.jpg",
            luxury: "/images/vehicles/luxury.jpg",
            van: "/images/vehicles/van.jpg",
            bus: "/images/vehicles/bus.jpg",
          }}
          ajaxUrl="/api/quotes"
          nonce="nextjs-csrf-token"
          onBookingSubmit={handleBookingSubmit}
        />
      </div>
    </MantineProvider>
  );
}

// Declare global types exactly as the original
declare global {
  interface Window {
    limoBookingConfig?: {
      isWordPress: boolean;
      isNextJS: boolean;
      apiEndpoint: string;
      environment: string;
    };
    closeTripSummary?: () => void;
  }
}
