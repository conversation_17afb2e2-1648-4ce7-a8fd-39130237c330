import { bookingDebugger } from '../utils/debugLogger';
import { themeService } from './themeService';
import { getCityFromCoordinates, getApiKey } from './hereApi';

interface ContactInfo {
  fullName: string;
  email: string;
  phone: string;
  specialRequests?: string;
  createAccount: boolean;
}

interface Location {
  address: string;
  coordinates: [number, number];
}

interface BaseBookingData {
  selectedVehicle: string;
  pickupDate: string;
  pickupLocation: Location;
  adults: number;
  children?: number;
  needCarSeats?: boolean;
  infantSeats?: number;
  toddlerSeats?: number;
  boosterSeats?: number;
  luggage?: number;
}

interface PointToPointData extends BaseBookingData {
  dropoffLocation: Location;
  stops?: Array<{ id: string; location: Location }>;
  isRoundTrip?: boolean;
  returnDate?: string;
  returnTime?: string;
}

interface HourlyData extends BaseBookingData {
  hours: number;
}

interface AirportData extends BaseBookingData {
  dropoffLocation: Location;
  departureFlight?: string;
  returnFlight?: string;
  isRoundTrip?: boolean;
  returnDate?: string;
  returnTime?: string;
}

interface MultiDayData extends BaseBookingData {
  days: Array<{
    pickupDate: string;
    pickupTime: string;
    pickupLocation: Location;
    dropoffLocation: Location;
    stops?: Array<{ id: string; location: Location }>;
  }>;
}

interface BookingDetails {
  formType: 'point-to-point' | 'hourly' | 'airport' | 'multi-day';
  pointToPointData?: PointToPointData;
  hourlyData?: HourlyData;
  airportData?: AirportData;
  multiDayData?: MultiDayData;
  contactInfo?: ContactInfo; // Make this optional to match existing code
}

interface BookingData extends ContactInfo {
  bookingDetails: BookingDetails;
  dealId?: string | null;
}

export interface SalesmateResponse {
  success: boolean;
  contactId: string | null;
  dealId: string | null;
  message: string;
}

// Retry configuration
const RETRY_CONFIG = {
  maxRetries: 4,
  interval: 2000,
  shouldContinue: true
};

// Helper function to handle retries
const fetchWithRetry = async (url: string, options: RequestInit, retryCount = 0) => {
  try {
    // Use relative URL if it's a Salesmate API call or HERE API call
    const isSalesmateAPI = url.includes('salesmate.io');
    const isHereAPI = url.includes('hereapi.com');
    
    let finalUrl = url;
    if (isSalesmateAPI) {
      finalUrl = `/api/proxy/${url.split('/apis/')[1]}`;
    } else if (isHereAPI) {
      finalUrl = `/api/proxy/here${url.split('hereapi.com')[1]}`;
    }
    
    console.log('Making request to:', finalUrl);
    console.log('Original URL:', url);
    console.log('Request options:', options);

    const response = await fetch(finalUrl, options);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Request failed:', error);
    
    if (retryCount < RETRY_CONFIG.maxRetries && RETRY_CONFIG.shouldContinue) {
      console.log(`Retrying... Attempt ${retryCount + 1} of ${RETRY_CONFIG.maxRetries}`);
      await new Promise(resolve => setTimeout(resolve, RETRY_CONFIG.interval));
      return fetchWithRetry(url, options, retryCount + 1);
    }
    
    throw error;
  }
};

// Helper function for making Salesmate API calls
const salesmateFetch = async ({
  endpoint,
  method = 'GET',
  body = null,
  queryParams = null
}: {
  endpoint: string;
  method?: string;
  body?: any;
  queryParams?: Record<string, string> | null;
}) => {
  // DIAGNOSTIC LOG - This should appear in the console if our updated code is running
  console.log('🚨🚨🚨 UPDATED CODE RUNNING - VERSION 1.2 🚨🚨🚨');
  console.log('Current URL path:', window.location.pathname);
  console.log('Current hostname:', window.location.hostname);
  
  // Check if we're in development mode
  const isDev = process.env.NODE_ENV === 'development';
  
  // IMPORTANT FIX: Properly detect standalone mode from environment variable
  // The string 'true' needs to be compared as a string, not converted to boolean
  const isStandalone = process.env.VITE_STANDALONE === 'true';
  
  console.log('Environment:', { 
    NODE_ENV: process.env.NODE_ENV,
    VITE_STANDALONE: process.env.VITE_STANDALONE,
    isDev,
    isStandalone
  });
  
  // Construct headers exactly as they work in the successful request
  const accessToken = process.env.NEXT_PUBLIC_SALESMATE_API_KEY || '';
  
  const headers = {
    'Content-Type': 'application/json',
    'x-linkname': 'wwlimo.salesmate.io',
    'accessToken': accessToken
  };

  // Build query string if params exist
  const queryString = queryParams 
    ? `?${new URLSearchParams(queryParams).toString()}` 
    : '';

  // Get current hostname
  const currentHostname = typeof window !== 'undefined' ? window.location.hostname : '';
  
  // Only force WordPress mode for specific domains, NOT in standalone mode
  const FORCE_WORDPRESS_MODE = 
    (currentHostname.includes('jetsetvilla.com') || 
     currentHostname.includes('wwmobilitysolutions.com')) && 
    !isStandalone;  // Never force WordPress mode in standalone
  
  // Direct URL construction to ensure it always uses the correct endpoint
  let url;
  
  if (FORCE_WORDPRESS_MODE) {
    // HARD-CODED path for known WordPress sites - bypass all other detection
    url = `/wp-json/limo-booking/v1/proxy/${endpoint}${queryString}`;
    console.log('🚨 EMERGENCY OVERRIDE: Forcing WordPress REST API path');
    console.log('Using forced WordPress endpoint:', url);
  } else if (isStandalone) {
    // STANDALONE MODE: Always use the proxy endpoint format for standalone
    const fixedEndpoint = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;
    url = `/api/proxy/${fixedEndpoint}${queryString}`;
    console.log('🚨 STANDALONE MODE DETECTED: Using standalone proxy endpoint:', url);
  } else {
    // Only proceed with detection logic if not in forced mode or standalone mode
    // More robust WordPress environment detection function
    const detectWordPress = (): { isWordPress: boolean; detectionMethod: string; apiBaseUrl?: string } => {
      // If we're explicitly in standalone mode, don't detect WordPress
      if (isStandalone) {
        return { isWordPress: false, detectionMethod: 'standalone-mode' };
      }
      
      if (typeof window === 'undefined') {
        return { isWordPress: false, detectionMethod: 'no-window' };
      }
      
      // METHOD 1: Check for limoBookingConfig with explicit isWordPress flag
      if (window.limoBookingConfig?.isWordPress === true) {
        return { 
          isWordPress: true, 
          detectionMethod: 'config-flag',
          apiBaseUrl: window.limoBookingConfig.apiBaseUrl || ''
        };
      }
      
      // METHOD 2: Check for WordPress REST API link in document head
      if (document.querySelector('link[rel="https://api.w.org/"]')) {
        const linkElement = document.querySelector('link[rel="https://api.w.org/"]') as HTMLLinkElement;
        const wpRestUrl = linkElement?.href || '';
        
        // Extract the base REST URL (normally ends with wp-json/)
        const baseRestUrl = wpRestUrl.replace(/\/wp-json\/?$/, '');
        
        return { 
          isWordPress: true, 
          detectionMethod: 'rest-api-link',
          apiBaseUrl: `${baseRestUrl}/wp-json/limo-booking/v1/proxy/`
        };
      }
      
      // METHOD 3: Check for WordPress admin bar
      if (document.getElementById('wpadminbar')) {
        return { 
          isWordPress: true, 
          detectionMethod: 'admin-bar',
          apiBaseUrl: '/wp-json/limo-booking/v1/proxy/'
        };
      }
      
      // METHOD 4: Check for common WordPress body classes
      const bodyClasses = document.body.className;
      if (bodyClasses.includes('wordpress') || bodyClasses.includes('wp-')) {
        return { 
          isWordPress: true, 
          detectionMethod: 'body-classes',
          apiBaseUrl: '/wp-json/limo-booking/v1/proxy/'
        };
      }
      
      // METHOD 5: Check for WordPress globals
      if (typeof window['wp'] !== 'undefined' || typeof window['wpApiSettings'] !== 'undefined') {
        return { 
          isWordPress: true, 
          detectionMethod: 'wp-globals',
          apiBaseUrl: '/wp-json/limo-booking/v1/proxy/'
        };
      }
      
      // METHOD 6: Domain-based detection (fallback to previously hardcoded approach)
      const currentDomain = window.location.hostname;
      const knownWpDomains = ['jetsetvilla.com', 'wwmobilitysolutions.com'];
      
      if (knownWpDomains.some(domain => currentDomain.includes(domain))) {
        return { 
          isWordPress: true, 
          detectionMethod: 'known-domains',
          apiBaseUrl: '/wp-json/limo-booking/v1/proxy/'
        };
      }
      
      // If we get here, we couldn't definitively detect WordPress
      return { isWordPress: false, detectionMethod: 'no-detection' };
    };

    // Perform WordPress detection
    const wpDetection = detectWordPress();
    
    // Log the detection results
    console.log('WordPress Detection Results:', {
      isWordPress: wpDetection.isWordPress,
      method: wpDetection.detectionMethod,
      apiBaseUrl: wpDetection.apiBaseUrl || 'none',
      domain: typeof window !== 'undefined' ? window.location.hostname : 'unknown',
      timestamp: new Date().toISOString()
    });

    if (wpDetection.isWordPress) {
      // WordPress environment detected - use REST API endpoint
      const wpApiBase = wpDetection.apiBaseUrl || '/wp-json/limo-booking/v1/proxy/';
      url = `${wpApiBase}${endpoint}${queryString}`;
      console.log(`WordPress detected [${wpDetection.detectionMethod}], using REST API endpoint:`, url);
    } else {
      // Standalone mode - use the correct proxy endpoint format
      // Fix the endpoint format for standalone mode
      const fixedEndpoint = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;
      url = `/api/proxy/${fixedEndpoint}${queryString}`;
      console.log('Standalone environment detected, using proxy endpoint:', url);
    }
  }

  try {
    console.log('Making Salesmate API request:', {
      url,
      method,
      headers: { ...headers, accessToken: '[REDACTED]' },
      body: body ? JSON.stringify(body) : undefined,
      forcedMode: FORCE_WORDPRESS_MODE,
      domain: currentHostname,
      endpoint,
      isStandalone
    });

    // Add detailed logging
    if (body) {
      console.log('Request body (parsed):', JSON.parse(JSON.stringify(body)));
      console.log('Request body keys:', Object.keys(body));
    }

    // DIAGNOSTIC: Log memory-efficient version of the request
    const requestLog = {
      url,
      method,
      headers: { ...headers, accessToken: '[REDACTED]' },
      bodyKeys: body ? Object.keys(body) : [],
      bodyFirstValues: body ? Object.entries(body).map(([k, v]) => [k, typeof v === 'object' ? '[object]' : String(v).substring(0, 50)]) : [],
      timestamp: new Date().toISOString()
    };
    console.log('[DIAGNOSTIC] Making Salesmate API request:', requestLog);

    try {
      console.log('[DIAGNOSTIC] Fetch started at:', new Date().toISOString());
    const response = await fetch(url, {
      method,
      headers,
      ...(body && { body: JSON.stringify(body) })
    });
      console.log('[DIAGNOSTIC] Fetch completed at:', new Date().toISOString());
      console.log('[DIAGNOSTIC] Response status:', response.status);
      console.log('[DIAGNOSTIC] Response headers:', JSON.stringify(Object.fromEntries([...response.headers.entries()])));
    
    if (!response.ok) {
      // Attempt to parse error response
      let errorData = null;
      try {
          const errorText = await response.text();
          console.log('[DIAGNOSTIC] Error response text:', errorText);
          
          try {
            errorData = JSON.parse(errorText);
            console.log('[DIAGNOSTIC] Parsed error data:', errorData);
      } catch (parseError) {
            console.error('[DIAGNOSTIC] Error parsing error response JSON:', parseError);
          }
        } catch (textError) {
          console.error('[DIAGNOSTIC] Error getting response text:', textError);
      }
      
      console.error('Salesmate API Error Response:', {
        status: response.status,
        statusText: response.statusText,
        errorData,
        rawError: errorData ? JSON.stringify(errorData, null, 2) : 'No error data returned'
      });
      
      let errorMessage = `HTTP error! status: ${response.status}`;
        
        // Special handling for missing API key error
        if (errorData?.code === 'missing_api_key') {
          errorMessage = `Salesmate API Key is missing. Please ask your WordPress administrator to configure the Salesmate API Key in the Limo Booking plugin settings.`;
        } else if (errorData?.Error?.message) {
        errorMessage = `Salesmate API Error: ${errorData.Error.message}`;
      } else if (errorData?.message) {
        errorMessage = `Salesmate API Error: ${errorData.message}`;
      }
      
      throw new Error(errorMessage);
    }

    const data = await response.json();
    return { status: response.status, data };
    } catch (error) {
      console.error('Salesmate API Error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`Salesmate API Error: ${error}`);
    }
  } catch (error) {
    console.error('Salesmate API Error:', error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error(`Salesmate API Error: ${error}`);
  }
};

// Helper functions for data formatting
function formatStopWithCoordinates(stop: any): string {
  if (!stop?.location) return '';
  const { address, coordinates } = stop.location;
  return coordinates ? 
    `${address},${coordinates[1]},${coordinates[0]}` : 
    address;
}

// Helper functions for data formatting
function formatVehicleName(vehicleId: string): string {
  const vehicleMap: { [key: string]: string } = {
    'sedan': 'Sedan',
    'luxury-sedan': 'Luxury Sedan',
    'suv': 'SUV',
    'luxury-suv': 'Luxury SUV',
    'sprinter': 'Mercedes Sprinter',
    'stretch': 'Stretch Limousine',
    'hummer': 'Hummer Limousine',
    'party-bus': 'Party Bus',
    'mini-bus': 'Mini Bus',
    'coach-bus': 'Coach Bus'
  };
  return vehicleMap[vehicleId] || vehicleId;
}

function formatServiceType(type: string): string {
  switch (type) {
    case 'point-to-point':
      return 'Point to Point';
    case 'hourly':
      return 'Hourly';
    case 'airport':
      return 'Airport Transfer';
    case 'multi-day':
      return 'Multi-Day';
    default:
      return type;
  }
}

// Extract city from address string
function extractCity(address: string): string {
  try {
    // Try to extract city from address (typically after first comma, before state)
    const cityMatch = address.match(/([^,]+),\s*([^,]+)/);
    if (cityMatch && cityMatch[2]) {
      return cityMatch[2].trim();
    }
    
    // Fallback: just return first part of address
    const parts = address.split(',');
    return parts.length > 1 ? parts[1].trim() : parts[0].trim();
  } catch (e) {
    console.error('[extractCity] Error extracting city:', e);
    return 'Unknown';
  }
}

// Update the calculateRouteDetails function to use HERE API
async function calculateRouteDetails(points: Location[]): Promise<{ distance: string; duration: string }> {
  if (points.length < 2) {
    return { distance: 'N/A', duration: 'N/A' };
  }

  const origin = points[0];
  const destination = points[points.length - 1];
  const viaPoints = points.slice(1, -1);
  
  try {
    // Debugging information about the request
    console.log('Making HERE API request:', {
      origin: origin.address,
      destination: destination.address,
      viaPoints: viaPoints.map(p => p.address)
    });
    
    // Determine if we're in WordPress environment
    const { isWordPress } = detectWordPressEnv();
    
    // Determine if we're in standalone mode
    const isStandalone = typeof window !== 'undefined' && window.location.hostname === 'localhost';
    
    // Special override for some domains
    const hostname = typeof window !== 'undefined' ? window.location.hostname : '';
    const forceWordPress = hostname === 'limo.up.railway.app';
    
    if (forceWordPress) {
      console.log('🚨 EMERGENCY OVERRIDE: Forcing WordPress HERE API path on domain:', hostname);
    }

    if (isStandalone) {
      console.log('🚨🚨🚨 STANDALONE MODE DETECTED - VERSION 1.3 🚨🚨🚨');
    }

    // Construct the URL for the HERE API
    let url;
    if (isWordPress || (forceWordPress && !isStandalone)) {
      // WordPress proxy endpoint
      url = '/wp-json/limo-booking/v1/proxy/v8/routes';
      console.log('Using WordPress HERE endpoint:', url);
    } else if (isStandalone) {
      // Standalone proxy endpoint - FIXED to use the correct path
      url = '/api/proxy/v8/routes';
      console.log('🚨 STANDALONE MODE DETECTED: Using standalone HERE endpoint:', url);
    } else {
      // Direct HERE API endpoint
      const hereApiKey = getApiKey();
      url = `https://router.hereapi.com/v8/routes?apiKey=${hereApiKey}`;
      console.log('Using direct HERE API endpoint');
    }

    // Add query parameters - only if not using proxy endpoints which handle this
    if (!isWordPress && !isStandalone && !forceWordPress) {
      // FIX: HERE API expects coordinates in the format latitude,longitude
      // So we need to swap the order from [longitude, latitude] to [latitude, longitude]
      url += `&origin=${origin.coordinates[1]},${origin.coordinates[0]}&destination=${destination.coordinates[1]},${destination.coordinates[0]}`;
      
      // Add required parameters
      url += `&transportMode=car&routingMode=fast`;

      // Add via points if any
      if (viaPoints.length > 0) {
        viaPoints.forEach(point => {
          url += `&via=${point.coordinates[1]},${point.coordinates[0]}`;
        });
      }
    } else {
      // For proxy endpoints, we need to add the query parameters differently
      const queryParams = new URLSearchParams();
      queryParams.append('transportMode', 'car');
      queryParams.append('routingMode', 'fast');
      // FIX: HERE API expects coordinates in the format latitude,longitude
      queryParams.append('origin', `${origin.coordinates[1]},${origin.coordinates[0]}`);
      queryParams.append('destination', `${destination.coordinates[1]},${destination.coordinates[0]}`);
      
      // Add via points if any
      if (viaPoints.length > 0) {
        viaPoints.forEach(point => {
          queryParams.append('via', `${point.coordinates[1]},${point.coordinates[0]}`);
        });
      }
      
      url += `?${queryParams.toString()}`;
    }
    
    console.log('Final HERE API URL:', url);
    console.log('Coordinates format check - Origin:', origin.coordinates, 'Destination:', destination.coordinates);

    // Make the request
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    // Check if response is OK
    if (!response.ok) {
      console.error('HERE API error:', response.status, response.statusText);
      // Try to get more detailed error information
      try {
      const errorText = await response.text();
        console.error('HERE API error details:', errorText);
      } catch (e) {
        console.error('Could not read error details');
      }
      return { distance: 'N/A', duration: 'N/A' };
    }

    // Get response text first to check if it's valid JSON
    const responseText = await response.text();
    
    // Check if response starts with HTML tags (error page)
    if (responseText.trim().startsWith('<')) {
      console.error('HERE API returned HTML instead of JSON:', responseText.substring(0, 100) + '...');
      return { distance: 'N/A', duration: 'N/A' };
    }
    
    // Try to parse as JSON
    let data;
    try {
      data = JSON.parse(responseText);
    } catch (error) {
      console.error('Failed to parse HERE API response as JSON:', error);
      return { distance: 'N/A', duration: 'N/A' };
    }

    // Extract route information
    const route = data.routes?.[0];
    if (!route) {
      console.error('No route found in HERE API response');
      return { distance: 'N/A', duration: 'N/A' };
    }

    const section = route.sections?.[0];
    if (!section) {
      console.error('No section found in HERE API route');
      return { distance: 'N/A', duration: 'N/A' };
    }

    // Format distance and duration
    const distanceInMeters = section.summary?.length || 0;
    const durationInSeconds = section.summary?.duration || 0;

    const distanceInMiles = (distanceInMeters / 1609.34).toFixed(1);
    const hours = Math.floor(durationInSeconds / 3600);
    const minutes = Math.floor((durationInSeconds % 3600) / 60);

    let formattedDuration = '';
    if (hours > 0) {
      formattedDuration += `${hours} hr `;
    }
    formattedDuration += `${minutes} min`;

    return {
      distance: `${distanceInMiles} mi`,
      duration: formattedDuration
    };
  } catch (error) {
    console.error('Error calculating route with HERE API:', error);
    // Return fallback values instead of throwing
    return { distance: 'N/A', duration: 'N/A' };
  }
}

// Update the createSalesmateLead function
export const createSalesmateLead = async (data: BookingData) => {
    console.log('[createSalesmateLead] Starting with data:', data);

  try {
    // 1. Create contact in Salesmate
    const contactPayload = {
      firstName: data.fullName.split(' ')[0],
      lastName: data.fullName.split(' ').slice(1).join(' ') || '',
      email: data.email,
      phone: data.phone,
      owner: '1' // Default owner ID
    };

    console.log('[createSalesmateLead] Creating contact with payload:', contactPayload);

    // Add diagnostic timestamps
    console.log('[DIAGNOSTIC] Starting Salesmate contact creation at:', new Date().toISOString());
    console.log('[DIAGNOSTIC] Contact payload:', JSON.stringify(contactPayload));
    
    // Create contact
    const contactResponse = await salesmateFetch({
      endpoint: '/contact/v4',
      method: 'POST',
      body: contactPayload
    });

    console.log('[DIAGNOSTIC] Received contact creation response at:', new Date().toISOString());
    console.log('[DIAGNOSTIC] Raw response object:', JSON.stringify(contactResponse));
    
    // Extract contact ID from response
    let contactId: string | null = null;
    
    // Log the type of contactData for debugging
    console.log('[DIAGNOSTIC] Contact data type:', typeof contactResponse.data);
    
    // Check if contactData is an object and has expected properties
    if (typeof contactResponse.data === 'object' && contactResponse.data !== null) {
      console.log('[DIAGNOSTIC] Contact data keys:', Object.keys(contactResponse.data));
      
      // Check for Data property (Salesmate API format)
      if ('Data' in contactResponse.data) {
        console.log('[DIAGNOSTIC] Contact Data property type:', typeof contactResponse.data.Data);
        
        if (typeof contactResponse.data.Data === 'object' && contactResponse.data.Data !== null) {
          console.log('[DIAGNOSTIC] Contact Data keys:', Object.keys(contactResponse.data.Data));
          
          // Check for id in Data
          if ('id' in contactResponse.data.Data) {
            contactId = contactResponse.data.Data.id.toString();
            console.log('[DIAGNOSTIC] Contact ID found in Data.id:', contactId);
          }
        }
      } 
      // Check for direct id property
      else if ('id' in contactResponse.data) {
        contactId = contactResponse.data.id.toString();
        console.log('[DIAGNOSTIC] Contact ID found directly in data.id:', contactId);
      }
      // Try to find id using a recursive function
      else {
        const findId = (obj: any): string | null => {
          if (!obj || typeof obj !== 'object') return null;
          
          if ('id' in obj) return obj.id.toString();
          
          for (const key in obj) {
            if (typeof obj[key] === 'object') {
              const found = findId(obj[key]);
              if (found) return found;
            }
          }
          
          return null;
        };
        
        contactId = findId(contactResponse.data);
        if (contactId) {
          console.log('[DIAGNOSTIC] Contact ID found using recursive search:', contactId);
        }
      }
    }
    
    // If we still don't have a contact ID, check if the response format is unexpected
    if (!contactId) {
      console.error('[createSalesmateLead] Failed to create contact - Invalid response format:', contactResponse.data);
      console.error('[createSalesmateLead] Raw response:', JSON.stringify(contactResponse));
      
      // Check if there's any indication of success in the response
      const hasSuccessIndicator = 
        (contactResponse.status >= 200 && contactResponse.status < 300) || 
        contactResponse.data?.success === true;
      
      if (hasSuccessIndicator) {
        // The request may have actually succeeded but we can't find the ID
        // Create a temporary ID for continued processing, but log the issue
        contactId = "temp_" + Date.now();
        console.warn('[DIAGNOSTIC] Created temporary contact ID due to response format issues:', contactId);
      } else {
        // This is a true error, throw exception
      throw new Error('Failed to create contact: Invalid response format');
      }
    }

    console.log('[createSalesmateLead] Contact created successfully with ID:', contactId);

    // 2. Get booking data based on form type
    const formType = data.bookingDetails.formType;
    let bookingData: any = null;

    switch (formType) {
      case 'point-to-point':
        bookingData = data.bookingDetails.pointToPointData;
        break;
      case 'hourly':
        bookingData = data.bookingDetails.hourlyData;
        break;
      case 'airport':
        bookingData = data.bookingDetails.airportData;
        break;
      case 'multi-day':
        bookingData = data.bookingDetails.multiDayData;
        break;
    }

    console.log('[createSalesmateLead] Processing booking data for type:', formType, bookingData);

    // 3. Calculate route details if applicable
    let routeDetails = { distance: 'N/A', duration: 'N/A' };
    
    try {
      if (formType === 'point-to-point' || formType === 'airport') {
        const points = [bookingData.pickupLocation];
        
        // Add stops if any
        if (bookingData.stops && bookingData.stops.length > 0) {
          bookingData.stops.forEach((stop: any) => {
            if (stop.location) {
              points.push(stop.location);
            }
          });
        }
        
        // Add dropoff location
        if (bookingData.dropoffLocation) {
          points.push(bookingData.dropoffLocation);
        }
        
        if (points.length >= 2) {
          routeDetails = await calculateRouteDetails(points);
        }
      }
    } catch (routeError) {
      // Don't fail the entire lead creation if route calculation fails
      console.error('Error calculating route details:', routeError);
      // Continue with default values
    }
    
    // 4. Create deal in Salesmate
    const pickupDate = new Date(bookingData.pickupDate);
    const formattedDate = `${pickupDate.getFullYear()}-${String(pickupDate.getMonth() + 1).padStart(2, '0')}-${String(pickupDate.getDate()).padStart(2, '0')}`;
    
    // Extract city from address for deal title
    const extractCity = (address: string) => {
      try {
        const parts = address.split(',');
        if (parts.length >= 2) {
          return parts[1].trim();
        }
        return address.substring(0, 15) + '...';
      } catch (e) {
        return 'Unknown';
      }
    };
    
    // Format date for Salesmate
    const formatDateForSalesmate = (dateStr: string) => {
      try {
      const date = new Date(dateStr);
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      } catch (e) {
        return dateStr;
      }
    };
    
    // Prepare deal data
    const pickupCity = bookingData.pickupLocation ? extractCity(bookingData.pickupLocation.address) : 'Unknown';
    const dropoffCity = bookingData.dropoffLocation ? extractCity(bookingData.dropoffLocation.address) : 'Unknown';
    
    const dealTitle = `${formType === 'hourly' ? 'Hourly' : formType === 'multi-day' ? 'Multi-Day' : ''} ${pickupCity} ${formType !== 'hourly' ? 'to ' + dropoffCity : ''} - ${data.fullName}`;
    
    const dealDescription = `
      Customer: ${data.fullName}
      Email: ${data.email}
      Phone: ${data.phone}
      
      Service Type: ${formatServiceType(formType)}
      Vehicle: ${formatVehicleName(bookingData.selectedVehicle)}
      Date: ${formattedDate}
      
      Pickup: ${bookingData.pickupLocation?.address || 'N/A'}
      ${formType !== 'hourly' ? `Dropoff: ${bookingData.dropoffLocation?.address || 'N/A'}` : ''}
      ${formType === 'hourly' ? `Hours: ${bookingData.hours}` : ''}
      
      Passengers: ${bookingData.adults + (bookingData.children || 0)}
      ${bookingData.needCarSeats ? `Car Seats: Yes (Infant: ${bookingData.infantSeats || 0}, Toddler: ${bookingData.toddlerSeats || 0}, Booster: ${bookingData.boosterSeats || 0})` : 'Car Seats: No'}
      
      ${routeDetails.distance !== 'N/A' ? `Estimated Distance: ${routeDetails.distance}` : ''}
      ${routeDetails.duration !== 'N/A' ? `Estimated Duration: ${routeDetails.duration}` : ''}
      
      Special Requests: ${data.specialRequests || 'None'}
    `;
    
    const dealPayload = {
      title: dealTitle,
      description: dealDescription,
      contactId: contactId,
      dealValue: 0, // Will be updated later
      currency: 'USD',
      dealStage: 1, // New/Inquiry stage
      dealPipeline: 1, // Default pipeline
      closingDate: formatDateForSalesmate(bookingData.pickupDate),
      owner: '1', // Default owner
      customFields: [
        {
          fieldName: 'Service Type',
          fieldValue: formatServiceType(formType)
        },
        {
          fieldName: 'Vehicle',
          fieldValue: formatVehicleName(bookingData.selectedVehicle)
        },
        {
          fieldName: 'Pickup Date',
          fieldValue: formatDateForSalesmate(bookingData.pickupDate)
        },
        {
          fieldName: 'Pickup Location',
          fieldValue: bookingData.pickupLocation?.address || 'N/A'
        },
        {
          fieldName: 'Dropoff Location',
          fieldValue: bookingData.dropoffLocation?.address || 'N/A'
        },
        {
          fieldName: 'Number of Passengers',
          fieldValue: String(bookingData.adults + (bookingData.children || 0))
        },
        {
          fieldName: 'Car Seats Needed',
          fieldValue: bookingData.needCarSeats ? 'Yes' : 'No'
        },
        {
          fieldName: 'Infant Seats',
          fieldValue: String(bookingData.infantSeats || 0)
        },
        {
          fieldName: 'Toddler Seats',
          fieldValue: String(bookingData.toddlerSeats || 0)
        },
        {
          fieldName: 'Booster Seats',
          fieldValue: String(bookingData.boosterSeats || 0)
        },
        {
          fieldName: 'Estimated Distance',
          fieldValue: routeDetails.distance
        },
        {
          fieldName: 'Estimated Duration',
          fieldValue: routeDetails.duration
        },
        {
          fieldName: 'Special Requests',
          fieldValue: data.specialRequests || 'None'
        }
      ],
      // Direct field mappings for Salesmate custom fields
      textCustomField14: formatServiceType(formType),
      textCustomField13: formatVehicleName(bookingData.selectedVehicle),
      dateTimeCustomField2: formatDateForSalesmate(bookingData.pickupDate),
      textCustomField8: bookingData.pickupLocation?.address || 'N/A',
      textCustomField9: bookingData.pickupLocation?.address ? extractCity(bookingData.pickupLocation.address) : 'N/A',
      textCustomField24: bookingData.pickupLocation?.coordinates?.[0]?.toString() || '',
      textCustomField25: bookingData.pickupLocation?.coordinates?.[1]?.toString() || '',
      textCustomField11: bookingData.dropoffLocation?.address || 'N/A',
      textCustomField12: bookingData.dropoffLocation?.address ? extractCity(bookingData.dropoffLocation.address) : 'N/A',
      textCustomField26: bookingData.dropoffLocation?.coordinates?.[0]?.toString() || '',
      textCustomField27: bookingData.dropoffLocation?.coordinates?.[1]?.toString() || '',
      intCustomField2: bookingData.adults || 0,
      intCustomField5: bookingData.children || 0,
      intCustomField7: (bookingData.adults || 0) + (bookingData.children || 0),
      intCustomField6: parseInt(routeDetails.distance.split(' ')[0]) || 0,
      textCustomField22: routeDetails.duration,
      textAreaCustomField1: bookingData.needCarSeats ? 
        `Car Seats Required:
         Infant Seats: ${bookingData.infantSeats || 0}
         Toddler Seats: ${bookingData.toddlerSeats || 0}
         Booster Seats: ${bookingData.boosterSeats || 0}` : 
        '',
      checkboxCustomField1: bookingData.isRoundTrip || false
    };
    
    console.log('[createSalesmateLead] Creating deal with payload:', dealPayload);
    
    // Add diagnostic timestamps
    console.log('[DIAGNOSTIC] Starting Salesmate deal creation at:', new Date().toISOString());
    console.log('[DIAGNOSTIC] Deal payload:', JSON.stringify(dealPayload));
    
    // Create deal
    const dealResponse = await salesmateFetch({
      endpoint: '/deal/v4',
        method: 'POST',
        body: dealPayload
      });
  
    console.log('[DIAGNOSTIC] Received deal creation response at:', new Date().toISOString());
    console.log('[DIAGNOSTIC] Raw deal response:', JSON.stringify(dealResponse));
    
    // Extract deal ID from response
    let dealId: string | null = null;
    
    // Similar extraction logic as for contact
    if (typeof dealResponse.data === 'object' && dealResponse.data !== null) {
      // Check for Data property (Salesmate API format)
      if ('Data' in dealResponse.data) {
        if (typeof dealResponse.data.Data === 'object' && dealResponse.data.Data !== null && 'id' in dealResponse.data.Data) {
          dealId = dealResponse.data.Data.id.toString();
        }
      } 
      // Check for direct id property
      else if ('id' in dealResponse.data) {
        dealId = dealResponse.data.id.toString();
      }
      // Try to find id using a recursive function
      else {
        const findId = (obj: any): string | null => {
          if (!obj || typeof obj !== 'object') return null;
          
          if ('id' in obj) return obj.id.toString();
          
          for (const key in obj) {
            if (typeof obj[key] === 'object') {
              const found = findId(obj[key]);
              if (found) return found;
            }
          }
          
          return null;
        };
        
        dealId = findId(dealResponse.data);
      }
    }
    
    if (!dealId) {
      console.error('[createSalesmateLead] Failed to create deal - Invalid response format:', dealResponse.data);
      // Continue anyway, as we've already created the contact
    } else {
      console.log('[createSalesmateLead] Deal created successfully with ID:', dealId);
      
      // 5. Update deal with quote number (using the deal ID)
      try {
        const updatePayload = {
          customFields: [
            {
              fieldName: 'Quote Number',
              fieldValue: dealId
            }
          ]
        };
        
        console.log('[createSalesmateLead] Updating deal with quote number:', dealId);
        
        const updateResponse = await salesmateFetch({
          endpoint: `/deal/v4/${dealId}`,
          method: 'PUT',
          body: updatePayload
        });
        
        console.log('[createSalesmateLead] Deal updated successfully:', updateResponse.data);
      } catch (updateError) {
        console.error('[createSalesmateLead] Error updating deal with quote number:', updateError);
        // Continue anyway, this is not critical
      }
    }
    
    // Return success response
    return {
      success: true,
      contactId,
      dealId,
      message: 'Lead created successfully'
    };
  } catch (error) {
    console.error('[createSalesmateLead] Error creating lead:', error);
    
      // Log the specific error message
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('[createSalesmateLead] Error message:', errorMessage);
    
    // Check for specific error types and provide better user messages
    if (errorMessage.includes('API Key is missing')) {
      throw new Error('Salesmate API Key is missing. Please ask your WordPress administrator to configure the Salesmate API Key in the Limo Booking plugin settings.');
    } else if (errorMessage.includes('401') || errorMessage.includes('Unauthorized')) {
      throw new Error('Unauthorized access to Salesmate API. Please ask your WordPress administrator to verify the API credentials.');
    } else if (errorMessage.includes('network') || errorMessage.includes('timeout') || errorMessage.includes('abort')) {
      throw new Error('Network error while connecting to booking service. Please check your internet connection and try again.');
    } else if (errorMessage.includes('HTML') || errorMessage.includes('not valid JSON')) {
      // This is likely the HERE API error we're seeing
      throw new Error('There was an issue calculating your route details, but your booking information has been saved. Our team will contact you shortly.');
    }
    
    // Log unknown errors for debugging
    console.error('[createSalesmateLead] Unknown error type:', error);
    
    // Rethrow with a general message
    throw new Error('Failed to create booking in Salesmate CRM. Please try again or contact support.');
  }
};

// Add version tracking at the top of the file
const API_VERSION = "2.0.0"; // Increment this when making significant changes
console.log(`[API Service] Loading API service version ${API_VERSION}`);

// Email service integration using proxy server
export const sendConfirmationEmail = async (data: BookingData) => {
  console.log('[Email Service] Version 2.0.0 - Starting email process');
  console.log('[Email Service] Preparing to send confirmation email');
  
  try {
    // Log the booking data structure for debugging
    console.log('[Email Service] Booking data structure:', JSON.stringify({
      formType: data.bookingDetails.formType,
      hasCoordinates: getPickupCoordinates(data) !== null,
      contactInfo: {
        name: data.fullName,
        email: data.email
      }
    }));
    
    // Get the pickup city for the email subject
    console.log('[Email Service] Getting pickup city for email subject');
    const pickupCity = await getPickupCity(data);
    console.log('[Email Service] Pickup city retrieved:', pickupCity);
    
    // Get the pickup date
    const pickupDate = getPickupDate(data);
    console.log('[Email Service] Pickup date:', pickupDate);
    
    // Format the date for better readability in the email subject
    let formattedDate = pickupDate;
    try {
      // Format the date directly without dynamic import to ensure it works
      const date = new Date(pickupDate);
      if (!isNaN(date.getTime())) {
        formattedDate = date.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          year: 'numeric'
        });
        console.log('[Email Service] Date formatted successfully:', formattedDate);
      } else {
        console.log('[Email Service] Invalid date format, using original date');
      }
    } catch (error) {
      console.error('[Email Service] Error formatting date:', error);
    }
    
    // Get the vehicle name
    const vehicleId = getVehicleId(data);
    const vehicleName = formatVehicleName(vehicleId);
    console.log('[Email Service] Vehicle name:', vehicleName);
    
    // Get the customer name
    const customerName = data.fullName;
    console.log('[Email Service] Customer name:', customerName);
    
    // Create the email subject
    // Format: "City Quote Request - Date - Vehicle - Customer Name"
    const emailSubject = `${pickupCity} Quote Request - ${formattedDate} - ${vehicleName} - ${customerName}`;
    console.log('[Email Service] Email subject:', emailSubject);
    
    // Generate the email HTML content
    const emailHtml = generateEmailTemplate(data);
    
    // Get the theme color for the email
    const themeColor = getThemeColor();
    
    // Determine the from email and name
    const fromEmail = '<EMAIL>';
    const fromName = 'WWMS';
    console.log('[Email Service] From Email:', fromEmail);
    console.log('[Email Service] From Name:', fromName);
    console.log('[Email Service] Sending email from:', `${fromName} <${fromEmail}>`);
    
    // Detect if we're in WordPress environment
    const wpEnv = detectWordPressEnv();
    
    // Determine the API endpoint
    let apiEndpoint = '/api/emails';
    
    // Override for WordPress environment
    if (wpEnv.isWordPress && wpEnv.apiBaseUrl) {
      apiEndpoint = wpEnv.apiBaseUrl;
    }
    
    // EMERGENCY OVERRIDE for specific domains
    const currentDomain = window.location.hostname;
    if (currentDomain.includes('jetsetvilla.com')) {
      console.log('[Email Service] 🚨 EMERGENCY OVERRIDE: Forcing WordPress email endpoint on domain:', currentDomain);
      apiEndpoint = '/wp-json/limo-booking/v1/proxy/emails';
      console.log('[Email Service] Using forced WordPress endpoint:', apiEndpoint);
    }
    
    console.log('[Email Service] Final email endpoint:', JSON.stringify({ apiEndpoint }));
    
    // Prepare the email payload
    const emailPayload = {
      to: data.email,
      from: `${fromName} <${fromEmail}>`, // Ensure this is always a string in the format "Name <email>"
      bcc: '<EMAIL>', // Add BCC
      subject: emailSubject,
      html: emailHtml,
      dealId: data.dealId || null,
      themeColor
    };
    
    // Add additional logging to diagnose the issue
    console.log('[Email Service] Email payload before sending:', JSON.stringify(emailPayload));
    console.log('[Email Service] From field type:', typeof emailPayload.from);
    
    // CRITICAL FIX: Ensure the from field is a string
    // This is a defensive measure in case something is transforming our payload
    const stringifiedPayload = JSON.stringify(emailPayload);
    const parsedPayload = JSON.parse(stringifiedPayload);
    
    // Check if the from field has been transformed into an object
    if (typeof parsedPayload.from === 'object' && parsedPayload.from !== null) {
      console.log('[Email Service] 🚨 CRITICAL FIX: From field was transformed into an object. Fixing it...');
      // Fix it by converting it back to a string
      parsedPayload.from = `${fromName} <${fromEmail}>`;
      console.log('[Email Service] Fixed from field:', parsedPayload.from);
    }
    
    // Send the email
    const response = await fetch(apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(parsedPayload) // Use the fixed payload
    });
    
    // Add additional logging after sending
    console.log('[Email Service] Request body sent:', JSON.stringify(parsedPayload));
    
    console.log('[Email Service] Response status:', response.status);
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to send email: ${JSON.stringify(errorData)}`);
    }
    
    console.log('[Email Service] Email sent successfully');
    return { success: true };
  } catch (error) {
    console.error('[Email Service] Error sending confirmation email:', error);
    return { success: false, error };
  }
};

// Helper function to get vehicle ID from booking data
const getVehicleId = (data: BookingData): string => {
  const { bookingDetails } = data;
  const { formType } = bookingDetails;
  
  if (formType === 'point-to-point' && bookingDetails.pointToPointData) {
    return bookingDetails.pointToPointData.selectedVehicle;
  } else if (formType === 'hourly' && bookingDetails.hourlyData) {
    return bookingDetails.hourlyData.selectedVehicle;
  } else if (formType === 'airport' && bookingDetails.airportData) {
    return bookingDetails.airportData.selectedVehicle;
  } else if (formType === 'multi-day' && bookingDetails.multiDayData) {
    return bookingDetails.multiDayData.selectedVehicle;
  }
  
  return '';
};

// Helper function to get vehicle details
const getVehicleDetails = (vehicleId: string | null) => {
  const vehicles = {
    sedan: { 
      name: 'Sedan', 
      capacity: 3,
      description: 'Leather seats, Tinted privacy glass, Premium sound system'
    },
    'luxury-sedan': { 
      name: 'Luxury Sedan', 
      capacity: 3,
      description: 'Plush leather seating, Rear climate control, Ambient lighting'
    },
    suv: { 
      name: 'SUV', 
      capacity: 6,
      description: 'Spacious interior, Leather seats, All-wheel drive, Premium sound system'
    },
    'luxury-suv': { 
      name: 'Luxury SUV', 
      capacity: 6,
      description: 'Leather seating with extra legroom, Advanced climate control, On-board WiFi'
    },
    sprinter: { 
      name: 'Mercedes Sprinter', 
      capacity: 12,
      description: 'Spacious interior, Premium sound system, On-board WiFi, USB charging ports'
    },
    stretch: { 
      name: 'Stretch Limousine', 
      capacity: 10,
      description: 'Leather seating, Mood lighting, Mini bar, Premium surround sound system'
    },
    hummer: { 
      name: 'Hummer Limousine', 
      capacity: 14,
      description: 'Leather seating, LED lighting, Mini bar, State-of-the-art sound system'
    },
    'party-bus': { 
      name: 'Party Bus', 
      capacity: 20,
      description: 'Dance floor, LED lighting, High-end sound system, Bar area, Lounge seating'
    },
    'mini-bus': { 
      name: 'Mini Bus', 
      capacity: 24,
      description: 'Comfortable seating, Ample legroom, Air conditioning, Generous luggage space'
    },
    'coach-bus': { 
      name: 'Coach Bus', 
      capacity: 50,
      description: 'Reclining seats, Climate control, On-board restroom, Entertainment system'
    }
  };

  return vehicleId ? vehicles[vehicleId as keyof typeof vehicles] : null;
};

// Get theme color from CSS variable
const getThemeColor = () => {
  const root = document.documentElement;
  return getComputedStyle(root).getPropertyValue('--primary').trim() || '#765a3d';
};

const styles = {
  container: `max-width: 600px; margin: 0 auto; font-family: Arial, Helvetica, sans-serif; background-color: ${getThemeColor()}; color: white; padding: 24px;`,
  header: `background-color: ${getThemeColor()}; padding: 24px; border-radius: 8px; margin-bottom: 24px; text-align: center;`,
  logoContainer: 'text-align: center; margin-bottom: 16px;',
  logo: 'display: inline-block !important; width: 100px !important; max-width: 100px !important; height: auto !important; margin: 0 auto !important;',
  title: 'font-size: 24px; font-weight: 300; margin: 0; color: white;',
  dateContainer: 'display: flex; gap: 16px; margin-top: 16px;',
  dateBox: `background-color: ${getThemeColor()}; color: white; padding: 16px; border-radius: 8px; flex: 1; text-align: center;`,
  dateText: 'font-size: 16px; text-transform: uppercase; margin: 0; font-weight: normal;',
  timeText: 'font-size: 24px; font-weight: bold; margin: 4px 0 0 0;',
  locationSection: 'margin-top: 16px; position: relative;',
  locationContainer: 'position: relative; padding-left: 32px; margin: 16px 0;',
  locationLine: `position: absolute; left: 15px; top: 30px; bottom: 0; width: 2px; background-color: ${getThemeColor()}; opacity: 0.3;`,
  locationDot: `position: absolute; left: 0; top: 8px; width: 24px; height: 24px; border-radius: 50%; border: 2px solid ${getThemeColor()}; background-color: ${getThemeColor()}; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; font-weight: bold; text-align: center;`,
  locationContent: 'background-color: var(--surface-dark, #1a1a1a); padding: 16px; border-radius: 8px;',
  locationLabel: `color: ${getThemeColor()}; font-size: 14px; margin-bottom: 8px;`,
  locationText: 'color: white; font-size: 14px; line-height: 1.4; margin: 0;',
  vehicleCard: 'background-color: var(--surface-dark, #1a1a1a); padding: 16px; border-radius: 8px; margin-top: 16px;',
  vehicleTitle: 'font-size: 20px; font-weight: 500; color: white; margin: 0 0 8px 0;',
  vehicleDesc: 'color: var(--text-muted, #999); font-size: 14px; margin: 0;',
  detailsSection: 'margin-top: 16px;',
  detailRow: 'background-color: var(--surface-dark, #1a1a1a); padding: 16px; border-radius: 8px; margin-top: 8px;',
  detailLabel: `color: ${getThemeColor()}; font-size: 14px; margin-bottom: 4px;`,
  detailText: 'color: white; font-size: 14px; margin: 0;',
  vehicleImage: 'width: 100%; height: auto; margin-bottom: 16px; border-radius: 8px; object-fit: cover;'
};

const generateEmailTemplate = (data: BookingData): string => {
  const bookingData = data.bookingDetails.pointToPointData;
  if (!bookingData) return '';

  // Get theme colors from theme service instead of hardcoded values
  const theme = themeService.loadTheme();
  
  // Use light theme colors for email compatibility, with fallbacks
  const themeColors = {
    primary: theme?.color?.base || '#765a3d',
    primaryLight: theme?.color?.light || '#8b6d4c',
    primaryDark: theme?.color?.dark || '#5d472f',
    background: '#ffffff', // Always use white background for emails
    surface: '#f5f5f5',    // Light surface for email compatibility
    text: '#333333',       // Dark text for readability
    textSecondary: '#666666'
  };
  
  const styles = {
    container: `max-width: 600px; margin: 0 auto; font-family: Arial, Helvetica, sans-serif; background-color: ${themeColors.background}; color: ${themeColors.text}; padding: 24px;`,
    header: `background-color: ${themeColors.surface}; padding: 24px; border-radius: 8px; margin-bottom: 24px; text-align: center;`,
    logoContainer: 'text-align: center; margin-bottom: 16px;',
    logo: 'display: inline-block !important; width: 100px !important; max-width: 100px !important; height: auto !important; margin: 0 auto !important;',
    title: `font-size: 24px; font-weight: 300; margin: 0; color: ${themeColors.text};`,
    dateTable: 'width: 100%; border-collapse: collapse; margin-top: 16px;',
    dateContainer: 'margin-top: 24px;',
    dateBox: `background-color: ${themeColors.primary}; color: white; padding: 16px; border-radius: 8px; text-align: center;`,
    dateText: 'font-size: 14px; text-transform: uppercase; margin: 0; font-weight: 500; letter-spacing: 0.5px;',
    timeText: 'font-size: 28px; font-weight: bold; margin: 4px 0 0 0;',
    locationSection: 'margin-top: 24px; position: relative; padding-left: 32px;',
    locationContainer: 'position: relative; margin: 24px 0;',
    locationLine: `position: absolute; left: 15px; top: 0; bottom: 0; width: 2px; background-color: ${themeColors.primary}; opacity: 0.3;`,
    locationDot: `position: absolute; left: 0; top: 8px; width: 24px; height: 24px; border-radius: 50%; border: 2px solid ${themeColors.primary}; background-color: ${themeColors.surface}; display: flex; align-items: center; justify-content: center; color: ${themeColors.primary}; font-size: 12px; font-weight: bold; text-align: center;`,
    locationContent: `background-color: ${themeColors.surface}; padding: 16px; border-radius: 8px; margin-left: 16px;`,
    locationLabel: `color: ${themeColors.primary}; font-size: 14px; margin-bottom: 8px; font-weight: 500;`,
    locationText: `color: ${themeColors.text}; font-size: 14px; line-height: 1.4; margin: 0;`,
    vehicleCard: `background-color: ${themeColors.surface}; padding: 24px; border-radius: 8px; margin-top: 24px; text-align: center;`,
    vehicleImage: 'width: 200px !important; max-width: 200px !important; height: auto !important; margin: 0 auto 16px auto; border-radius: 8px; object-fit: cover;',
    vehicleTitle: `font-size: 20px; font-weight: 500; color: ${themeColors.text}; margin: 0 0 8px 0;`,
    vehicleDesc: `color: ${themeColors.text}; opacity: 0.7; font-size: 14px; margin: 0; line-height: 1.6;`,
    detailsSection: 'margin-top: 24px;',
    detailRow: `background-color: ${themeColors.surface}; padding: 16px; border-radius: 8px; margin-bottom: 16px;`,
    detailLabel: `color: ${themeColors.primary}; font-size: 14px; margin-bottom: 8px; font-weight: 500;`,
    detailText: `color: ${themeColors.text}; font-size: 14px; margin: 0;`,
    footer: `text-align: center; margin-top: 32px; padding: 24px; background-color: ${themeColors.surface}; border-radius: 8px;`,
    footerText: `margin: 0; color: ${themeColors.text}; opacity: 0.7; font-size: 14px;`
  };

  const formatDateForEmail = (date: string) => {
    return new Date(date).toLocaleString('en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).toUpperCase();
  };

  // Update paths to use WordPress uploads directory
  const ASSETS_BASE_URL = '/wp-content/uploads/limo-assets';
  const EMAIL_ASSETS_URL = '/wp-content/uploads/limo-assets/email';
  
  // Get the current domain for absolute URLs - use a hardcoded fallback for server-side rendering
  const currentDomain = typeof window !== 'undefined' ? window.location.origin : 'https://jetsetvilla.com';
  const FULL_ASSETS_URL = `${currentDomain}${EMAIL_ASSETS_URL}`;
  const FULL_VEHICLES_URL = `${currentDomain}${ASSETS_BASE_URL}/vehicles`;

  // Extract customer data directly from the top-level data object
  const customerName = data.fullName || 'Not provided';
  const customerEmail = data.email || 'Not provided';
  const customerPhone = data.phone || 'Not provided';
  const specialRequests = data.specialRequests || '';

  // Generate a unique timestamp to prevent caching of the logo image
  const timestamp = new Date().getTime();

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="color-scheme" content="light">
      <meta name="supported-color-schemes" content="light">
      <title>Quote Request</title>
      <!--[if mso]>
      <style type="text/css">
        table {border-collapse: collapse; border-spacing: 0; margin: 0;}
        div, td {padding: 0;}
        div {margin: 0 !important;}
      </style>
      <noscript>
      <xml>
        <o:OfficeDocumentSettings>
          <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
      </xml>
      </noscript>
      <![endif]-->
      <style type="text/css">
        /* CLIENT-SPECIFIC STYLES */
        body, table, td, a { -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }
        table, td { mso-table-lspace: 0pt; mso-table-rspace: 0pt; }
        img { -ms-interpolation-mode: bicubic; }

        /* RESET STYLES */
        img { border: 0; height: auto; line-height: 100%; outline: none; text-decoration: none; }
        table { border-collapse: collapse !important; }
        body { height: 100% !important; margin: 0 !important; padding: 0 !important; width: 100% !important; }

        /* Force smaller logo size */
        img.g-img { 
          width: 100px !important;
          max-width: 100px !important;
          min-width: 100px !important;
          height: auto !important;
        }

        /* iOS BLUE LINKS */
        a[x-apple-data-detectors] {
          color: inherit !important;
          text-decoration: none !important;
          font-size: inherit !important;
          font-family: inherit !important;
          font-weight: inherit !important;
          line-height: inherit !important;
        }
        
        /* GMAIL BLUE LINKS */
        u + .body a {
          color: inherit !important;
          text-decoration: none !important;
          font-size: inherit !important;
          font-family: inherit !important;
          font-weight: inherit !important;
          line-height: inherit !important;
        }
        
        /* SAMSUNG MAIL BLUE LINKS */
        #MessageViewBody a {
          color: inherit !important;
          text-decoration: none !important;
          font-size: inherit !important;
          font-family: inherit !important;
          font-weight: inherit !important;
          line-height: inherit !important;
        }

        /* Gmail-specific image fixes */
        @media screen and (max-width: 600px) {
          .g-img {
            width: 100px !important;
            height: auto !important;
          }
          .v-img {
            width: 200px !important;
            height: auto !important;
          }
        }
        
        /* Gmail image hacks */
        u + .body .g-img {
          width: 100px !important;
          max-width: 100px !important;
          min-width: 100px !important;
        }
        
        u + .body .v-img {
          width: 200px !important;
          max-width: 200px !important;
          min-width: 200px !important;
        }
      </style>
      <!--[if gte mso 9]>
      <style type="text/css">
        /* MSO-specific styles */
        .mso-logo {
          width: 100px !important;
          height: auto !important;
        }
        .mso-vehicle {
          width: 200px !important;
          height: auto !important;
        }
      </style>
      <![endif]-->
    </head>
    <body class="body" style="margin: 0; padding: 0; background-color: #f7f7f7; font-family: Arial, Helvetica, sans-serif;">
      <div style="${styles.container}">
        <!-- Header -->
        <div style="${styles.header}">
          <!--[if mso]>
          <table width="100%" cellpadding="0" cellspacing="0" border="0">
            <tr>
              <td style="padding: 24px; background-color: ${themeColors.surface}; border-radius: 8px; text-align: center;">
                <table width="100%" cellpadding="0" cellspacing="0" border="0">
                  <tr>
                    <td align="center" style="padding-bottom: 16px;">
          <![endif]-->
          <div style="${styles.logoContainer}">
            <!-- Add timestamp to prevent caching -->
            <img src="${FULL_ASSETS_URL}/logo.jpg?t=${timestamp}" alt="Company Logo" style="${styles.logo}" width="100" height="auto" class="g-img mso-logo">
          </div>
          <!--[if mso]>
                    </td>
                  </tr>
                  <tr>
                    <td>
          <![endif]-->
          <h1 style="${styles.title}">${data.dealId ? `Quote Request #${data.dealId}` : 'QUOTE REQUEST - NOT A BOOKING CONFIRMATION'}</h1>
          <p style="color: ${themeColors.text}; margin-top: 10px; font-size: 16px;">We'll review your request and contact you with pricing soon.</p>
          <!--[if mso]>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
          <![endif]-->
        </div>

        <!-- Date and Time -->
        <div style="${styles.dateContainer}">
          <div style="${styles.dateBox}">
            <p style="${styles.dateText}">${formatDateForEmail(bookingData.pickupDate)}</p>
            <p style="${styles.timeText}">${new Date(bookingData.pickupDate).toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true })}</p>
          </div>
        </div>

        <!-- Locations -->
        <div style="${styles.locationSection}">
          <div style="${styles.locationLine}"></div>
          
          <!-- Pickup Location -->
          <div style="${styles.locationContainer}">
            <div style="${styles.locationDot}">A</div>
            <div style="${styles.locationContent}">
              <p style="${styles.locationLabel}">PICKUP LOCATION</p>
              <p style="${styles.locationText}">${bookingData.pickupLocation?.address || 'Not provided'}</p>
            </div>
          </div>
          
          <!-- Dropoff Location -->
          <div style="${styles.locationContainer}">
            <div style="${styles.locationDot}">B</div>
            <div style="${styles.locationContent}">
              <p style="${styles.locationLabel}">DROPOFF LOCATION</p>
              <p style="${styles.locationText}">${bookingData.dropoffLocation?.address || 'Not provided'}</p>
            </div>
          </div>
        </div>

        <!-- Vehicle -->
        <div style="${styles.vehicleCard}">
          <!--[if mso]>
          <table width="100%" cellpadding="0" cellspacing="0" border="0">
            <tr>
              <td align="center" style="padding-bottom: 16px;">
          <![endif]-->
          <img src="${FULL_VEHICLES_URL}/${bookingData.selectedVehicle}.png" alt="Selected Vehicle" style="${styles.vehicleImage}" width="200" height="auto" class="v-img mso-vehicle">
          <!--[if mso]>
              </td>
            </tr>
            <tr>
              <td>
          <![endif]-->
          <h3 style="${styles.vehicleTitle}">${getVehicleDetails(bookingData.selectedVehicle)?.name || 'Vehicle not selected'}</h3>
          <!--[if mso]>
              </td>
            </tr>
          </table>
          <![endif]-->
        </div>

        <!-- Customer Details -->
        <div style="${styles.detailsSection}">
          <h2 style="font-size: 18px; margin: 0 0 16px 0; color: ${themeColors.text};">Customer Information</h2>
          
          <!-- Quote ID -->
          ${data.dealId ? `
          <div style="${styles.detailRow}">
            <p style="${styles.detailLabel}">QUOTE ID</p>
            <p style="${styles.detailText}">${data.dealId}</p>
          </div>
          ` : ''}
          
          <!-- Name -->
          <div style="${styles.detailRow}">
            <p style="${styles.detailLabel}">NAME</p>
            <p style="${styles.detailText}">${customerName}</p>
          </div>
          
          <!-- Email -->
          <div style="${styles.detailRow}">
            <p style="${styles.detailLabel}">EMAIL</p>
            <p style="${styles.detailText}">${customerEmail}</p>
          </div>
          
          <!-- Phone -->
          <div style="${styles.detailRow}">
            <p style="${styles.detailLabel}">PHONE</p>
            <p style="${styles.detailText}">${customerPhone}</p>
          </div>
          
          <!-- Special Requests (if any) -->
          ${specialRequests ? `
          <div style="${styles.detailRow}">
            <p style="${styles.detailLabel}">SPECIAL REQUESTS</p>
            <p style="${styles.detailText}">${specialRequests}</p>
          </div>
          ` : ''}
        </div>

        <!-- Footer -->
        <div style="${styles.footer}">
          <p style="${styles.footerText}">This is a quote request only. We will contact you with pricing information.</p>
          <p style="${styles.footerText}">© ${new Date().getFullYear()} WWLIMO Chauffeur Service. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;

  return html;
};

// Helper function to get pickup coordinates
const getPickupCoordinates = (data: BookingData): [number, number] | null => {
  const { bookingDetails } = data;
  const { formType } = bookingDetails;
  
  let pickupLocation;
  
  if (formType === 'point-to-point' && bookingDetails.pointToPointData) {
    pickupLocation = bookingDetails.pointToPointData.pickupLocation;
  } else if (formType === 'hourly' && bookingDetails.hourlyData) {
    pickupLocation = bookingDetails.hourlyData.pickupLocation;
  } else if (formType === 'airport' && bookingDetails.airportData) {
    pickupLocation = bookingDetails.airportData.pickupLocation;
  } else if (formType === 'multi-day' && bookingDetails.multiDayData && bookingDetails.multiDayData.days.length > 0) {
    pickupLocation = bookingDetails.multiDayData.days[0].pickupLocation;
  }
  
  if (pickupLocation && pickupLocation.coordinates && 
      Array.isArray(pickupLocation.coordinates) && 
      pickupLocation.coordinates.length === 2) {
    return pickupLocation.coordinates;
  }
  
  return null;
};

// Helper function to get pickup address
const getPickupAddress = (data: BookingData): string | null => {
  const { bookingDetails } = data;
  const { formType } = bookingDetails;
  
  let pickupLocation;
  
  if (formType === 'point-to-point' && bookingDetails.pointToPointData) {
    pickupLocation = bookingDetails.pointToPointData.pickupLocation;
  } else if (formType === 'hourly' && bookingDetails.hourlyData) {
    pickupLocation = bookingDetails.hourlyData.pickupLocation;
  } else if (formType === 'airport' && bookingDetails.airportData) {
    pickupLocation = bookingDetails.airportData.pickupLocation;
  } else if (formType === 'multi-day' && bookingDetails.multiDayData) {
    pickupLocation = bookingDetails.multiDayData.days[0]?.pickupLocation;
  }
  
  return pickupLocation?.address || null;
};

/**
 * Get the pickup city from coordinates using only reverse geocoding APIs
 * @version 3.0.0 - Using only coordinate-based city extraction with no text parsing
 * @param formData The form data containing pickup information
 * @returns The city name or null if not found
 */
export const getPickupCity = async (formData: any): Promise<string | null> => {
  console.log('[getPickupCity] Version 3.0.0 - Getting pickup city from coordinates only');
  
  // Import both API modules upfront to avoid dynamic import issues
  const { getCityFromCoordinates: getHereCity } = await import('../../services/hereApi');
  const { getCityFromCoordinates: getMapboxCity } = await import('../../services/mapboxApi');
  
  try {
    // Extract coordinates from the form data
    const pickupCoordinates = formData.pickupCoordinates || 
                             (formData.bookingDetails?.pointToPointData?.pickupLocation?.coordinates) ||
                             (formData.bookingDetails?.airportData?.pickupLocation?.coordinates) ||
                             (formData.bookingDetails?.hourlyData?.pickupLocation?.coordinates);
    
    if (!pickupCoordinates) {
      console.log('[getPickupCity] No coordinates found in form data');
      return null;
    }
    
    console.log('[getPickupCity] Extracting city from coordinates:', pickupCoordinates);
    
    // Ensure coordinates are in the correct format
    let coordinates: [number, number];
    
    // Check if coordinates are in object format {latitude, longitude}
    if (typeof pickupCoordinates === 'object' && 'latitude' in pickupCoordinates && 'longitude' in pickupCoordinates) {
      console.log('[getPickupCity] Converting coordinates from object format to array format');
      // Convert to array format [longitude, latitude] for HERE API
      coordinates = [pickupCoordinates.longitude, pickupCoordinates.latitude];
    } else if (Array.isArray(pickupCoordinates) && pickupCoordinates.length === 2) {
      // Already in array format, but we need to ensure the order is correct
      coordinates = [...pickupCoordinates] as [number, number];
      
      // Check if coordinates might be in the wrong order (latitude, longitude)
      // Longitude should be between -180 and 180, latitude between -90 and 90
      if (Math.abs(coordinates[0]) > 90 && Math.abs(coordinates[1]) <= 180) {
        // Coordinates are likely in the wrong order, swap them
        console.log('[getPickupCity] Coordinates appear to be in wrong order, swapping');
        coordinates = [coordinates[1], coordinates[0]];
      }
    } else {
      console.error('[getPickupCity] Invalid coordinates format:', pickupCoordinates);
      return null;
    }
    
    // Validate coordinates are in reasonable ranges
    if (Math.abs(coordinates[0]) > 180 || Math.abs(coordinates[1]) > 90) {
      console.error('[getPickupCity] Coordinates out of valid range:', coordinates);
      return null;
    }
    
    // Try to get city from HERE API
    console.log('[getPickupCity] Trying HERE API with coordinates:', coordinates);
    const hereCity = await getHereCity(coordinates);
    
    if (hereCity) {
      console.log('[getPickupCity] City found from HERE API:', hereCity);
      return hereCity;
    }
    
    console.log('[getPickupCity] No city found from HERE API, trying Mapbox API');
    
    // For Mapbox, we need to ensure coordinates are in [longitude, latitude] order
    const mapboxCity = await getMapboxCity(coordinates);
    
    if (mapboxCity) {
      console.log('[getPickupCity] City found from Mapbox API:', mapboxCity);
      return mapboxCity;
    }
    
    console.log('[getPickupCity] No city found from any API service');
    return null;
  } catch (error) {
    console.error('[getPickupCity] Error getting pickup city:', error);
    return null;
  }
};

const getPickupDate = (data: BookingData): string => {
  const formType = data.bookingDetails.formType;
  
  if (formType === 'point-to-point' && data.bookingDetails.pointToPointData) {
    return data.bookingDetails.pointToPointData.pickupDate;
  } else if (formType === 'hourly' && data.bookingDetails.hourlyData) {
    return data.bookingDetails.hourlyData.pickupDate;
  } else if (formType === 'airport' && data.bookingDetails.airportData) {
    return data.bookingDetails.airportData.pickupDate;
  } else if (formType === 'multi-day' && data.bookingDetails.multiDayData) {
    return data.bookingDetails.multiDayData.pickupDate;
  }
  
  return '';
};

const getTotalPassengers = (data: BookingData): string => {
  const formType = data.bookingDetails.formType;
  let adults = 0;
  let children = 0;
  
  if (formType === 'point-to-point' && data.bookingDetails.pointToPointData) {
    adults = data.bookingDetails.pointToPointData.adults;
    children = data.bookingDetails.pointToPointData.children || 0;
  } else if (formType === 'hourly' && data.bookingDetails.hourlyData) {
    adults = data.bookingDetails.hourlyData.adults;
    children = data.bookingDetails.hourlyData.children || 0;
  } else if (formType === 'airport' && data.bookingDetails.airportData) {
    adults = data.bookingDetails.airportData.adults;
    children = data.bookingDetails.airportData.children || 0;
  } else if (formType === 'multi-day' && data.bookingDetails.multiDayData) {
    adults = data.bookingDetails.multiDayData.adults;
    children = data.bookingDetails.multiDayData.children || 0;
  }
  
  return `${adults + children} pax`;
};

// Format date for email display
const formatDateForEmail = (date: string) => {
  return new Date(date).toLocaleString('en-US', {
    weekday: 'short',
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Detect if we're in WordPress environment
const detectWordPressEnv = (): { isWordPress: boolean; apiBaseUrl?: string } => {
  if (typeof window === 'undefined') {
    return { isWordPress: false };
  }

  // Check for WordPress-specific global variables or paths
  const isWordPress = 
    // Check URL patterns
    window.location.pathname.includes('/wp-') || 
    window.location.href.includes('/wp-') ||
    // Check for WordPress global
    (typeof window['wp'] !== 'undefined');
  
  if (isWordPress) {
    return { 
      isWordPress: true,
      apiBaseUrl: '/wp-json/limo-booking/v1/proxy/emails'
    };
  }
  
  return { isWordPress: false };
};