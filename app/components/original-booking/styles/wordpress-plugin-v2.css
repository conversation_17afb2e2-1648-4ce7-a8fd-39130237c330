/* 
 * LIMO BOOKING WORDPRESS PLUGIN CSS v2.0
 * Commercial-Grade CSS Architecture for WordPress Plugin Distribution
 * 
 * ARCHITECTURE PRINCIPLES:
 * 1. Complete CSS isolation using .limo-booking-plugin namespace
 * 2. No global CSS pollution - all styles scoped
 * 3. WordPress theme compatibility through CSS custom properties
 * 4. Bulletproof specificity without !important abuse
 * 5. Component-based styling with consistent naming
 * 6. Performance optimized with minimal CSS footprint
 */

/* =============================================================================
   PLUGIN ROOT CONTAINER & CSS ISOLATION
   ============================================================================= */

.limo-booking-plugin {
  /* CSS ISOLATION - Prevent WordPress theme interference */
  all: initial;
  
  /* CORE PLUGIN VARIABLES - Single source of truth */
  --limo-primary: #111827;
  --limo-primary-light: #8b6d4c;
  --limo-primary-dark: #5d472f;
  --limo-primary-transparent: rgba(118, 90, 61, 0.5);
  --limo-background: #000000;
  --limo-surface: #141414;
  --limo-surface-dark: #1A1A1A;
  --limo-surface-light: rgba(255, 255, 255, 0.03);
  --limo-text-primary: #ffffff;
  --limo-text-secondary: #e5e7eb;
  --limo-text-muted: #9ca3af;
  --limo-text-disabled: rgba(255, 255, 255, 0.5);
  --limo-border: #333333;
  --limo-border-hover: #4D4D4D;
  --limo-shadow: rgba(0, 0, 0, 0.3);
  --limo-error: #ef4444;
  --limo-success: #10b981;
  --limo-warning: #f59e0b;
  --limo-info: #3b82f6;
  
  /* TYPOGRAPHY FOUNDATION */
  font-family: 'Raleway', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 400;
  
  /* CONTAINER FOUNDATION */
  display: block;
  position: relative;
  width: 100%;
  background-color: var(--limo-background);
  color: var(--limo-text-primary);
  box-sizing: border-box;
  
  /* RESET ALL CHILD ELEMENTS */
  * {
    box-sizing: border-box;
    font-family: inherit;
  }
  
  /* PREVENT WORDPRESS THEME STYLE LEAKAGE */
  *:not([class*="mapbox"]):not([class*="mantine"]) {
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
    vertical-align: baseline;
    background: transparent;
  }
}

/* =============================================================================
   FORM ELEMENTS - BULLETPROOF INPUT STYLING
   ============================================================================= */

.limo-booking-plugin input,
.limo-booking-plugin textarea,
.limo-booking-plugin select {
  display: block;
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--limo-text-primary);
  background-color: var(--limo-surface);
  border: 1px solid var(--limo-border);
  border-radius: 0.375rem;
  font-family: inherit;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.limo-booking-plugin input:focus,
.limo-booking-plugin textarea:focus,
.limo-booking-plugin select:focus {
  border-color: var(--limo-primary);
  outline: 0;
  box-shadow: 0 0 0 0.125rem var(--limo-primary-transparent);
}

.limo-booking-plugin input::placeholder,
.limo-booking-plugin textarea::placeholder {
  color: var(--limo-text-muted);
  opacity: 1;
}

.limo-booking-plugin input:disabled,
.limo-booking-plugin textarea:disabled,
.limo-booking-plugin select:disabled {
  background-color: var(--limo-surface-dark);
  color: var(--limo-text-disabled);
  cursor: not-allowed;
}

/* =============================================================================
   BUTTON COMPONENTS
   ============================================================================= */

.limo-booking-plugin button,
.limo-booking-plugin .limo-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.5;
  color: #ffffff;
  background-color: var(--limo-primary);
  border: 1px solid var(--limo-primary);
  border-radius: 0.375rem;
  font-family: inherit;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.15s ease-in-out;
}

.limo-booking-plugin button:hover,
.limo-booking-plugin .limo-btn:hover {
  background-color: var(--limo-primary-light);
  border-color: var(--limo-primary-light);
}

.limo-booking-plugin button:active,
.limo-booking-plugin .limo-btn:active {
  background-color: var(--limo-primary-dark);
  border-color: var(--limo-primary-dark);
}

.limo-booking-plugin button:disabled,
.limo-booking-plugin .limo-btn:disabled {
  background-color: var(--limo-text-disabled);
  border-color: var(--limo-text-disabled);
  cursor: not-allowed;
}

/* =============================================================================
   MANTINE COMPONENT OVERRIDES - BULLETPROOF SPECIFICITY
   ============================================================================= */

.limo-booking-plugin [class*="mantine-TextInput"] input,
.limo-booking-plugin [class*="mantine-Textarea"] textarea,
.limo-booking-plugin [class*="mantine-Select"] input,
.limo-booking-plugin [class*="mantine-NumberInput"] input,
.limo-booking-plugin [class*="mantine-DateInput"] input,
.limo-booking-plugin [class*="mantine-DateTimePicker"] input {
  background-color: var(--limo-surface);
  border-color: var(--limo-border);
  color: var(--limo-text-primary);
}

.limo-booking-plugin [class*="mantine-TextInput"] input:focus,
.limo-booking-plugin [class*="mantine-Textarea"] textarea:focus,
.limo-booking-plugin [class*="mantine-Select"] input:focus,
.limo-booking-plugin [class*="mantine-NumberInput"] input:focus,
.limo-booking-plugin [class*="mantine-DateInput"] input:focus,
.limo-booking-plugin [class*="mantine-DateTimePicker"] input:focus {
  border-color: var(--limo-primary);
  box-shadow: 0 0 0 0.125rem var(--limo-primary-transparent);
}

.limo-booking-plugin [class*="mantine-Button"] {
  background-color: var(--limo-primary);
  color: #ffffff;
  border-color: var(--limo-primary);
}

.limo-booking-plugin [class*="mantine-Button"]:hover {
  background-color: var(--limo-primary-light);
  border-color: var(--limo-primary-light);
}

.limo-booking-plugin [class*="mantine-NumberInput"] [class*="control"] {
  background-color: var(--limo-surface);
  border-color: var(--limo-border);
  color: var(--limo-text-primary);
}

.limo-booking-plugin [class*="mantine-NumberInput"] [class*="control"]:hover {
  background-color: var(--limo-surface-light);
  border-color: var(--limo-primary);
}

.limo-booking-plugin [class*="mantine-Switch"] [class*="track"] {
  background-color: var(--limo-text-disabled);
}

.limo-booking-plugin [class*="mantine-Switch"] [class*="track"][data-checked] {
  background-color: var(--limo-primary);
}

.limo-booking-plugin [class*="mantine-Switch"] [class*="thumb"] {
  background-color: #ffffff;
}

/* =============================================================================
   LAYOUT COMPONENTS
   ============================================================================= */

.limo-booking-plugin .limo-grid {
  display: grid;
  gap: 1rem;
}

.limo-booking-plugin .limo-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.limo-booking-plugin .limo-grid-auto {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.limo-booking-plugin .limo-card {
  background-color: var(--limo-surface);
  border: 1px solid var(--limo-border);
  border-radius: 0.5rem;
  padding: 1rem;
  transition: all 0.15s ease-in-out;
}

.limo-booking-plugin .limo-card:hover {
  border-color: var(--limo-primary);
  box-shadow: 0 4px 12px var(--limo-shadow);
}

.limo-booking-plugin .limo-card.selected {
  border-color: var(--limo-primary);
  background-color: var(--limo-primary-transparent);
}

/* =============================================================================
   TYPOGRAPHY
   ============================================================================= */

.limo-booking-plugin h1,
.limo-booking-plugin h2,
.limo-booking-plugin h3,
.limo-booking-plugin h4,
.limo-booking-plugin h5,
.limo-booking-plugin h6 {
  color: var(--limo-text-primary);
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5rem;
}

.limo-booking-plugin p {
  color: var(--limo-text-primary);
  margin-bottom: 1rem;
}

.limo-booking-plugin label {
  display: block;
  color: var(--limo-text-primary);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

/* =============================================================================
   RESPONSIVE DESIGN
   ============================================================================= */

@media (max-width: 768px) {
  .limo-booking-plugin .limo-grid-2 {
    grid-template-columns: 1fr;
  }
  
  .limo-booking-plugin .limo-grid-auto {
    grid-template-columns: 1fr;
  }
}
