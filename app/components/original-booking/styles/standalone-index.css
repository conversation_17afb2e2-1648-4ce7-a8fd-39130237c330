/*
 * STANDALONE CSS - PURE STYLING WITHOUT MANTINE UI
 * This file provides the exact styling used in the standalone version
 */

@import url('https://fonts.googleapis.com/css2?family=Raleway:wght@300;400;500;600;700&display=swap');
@import 'react-datepicker/dist/react-datepicker.css';

/* CSS Variables for theming - Scoped to booking form only */
.original-booking-form {
  --primary: #111827;
  --primary-light: #8b6d4c;
  --primary-dark: #5d472f;
  --background: #ffffff;
  --surface: #f8f9fa;
  --surface-light: #e9ecef;
  --border: #e5e7eb;
  --text-primary: #000000;
  --text-secondary: #374151;
  --text-muted: #6b7280;
  --text-disabled: #9ca3af;
  --theme-primary: var(--text-primary);
  --theme-secondary: var(--text-secondary);
  --theme-muted: var(--text-muted);
}

/* Base reset */
* {
  box-sizing: border-box;
}

/* Typography - Scoped to booking form */
.original-booking-form {
  font-family: 'Raleway', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: var(--background);
  color: var(--text-primary);
  line-height: 1.6;
}

/* Layout utilities - Scoped to booking form */
.original-booking-form .space-y-6 > * + * { margin-top: 1.5rem; }
.original-booking-form .space-y-4 > * + * { margin-top: 1rem; }
.original-booking-form .space-y-2 > * + * { margin-top: 0.5rem; }
.original-booking-form .gap-4 { gap: 1rem; }
.original-booking-form .gap-3 { gap: 0.75rem; }
.original-booking-form .gap-2 { gap: 0.5rem; }

/* Grid system - Scoped to booking form */
.original-booking-form .grid { display: grid; }
.original-booking-form .grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.original-booking-form .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.original-booking-form .grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.original-booking-form .grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }
.original-booking-form .col-span-3 { grid-column: span 3 / span 3; }

/* Flexbox utilities - Scoped to booking form */
.original-booking-form .flex { display: flex; }
.original-booking-form .items-center { align-items: center; }
.original-booking-form .items-start { align-items: flex-start; }
.original-booking-form .justify-between { justify-content: space-between; }
.original-booking-form .justify-center { justify-content: center; }
.original-booking-form .flex-grow { flex-grow: 1; }
.original-booking-form .flex-shrink-0 { flex-shrink: 0; }

/* Positioning */
.relative { position: relative; }
.absolute { position: absolute; }
.left-3 { left: 0.75rem; }
.right-3 { right: 0.75rem; }
.top-1\/2 { top: 50%; }
.-translate-y-1\/2 { transform: translateY(-50%); }

/* Sizing */
.w-full { width: 100%; }
.w-5 { width: 1.25rem; }
.h-5 { height: 1.25rem; }
.h-\[46px\] { height: 46px; }
.min-w-\[2\.5rem\] { min-width: 2.5rem; }
.min-h-\[2\.5rem\] { min-height: 2.5rem; }

/* Padding and margin */
.p-2 { padding: 0.5rem; }
.p-4 { padding: 1rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.pl-10 { padding-left: 2.5rem; }
.pr-20 { padding-right: 5rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mt-2 { margin-top: 0.5rem; }

/* Border radius */
.rounded-lg { border-radius: 0.5rem; }
.rounded-full { border-radius: 9999px; }

/* Colors */
.bg-surface { background-color: var(--surface); }
.bg-surface-light { background-color: var(--surface-light); }
.bg-primary { background-color: var(--primary); }
.text-theme-primary { color: var(--theme-primary); }
.text-theme-secondary { color: var(--theme-secondary); }
.text-theme-muted { color: var(--theme-muted); }
.text-primary { color: var(--primary); }
.text-red-400 { color: #f87171; }
.text-red-300 { color: #fca5a5; }
.border-border { border-color: var(--border); }

/* Hover effects */
.hover\:bg-surface-light:hover { background-color: var(--surface-light); }
.hover\:text-primary\/70:hover { color: rgba(118, 90, 61, 0.7); }
.hover\:text-red-300:hover { color: #fca5a5; }
.hover\:bg-red-400\/10:hover { background-color: rgba(248, 113, 113, 0.1); }

/* Focus effects */
.focus\:outline-none:focus { outline: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 2px var(--primary); }
.focus\:ring-primary\/50:focus { box-shadow: 0 0 0 2px rgba(118, 90, 61, 0.5); }

/* Transitions */
.transition-colors { transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out; }

/* Pointer events */
.pointer-events-none { pointer-events: none; }

/* Form inputs - Scoped to booking form */
.original-booking-form input,
.original-booking-form textarea,
.original-booking-form select {
  background-color: var(--surface);
  border: 1px solid var(--border);
  color: var(--text-primary);
  border-radius: 0.5rem;
  padding: 0.75rem;
  font-family: inherit;
  font-size: 1rem;
  transition: border-color 0.2s ease-in-out;
}

.original-booking-form input:focus,
.original-booking-form textarea:focus,
.original-booking-form select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(118, 90, 61, 0.2);
}

/* Buttons - Scoped to booking form */
.original-booking-form button {
  font-family: inherit;
  cursor: pointer;
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.original-booking-form button:hover {
  transform: translateY(-1px);
}

/* Primary button - Scoped to booking form */
.original-booking-form .btn-primary {
  background-color: var(--primary);
  color: white;
}

.original-booking-form .btn-primary:hover {
  background-color: var(--primary-dark);
}

/* Outline button - Scoped to booking form */
.original-booking-form .btn-outline {
  background-color: transparent;
  border: 1px solid var(--border);
  color: var(--text-primary);
}

.original-booking-form .btn-outline:hover {
  background-color: var(--surface-light);
}

/* Toggle switch */
.toggle-switch {
  position: relative;
  display: inline-flex;
  height: 1.5rem;
  width: 2.75rem;
  align-items: center;
  border-radius: 9999px;
  transition: background-color 0.2s ease-in-out;
  cursor: pointer;
}

.toggle-switch[aria-checked="true"] {
  background-color: var(--primary);
}

.toggle-switch[aria-checked="false"] {
  background-color: var(--text-disabled);
}

.toggle-thumb {
  position: absolute;
  height: 1rem;
  width: 1rem;
  background-color: white;
  border-radius: 9999px;
  transition: transform 0.2s ease-in-out;
  transform: translateX(0.25rem);
}

.toggle-switch[aria-checked="true"] .toggle-thumb {
  transform: translateX(1.5rem);
}

/* React DatePicker overrides - Scoped to booking form */
.original-booking-form .react-datepicker {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  font-family: inherit !important;
}

.original-booking-form .react-datepicker__header {
  background-color: var(--surface) !important;
  border-bottom: 1px solid var(--border) !important;
}

.original-booking-form .react-datepicker__current-month,
.original-booking-form .react-datepicker-time__header,
.original-booking-form .react-datepicker__day-name {
  color: var(--text-primary) !important;
}

.original-booking-form .react-datepicker__day {
  color: var(--text-primary) !important;
}

.original-booking-form .react-datepicker__day:hover {
  background-color: var(--primary) !important;
  color: white !important;
}

.original-booking-form .react-datepicker__day--selected,
.original-booking-form .react-datepicker__day--keyboard-selected {
  background-color: var(--primary) !important;
  color: white !important;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item:hover {
  background-color: var(--primary) !important;
  color: white !important;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected {
  background-color: var(--primary) !important;
  color: white !important;
}

/* Responsive design */
@media (min-width: 1024px) {
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .lg\:flex { display: flex; }
  .hidden.lg\:flex { display: none; }
}

@media (min-width: 1024px) {
  .hidden.lg\:flex { display: flex; }
}
