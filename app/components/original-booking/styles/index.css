@import url('https://fonts.googleapis.com/css2?family=Raleway:wght@300;400;500;600;700&display=swap');

@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@import './datepicker.css';
@import './utilities.css';
@import './wordpress-plugin.css';

/* Theme Variables - Scoped to booking form only */
.original-booking-form {
  /* Brand Colors */
  --primary: var(--wp-primary, #765a3d);
  --primary-light: var(--wp-primary-light, #8b6d4c);
  --primary-dark: var(--wp-primary-dark, #5d472f);
  --primary-transparent: var(--wp-primary-transparent, rgba(118, 90, 61, 0.5));
  --primary-hover: var(--primary-light);
  --primary-active: var(--primary-dark);

  /* Surface Colors - Light theme defaults for SaaS */
  --background: var(--wp-background, #ffffff);
  --surface: var(--wp-surface, #f8f9fa);
  --surface-dark: var(--wp-surface-dark, #e9ecef);
  --surface-light: var(--wp-surface-light, rgba(0, 0, 0, 0.03));

  /* Text Colors - Dark text for light backgrounds */
  --text-primary: var(--wp-text-primary, #000000);
  --text-secondary: var(--wp-text-secondary, #374151);
  --text-muted: var(--wp-text-muted, #6b7280);
  --text-disabled: var(--wp-text-disabled, rgba(0, 0, 0, 0.5));

  /* Border Colors */
  --border: var(--wp-border, #e5e7eb);
  --border-hover: var(--wp-border-hover, #d1d5db);

  /* Status Colors */
  --success: #4CAF50;
  --error: #F44336;
  --warning: #FFC107;

  /* Status Background Colors */
  --success-bg: rgba(76, 175, 80, 0.1);
  --error-bg: rgba(244, 67, 54, 0.1);
  --warning-bg: rgba(255, 152, 0, 0.1);

  /* Overlay Colors */
  --overlay-light: rgba(0, 0, 0, 0.05);
  --overlay-medium: rgba(0, 0, 0, 0.1);
  --overlay-dark: rgba(0, 0, 0, 0.5);

  /* Shadow */
  --shadow: rgba(0, 0, 0, 0.1);

  /* Z-index Hierarchy */
  --z-base: 1;
  --z-map: 10;
  --z-controls: 20;
  --z-inputs: 30;
  --z-dropdown: 40;
  --z-datepicker: 50;
  --z-content: 60;
  --z-mobile-stepper: 100;
  --z-modal-backdrop: 900;
  --z-modal: 1000;
  --z-theme-modal: 9000;
  --z-tooltip: 9500;

  /* Add these new styles */
  --primary-rgb: 118, 90, 61;
  /* This matches your brown theme */

  /* Map Theme - Light theme for SaaS */
  --map-style: var(--wp-map-style, mapbox://styles/mapbox/light-v11);
}

/* Light theme overrides */
[data-theme="light"] {
  --text-primary: #000000;
  --text-secondary: #4b5563;
  --text-muted: #6b7280;
  --text-disabled: rgba(0, 0, 0, 0.5);
  --background: #ffffff;
  --surface: #f3f4f6;
  --surface-dark: #e5e7eb;
  --surface-light: rgba(0, 0, 0, 0.03);
  --border: #e5e7eb;
  --border-hover: #d1d5db;
}

/* Base styles - Scoped to booking form only */
.original-booking-form {
  background-color: var(--background);
  color: var(--text-primary);
  font-family: 'Raleway', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Force text colors in all contexts - Scoped to booking form */
.original-booking-form .text-theme-primary,
.original-booking-form .text-primary,
.original-booking-form span,
.original-booking-form p {
  color: var(--text-primary) !important;
}

/* Force white text in colored backgrounds - Scoped to booking form */
.original-booking-form .bg-primary,
.original-booking-form [class*="bg-primary-"],
.original-booking-form .bg-success,
.original-booking-form [class*="bg-success-"],
.original-booking-form .bg-error,
.original-booking-form [class*="bg-error-"],
.original-booking-form .bg-warning,
.original-booking-form [class*="bg-warning-"],
.original-booking-form .bg-theme-primary,
.original-booking-form [class*="bg-theme-"],
.original-booking-form button[class*="bg-primary"],
.original-booking-form button[class*="bg-success"],
.original-booking-form button[class*="bg-error"],
.original-booking-form button[class*="bg-warning"],
.original-booking-form button[class*="bg-theme"] {
  color: #ffffff !important;
}

/* Force white text in all contexts - Scoped to booking form */
.original-booking-form [class*="bg-primary"],
.original-booking-form [class*="bg-success"],
.original-booking-form [class*="bg-error"],
.original-booking-form [class*="bg-warning"],
.original-booking-form [class*="bg-theme"],
.original-booking-form .bg-primary,
.original-booking-form .bg-success,
.original-booking-form .bg-error,
.original-booking-form .bg-warning,
.original-booking-form .bg-theme-primary {
  color: #ffffff !important;
}

.original-booking-form [class*="bg-primary"] *,
.original-booking-form [class*="bg-success"] *,
.original-booking-form [class*="bg-error"] *,
.original-booking-form [class*="bg-warning"] *,
.original-booking-form [class*="bg-theme"] *,
.original-booking-form .bg-primary *,
.original-booking-form .bg-success *,
.original-booking-form .bg-error *,
.original-booking-form .bg-warning *,
.original-booking-form .bg-theme-primary * {
  color: #ffffff !important;
}

/* Ensure icons in colored backgrounds are white - Scoped to booking form */
.original-booking-form [class*="bg-primary"] svg,
.original-booking-form [class*="bg-success"] svg,
.original-booking-form [class*="bg-error"] svg,
.original-booking-form [class*="bg-warning"] svg,
.original-booking-form [class*="bg-theme"] svg,
.original-booking-form .bg-primary svg,
.original-booking-form .bg-success svg,
.original-booking-form .bg-error svg,
.original-booking-form .bg-warning svg,
.original-booking-form .bg-theme-primary svg {
  color: #ffffff !important;
  fill: #ffffff !important;
  stroke: #ffffff !important;
}

/* WordPress-specific overrides - Use namespaced classes instead */
/* See wordpress-plugin.css for the namespaced implementation */
html[data-wp] .limo-booking-wrapper,
html[data-wp] #limo-booking-form {
  /* Add the limo-booking-plugin class to these elements for styling */
}

/* Map container styles - Scoped to booking form */
.original-booking-form .mapboxgl-map {
  position: absolute !important;
  inset: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background-color: var(--background) !important;
  min-height: 300px !important;
  display: block !important;
}

.mapboxgl-canvas-container {
  position: absolute !important;
  inset: 0 !important;
  width: 100% !important;
  height: 100% !important;
  min-height: 300px !important;
  display: block !important;
}

.mapboxgl-canvas {
  position: absolute !important;
  inset: 0 !important;
  width: 100% !important;
  height: 100% !important;
  min-height: 300px !important;
  display: block !important;
}

/* Map Controls */
.mapboxgl-control-container {
  position: relative !important;
  z-index: var(--z-controls) !important;
}

.mapboxgl-ctrl-top-right {
  z-index: var(--z-controls) !important;
}

.mapboxgl-ctrl-group {
  background-color: var(--surface) !important;
  border: 1px solid var(--border) !important;
  box-shadow: 0 2px 4px var(--shadow) !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

.mapboxgl-ctrl-group button {
  background-color: transparent !important;
  border: none !important;
  width: 32px !important;
  height: 32px !important;
}

.mapboxgl-ctrl-group button:hover {
  background-color: var(--overlay-light) !important;
}

/* Map Markers */
.marker {
  width: 30px !important;
  height: 30px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  border: 2px solid #ffffff !important;
  box-shadow: 0 2px 4px var(--shadow) !important;
  transition: transform 0.2s !important;
  cursor: pointer !important;
  background-color: var(--primary) !important;
}

.marker:hover {
  transform: scale(1.1) !important;
}

.pickup-marker {
  background-color: var(--success) !important;
  z-index: calc(var(--z-map) + 1) !important;
}

.stop-marker {
  background-color: var(--primary) !important;
  z-index: calc(var(--z-map) + 2) !important;
}

.dropoff-marker {
  background-color: var(--error) !important;
  z-index: calc(var(--z-map) + 3) !important;
}

/* Mobile styles */
@media screen and (max-width: 768px) {

  .mapboxgl-map,
  .mapboxgl-canvas-container,
  .mapboxgl-canvas {
    height: 100% !important;
    min-height: 250px !important;
    width: 100% !important;
    display: block !important;
    position: absolute !important;
    inset: 0 !important;
  }

  /* Force parent containers to maintain height */
  .w-full.h-\[300px\] {
    min-height: 250px !important;
    height: 250px !important;
    display: block !important;
  }
}

/* Map parent container */
.map-container {
  position: relative !important;
  width: 100% !important;
  height: 300px !important;
  min-height: 300px !important;
  display: block !important;
  background-color: var(--background) !important;
  border-radius: 0.5rem !important;
  overflow: hidden !important;
}

@media screen and (max-width: 768px) {
  .map-container {
    height: 250px !important;
    min-height: 250px !important;
  }
}

/* Marker animations */
@keyframes markerPulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}

.marker.active {
  animation: markerPulse 1.5s infinite;
}

/* Ensure Tailwind classes use CSS variables - Scoped to booking form */
@layer utilities {
  .original-booking-form .bg-background {
    background-color: var(--background);
  }

  .original-booking-form .bg-surface {
    background-color: var(--surface);
  }

  .original-booking-form .bg-surface-dark {
    background-color: var(--surface-dark);
  }

  .original-booking-form .bg-surface-light {
    background-color: var(--surface-light);
  }

  .original-booking-form .bg-primary {
    background-color: var(--primary);
  }

  .original-booking-form .bg-primary-light {
    background-color: var(--primary-light);
  }

  .original-booking-form .bg-primary-dark {
    background-color: var(--primary-dark);
  }

  /* Override bg-theme-primary to not affect children */
  .original-booking-form .bg-theme-primary {
    background-color: var(--text-primary);
  }

  .original-booking-form .bg-theme-primary.toggle-thumb,
  .original-booking-form .bg-theme-primary .toggle-thumb,
  .original-booking-form .toggle-button.bg-theme-primary .toggle-thumb {
    background-color: #ffffff !important;
  }

  /* Text color utilities */
  .original-booking-form .text-theme-primary {
    color: var(--text-primary) !important;
  }

  .original-booking-form .text-theme-secondary {
    color: var(--text-secondary) !important;
  }

  .original-booking-form .text-theme-muted {
    color: var(--text-muted) !important;
  }

  .original-booking-form .text-theme-disabled {
    color: var(--text-disabled) !important;
  }

  /* Additional text color utilities */
  .original-booking-form .text-white\/10 {
    color: color-mix(in srgb, var(--text-primary) 10%, transparent) !important;
  }

  .original-booking-form .text-white\/20 {
    color: color-mix(in srgb, var(--text-primary) 20%, transparent) !important;
  }

  .original-booking-form .text-white\/50 {
    color: color-mix(in srgb, var(--text-primary) 50%, transparent) !important;
  }

  .original-booking-form .text-white\/70 {
    color: color-mix(in srgb, var(--text-primary) 70%, transparent) !important;
  }

  .original-booking-form .border-primary {
    border-color: var(--primary);
  }

  .original-booking-form .hover\:bg-primary:hover {
    background-color: var(--primary-hover);
  }

  .original-booking-form .hover\:text-primary:hover {
    color: var(--primary);
  }

  .original-booking-form .hover\:border-primary:hover {
    border-color: var(--primary);
  }
}

/* Mobile Trip Summary - ensure it's above everything */
.trip-summary-overlay {
  position: fixed !important;
  inset: 0 !important;
  z-index: 9999 !important;
}

.trip-summary-panel {
  position: fixed !important;
  inset: 0 auto 0 0 !important;
  z-index: 9999 !important;
  width: 100% !important;
  max-width: 400px !important;
  background: var(--background) !important;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5) !important;
}

/* Debug Panel Styles */
.debug-overlay {
  position: fixed;
  bottom: 0;
  right: 0;
  z-index: 9999;
  pointer-events: auto;
}

/* Ensure the debug panel is always on top */
#root {
  isolation: isolate;
}

/* Debug Panel Reset */
.debug-overlay,
.debug-overlay * {
  all: revert;
  box-sizing: border-box;
}

.debug-overlay {
  position: fixed !important;
  bottom: 0 !important;
  right: 0 !important;
  z-index: 999999 !important;
  font-family: ui-monospace, monospace !important;
}

/* Mobile Stepper - ensure it stays above content but below overlays */
.mobile-stepper {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 500 !important;
  background-color: var(--surface) !important;
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
}

@media (min-width: 1024px) {
  .booking-form-container {
    padding-bottom: 0 !important;
  }
  
  /* Ensure consistent width for form content */
  .booking-form-container .bg-background > div {
    width: 100% !important;
    max-width: 100% !important;
  }
  
  /* Ensure form components take full width */
  .booking-form-container form,
  .booking-form-container .space-y-6 {
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Fix for tab content width */
  .booking-form-container [class*="min-h-screen"] {
    width: 100% !important;
    flex: 1 1 auto !important;
    min-width: 0 !important;
  }

  /* Fix for grid layout */
  .booking-form-container .lg\:grid {
    display: grid !important;
    width: 100% !important;
  }

  /* Fix for grid columns */
  .booking-form-container .lg\:grid-cols-\[450px\,1fr\] {
    grid-template-columns: 450px 1fr !important;
  }

  /* Fix for main content area */
  .booking-form-container .lg\:grid-cols-\[450px\,1fr\] > div:last-child {
    width: 100% !important;
    min-width: 0 !important;
  }
}

/* Fix for form width in all tabs */
.booking-form-container [data-tab="point-to-point"],
.booking-form-container [data-tab="hourly"],
.booking-form-container [data-tab="airport"] {
  width: 100% !important;
  max-width: 100% !important;
}

/* Toggle button styles */
.toggle-button {
  position: relative;
  display: inline-flex;
  height: 1.5rem;
  width: 2.75rem;
  align-items: center;
  border-radius: 9999px;
  transition-property: background-color;
  transition-duration: 200ms;
  transition-timing-function: ease-in-out;
}

.toggle-button[aria-checked="true"] {
  background-color: var(--primary) !important;
}

.toggle-button[aria-checked="false"] {
  background-color: var(--text-disabled) !important;
}

.toggle-thumb {
  position: absolute;
  height: 1rem;
  width: 1rem;
  background-color: white !important;
  border-radius: 9999px;
  transition-property: transform;
  transition-duration: 200ms;
  transition-timing-function: ease-in-out;
  transform: translateX(0.25rem);
}

.toggle-button[aria-checked="true"] .toggle-thumb {
  transform: translateX(1.5rem);
}

/* Force white background in all contexts */
.toggle-button .toggle-thumb,
[data-theme="light"] .toggle-thumb,
[data-theme="dark"] .toggle-thumb,
.bg-theme-primary .toggle-thumb,
[class*="bg-"] .toggle-thumb {
  background-color: white !important;
}

/* Map Markers */
.marker {
  width: 30px !important;
  height: 30px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  border: 2px solid #ffffff !important;
  box-shadow: 0 2px 4px var(--shadow) !important;
  transition: transform 0.2s !important;
  cursor: pointer !important;
  background-color: var(--primary) !important;
}

.marker:hover {
  transform: scale(1.1) !important;
}

.pickup-marker {
  background-color: var(--success) !important;
  z-index: calc(var(--z-map) + 1) !important;
}

.stop-marker {
  background-color: var(--primary) !important;
  z-index: calc(var(--z-map) + 2) !important;
}

.dropoff-marker {
  background-color: var(--error) !important;
  z-index: calc(var(--z-map) + 3) !important;
}

/* Map container styles */
.mapboxgl-map {
  position: absolute !important;
  inset: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background-color: var(--background) !important;
  min-height: 300px !important;
  display: block !important;
}

.mapboxgl-canvas-container {
  position: absolute !important;
  inset: 0 !important;
  width: 100% !important;
  height: 100% !important;
  min-height: 300px !important;
  display: block !important;
}

.mapboxgl-canvas {
  position: absolute !important;
  inset: 0 !important;
  width: 100% !important;
  height: 100% !important;
  min-height: 300px !important;
  display: block !important;
}

/* Mobile styles */
@media screen and (max-width: 768px) {

  .mapboxgl-map,
  .mapboxgl-canvas-container,
  .mapboxgl-canvas {
    height: 100% !important;
    min-height: 250px !important;
    width: 100% !important;
    display: block !important;
    position: absolute !important;
    inset: 0 !important;
  }

  /* Force parent containers to maintain height */
  .w-full.h-\[300px\] {
    min-height: 250px !important;
    height: 250px !important;
    display: block !important;
  }
}

/* Marker animations */
@keyframes markerPulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}

.marker.active {
  animation: markerPulse 1.5s infinite;
}

/* Add these new styles */
.animate-glow-pulse {
  animation: glow-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite !important;
  position: relative !important;
  z-index: 1 !important;
}

@keyframes glow-pulse {

  0%,
  100% {
    background-color: rgba(118, 90, 61, 0.1) !important;
    box-shadow: 0 0 0 0 rgba(118, 90, 61, 0.4) !important;
  }

  50% {
    background-color: rgba(118, 90, 61, 0.3) !important;
    box-shadow: 0 0 20px 0 rgba(118, 90, 61, 0.8) !important;
  }
}

/* Make the updating state more visible */
.bg-primary\/20 {
  background-color: rgba(118, 90, 61, 0.2) !important;
}

.ring-primary {
  box-shadow: 0 0 0 2px #765a3d !important;
}

/* Add a new class for the updating header */
.updating-header {
  position: relative !important;
  overflow: hidden !important;
}

.updating-header::before {
  content: '' !important;
  position: absolute !important;
  inset: 0 !important;
  background: linear-gradient(90deg,
      rgba(118, 90, 61, 0.1),
      rgba(118, 90, 61, 0.3),
      rgba(118, 90, 61, 0.1)) !important;
  animation: shimmer 2s infinite !important;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) !important;
  }

  100% {
    transform: translateX(100%) !important;
  }
}

/* Ensure the sticky header stays on top */
.sticky-header {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 9999 !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  border-bottom: 1px solid rgba(118, 90, 61, 0.2) !important;
  background-color: rgba(20, 20, 20, 0.95) !important;
}

/* Add styles for the ping animation */
.animate-ping {
  animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite !important;
}

@keyframes ping {
  0% {
    transform: scale(1) !important;
    opacity: 1 !important;
  }

  75%,
  100% {
    transform: scale(2) !important;
    opacity: 0 !important;
  }
}

/* Update datepicker z-index */
.react-datepicker-popper {
  z-index: var(--z-datepicker) !important;
}

/* Form inputs */
input,
select,
textarea {
  z-index: var(--z-inputs) !important;
}

/* Dropdowns and autocomplete */
.autocomplete-dropdown {
  z-index: var(--z-dropdown) !important;
}

/* Theme modal */
.theme-modal-backdrop {
  z-index: var(--z-modal-backdrop) !important;
}

.theme-modal {
  z-index: var(--z-theme-modal) !important;
}

/* Force white text in primary buttons and active states */
button.bg-primary,
[class*="bg-primary"],
.bg-primary,
button[class*="bg-primary"],
.booking-tabs button[class*="bg-primary"],
.booking-tabs button.bg-primary,
button[class*="next"],
button[class*="Next"],
button:has(> span:contains("Next")),
.booking-tabs button[data-active="true"],
.booking-tabs button[aria-selected="true"] {
  color: #ffffff !important;
}

button.bg-primary *,
[class*="bg-primary"] *,
.bg-primary *,
button[class*="bg-primary"] *,
.booking-tabs button[class*="bg-primary"] *,
.booking-tabs button.bg-primary *,
button[class*="next"] *,
button[class*="Next"] *,
button:has(> span:contains("Next")) *,
.booking-tabs button[data-active="true"] *,
.booking-tabs button[aria-selected="true"] * {
  color: #ffffff !important;
}

/* Force white icons in primary buttons and active states */
button.bg-primary svg,
[class*="bg-primary"] svg,
.bg-primary svg,
button[class*="bg-primary"] svg,
.booking-tabs button[class*="bg-primary"] svg,
.booking-tabs button.bg-primary svg,
button[class*="next"] svg,
button[class*="Next"] svg,
button:has(> span:contains("Next")) svg,
.booking-tabs button[data-active="true"] svg,
.booking-tabs button[aria-selected="true"] svg {
  color: #ffffff !important;
  fill: #ffffff !important;
  stroke: #ffffff !important;
}

/* Additional WordPress-specific overrides - Use namespaced classes instead */
/* See wordpress-plugin.css for the proper implementation with the limo-booking-plugin class */
/* 
Example usage:
<div class="limo-booking-plugin">
  <button class="limo-button limo-button-primary">Next</button>
  <div class="limo-tabs">
    <button class="limo-tab" data-active="true">Active Tab</button>
  </div>
</div>
*/

/* SVG icon overrides - Use namespaced classes instead */
/* See wordpress-plugin.css for the proper implementation with the limo-booking-plugin class */
/* 
Example usage for SVG icons:
<div class="limo-booking-plugin">
  <button class="limo-button limo-button-primary">
    <svg>...</svg> Next
  </button>
</div>
*/

/* Calendar/Datepicker styles */
.react-datepicker {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
}

.react-datepicker__header {
  background-color: var(--surface) !important;
  border-color: var(--border) !important;
}

.react-datepicker__current-month,
.react-datepicker-time__header,
.react-datepicker__day-name {
  color: var(--text-primary) !important;
}

.react-datepicker__day {
  color: var(--text-primary) !important;
}

.react-datepicker__day:hover {
  background-color: var(--primary) !important;
  color: #ffffff !important;
}

.react-datepicker__day--selected,
.react-datepicker__day--keyboard-selected {
  background-color: var(--primary) !important;
  color: #ffffff !important;
}

.react-datepicker__day--disabled {
  color: var(--text-disabled) !important;
}

.react-datepicker__time-container {
  border-color: var(--border) !important;
}

.react-datepicker__time-container .react-datepicker__time {
  background-color: var(--surface) !important;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item {
  color: var(--text-primary) !important;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item:hover {
  background-color: var(--primary) !important;
  color: #ffffff !important;
}

.react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list li.react-datepicker__time-list-item--selected {
  background-color: var(--primary) !important;
  color: #ffffff !important;
}

/* Next button styles */
button.bg-primary,
.bg-primary,
[class*="bg-primary"] {
  color: #ffffff !important;
}

/* Ensure icons in primary buttons are white */
button.bg-primary svg,
.bg-primary svg,
[class*="bg-primary"] svg {
  color: #ffffff !important;
  fill: #ffffff !important;
  stroke: #ffffff !important;
}

/* Force text colors in WordPress environment */
html[data-wp] .booking-tabs button.bg-primary,
html[data-wp] .booking-tabs button[aria-selected="true"],
html[data-wp] button.bg-primary,
html[data-wp] .bg-primary {
  color: #ffffff !important;
}

html[data-wp] .booking-tabs button.bg-primary *,
html[data-wp] .booking-tabs button[aria-selected="true"] *,
html[data-wp] button.bg-primary *,
html[data-wp] .bg-primary * {
  color: #ffffff !important;
}

/* Force white text in buttons with solid backgrounds */
html[data-wp] button.bg-primary,
html[data-wp] button[class*="bg-primary"],
html[data-wp] .bg-primary,
html[data-wp] [class*="bg-primary"],
html[data-wp] .booking-tabs button.bg-primary,
html[data-wp] .booking-tabs button[class*="bg-primary"] {
  color: #ffffff !important;
}

html[data-wp] button.bg-primary *,
html[data-wp] button[class*="bg-primary"] *,
html[data-wp] .bg-primary *,
html[data-wp] [class*="bg-primary"] *,
html[data-wp] .booking-tabs button.bg-primary *,
html[data-wp] .booking-tabs button[class*="bg-primary"] * {
  color: #ffffff !important;
}

/* Force white icons in buttons with solid backgrounds */
html[data-wp] button.bg-primary svg,
html[data-wp] button[class*="bg-primary"] svg,
html[data-wp] .bg-primary svg,
html[data-wp] [class*="bg-primary"] svg,
html[data-wp] .booking-tabs button.bg-primary svg,
html[data-wp] .booking-tabs button[class*="bg-primary"] svg {
  color: #ffffff !important;
  fill: #ffffff !important;
  stroke: #ffffff !important;
}

/* Ensure text and icons are white in active tab */
html[data-wp] .booking-tabs button[aria-selected="true"],
html[data-wp] .booking-tabs button[data-active="true"],
html[data-wp] .booking-tabs button[data-state="active"],
html[data-wp] .booking-tabs button.active {
  color: #ffffff !important;
}

html[data-wp] .booking-tabs button[aria-selected="true"] *,
html[data-wp] .booking-tabs button[data-active="true"] *,
html[data-wp] .booking-tabs button[data-state="active"] *,
html[data-wp] .booking-tabs button.active * {
  color: #ffffff !important;
}

html[data-wp] .booking-tabs button[aria-selected="true"] svg,
html[data-wp] .booking-tabs button[data-active="true"] svg,
html[data-wp] .booking-tabs button[data-state="active"] svg,
html[data-wp] .booking-tabs button.active svg {
  color: #ffffff !important;
  fill: #ffffff !important;
  stroke: #ffffff !important;
}

/* Force white text in Next button */
html[data-wp] button:has(> span:contains("Next")),
html[data-wp] button[class*="next"],
html[data-wp] button[class*="Next"] {
  color: #ffffff !important;
}

html[data-wp] button:has(> span:contains("Next")) *,
html[data-wp] button[class*="next"] *,
html[data-wp] button[class*="Next"] * {
  color: #ffffff !important;
}

/* Additional specificity for WordPress environment */
html[data-wp] .booking-form-container button.bg-primary,
html[data-wp] .booking-form-container [class*="bg-primary"],
html[data-wp] #limo-booking-form button.bg-primary,
html[data-wp] #limo-booking-form [class*="bg-primary"] {
  color: #ffffff !important;
}

html[data-wp] .booking-form-container button.bg-primary *,
html[data-wp] .booking-form-container [class*="bg-primary"] *,
html[data-wp] #limo-booking-form button.bg-primary *,
html[data-wp] #limo-booking-form [class*="bg-primary"] * {
  color: #ffffff !important;
}

/* Ensure all SVG icons in primary buttons are white */
html[data-wp] button.bg-primary svg,
html[data-wp] [class*="bg-primary"] svg,
html[data-wp] .booking-tabs button.bg-primary svg,
html[data-wp] .booking-tabs [class*="bg-primary"] svg {
  color: #ffffff !important;
  fill: #ffffff !important;
  stroke: #ffffff !important;
}

/* WordPress-specific fixes */
html[data-wp] .booking-form-container,
html[data-wp] .wordpress-form {
  width: 100% !important;
  max-width: 100% !important;
}

html[data-wp] .booking-form-container [data-main-content],
html[data-wp] .wordpress-form [data-main-content] {
  width: 100% !important;
  flex: 1 1 auto !important;
  min-width: 0 !important;
}

html[data-wp] .booking-form-container [data-tab],
html[data-wp] .wordpress-form [data-tab] {
  width: 100% !important;
  max-width: 100% !important;
}

/* Fix for tab switching */
.booking-form-container .lg\:grid,
.wordpress-form .lg\:grid {
  width: 100% !important;
}

/* Fix for form width in all tabs */
[data-tab="point-to-point"],
[data-tab="hourly"],
[data-tab="airport"],
[data-tab="multi-day"],
[data-tab="vehicle-selection"],
[data-tab="booking-confirmation"] {
  width: 100% !important;
  max-width: 100% !important;
}