/* Input field icon overlap fixes */

/* Force proper spacing for location inputs */
.original-booking-form input[placeholder*="pickup"],
.original-booking-form input[placeholder*="drop-off"],
.original-booking-form input[placeholder*="address"],
.original-booking-form input[placeholder*="location"] {
  padding-left: 3rem !important;
}

/* Force proper spacing for date inputs */
.original-booking-form input[placeholder*="date"],
.original-booking-form input[placeholder*="time"] {
  padding-left: 3rem !important;
}

/* Force proper icon positioning */
.original-booking-form .relative > svg[class*="MapPin"],
.original-booking-form .relative > svg[class*="Calendar"] {
  left: 1rem !important;
  position: absolute !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  pointer-events: none !important;
  z-index: 1 !important;
}

/* Specific overrides for the components */
.original-booking-form .relative input + svg {
  left: 1rem !important;
  position: absolute !important;
}

/* Vehicle grid fixes */
.original-booking-form .vehicle-selection-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
  gap: 1.5rem !important;
  width: 100% !important;
}

@media (min-width: 768px) {
  .original-booking-form .vehicle-selection-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 767px) {
  .original-booking-form .vehicle-selection-grid {
    grid-template-columns: 1fr !important;
  }
}
