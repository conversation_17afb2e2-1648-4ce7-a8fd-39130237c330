import React from 'react';

interface InputWithIconProps {
  children: React.ReactNode;
  icon: React.ReactNode;
  className?: string;
}

export const InputWithIcon: React.FC<InputWithIconProps> = ({ children, icon, className = '' }) => {
  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        {React.cloneElement(children as React.ReactElement, {
          style: { paddingLeft: '3rem' },
          className: `${(children as React.ReactElement).props.className || ''}`
        })}
        <div 
          className="absolute top-1/2 -translate-y-1/2 w-5 h-5 text-primary pointer-events-none"
          style={{ left: '1rem' }}
        >
          {icon}
        </div>
      </div>
    </div>
  );
};
