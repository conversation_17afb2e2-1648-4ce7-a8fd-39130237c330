import { MinusIcon, PlusIcon } from '@heroicons/react/24/outline';

interface DurationPickerProps {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
}

export const DurationPicker = ({ 
  value, 
  onChange, 
  min = 1, 
  max = 24 
}: DurationPickerProps) => {
  const handleIncrement = () => {
    if (value < max) onChange(value + 1);
  };

  const handleDecrement = () => {
    if (value > min) onChange(value - 1);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = parseInt(e.target.value);
    if (!isNaN(newValue) && newValue >= min && newValue <= max) {
      onChange(newValue);
    }
  };

  return (
    <div className="relative flex items-center w-full bg-[#1A1A1A] rounded-lg border border-[#765a3d]">
      <button
        onClick={handleDecrement}
        className="p-3 text-[#765a3d] hover:text-[#8b6b48] transition-colors"
        disabled={value <= min}
      >
        <MinusIcon className="w-5 h-5" />
      </button>
      
      <input
        type="number"
        value={value}
        onChange={handleInputChange}
        min={min}
        max={max}
        className="w-full bg-transparent text-white text-center focus:outline-none"
      />
      
      <button
        onClick={handleIncrement}
        className="p-3 text-[#765a3d] hover:text-[#8b6b48] transition-colors"
        disabled={value >= max}
      >
        <PlusIcon className="w-5 h-5" />
      </button>

      <div className="absolute -bottom-6 left-0 right-0 flex justify-between px-1 text-xs text-white/50">
        <span>Min: {min}hr</span>
        <span>Max: {max}hr</span>
      </div>
    </div>
  );
};