import { useState } from "react";
import { useFlightTracking } from "../../../../hooks/useFlightTracking";
import { PaperAirplaneIcon } from "@heroicons/react/24/outline";

export const FlightTracker = () => {
  const [flightNumber, setFlightNumber] = useState("");
  const { flightInfo, loading, error, trackFlight } = useFlightTracking();

  const handleTrackFlight = (e: React.FormEvent) => {
    e.preventDefault();
    if (flightNumber.trim()) {
      trackFlight(flightNumber.trim());
    }
  };

  return (
    <div className="mt-6 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-white font-medium">Flight Information</h3>
        <form onSubmit={handleTrackFlight} className="flex gap-2">
          <input
            type="text"
            value={flightNumber}
            onChange={(e) => setFlightNumber(e.target.value)}
            placeholder="Enter flight number"
            className="bg-[#1A1A1A] text-white px-3 py-2 rounded-lg focus:outline-none"
          />
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 bg-[#111827] text-white rounded-lg hover:bg-[#8b6b48] transition-colors disabled:opacity-50"
          >
            Track
          </button>
        </form>
      </div>

      {loading && (
        <div className="text-white/50">Loading flight information...</div>
      )}

      {error && (
        <div className="text-red-500 bg-red-500/10 p-3 rounded-lg">{error}</div>
      )}

      {flightInfo && (
        <div className="bg-[#1A1A1A] rounded-lg p-4 space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-white/70">Flight</span>
            <span className="text-white font-medium">
              {flightInfo.flightNumber}
            </span>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="text-white/70 text-sm">Departure</div>
              <div className="text-white">{flightInfo.departure.airport}</div>
              <div className="text-sm text-white/50">
                {new Date(
                  String(flightInfo.departure.scheduled)
                ).toLocaleString()}
              </div>
              {flightInfo.departure.terminal && (
                <div className="text-sm text-white/50">
                  Terminal {flightInfo.departure.terminal}
                  {flightInfo.departure.gate &&
                    `, Gate ${flightInfo.departure.gate}`}
                </div>
              )}
            </div>

            <PaperAirplaneIcon className="w-5 h-5 text-[#111827]" />

            <div className="flex-1">
              <div className="text-white/70 text-sm">Arrival</div>
              <div className="text-white">{flightInfo.arrival.airport}</div>
              <div className="text-sm text-white/50">
                {new Date(
                  String(flightInfo.arrival.scheduled)
                ).toLocaleString()}
              </div>
              {flightInfo.arrival.terminal && (
                <div className="text-sm text-white/50">
                  Terminal {flightInfo.arrival.terminal}
                  {flightInfo.arrival.gate &&
                    `, Gate ${flightInfo.arrival.gate}`}
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center justify-between border-t border-white/10 pt-3">
            <span className="text-white/70">Status</span>
            <span
              className={`px-2 py-1 rounded text-sm ${
                flightInfo.status.text === "active"
                  ? "bg-green-500/20 text-green-500"
                  : flightInfo.status.text === "scheduled"
                  ? "bg-blue-500/20 text-blue-500"
                  : "bg-red-500/20 text-red-500"
              }`}
            >
              {flightInfo.status.text.toUpperCase()}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};
