.geocoder-container {
  position: relative;
  isolation: isolate;
}

.mapboxgl-ctrl-geocoder {
  width: 100% !important;
  max-width: none !important;
  background-color: #1A1A1A !important;
  border-radius: 0.5rem !important;
  box-shadow: none !important;
  position: relative;
}

.mapboxgl-ctrl-geocoder input {
  color: white !important;
  background-color: transparent !important;
  padding-left: 2.5rem !important;
  height: 46px !important;
}

.mapboxgl-ctrl-geocoder input::placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
}

.mapboxgl-ctrl-geocoder--icon {
  display: none !important;
}

.mapboxgl-ctrl-geocoder.focused::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: -1;
  animation: fadeIn 0.2s ease-out;
}

.mapboxgl-ctrl-geocoder.focused {
  position: relative;
  isolation: isolate;
  z-index: 10000 !important;
}

.mapboxgl-ctrl-geocoder .suggestions {
  position: absolute !important;
  background-color: #1A1A1A !important;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  margin-top: 4px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 10000 !important;
  isolation: isolate;
}

.mapboxgl-ctrl-geocoder .suggestions>li>a {
  color: white !important;
  padding: 8px 12px;
  transition: background-color 0.2s ease;
}

.mapboxgl-ctrl-geocoder .suggestions>.active>a,
.mapboxgl-ctrl-geocoder .suggestions>li>a:hover {
  background-color: #111827 !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}