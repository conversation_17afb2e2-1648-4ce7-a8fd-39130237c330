import { FlightDetails } from "../../../../types/flight";

interface FlightInfoDisplayProps {
  flightInfo: FlightDetails;
}

export const FlightInfoDisplay = ({ flightInfo }: FlightInfoDisplayProps) => {
  return (
    <div className="bg-[#1A1A1A] rounded-lg p-4 border border-[#111827]/20">
      {/* Flight Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <span className="text-white font-medium">{flightInfo.airline}</span>
          <span className="text-white/50">•</span>
          <span className="text-[#111827]">{flightInfo.flightNumber}</span>
        </div>
        <span className="px-3 py-1 rounded-full text-sm bg-green-500/10 text-green-500">
          {flightInfo.status}
        </span>
      </div>

      {/* Flight Route */}
      <div className="grid grid-cols-2 gap-6 mb-4">
        <div>
          <div className="text-white/50 text-sm mb-1">Departure</div>
          <div className="text-white font-medium">
            {flightInfo.departure.airport}
          </div>
          <div className="text-[#111827] text-sm">
            Terminal {flightInfo.departure.terminal} • Gate{" "}
            {flightInfo.departure.gate}
          </div>
          <div className="text-white/70 text-sm mt-1">
            {flightInfo.departure.time}
          </div>
          <div className="text-white/50 text-sm mt-2">
            {flightInfo.weather.departure.temp} •{" "}
            {flightInfo.weather.departure.condition}
          </div>
        </div>
        <div>
          <div className="text-white/50 text-sm mb-1">Arrival</div>
          <div className="text-white font-medium">
            {flightInfo.arrival.airport}
          </div>
          <div className="text-[#111827] text-sm">
            Terminal {flightInfo.arrival.terminal} • Gate{" "}
            {flightInfo.arrival.gate}
          </div>
          <div className="text-white/70 text-sm mt-1">
            {flightInfo.arrival.time}
          </div>
          <div className="text-white/50 text-sm mt-2">
            {flightInfo.weather.arrival.temp} •{" "}
            {flightInfo.weather.arrival.condition}
          </div>
        </div>
      </div>

      {/* Flight Details */}
      <div className="grid grid-cols-4 gap-4 pt-4 border-t border-white/10">
        <div>
          <div className="text-white/50 text-sm mb-1">Aircraft</div>
          <div className="text-white text-sm">{flightInfo.aircraft}</div>
        </div>
        <div>
          <div className="text-white/50 text-sm mb-1">Duration</div>
          <div className="text-white text-sm">{flightInfo.duration}</div>
        </div>
        <div>
          <div className="text-white/50 text-sm mb-1">Distance</div>
          <div className="text-white text-sm">{flightInfo.distance}</div>
        </div>
        <div>
          <div className="text-white/50 text-sm mb-1">Baggage Claim</div>
          <div className="text-white text-sm">{flightInfo.baggageClaim}</div>
        </div>
      </div>

      {/* On-Time Performance */}
      <div className="mt-4 pt-4 border-t border-white/10">
        <div className="flex items-center justify-between">
          <span className="text-white/50 text-sm">On-Time Performance</span>
          <div className="flex items-center space-x-2">
            <div className="w-32 h-2 bg-white/10 rounded-full overflow-hidden">
              <div
                className="h-full bg-[#111827] rounded-full"
                style={{ width: `${flightInfo.onTimePerformance}%` }}
              />
            </div>
            <span className="text-white text-sm">
              {flightInfo.onTimePerformance}%
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
