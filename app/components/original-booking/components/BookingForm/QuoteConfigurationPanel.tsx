import { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon, CogIcon, UserGroupIcon } from '@heroicons/react/24/outline';
import { useBookingStore } from '../../store/bookingStore';
import { EventSelection } from './EventSelection';
import { PassengerSelection } from './PassengerSelection';
import { cn } from '../../lib/utils';

export const QuoteConfigurationPanel = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const store = useBookingStore();

  return (
    <div className="mb-6 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl shadow-sm">
      {/* Header */}
      <div 
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-blue-50/50 transition-colors rounded-t-xl"
        onClick={() => setIsCollapsed(!isCollapsed)}
      >
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center">
            <CogIcon className="w-4 h-4 text-blue-600" />
          </div>
          <div>
            <h3 className="text-sm font-semibold text-gray-900">Quote Configuration</h3>
            <p className="text-xs text-gray-500">Event linking and passenger selection</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Quick Status Indicators */}
          <div className="flex items-center gap-1 text-xs text-gray-500">
            <span className={cn(
              "w-2 h-2 rounded-full",
              store.pointToPointData.isStandalone ? "bg-green-400" : "bg-blue-400"
            )}></span>
            <span>{store.pointToPointData.isStandalone ? "Standalone" : "Event Linked"}</span>
          </div>
          
          <div className="flex items-center gap-1 text-xs text-gray-500">
            <UserGroupIcon className="w-3 h-3" />
            <span>{(store.pointToPointData.passengerIds || []).length} selected</span>
          </div>
          
          <button className="p-1 hover:bg-blue-100 rounded transition-colors">
            {isCollapsed ? (
              <ChevronDownIcon className="w-4 h-4 text-gray-400" />
            ) : (
              <ChevronUpIcon className="w-4 h-4 text-gray-400" />
            )}
          </button>
        </div>
      </div>

      {/* Collapsible Content */}
      <div className={cn(
        "overflow-hidden transition-all duration-300 ease-in-out",
        isCollapsed ? "max-h-0" : "max-h-96"
      )}>
        <div className="p-4 pt-0 space-y-4 border-t border-blue-100">
          {/* Compact Event Selection */}
          <div className="bg-white rounded-lg p-3 border border-blue-100">
            <EventSelection
              selectedEventId={store.pointToPointData.eventId || null}
              isStandalone={store.pointToPointData.isStandalone ?? true}
              onEventChange={(eventId) => {
                store.updatePointToPointData({ eventId });
              }}
              onStandaloneChange={(isStandalone) => {
                store.updatePointToPointData({ isStandalone });
              }}
              compact={true}
            />
          </div>

          {/* Compact Passenger Selection */}
          <div className="bg-white rounded-lg p-3 border border-blue-100">
            <PassengerSelection
              selectedPassengers={store.pointToPointData.passengerIds || []}
              onPassengersChange={(passengerIds) => {
                store.updatePointToPointData({ passengerIds });
              }}
              maxPassengers={20}
              compact={true}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
