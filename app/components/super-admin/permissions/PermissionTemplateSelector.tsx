"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { But<PERSON> } from "@/app/components/ui/button"
import { Badge } from "@/app/components/ui/badge"
import { RadioGroup, RadioGroupItem } from "@/app/components/ui/radio-group"
import { Label } from "@/app/components/ui/label"
import { Textarea } from "@/app/components/ui/textarea"
import { AlertTriangle, CheckCircle2, Users, Building, Stethoscope, Car } from "lucide-react"

interface PermissionTemplate {
  id: string
  name: string
  description: string
  template_type: 'subscription_tier' | 'role_based' | 'custom'
  permissions: Record<string, any>
  ui_customizations: Record<string, any>
}

interface PermissionTemplateSelectorProps {
  selectedTemplateId?: string
  onTemplateSelect: (template: PermissionTemplate | null) => void
  onNotesChange: (notes: string) => void
  notes?: string
}

export function PermissionTemplateSelector({
  selectedTemplateId,
  onTemplateSelect,
  onNotesChange,
  notes = ""
}: PermissionTemplateSelectorProps) {
  const [templates, setTemplates] = useState<PermissionTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setLoading(true)
        const response = await fetch('/api/super-admin/permission-templates')
        
        if (!response.ok) {
          throw new Error('Failed to fetch permission templates')
        }
        
        const data = await response.json()
        setTemplates(data.templates || [])
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load templates')
        console.error('Error fetching permission templates:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchTemplates()
  }, [])

  const getTemplateIcon = (templateType: string, templateName: string) => {
    if (templateName.toLowerCase().includes('medical')) {
      return <Stethoscope className="h-5 w-5 text-blue-500" />
    }
    if (templateName.toLowerCase().includes('tnc')) {
      return <Car className="h-5 w-5 text-green-500" />
    }
    if (templateType === 'role_based') {
      return <Users className="h-5 w-5 text-purple-500" />
    }
    return <Building className="h-5 w-5 text-gray-500" />
  }

  const getTemplateVariant = (templateType: string) => {
    switch (templateType) {
      case 'subscription_tier': return 'default'
      case 'role_based': return 'secondary'
      case 'custom': return 'outline'
      default: return 'secondary'
    }
  }

  const handleTemplateChange = (templateId: string) => {
    if (templateId === 'none') {
      onTemplateSelect(null)
      return
    }
    
    const template = templates.find(t => t.id === templateId)
    if (template) {
      onTemplateSelect(template)
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Permission Templates</CardTitle>
          <CardDescription>Loading available templates...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Permission Templates</CardTitle>
          <CardDescription>Error loading templates</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-4 w-4" />
            <span className="text-sm">{error}</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Permission Templates</CardTitle>
        <CardDescription>
          Choose a pre-configured template or start with custom permissions
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <RadioGroup 
          value={selectedTemplateId || 'none'} 
          onValueChange={handleTemplateChange}
        >
          {/* No Template Option */}
          <div className="flex items-center space-x-2 p-3 border rounded-lg">
            <RadioGroupItem value="none" id="none" />
            <Label htmlFor="none" className="flex-1 cursor-pointer">
              <div className="flex items-center gap-3">
                <CheckCircle2 className="h-5 w-5 text-gray-400" />
                <div>
                  <div className="font-medium">Custom Permissions</div>
                  <div className="text-sm text-muted-foreground">
                    Configure permissions manually without a template
                  </div>
                </div>
              </div>
            </Label>
          </div>

          {/* Template Options */}
          {templates.map((template) => (
            <div key={template.id} className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value={template.id} id={template.id} />
              <Label htmlFor={template.id} className="flex-1 cursor-pointer">
                <div className="flex items-center gap-3">
                  {getTemplateIcon(template.template_type, template.name)}
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{template.name}</span>
                      <Badge variant={getTemplateVariant(template.template_type)}>
                        {template.template_type.replace('_', ' ')}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {template.description}
                    </div>
                    
                    {/* Preview key permissions */}
                    <div className="flex flex-wrap gap-1 mt-2">
                      {Object.entries(template.permissions || {})
                        .filter(([_, value]) => value === true)
                        .slice(0, 4)
                        .map(([key, _]) => (
                          <Badge key={key} variant="outline" className="text-xs">
                            {key.replace(/[._]/g, ' ')}
                          </Badge>
                        ))}
                      {Object.keys(template.permissions || {}).length > 4 && (
                        <Badge variant="outline" className="text-xs">
                          +{Object.keys(template.permissions || {}).length - 4} more
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </Label>
            </div>
          ))}
        </RadioGroup>

        {/* Notes Section */}
        <div className="space-y-2">
          <Label htmlFor="permission-notes">Notes (Optional)</Label>
          <Textarea
            id="permission-notes"
            placeholder="Add notes about why these permissions were assigned..."
            value={notes}
            onChange={(e) => onNotesChange(e.target.value)}
            rows={3}
          />
        </div>
      </CardContent>
    </Card>
  )
}
