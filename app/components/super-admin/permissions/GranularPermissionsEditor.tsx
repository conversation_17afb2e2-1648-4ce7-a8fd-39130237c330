"use client";

import { useState } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";
import { Switch } from "@/app/components/ui/switch";
import { Label } from "@/app/components/ui/label";
import { Badge } from "@/app/components/ui/badge";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@/app/components/ui/tabs";
import { Separator } from "@/app/components/ui/separator";
import {
  FileText,
  Calendar,
  Car,
  Users,
  BarChart3,
  DollarSign,
  Eye,
  EyeOff,
  Palette,
  Shield,
  Building,
  Stethoscope,
} from "lucide-react";

interface PermissionConfig {
  feature_permissions: Record<string, boolean>;
  ui_customizations: Record<string, "visible" | "hidden" | "restricted">;
  access_controls: Record<string, any>;
}

interface GranularPermissionsEditorProps {
  permissions: PermissionConfig;
  onChange: (permissions: PermissionConfig) => void;
  disabled?: boolean;
}

// Define permission categories and their permissions
const PERMISSION_CATEGORIES = {
  quotes: {
    icon: FileText,
    label: "Quotes & Bookings",
    color: "text-blue-500",
    permissions: {
      "quotes.view": "View quotes",
      "quotes.create": "Create new quotes",
      "quotes.edit": "Edit existing quotes",
      "quotes.delete": "Delete quotes",
      "quotes.send_to_affiliates": "Send quotes to affiliates",
      "quotes.fixed_rate": "Set fixed rates for quotes",
      "quotes.accept": "Accept quotes on behalf of organization",
      "quotes.reject": "Reject quotes on behalf of organization",
      "quotes.archive": "Archive quotes",
      "quotes.correct": "Correct quote details (admin function)",
      "quotes.reorder_affiliates": "Reorder affiliate submission priority",
      "quotes.add_affiliates": "Add more affiliates to existing quotes",
      "quotes.request_changes": "Request changes to quotes",
      "quotes.assign": "Manually assign quotes to affiliates",
      "quotes.export": "Export quote data",
    },
  },
  trips: {
    icon: Car,
    label: "Trip Management",
    color: "text-green-500",
    permissions: {
      "trips.view": "View trips",
      "trips.manage": "Manage trip details",
      "trips.assign_driver": "Assign drivers",
      "trips.track": "Track live trips",
      "trips.complete": "Mark trips complete",
    },
  },
  events: {
    icon: Calendar,
    label: "Event Management",
    color: "text-purple-500",
    permissions: {
      "events.view": "View events",
      "events.create": "Create events",
      "events.edit": "Edit events",
      "events.delete": "Delete events",
      "events.manage_quotes": "Manage event quotes",
    },
  },
  passengers: {
    icon: Users,
    label: "Passenger Management",
    color: "text-orange-500",
    permissions: {
      "passengers.view": "View passenger data",
      "passengers.create": "Add new passengers",
      "passengers.edit": "Edit passenger info",
      "passengers.delete": "Remove passengers",
      "passengers.export": "Export passenger data",
    },
  },
  affiliates: {
    icon: Building,
    label: "Affiliate Operations",
    color: "text-indigo-500",
    permissions: {
      "affiliates.view": "View affiliates",
      "affiliates.select": "Select affiliates for quotes",
      "affiliates.manage": "Manage affiliate relationships",
      "affiliates.negotiate": "Negotiate rates",
      "affiliates.view_rates": "View affiliate rates",
      "affiliates.network_management": "Manage network affiliates (TNC)",
      "affiliates.set_network_rates": "Set network-wide rates",
      "affiliates.approve": "Approve affiliate applications",
      "affiliates.contact": "Contact affiliates directly",
      "affiliates.performance": "View affiliate performance metrics",
    },
  },
  analytics: {
    icon: BarChart3,
    label: "Analytics & Reports",
    color: "text-cyan-500",
    permissions: {
      "analytics.basic": "Basic analytics",
      "analytics.advanced": "Advanced analytics",
      "analytics.financial": "Financial reports",
      "analytics.export": "Export reports",
      "analytics.medical": "Medical transport analytics",
    },
  },
  financial: {
    icon: DollarSign,
    label: "Financial Access",
    color: "text-emerald-500",
    permissions: {
      "financial.view_costs": "View service costs",
      "financial.view_rates": "View detailed rates",
      "financial.view_profit": "View profit margins",
      "financial.manage_billing": "Manage billing",
    },
  },
  system: {
    icon: Shield,
    label: "System & Access",
    color: "text-red-500",
    permissions: {
      "customers.register": "Customer registration",
      "system.white_label": "White label branding",
      "compliance.hipaa": "HIPAA compliance features",
      "admin.user_management": "User management",
      "admin.system_settings": "System settings",
      "admin.impersonate": "Impersonate other users",
    },
  },
};

// UI Customization options
const UI_CUSTOMIZATIONS = {
  sidebar: {
    label: "Sidebar Elements",
    options: {
      "sidebar.affiliates": "Affiliates menu",
      "sidebar.financial_reports": "Financial reports",
      "sidebar.analytics": "Analytics menu",
      "sidebar.admin": "Admin functions",
    },
  },
  pages: {
    label: "Page Access",
    options: {
      "page.affiliate_management": "Affiliate management page",
      "page.advanced_analytics": "Advanced analytics",
      "page.financial_dashboard": "Financial dashboard",
      "page.customer_registration": "Customer registration",
      "page.quote_management": "Advanced quote management",
      "page.user_management": "User management (Super Admin)",
    },
  },
  sections: {
    label: "Page Sections",
    options: {
      "section.affiliate_rates": "Affiliate rate information",
      "section.cost_breakdown": "Detailed cost breakdown",
      "section.profit_margins": "Profit margin data",
      "section.hipaa_compliance": "HIPAA compliance tools",
      "section.quote_ctas": "Quote action buttons (CTAs)",
      "section.affiliate_selection": "Affiliate selection controls",
      "section.rate_negotiation": "Rate negotiation interface",
    },
  },
  branding: {
    label: "Branding & Customization",
    options: {
      "branding.custom": "Custom branding",
      "branding.logo": "Custom logo",
      "branding.colors": "Custom colors",
      "branding.white_label": "Full white label",
    },
  },
};

export function GranularPermissionsEditor({
  permissions,
  onChange,
  disabled = false,
}: GranularPermissionsEditorProps) {
  const updateFeaturePermission = (key: string, value: boolean) => {
    onChange({
      ...permissions,
      feature_permissions: {
        ...permissions.feature_permissions,
        [key]: value,
      },
    });
  };

  const updateUICustomization = (
    key: string,
    value: "visible" | "hidden" | "restricted"
  ) => {
    onChange({
      ...permissions,
      ui_customizations: {
        ...permissions.ui_customizations,
        [key]: value,
      },
    });
  };

  const updateAccessControl = (key: string, value: any) => {
    onChange({
      ...permissions,
      access_controls: {
        ...permissions.access_controls,
        [key]: value,
      },
    });
  };

  const getVisibilityIcon = (visibility: string) => {
    switch (visibility) {
      case "hidden":
        return <EyeOff className="h-4 w-4 text-red-500" />;
      case "restricted":
        return <Shield className="h-4 w-4 text-yellow-500" />;
      default:
        return <Eye className="h-4 w-4 text-green-500" />;
    }
  };

  const getVisibilityColor = (visibility: string) => {
    switch (visibility) {
      case "hidden":
        return "destructive";
      case "restricted":
        return "secondary";
      default:
        return "default";
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Granular Permissions</CardTitle>
        <CardDescription>
          Configure detailed permissions and UI customizations
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="features" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="features">Feature Permissions</TabsTrigger>
            <TabsTrigger value="ui">UI Customizations</TabsTrigger>
            <TabsTrigger value="access">Access Controls</TabsTrigger>
          </TabsList>

          <TabsContent value="features" className="space-y-6">
            {Object.entries(PERMISSION_CATEGORIES).map(
              ([categoryKey, category]) => {
                const Icon = category.icon;
                return (
                  <Card key={categoryKey}>
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-2 text-base">
                        <Icon className={`h-5 w-5 ${category.color}`} />
                        {category.label}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {Object.entries(category.permissions).map(
                        ([permKey, permLabel]) => (
                          <div
                            key={permKey}
                            className="flex items-center justify-between"
                          >
                            <Label
                              htmlFor={permKey}
                              className="text-sm font-normal"
                            >
                              {permLabel}
                            </Label>
                            <Switch
                              id={permKey}
                              checked={
                                permissions.feature_permissions[permKey] ||
                                false
                              }
                              onCheckedChange={(checked) =>
                                updateFeaturePermission(permKey, checked)
                              }
                              disabled={disabled}
                            />
                          </div>
                        )
                      )}
                    </CardContent>
                  </Card>
                );
              }
            )}
          </TabsContent>

          <TabsContent value="ui" className="space-y-6">
            {Object.entries(UI_CUSTOMIZATIONS).map(([sectionKey, section]) => (
              <Card key={sectionKey}>
                <CardHeader className="pb-3">
                  <CardTitle className="text-base">{section.label}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {Object.entries(section.options).map(([uiKey, uiLabel]) => {
                    const currentValue =
                      permissions.ui_customizations[uiKey] || "visible";
                    return (
                      <div
                        key={uiKey}
                        className="flex items-center justify-between"
                      >
                        <Label className="text-sm font-normal">{uiLabel}</Label>
                        <div className="flex items-center gap-2">
                          {getVisibilityIcon(currentValue)}
                          <select
                            value={currentValue}
                            onChange={(e) =>
                              updateUICustomization(
                                uiKey,
                                e.target.value as any
                              )
                            }
                            disabled={disabled}
                            className="text-sm border rounded px-2 py-1"
                          >
                            <option value="visible">Visible</option>
                            <option value="restricted">Restricted</option>
                            <option value="hidden">Hidden</option>
                          </select>
                        </div>
                      </div>
                    );
                  })}
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="access" className="space-y-6">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-base">
                  Access Control Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">
                      White Label Access
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      Allow custom branding and white label features
                    </p>
                  </div>
                  <Switch
                    checked={permissions.access_controls.white_label || false}
                    onCheckedChange={(checked) =>
                      updateAccessControl("white_label", checked)
                    }
                    disabled={disabled}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">
                      Cross-Tenant Access
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      Access data across multiple tenants
                    </p>
                  </div>
                  <Switch
                    checked={permissions.access_controls.cross_tenant || false}
                    onCheckedChange={(checked) =>
                      updateAccessControl("cross_tenant", checked)
                    }
                    disabled={disabled}
                  />
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">Data Export</Label>
                    <p className="text-xs text-muted-foreground">
                      Export data and generate reports
                    </p>
                  </div>
                  <Switch
                    checked={permissions.access_controls.data_export || false}
                    onCheckedChange={(checked) =>
                      updateAccessControl("data_export", checked)
                    }
                    disabled={disabled}
                  />
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label className="text-sm font-medium">Data Scope</Label>
                  <select
                    value={
                      permissions.access_controls.data_scope || "organization"
                    }
                    onChange={(e) =>
                      updateAccessControl("data_scope", e.target.value)
                    }
                    disabled={disabled}
                    className="w-full text-sm border rounded px-3 py-2"
                  >
                    <option value="user">User Level Only</option>
                    <option value="organization">Organization Level</option>
                    <option value="tenant">Tenant Level</option>
                    <option value="global">Global Access</option>
                  </select>
                  <p className="text-xs text-muted-foreground">
                    Defines the scope of data this user can access
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
