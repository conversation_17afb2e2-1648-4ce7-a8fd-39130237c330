"use client";

import React, { useState } from 'react';
import { Button } from '@/app/components/ui/button';
import { Input } from '@/app/components/ui/input';
import { Label } from '@/app/components/ui/label';
import { Textarea } from '@/app/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Badge } from '@/app/components/ui/badge';
import { Separator } from '@/app/components/ui/separator';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select';
import { 
  Save, 
  X, 
  AlertTriangle, 
  Clock, 
  MapPin, 
  Car, 
  Users,
  Calendar
} from 'lucide-react';
import { useToast } from '@/app/components/ui/use-toast';

interface QuoteEditPanelProps {
  quote: any;
  isEmergencyEdit?: boolean;
  onSave: (updatedQuote: any) => void;
  onCancel: () => void;
  userRole: 'SUPER_ADMIN' | 'TNC_ADMIN' | 'CLIENT' | 'SUPER_CLIENT';
}

export function QuoteEditPanel({
  quote,
  isEmergencyEdit = false,
  onSave,
  onCancel,
  userRole
}: QuoteEditPanelProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [editedQuote, setEditedQuote] = useState({
    pickup_location: quote.pickup_location || '',
    dropoff_location: quote.dropoff_location || '',
    date: quote.date || '',
    time: quote.time || '',
    vehicle_type: quote.vehicle_type || '',
    passenger_count: quote.passenger_count || 1,
    luggage_count: quote.luggage_count || 0,
    special_requests: quote.special_requests?.join(', ') || '',
    city: quote.city || '',
    service_type: quote.service_type || 'airport',
    priority: quote.priority || 'medium'
  });

  const [changes, setChanges] = useState<string[]>([]);

  const handleFieldChange = (field: string, value: any) => {
    setEditedQuote(prev => ({ ...prev, [field]: value }));
    
    // Track changes for audit
    if (quote[field] !== value && !changes.includes(field)) {
      setChanges(prev => [...prev, field]);
    }
  };

  const handleSave = async () => {
    if (changes.length === 0) {
      toast({
        title: "No Changes",
        description: "No changes were made to the quote.",
        variant: "default"
      });
      return;
    }

    setIsLoading(true);
    try {
      // Prepare the updated quote data
      const updatedQuote = {
        ...quote,
        ...editedQuote,
        special_requests: editedQuote.special_requests 
          ? editedQuote.special_requests.split(',').map(s => s.trim()).filter(Boolean)
          : [],
        updated_at: new Date().toISOString(),
        last_modified_by: userRole,
        modification_reason: isEmergencyEdit ? 'Emergency correction' : 'Standard edit',
        modified_fields: changes
      };

      await onSave(updatedQuote);
      
      toast({
        title: isEmergencyEdit ? "Emergency Correction Applied" : "Quote Updated",
        description: `Successfully updated ${changes.length} field(s).`,
        variant: "default"
      });
    } catch (error) {
      toast({
        title: "Update Failed",
        description: "Failed to update the quote. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getEditCapabilities = () => {
    switch (userRole) {
      case 'SUPER_ADMIN':
        return {
          canEditAll: true,
          canChangeStatus: true,
          canCorrectErrors: true,
          label: 'Emergency Correction'
        };
      case 'TNC_ADMIN':
        return {
          canEditAll: true,
          canChangeStatus: false,
          canCorrectErrors: true,
          label: 'Network Edit'
        };
      case 'SUPER_CLIENT':
        return {
          canEditAll: false,
          canChangeStatus: false,
          canCorrectErrors: false,
          label: 'Client Edit'
        };
      default:
        return {
          canEditAll: false,
          canChangeStatus: false,
          canCorrectErrors: false,
          label: 'Basic Edit'
        };
    }
  };

  const capabilities = getEditCapabilities();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold flex items-center gap-2">
            {isEmergencyEdit && <AlertTriangle className="h-5 w-5 text-orange-500" />}
            {capabilities.label}
          </h3>
          <p className="text-sm text-muted-foreground">
            {isEmergencyEdit 
              ? "Emergency correction mode - all fields editable"
              : "Edit quote details as permitted by your role"
            }
          </p>
        </div>
        <Badge variant={isEmergencyEdit ? "destructive" : "secondary"}>
          {quote.status}
        </Badge>
      </div>

      {isEmergencyEdit && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="pt-4">
            <div className="flex items-center gap-2 text-orange-700">
              <AlertTriangle className="h-4 w-4" />
              <span className="text-sm font-medium">Emergency Edit Mode</span>
            </div>
            <p className="text-sm text-orange-600 mt-1">
              This is an emergency correction. All changes will be logged for audit purposes.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Edit Form */}
      <div className="space-y-6">
        {/* Trip Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Trip Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="pickup">Pickup Location</Label>
                <Input
                  id="pickup"
                  value={editedQuote.pickup_location}
                  onChange={(e) => handleFieldChange('pickup_location', e.target.value)}
                  disabled={!capabilities.canEditAll && !isEmergencyEdit}
                />
              </div>
              <div>
                <Label htmlFor="dropoff">Dropoff Location</Label>
                <Input
                  id="dropoff"
                  value={editedQuote.dropoff_location}
                  onChange={(e) => handleFieldChange('dropoff_location', e.target.value)}
                  disabled={!capabilities.canEditAll && !isEmergencyEdit}
                />
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="date">Date</Label>
                <Input
                  id="date"
                  type="date"
                  value={editedQuote.date}
                  onChange={(e) => handleFieldChange('date', e.target.value)}
                  disabled={!capabilities.canEditAll && !isEmergencyEdit}
                />
              </div>
              <div>
                <Label htmlFor="time">Time</Label>
                <Input
                  id="time"
                  type="time"
                  value={editedQuote.time}
                  onChange={(e) => handleFieldChange('time', e.target.value)}
                  disabled={!capabilities.canEditAll && !isEmergencyEdit}
                />
              </div>
              <div>
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={editedQuote.city}
                  onChange={(e) => handleFieldChange('city', e.target.value)}
                  disabled={!capabilities.canEditAll && !isEmergencyEdit}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Vehicle & Passengers */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Car className="h-4 w-4" />
              Vehicle & Passengers
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="vehicle_type">Vehicle Type</Label>
                <Select
                  value={editedQuote.vehicle_type}
                  onValueChange={(value) => handleFieldChange('vehicle_type', value)}
                  disabled={!capabilities.canEditAll && !isEmergencyEdit}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sedan">Sedan</SelectItem>
                    <SelectItem value="suv">SUV</SelectItem>
                    <SelectItem value="luxury">Luxury</SelectItem>
                    <SelectItem value="van">Van</SelectItem>
                    <SelectItem value="bus">Bus</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="passengers">Passengers</Label>
                <Input
                  id="passengers"
                  type="number"
                  min="1"
                  value={editedQuote.passenger_count}
                  onChange={(e) => handleFieldChange('passenger_count', parseInt(e.target.value))}
                  disabled={!capabilities.canEditAll && !isEmergencyEdit}
                />
              </div>
              <div>
                <Label htmlFor="luggage">Luggage Count</Label>
                <Input
                  id="luggage"
                  type="number"
                  min="0"
                  value={editedQuote.luggage_count}
                  onChange={(e) => handleFieldChange('luggage_count', parseInt(e.target.value))}
                  disabled={!capabilities.canEditAll && !isEmergencyEdit}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Special Requests */}
        <Card>
          <CardHeader>
            <CardTitle>Special Requests</CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder="Enter special requests (comma-separated)"
              value={editedQuote.special_requests}
              onChange={(e) => handleFieldChange('special_requests', e.target.value)}
              disabled={!capabilities.canEditAll && !isEmergencyEdit}
              rows={3}
            />
          </CardContent>
        </Card>

        {/* Changes Summary */}
        {changes.length > 0 && (
          <Card className="border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="text-blue-700">Changes Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {changes.map((field) => (
                  <Badge key={field} variant="outline" className="text-blue-700">
                    {field.replace(/_/g, ' ')}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between pt-4 border-t">
        <Button
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          <X className="h-4 w-4 mr-2" />
          Cancel
        </Button>
        
        <Button
          onClick={handleSave}
          disabled={isLoading || changes.length === 0}
          className={isEmergencyEdit ? "bg-orange-600 hover:bg-orange-700" : ""}
        >
          <Save className="h-4 w-4 mr-2" />
          {isLoading ? "Saving..." : "Save Changes"}
        </Button>
      </div>
    </div>
  );
}
