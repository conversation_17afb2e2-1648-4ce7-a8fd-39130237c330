"use client";

import React from 'react';
import { Button } from '@/app/components/ui/button';
import { Badge } from '@/app/components/ui/badge';
import { 
  Send, 
  DollarSign, 
  CheckCircle, 
  XCircle, 
  MessageSquare, 
  Phone, 
  Archive, 
  Edit3, 
  UserPlus, 
  RotateCcw,
  AlertTriangle,
  Settings
} from 'lucide-react';
import { getQuoteActionPermissions, getUIVisibility } from '@/app/utils/quote-permissions';
import type { QuotePermissionContext } from '@/app/utils/quote-permissions';

interface PermissionBasedCTAsProps {
  quote: any;
  userContext: QuotePermissionContext;
  onAction: (action: string, data?: any) => void;
  variant?: 'row' | 'panel';
  className?: string;
}

export function PermissionBasedCTAs({
  quote,
  userContext,
  onAction,
  variant = 'row',
  className = ''
}: PermissionBasedCTAsProps) {
  const permissions = getQuoteActionPermissions(userContext);
  
  // Get UI visibility for different elements
  const getUIElementVisibility = (element: string) => {
    return getUIVisibility(userContext.granularPermissions, element);
  };

  // Check if CTA should be shown based on permissions and UI customizations
  const shouldShowCTA = (permission: keyof typeof permissions, uiElement?: string) => {
    const hasPermission = permissions[permission];
    if (!hasPermission) return false;
    
    if (uiElement) {
      const visibility = getUIElementVisibility(uiElement);
      return visibility !== 'hidden';
    }
    
    return true;
  };

  // Get contextual CTAs based on quote status and user role
  const getContextualCTAs = () => {
    const status = quote.status?.toLowerCase();
    const isSuperAdmin = userContext.userRoles?.includes('SUPER_ADMIN');
    const isTNCAdmin = userContext.userRoles?.includes('TNC_ADMIN');
    
    // Base CTAs available for all statuses (if permissions allow)
    const baseCTAs = [
      {
        key: 'contact',
        label: 'Call',
        icon: Phone,
        action: 'contact_customer',
        permission: 'canContact' as const,
        variant: 'outline' as const,
        show: shouldShowCTA('canContact')
      },
      {
        key: 'message',
        label: 'Message',
        icon: MessageSquare,
        action: 'message_customer',
        permission: 'canContact' as const,
        variant: 'outline' as const,
        show: shouldShowCTA('canContact')
      }
    ];

    // Status-specific CTAs
    const statusCTAs: Record<string, any[]> = {
      pending: [
        {
          key: 'send_to_affiliates',
          label: 'Send to Affiliates',
          icon: Send,
          action: 'send_to_affiliates',
          permission: 'canSendToAffiliates' as const,
          variant: 'default' as const,
          show: shouldShowCTA('canSendToAffiliates', 'section.quote_ctas'),
          emergency: isSuperAdmin
        },
        {
          key: 'fixed_rate',
          label: 'Fixed Rate',
          icon: DollarSign,
          action: 'set_fixed_rate',
          permission: 'canFixedRate' as const,
          variant: 'outline' as const,
          show: shouldShowCTA('canFixedRate', 'section.quote_ctas'),
          emergency: isSuperAdmin
        },
        {
          key: 'edit',
          label: isSuperAdmin ? 'Correct Quote' : 'Edit',
          icon: Edit3,
          action: 'edit_quote',
          permission: 'canEdit' as const,
          variant: 'outline' as const,
          show: shouldShowCTA('canEdit'),
          emergency: isSuperAdmin
        }
      ],
      rate_requested: [
        {
          key: 'accept',
          label: 'Accept',
          icon: CheckCircle,
          action: 'accept_quote',
          permission: 'canAccept' as const,
          variant: 'default' as const,
          show: shouldShowCTA('canAccept'),
          emergency: isSuperAdmin
        },
        {
          key: 'reject',
          label: 'Reject',
          icon: XCircle,
          action: 'reject_quote',
          permission: 'canReject' as const,
          variant: 'destructive' as const,
          show: shouldShowCTA('canReject'),
          emergency: isSuperAdmin
        },
        {
          key: 'assign',
          label: 'Manual Assign',
          icon: UserPlus,
          action: 'manual_assign',
          permission: 'canAssign' as const,
          variant: 'outline' as const,
          show: shouldShowCTA('canAssign') && isSuperAdmin
        }
      ],
      fixed_offer: [
        {
          key: 'accept',
          label: 'Accept',
          icon: CheckCircle,
          action: 'accept_quote',
          permission: 'canAccept' as const,
          variant: 'default' as const,
          show: shouldShowCTA('canAccept')
        },
        {
          key: 'reject',
          label: 'Reject',
          icon: XCircle,
          action: 'reject_quote',
          permission: 'canReject' as const,
          variant: 'destructive' as const,
          show: shouldShowCTA('canReject')
        }
      ]
    };

    // Always available CTAs (if permissions allow)
    const alwaysAvailableCTAs = [
      {
        key: 'request_changes',
        label: 'Request Changes',
        icon: RotateCcw,
        action: 'request_changes',
        permission: 'canRequestChanges' as const,
        variant: 'outline' as const,
        show: shouldShowCTA('canRequestChanges')
      },
      {
        key: 'archive',
        label: 'Archive',
        icon: Archive,
        action: 'archive_quote',
        permission: 'canArchive' as const,
        variant: 'outline' as const,
        show: shouldShowCTA('canArchive')
      }
    ];

    // Combine CTAs based on status
    const currentStatusCTAs = statusCTAs[status] || [];
    const allCTAs = [...currentStatusCTAs, ...alwaysAvailableCTAs, ...baseCTAs];

    return allCTAs.filter(cta => cta.show);
  };

  const contextualCTAs = getContextualCTAs();

  // Render CTAs based on variant
  if (variant === 'row') {
    // Row variant: Show primary CTAs only
    const primaryCTAs = contextualCTAs.slice(0, 4);
    
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        {primaryCTAs.map((cta) => (
          <Button
            key={cta.key}
            variant={cta.variant}
            size="sm"
            onClick={() => onAction(cta.action)}
            className={`${cta.emergency ? 'border-orange-500 text-orange-600' : ''}`}
          >
            <cta.icon className="h-4 w-4 mr-1" />
            {cta.label}
            {cta.emergency && (
              <Badge variant="outline" className="ml-1 text-xs">
                Emergency
              </Badge>
            )}
          </Button>
        ))}
        
        {contextualCTAs.length > 4 && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onAction('show_more_actions')}
          >
            <Settings className="h-4 w-4 mr-1" />
            Show More
          </Button>
        )}
      </div>
    );
  }

  // Panel variant: Show all CTAs in organized sections
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Primary Actions */}
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-muted-foreground">Primary Actions</h4>
        <div className="grid grid-cols-2 gap-2">
          {contextualCTAs.slice(0, 4).map((cta) => (
            <Button
              key={cta.key}
              variant={cta.variant}
              size="sm"
              onClick={() => onAction(cta.action)}
              className={`${cta.emergency ? 'border-orange-500 text-orange-600' : ''}`}
            >
              <cta.icon className="h-4 w-4 mr-1" />
              {cta.label}
              {cta.emergency && (
                <AlertTriangle className="h-3 w-3 ml-1 text-orange-500" />
              )}
            </Button>
          ))}
        </div>
      </div>

      {/* Secondary Actions */}
      {contextualCTAs.length > 4 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-muted-foreground">Additional Actions</h4>
          <div className="grid grid-cols-1 gap-2">
            {contextualCTAs.slice(4).map((cta) => (
              <Button
                key={cta.key}
                variant={cta.variant}
                size="sm"
                onClick={() => onAction(cta.action)}
                className={`justify-start ${cta.emergency ? 'border-orange-500 text-orange-600' : ''}`}
              >
                <cta.icon className="h-4 w-4 mr-2" />
                {cta.label}
                {cta.emergency && (
                  <Badge variant="outline" className="ml-auto text-xs">
                    Emergency
                  </Badge>
                )}
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
