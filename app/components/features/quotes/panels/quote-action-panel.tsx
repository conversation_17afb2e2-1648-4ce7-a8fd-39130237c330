import React from "react";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";
import { Separator } from "@/app/components/ui/separator";
import { Badge } from "@/app/components/ui/badge";
import { Switch } from "@/app/components/ui/switch";
import { Input } from "@/app/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
} from "@/app/components/ui/sheet";
import { RadioGroup, RadioGroupItem } from "@/app/components/ui/radio-group";
import {
  Clock,
  Send,
  Users,
  CheckCircle2,
  XCircle,
  AlertTriangle,
  DollarSign,
  Timer,
  Car,
  Calendar,
  Building2,
  MapPin,
  Star,
  History,
  X,
  ArrowUpCircle,
  ArrowDownCircle,
  ArrowRight,
  AlertCircle,
  Mail,
  Loader2,
  CheckCircle,
  Info,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { isNewQuote, hasQuoteOffers } from "@/lib/utils/quote-status-mapping";
import {
  sendQuoteToAffiliates,
  AffiliateWithStats,
} from "@/lib/api/affiliates";
import { getSupabaseClient } from "@/lib/supabase";
import {
  getQuoteResponses,
  acceptQuoteResponse,
  rejectQuoteResponse,
} from "@/lib/api/quote-responses";
import { Alert as UiAlert, AlertDescription } from "@/app/components/ui/alert";
import { Label } from "@/app/components/ui/label";
import { Textarea } from "@/app/components/ui/textarea";
import { toast } from "@/app/components/ui/use-toast";
import { safelyExtractDate, safelyExtractTime } from "@/lib/utils";
import { formatDistanceToNow, format } from "date-fns";
import {
  type Quote,
  type QuoteResponse,
  type QuoteNote,
} from "@/app/components/features/quotes/types"; // Import shared Quote type and related types
import { PermissionBasedCTAs } from "../components/PermissionBasedCTAs";
import { QuoteEditPanel } from "../components/QuoteEditPanel";
import { getQuoteActionPermissions } from "@/app/utils/quote-permissions";
import type { QuotePermissionContext } from "@/app/utils/quote-permissions";

// Remove local Quote interface definition

interface Affiliate {
  id: string;
  name: string;
  area: string;
  rating: number;
  avgResponse: string;
  onTime: string;
  similarTrips: number;
  baseRate: number;
  totalRate: number;
  priceRange: {
    min: number;
    max: number;
  };
  selected: boolean;
  selectionOrder?: number;
  isSkipped: boolean;
  distanceToPickup: string;
}

interface AffiliateWithUIState extends AffiliateWithStats {
  selected: boolean;
  selectionOrder?: number;
  isSkipped: boolean;
}

interface QuoteActionPanelProps {
  quote: Quote; // Use the imported Quote type
  onClose: () => void;
  onAction: (action: string, data?: any) => void;
  activePanel?: string;
  userType?: "admin" | "user";
  userContext?: QuotePermissionContext; // Add user context for permissions
}

export function QuoteActionPanel({
  quote,
  onClose,
  onAction,
  activePanel,
  userType = "admin",
  userContext,
}: QuoteActionPanelProps) {
  const [submissionType, setSubmissionType] = React.useState<
    "fixed_offer" | "rate_request"
  >("fixed_offer");
  const [overrideRates, setOverrideRates] = React.useState(false);
  const [overrideRate, setOverrideRate] = React.useState("");
  const [lowestAffiliateRate, setLowestAffiliateRate] = React.useState("0");
  const [responseTime, setResponseTime] = React.useState("2h");
  const [enableAutoSubmission, setEnableAutoSubmission] = React.useState(false);
  const [selectedAffiliates, setSelectedAffiliates] = React.useState<
    Array<{ id: string; order: number }>
  >([]);
  const [affiliates, setAffiliates] = React.useState<AffiliateWithUIState[]>(
    []
  );
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [showAffiliateSelection, setShowAffiliateSelection] =
    React.useState(false);

  const [selectedCount, setSelectedCount] = React.useState(0);
  const [currentAffiliateId, setCurrentAffiliateId] = React.useState<
    string | null
  >(null);
  const [responses, setResponses] = React.useState<QuoteResponse[]>([]); // Use imported QuoteResponse
  const [loadingResponse, setLoadingResponse] = React.useState(false);
  const [clientSelectedAffiliates, setClientSelectedAffiliates] =
    React.useState<AffiliateWithUIState[]>([]);
  const [loadingClientSelections, setLoadingClientSelections] =
    React.useState(false);

  // State for manual email form
  const [affiliateEmail, setAffiliateEmail] = React.useState("");
  const [affiliateName, setAffiliateName] = React.useState("");
  const [emailMessage, setEmailMessage] = React.useState("");

  const [quoteHasOffers, setQuoteHasOffers] = React.useState(false);

  const [statusHistory, setStatusHistory] = React.useState<
    Array<{
      // Use imported QuoteNote or a specific history type if available
      status: string;
      timestamp: string;
      user?: string;
      // Consider aligning this with QuoteNote if applicable
    }>
  >([]);

  const [hasBeenProcessed, setHasBeenProcessed] = React.useState(false);

  // Add edit mode state
  const [isEditMode, setIsEditMode] = React.useState(false);

  // Create default user context if not provided
  const defaultUserContext: QuotePermissionContext = {
    userRoles: userType === "admin" ? ["SUPER_ADMIN"] : ["CLIENT"],
    tenantType: "shared",
    quoteStatus: quote.status,
    userType: userType === "admin" ? "admin" : "customer",
    isQuoteOwner: true,
    organizationId: (quote as any).company_id,
    // NEW: Determine workflow type based on quote data
    workflowType: "pure_saas", // Default to Pure SaaS for current implementation
    clientSelectedAffiliates:
      !!(quote as any).selectedAffiliates || !!(quote as any).affiliate_id, // Check if client already selected affiliates
  };

  const effectiveUserContext = userContext || defaultUserContext;

  // Get permissions for current user context
  const permissions = getQuoteActionPermissions(effectiveUserContext);

  // Add a check for whether we should show the process button
  const shouldShowProcessButton = React.useMemo(() => {
    // Ensure quote.status is accessed safely
    return isNewQuote(quote?.status ?? "") && !hasBeenProcessed;
  }, [quote?.status, hasBeenProcessed]);

  // Function to handle manual email submission
  const handleSendManualEmail = async () => {
    if (!affiliateEmail) {
      setError("Please enter an affiliate email address");
      return;
    }

    if (!affiliateName) {
      setError("Please enter an affiliate name or company");
      return;
    }

    try {
      // First update the quote status in the database
      const statusResponse = await fetch(
        `/api/super-admin/quotes/${quote.id}/status`,
        {
          // Use quote.id safely
          method: "PATCH",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ status: "manually_sent" }),
        }
      );

      if (!statusResponse.ok) {
        throw new Error("Failed to update quote status");
      }

      // Then send the email
      const emailResponse = await fetch(
        "/api/super-admin/quotes/send-manual-email",
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            quoteId: quote.id, // Use quote.id safely
            email: affiliateEmail,
            name: affiliateName,
            message: emailMessage,
          }),
        }
      );

      if (!emailResponse.ok) {
        throw new Error("Failed to send email");
      }

      // Call the onAction function with the email details and new status
      onAction("send_manual_email", {
        email: affiliateEmail,
        name: affiliateName,
        message: emailMessage,
        quoteId: quote.id, // Use quote.id safely
        status: "manually_sent",
      });

      // Show success notification
      toast({
        title: "Success",
        description: `Quote request sent to ${affiliateName}`,
      });

      // Reset the form
      setAffiliateEmail("");
      setAffiliateName("");
      setEmailMessage("");

      // Close the panel
      onClose();
    } catch (error) {
      console.error("Error sending manual email:", error);
      setError("Failed to send email. Please try again.");

      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to send email",
        variant: "destructive",
      });
    }
  };

  // Fetch affiliates when panel opens
  React.useEffect(() => {
    console.log("[QuoteActionPanel] useEffect triggered - fetching affiliates");
    console.log(
      "[QuoteActionPanel] Full quote object:",
      JSON.stringify(quote, null, 2)
    );
    console.log("[QuoteActionPanel] Quote city field:", (quote as any)?.city);
    console.log("[QuoteActionPanel] Quote keys:", Object.keys(quote));
    let isMounted = true;

    async function fetchAffiliates() {
      // Extract city from quote - with fallback for old quotes
      let city = (quote as any)?.city;

      // Fallback: Extract city from pickup_location for old quotes
      if (!city && (quote as any)?.pickup_location) {
        const pickupLocation = (quote as any).pickup_location;
        console.log(
          "[QuoteActionPanel] No city field, extracting from pickup_location:",
          pickupLocation
        );

        // Extract city from common patterns like "Miami International Airport (MIA)"
        if (pickupLocation.toLowerCase().includes("miami")) {
          city = "Miami";
        } else if (pickupLocation.toLowerCase().includes("new york")) {
          city = "New York";
        } else if (pickupLocation.toLowerCase().includes("los angeles")) {
          city = "Los Angeles";
        }
        // Add more city extraction patterns as needed

        console.log(
          "[QuoteActionPanel] Extracted city from pickup_location:",
          city
        );
      }

      if (!city) {
        console.error(
          "[QuoteActionPanel] Quote city is missing and could not be extracted",
          quote
        );
        setError("Quote city information is missing");
        setAffiliates([]);
        setLoading(false);
        return;
      }

      console.log("[QuoteActionPanel] Using city for affiliate search:", city);

      setLoading(true);
      setError(null);

      try {
        // Check if we have a valid auth session first
        const supabase = getSupabaseClient();
        if (!supabase) {
          console.error("[QuoteActionPanel] Supabase client not available");
          setError("Authentication service unavailable");
          setLoading(false);
          return;
        }
        const {
          data: { session },
          error: authError,
        } = await supabase.auth.getSession();
        if (authError) {
          console.error("[QuoteActionPanel] Auth error:", authError);
          if (isMounted) {
            setError("Authentication error. Please try logging in again.");
            setLoading(false);
          }
          return;
        }

        if (!session) {
          console.error("[QuoteActionPanel] No auth session");
          if (isMounted) {
            setError("Please log in to view affiliates");
            setLoading(false);
          }
          return;
        }

        // Check if this quote has already been sent to affiliates
        if (isNewQuote(quote?.status ?? "")) {
          // Safe status access
          const hasOffers = await hasQuoteOffers(quote.id); // ID checked above
          if (isMounted) {
            setQuoteHasOffers(hasOffers);
          }
          console.log(
            `[QuoteActionPanel] Quote ${quote.id} has offers: ${hasOffers}`
          );

          if (hasOffers) {
            console.log(
              "[QuoteActionPanel] Quote has already been sent to affiliates"
            );
          }
        }

        try {
          // Get affiliates based on the extracted city using API endpoint
          console.log(
            `[QuoteActionPanel] Loading affiliates for city: "${city}" via API endpoint`
          );

          const response = await fetch(
            `/api/quotes/affiliates?city=${encodeURIComponent(city)}`
          );
          const result = await response.json();

          if (!isMounted) return;

          if (result.success && result.data?.length) {
            console.log(
              `[QuoteActionPanel] Found ${result.data.length} affiliates for city: ${city}`
            );
            setAffiliates(
              result.data.map((affiliate: any) => ({
                ...affiliate,
                selected: false,
                selectionOrder: undefined,
                isSkipped: false,
              }))
            );
          } else {
            console.log(
              "[QuoteActionPanel] No affiliates found for city:",
              city
            );
            setError(`No active affiliates found in ${city}`);
            setAffiliates([]);
          }
        } catch (error) {
          console.error("[QuoteActionPanel] Error fetching affiliates:", error);
          setError("Failed to load affiliates. Please try again.");
          setAffiliates([]);
        }

        setLoading(false);
      } catch (err) {
        console.error("[QuoteActionPanel] Unexpected error:", err);
        if (isMounted) {
          setError("An unexpected error occurred. Please try again.");
          setLoading(false);
        }
      }
    }

    fetchAffiliates();

    return () => {
      isMounted = false;
    };
  }, [quote?.id, (quote as any)?.city, quote?.status, activePanel]); // Dependencies already use safe access

  // Fetch quote responses when panel opens
  React.useEffect(() => {
    let isMounted = true;

    async function fetchResponses() {
      if (!quote?.id) return;

      setLoadingResponse(true);
      setError(null);

      try {
        const rawResponses = await getQuoteResponses(quote.id); // ID checked above

        if (!isMounted) return;

        setResponses(rawResponses as any);
        setLoadingResponse(false);

        // Log response count for debugging
        console.log(
          `[QuoteActionPanel] Loaded ${rawResponses.length} responses for quote ${quote.id}`
        );
      } catch (err) {
        console.error("[QuoteActionPanel] Error fetching responses:", err);
        if (isMounted) {
          setError("Failed to load responses");
          setLoadingResponse(false);

          // Show error toast
          toast({
            title: "Error loading responses",
            description: "Please try refreshing the panel",
            variant: "destructive",
          });
        }
      }
    }

    // Only fetch responses for quotes that can have them
    if (
      ["quote_ready", "rate_requested", "fixed_offer"].includes(
        quote?.status ?? ""
      )
    ) {
      // Safe status access
      fetchResponses();
    } else {
      setLoadingResponse(false);
    }

    return () => {
      isMounted = false;
    };
  }, [quote?.id, quote?.status]); // Dependencies already use safe access

  // Fetch client's affiliate selections (quote offers)
  React.useEffect(() => {
    let isMounted = true;

    async function fetchClientSelections() {
      if (!quote?.id) return;

      setLoadingClientSelections(true);

      try {
        // Fetch quote offers to see which affiliates the client selected
        const response = await fetch(`/api/quotes/${quote.id}/offers`);
        const result = await response.json();

        if (!isMounted) return;

        if (result.success && result.data?.length) {
          // Get affiliate details for the selected affiliates
          const affiliateIds = result.data.map(
            (offer: any) => offer.company_id
          );

          if (affiliateIds.length > 0) {
            const affiliatesResponse = await fetch(
              `/api/quotes/affiliates?ids=${affiliateIds.join(",")}`
            );
            const affiliatesResult = await affiliatesResponse.json();

            if (affiliatesResult.success && affiliatesResult.data?.length) {
              const selectedAffiliates = affiliatesResult.data.map(
                (affiliate: any, index: number) => ({
                  ...affiliate,
                  selected: true,
                  selectionOrder: index + 1,
                  isSkipped: false,
                  // Add offer details
                  offerAmount: result.data.find(
                    (offer: any) => offer.company_id === affiliate.id
                  )?.rate_amount,
                  offerStatus: result.data.find(
                    (offer: any) => offer.company_id === affiliate.id
                  )?.status,
                })
              );

              setClientSelectedAffiliates(selectedAffiliates);
              console.log(
                `[QuoteActionPanel] Found ${selectedAffiliates.length} client-selected affiliates`
              );
            }
          }
        } else {
          console.log(
            "[QuoteActionPanel] No client affiliate selections found"
          );
          setClientSelectedAffiliates([]);
        }

        setLoadingClientSelections(false);
      } catch (err) {
        console.error(
          "[QuoteActionPanel] Error fetching client selections:",
          err
        );
        if (isMounted) {
          setLoadingClientSelections(false);
        }
      }
    }

    // Only fetch for Pure SaaS mode where client selections matter
    if (effectiveUserContext.workflowType === "pure_saas") {
      fetchClientSelections();
    } else {
      setLoadingClientSelections(false);
    }

    return () => {
      isMounted = false;
    };
  }, [quote?.id, effectiveUserContext.workflowType]);

  // Fetch quote status history
  React.useEffect(() => {
    let isMounted = true;

    async function fetchStatusHistory() {
      if (!quote?.id) return;

      try {
        // Fetch status history from the database
        const supabase = getSupabaseClient();
        if (!supabase) {
          console.error(
            "[QuoteActionPanel] Supabase client not available for status history"
          );
          return;
        }
        const { data, error } = await supabase
          .from("quote_status_history")
          .select("*")
          .eq("quote_id", quote.id)
          .order("created_at", { ascending: false });

        if (error) {
          console.error(
            "[QuoteActionPanel] Error fetching status history:",
            error
          );
          return;
        }

        if (data && isMounted) {
          setStatusHistory(
            data.map((item) => ({
              status: item.status,
              timestamp: item.created_at,
              user: item.created_by || undefined,
            }))
          );
        }
      } catch (err) {
        console.error("[QuoteActionPanel] Error fetching status history:", err);
      }
    }

    fetchStatusHistory();

    return () => {
      isMounted = false;
    };
  }, [quote?.id]);

  // Add this effect to check if the quote has been processed
  React.useEffect(() => {
    let isMounted = true;

    async function checkQuoteProcessed() {
      try {
        // Check if this quote has already been sent to affiliates
        if (quote?.id) {
          const hasOffers = await hasQuoteOffers(quote.id);
          if (isMounted) {
            setHasBeenProcessed(hasOffers);
            // If quote has been processed, show affiliate selection directly
            if (hasOffers) {
              setShowAffiliateSelection(true);
              onAction("set_panel", "affiliate-selection");
            }
          }
          console.log(
            `[QuoteActionPanel] Quote ${quote.id} processed status:`,
            hasOffers
          );
        }
      } catch (err) {
        console.error(
          "[QuoteActionPanel] Error checking quote processed status:",
          err
        );
      }
    }

    // Run the check when the panel opens
    checkQuoteProcessed();

    return () => {
      isMounted = false;
    };
  }, [quote?.id]);

  const getStatusBadge = () => {
    // For quotes with status 'pending' or 'pending_quote', treat them as "new"
    if (isNewQuote(quote?.status ?? "")) {
      // Safe status access
      return (
        <Badge variant="secondary" className="bg-orange-100 text-orange-800">
          New
        </Badge>
      );
    }

    // Normalize status to lowercase for case-insensitive comparison
    const normalizedStatus = (
      quote?.status ?? ""
    ).toLowerCase() as Quote["status"]; // Safe status access

    const statusConfig: Record<
      string,
      {
        label: string;
        variant: "default" | "secondary" | "outline" | "destructive";
        className?: string;
        icon?: React.ReactNode;
      }
    > = {
      new: {
        label: "Pending Offer",
        variant: "secondary",
        className: "bg-orange-100 text-orange-800",
        icon: <Clock className="h-3 w-3 mr-1" />,
      },
      rate_requested: {
        label: "Rate Requested",
        variant: "default",
        icon: <ArrowUpCircle className="h-3 w-3 mr-1" />,
      },
      fixed_offer: {
        label: "Fixed Offer",
        variant: "secondary",
        icon: <DollarSign className="h-3 w-3 mr-1" />,
      },
      accepted: {
        label: "Accepted",
        variant: "outline",
        icon: <CheckCircle2 className="h-3 w-3 mr-1" />,
      },
      rejected: {
        label: "Rejected",
        variant: "destructive",
        icon: <XCircle className="h-3 w-3 mr-1" />,
      },
      pending: {
        label: "Pending",
        variant: "secondary",
        icon: <Clock className="h-3 w-3 mr-1" />,
      },
      manually_sent: {
        label: "Manually Sent",
        variant: "default",
        className: "bg-indigo-100 text-indigo-800",
        icon: <Mail className="h-3 w-3 mr-1" />,
      },
    };

    const config = statusConfig[normalizedStatus] || statusConfig.pending;
    return (
      <Badge variant={config.variant} className={config.className}>
        <div className="flex items-center">
          {config.icon}
          {config.label}
        </div>
      </Badge>
    );
  };

  const handleSendToAffiliates = async () => {
    // Validate affiliate selection
    const selectedAffiliatesList = affiliates.filter((a) => a.selected);
    if (selectedAffiliatesList.length === 0) {
      setError("Please select at least one affiliate");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Get the IDs of selected affiliates
      const selectedAffiliateIds = selectedAffiliatesList.map((a) => a.id);

      console.log("[QuoteActionPanel] Sending quote to affiliates:", {
        quoteId: quote.id, // ID checked above
        affiliateIds: selectedAffiliateIds,
        submissionType,
        expiryHours: 24,
      });

      // Send quote to selected affiliates
      const { success, error } = await sendQuoteToAffiliates(
        quote.id, // ID checked above
        selectedAffiliateIds,
        submissionType === "fixed_offer" ? "fixed_offer" : "rate_request",
        24,
        overrideRates ? parseFloat(overrideRate) : undefined
      );

      if (error) {
        console.error(
          "[QuoteActionPanel] Error sending quote to affiliates:",
          error
        );
        setError(error.message);
        return;
      }

      if (success) {
        // Set status based on submission type
        const newStatus =
          submissionType === "fixed_offer" ? "quote_ready" : "rate_requested";

        // Notify parent component about the status change
        onAction("quote_sent", {
          affiliateIds: selectedAffiliateIds,
          submissionType,
          expiryHours: 24,
          status: newStatus,
          quote: {
            ...quote,
            status: newStatus,
            affiliate_responses: (quote as any).affiliate_responses || [],
          },
        }); // quote object already spread, no direct quote.id access here

        // Show success notification
        toast({
          title: "Success",
          description: `Quote sent to ${selectedAffiliateIds.length} affiliate${
            selectedAffiliateIds.length === 1 ? "" : "s"
          }. Responses expected within 24 hours.`,
        });

        // Set processed flag and close modal
        setHasBeenProcessed(true);
        onClose();
      }
    } catch (error) {
      console.error("[QuoteActionPanel] Unexpected error:", error);
      setError("An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };

  const handleAcceptResponse = async (response: QuoteResponse) => {
    if (!(response as any).type) {
      console.error("[QuoteActionPanel] Response type is missing");
      return;
    }

    try {
      await acceptQuoteResponse(quote.id, response.id, (response as any).type); // ID checked above
      // Notify parent of acceptance
      onAction("response_accepted", {
        responseId: response.id,
        type: (response as any).type,
        quote, // quote object passed, no direct quote.id access here
      });
      // Close the panel
      onClose();
    } catch (err) {
      console.error("[QuoteActionPanel] Error accepting response:", err);
      toast({
        title: "Error accepting response",
        description: "Please try again",
        variant: "destructive",
      });
    }
  };

  const handleRejectResponse = async (response: QuoteResponse) => {
    if (!(response as any).type) {
      console.error("[QuoteActionPanel] Response type is missing");
      return;
    }

    try {
      await rejectQuoteResponse(quote.id, response.id, (response as any).type); // ID checked above
      // Refresh the quote list
      onAction("response_rejected", {
        responseId: response.id,
        type: (response as any).type,
        quote, // quote object passed, no direct quote.id access here
      });
    } catch (err) {
      console.error("[QuoteActionPanel] Error rejecting response:", err);
      toast({
        title: "Error rejecting response",
        description: "Please try again",
        variant: "destructive",
      });
    }
  };

  const handleManualProcess = async () => {
    if (
      !overrideRate ||
      isNaN(parseFloat(overrideRate)) ||
      parseFloat(overrideRate) <= 0
    ) {
      setError("Please enter a valid rate");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log(
        "[QuoteActionPanel] Processing quote manually with rate:",
        overrideRate
      );

      // Call the onAction function with the manual processing details
      onAction("quote_manual_process", {
        rate: parseFloat(overrideRate),
        submissionType,
        expiryHours: parseInt(responseTime.replace(/[^0-9]/g, "")),
      });
    } catch (err) {
      console.error("[QuoteActionPanel] Error manually processing quote:", err);
      setError("Failed to process quote. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const renderNewQuoteContent = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4" />
            <p className="text-muted-foreground">Loading affiliates...</p>
          </div>
        </div>
      );
    }

    // If the quote has already been sent to affiliates, show a message
    if (quoteHasOffers) {
      return (
        <div className="space-y-4">
          <Card className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <Send className="h-6 w-6 text-blue-500" />
              <h3 className="text-lg font-semibold">
                Quote Sent to Affiliates
              </h3>
            </div>
            <p className="text-muted-foreground mb-4">
              This quote has already been sent to affiliates. You can view
              responses as they come in.
            </p>
            <Button
              className="w-full"
              onClick={() => onAction("view_responses")}
            >
              View Responses
            </Button>
          </Card>

          {error && <div className="text-sm text-red-500 mt-2">{error}</div>}
        </div>
      );
    }

    // No affiliates available for this city
    if (affiliates.length === 0) {
      console.log(
        "[QuoteActionPanel] Showing no-affiliates UI - city:",
        (quote as any)?.city
      ); // Safe city access

      return (
        <div className="space-y-8">
          {/* No Affiliates UI */}
          <div className="space-y-4 mt-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <AlertTriangle
                    className="h-5 w-5 text-yellow-400"
                    aria-hidden="true"
                  />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">
                    No affiliates available
                  </h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <p>
                      There are no active affiliates for{" "}
                      {(quote as any)?.city || "this city"}. You can send a
                      quote request to external affiliates.{" "}
                      {/* Safe city access */}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <Card className="p-4">
              <h3 className="text-lg font-semibold mb-4">
                Send Quote Request to External Affiliates
              </h3>
              <div className="text-sm text-muted-foreground mb-4">
                <p>
                  You can send a quote information request by email to
                  affiliates not yet in our system. They will receive a link to
                  view the quote details and submit their rate.
                </p>
              </div>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="affiliate-email">Affiliate Email</Label>
                  <Input
                    id="affiliate-email"
                    type="email"
                    placeholder="Enter affiliate email"
                    value={affiliateEmail}
                    onChange={(e) => setAffiliateEmail(e.target.value)}
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="affiliate-name">Affiliate Name/Company</Label>
                  <Input
                    id="affiliate-name"
                    type="text"
                    placeholder="Enter affiliate name or company"
                    value={affiliateName}
                    onChange={(e) => setAffiliateName(e.target.value)}
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="email-message">Additional Message</Label>
                  <Textarea
                    id="email-message"
                    placeholder="Enter any additional information for the affiliate"
                    value={emailMessage}
                    onChange={(e) => setEmailMessage(e.target.value)}
                    className="mt-1"
                    rows={3}
                  />
                </div>
              </div>
            </Card>

            {/* Action Buttons */}
            <div className="flex justify-end gap-4 pt-2">
              <Button variant="outline" onClick={onClose} disabled={loading}>
                Cancel
              </Button>

              <Button
                onClick={handleSendManualEmail}
                disabled={loading || !affiliateEmail || !affiliateName}
              >
                {loading ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
                    Sending...
                  </div>
                ) : (
                  <>
                    <Mail className="h-5 w-5 mr-2" />
                    Send Quote Request
                  </>
                )}
              </Button>
            </div>

            {error && <div className="text-sm text-red-500 mt-2">{error}</div>}
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <Card className="p-6">
          <div className="text-center text-destructive">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p>{error}</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => window.location.reload()}
            >
              Retry
            </Button>
          </div>
        </Card>
      );
    }

    // Affiliates available for this city - show affiliate selection UI
    return (
      <div className="space-y-8">
        {/* Top Row - Stats */}
        <div className="flex gap-4">
          {/* Market Rate */}
          <Card className="flex-1 p-4">
            <div className="flex items-center gap-2 mb-2">
              <DollarSign className="h-5 w-5" />
              <h3 className="text-lg font-semibold">Market Rate</h3>
            </div>
            <Badge variant="secondary" className="mb-3">
              Medium Demand
            </Badge>
            <div className="text-muted-foreground">
              <div>Similar Trips: 42 last month</div>
              <div>Success Rate: 85%</div>
            </div>
          </Card>

          {/* Markup */}
          <Card className="flex-1 p-4">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-lg font-semibold">Markup</h3>
              <span className="text-lg font-semibold text-green-600">+15%</span>
            </div>
            <div className="text-muted-foreground">Seasonal Trend</div>
          </Card>
        </div>

        {/* Assignment Settings */}
        <Card className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Assignment Settings</h3>
            <Badge variant="outline" className="py-1.5 px-3">
              <Clock className="h-4 w-4 mr-2" />
              Sequential Assignment
            </Badge>
          </div>

          <div className="space-y-6">
            {/* Submission Type */}
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <h4 className="font-medium">Submission Type</h4>
                <p className="text-sm text-muted-foreground">
                  {submissionType === "rate_request"
                    ? "Request custom rates"
                    : "Using fixed system rate"}
                </p>
              </div>
              <RadioGroup
                value={submissionType}
                onValueChange={(value: "fixed_offer" | "rate_request") => {
                  setSubmissionType(value);
                  onAction("set_rate_type", value);
                }}
                className="flex items-center bg-muted p-1 rounded-lg"
              >
                <div className="grid grid-cols-2 gap-1">
                  <label
                    htmlFor="fixed_offer"
                    className={`px-3 py-2 rounded-md cursor-pointer flex items-center justify-center text-sm font-medium transition-colors
                      ${submissionType === "fixed_offer" ? "bg-background shadow-sm" : "hover:bg-background/50"}`}
                  >
                    <DollarSign className="h-4 w-4 mr-2" />
                    <RadioGroupItem
                      value="fixed_offer"
                      id="fixed_offer"
                      className="sr-only"
                    />
                    Fixed Rate
                  </label>
                  <label
                    htmlFor="rate_request"
                    className={`px-3 py-2 rounded-md cursor-pointer flex items-center justify-center text-sm font-medium transition-colors
                      ${submissionType === "rate_request" ? "bg-background shadow-sm" : "hover:bg-background/50"}`}
                  >
                    <Users className="h-4 w-4 mr-2" />
                    <RadioGroupItem
                      value="rate_request"
                      id="rate_request"
                      className="sr-only"
                    />
                    Rate Request
                  </label>
                </div>
              </RadioGroup>
            </div>

            {/* Submission Interval */}
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <h4 className="font-medium">Submission Interval</h4>
                <p className="text-sm text-muted-foreground">
                  Time given to each affiliate to respond
                </p>
              </div>
              <div className="w-[180px]">
                <Select value={responseTime} onValueChange={setResponseTime}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="2h">2 Hours</SelectItem>
                    <SelectItem value="4h">4 Hours</SelectItem>
                    <SelectItem value="6h">6 Hours</SelectItem>
                    <SelectItem value="12h">12 Hours</SelectItem>
                    <SelectItem value="1d">1 Day</SelectItem>
                    <SelectItem value="2d">2 Days</SelectItem>
                    <SelectItem value="3d">3 Days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Override Rates - Only show for fixed_offer */}
            {submissionType === "fixed_offer" && (
              <div>
                <div className="flex items-center justify-between mb-4">
                  <div className="space-y-1">
                    <h4 className="font-medium">Override Rates</h4>
                    <p className="text-sm text-muted-foreground">
                      Override system calculated rates
                    </p>
                  </div>
                  <Switch
                    checked={overrideRates}
                    onCheckedChange={setOverrideRates}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="flex items-center">
                      <DollarSign className="h-5 w-5 text-muted-foreground" />
                      <Input
                        value={lowestAffiliateRate}
                        className="text-base"
                        disabled
                      />
                    </div>
                    <span className="text-sm text-muted-foreground mt-2 block">
                      Current Rate
                    </span>
                  </div>

                  <div>
                    <div className="flex items-center">
                      <DollarSign className="h-5 w-5 text-muted-foreground" />
                      <Input
                        value={overrideRate}
                        onChange={(e) => setOverrideRate(e.target.value)}
                        className="text-base"
                        disabled={!overrideRates}
                      />
                    </div>
                    <span className="text-sm text-muted-foreground mt-2 block">
                      Override Rate
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* Available Affiliates */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold">Available Affiliates</h3>
              <Badge variant="outline">{selectedCount} sequential</Badge>
            </div>
            {selectedCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setAffiliates((prev) =>
                    prev.map((a) => ({
                      ...a,
                      selected: false,
                      selectionOrder: undefined,
                    }))
                  );
                  setSelectedCount(0);
                }}
              >
                Clear All
              </Button>
            )}
          </div>

          {/* Affiliate List */}
          <div className="space-y-3">
            {affiliates
              .sort((a, b) => {
                if (!a.selected && !b.selected) return 0;
                if (!a.selected) return 1;
                if (!b.selected) return -1;
                return (a.selectionOrder || 0) - (b.selectionOrder || 0);
              })
              .map((affiliate) => {
                try {
                  const isCurrent =
                    affiliate.selected && affiliate.selectionOrder === 1;
                  const isNext =
                    affiliate.selected && affiliate.selectionOrder === 2;

                  // Log each affiliate being rendered
                  console.log(`[QuoteActionPanel] Rendering affiliate:`, {
                    id: affiliate.id,
                    name: affiliate.name,
                    city: affiliate.city,
                    priceRange: affiliate.priceRange,
                    baseRate: affiliate.baseRate,
                    totalRate: affiliate.totalRate,
                  });

                  return (
                    <div
                      key={affiliate.id}
                      className={cn(
                        "relative p-3 border rounded-lg cursor-pointer transition-all",
                        affiliate.isSkipped ? "opacity-50 bg-gray-50" : "",
                        affiliate.selected
                          ? isCurrent
                            ? "border-green-500 bg-green-50/30"
                            : isNext
                              ? "border-red-500 bg-red-50/30"
                              : "border-primary bg-primary-50/30"
                          : "border-border hover:border-primary/50 hover:bg-gray-50/50"
                      )}
                      onClick={() => handleAffiliateClick(affiliate)}
                    >
                      {/* Order Number */}
                      {affiliate.selected && affiliate.selectionOrder && (
                        <div className="absolute -top-2 -left-2 w-6 h-6 rounded-full bg-primary text-white flex items-center justify-center text-sm font-medium shadow-sm">
                          {affiliate.selectionOrder}
                        </div>
                      )}

                      {/* Main Content Grid */}
                      <div className="grid grid-cols-12 gap-3 items-center">
                        {/* Company Info */}
                        <div className="col-span-4">
                          <div className="flex items-center gap-2">
                            <div>
                              <h4 className="font-medium text-sm line-clamp-1">
                                {affiliate.name}
                              </h4>
                              <div className="flex items-center gap-1 mt-0.5">
                                <Badge
                                  variant="outline"
                                  className="px-1 py-0 text-xs"
                                >
                                  {affiliate.city}, {affiliate.state}
                                </Badge>
                                <div className="flex items-center gap-0.5">
                                  <Star className="h-3 w-3 text-yellow-400" />
                                  <span className="text-xs">
                                    {affiliate.rating || "N/A"}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Stats */}
                        <div className="col-span-4 grid grid-cols-4 gap-2 text-center">
                          <div className="bg-gray-50 rounded px-1 py-0.5">
                            <div className="text-xs font-medium">
                              {affiliate.avgResponse}
                            </div>
                            <div className="text-[10px] text-muted-foreground">
                              Response
                            </div>
                          </div>
                          <div className="bg-gray-50 rounded px-1 py-0.5">
                            <div className="text-xs font-medium">
                              {affiliate.onTime}
                            </div>
                            <div className="text-[10px] text-muted-foreground">
                              On Time
                            </div>
                          </div>
                          <div className="bg-gray-50 rounded px-1 py-0.5">
                            <div className="text-xs font-medium">
                              {affiliate.similarTrips}
                            </div>
                            <div className="text-[10px] text-muted-foreground">
                              Similar
                            </div>
                          </div>
                          <div
                            className={cn(
                              "rounded px-1 py-0.5",
                              parseFloat(affiliate.distanceToPickup || "0") <= 3
                                ? "bg-green-50 text-green-700"
                                : parseFloat(
                                      affiliate.distanceToPickup || "0"
                                    ) <= 6
                                  ? "bg-yellow-50 text-yellow-700"
                                  : "bg-red-50 text-red-700"
                            )}
                          >
                            <div className="text-xs font-medium">
                              {affiliate.distanceToPickup}
                            </div>
                            <div className="text-[10px] text-muted-foreground">
                              Distance
                            </div>
                          </div>
                        </div>

                        {/* Pricing */}
                        <div className="col-span-3 text-right">
                          <div className="text-xs text-muted-foreground">
                            Base: ${affiliate.baseRate}
                          </div>
                          <div className="font-medium text-sm">
                            ${affiliate.totalRate}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            ${affiliate.priceRange.min} - $
                            {affiliate.priceRange.max}
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="col-span-1 flex justify-end">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 px-2 text-xs"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleSkipAffiliate(affiliate);
                            }}
                          >
                            {affiliate.isSkipped ? "Restore" : "Skip"}
                          </Button>
                        </div>
                      </div>
                    </div>
                  );
                } catch (error) {
                  console.error(
                    `[QuoteActionPanel] Error rendering affiliate ${affiliate?.id || "unknown"}:`,
                    error
                  );
                  console.log(
                    `[QuoteActionPanel] Problematic affiliate data:`,
                    affiliate
                  );
                  return (
                    <div
                      key={affiliate?.id || Math.random()}
                      className="p-3 border border-red-300 rounded-lg bg-red-50"
                    >
                      <div className="text-red-600">
                        Error rendering affiliate:{" "}
                        {affiliate?.name || "Unknown"}
                      </div>
                      <div className="text-xs text-red-500">
                        {error instanceof Error ? error.message : String(error)}
                      </div>
                    </div>
                  );
                }
              })}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between gap-4 pt-2">
            <Button
              variant="outline"
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
              onClick={() => onAction("clear_assignment")}
              disabled={loading}
            >
              Clear Assignment
            </Button>

            <Button
              className="flex-1 py-6 text-base"
              onClick={handleSendToAffiliates}
              disabled={loading || selectedCount === 0}
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
                  Sending...
                </div>
              ) : (
                <>
                  <Send className="h-5 w-5 mr-2" />
                  Send to {selectedCount}{" "}
                  {selectedCount === 1 ? "Affiliate" : "Affiliates"}
                </>
              )}
            </Button>
          </div>

          {error && <div className="text-sm text-red-500 mt-2">{error}</div>}
        </div>
      </div>
    );
  };

  // Client-specific quote response interface for Pure SaaS mode
  const renderClientQuoteResponses = () => {
    if (loadingResponse) {
      return (
        <div className="flex items-center justify-center h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4" />
            <p className="text-muted-foreground">
              Loading affiliate responses...
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <Card className="p-4">
          <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            Affiliate Rate Submissions
          </h3>
          <p className="text-sm text-muted-foreground mb-4">
            Your selected affiliates have submitted their rates. Choose the best
            option for your trip.
          </p>
        </Card>

        {responses.length === 0 ? (
          <Card className="p-4">
            <div className="text-center py-6">
              <Clock className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">
                Waiting for affiliate responses...
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                Your selected affiliates will submit their rates soon.
              </p>
            </div>
          </Card>
        ) : (
          responses.map((response: QuoteResponse) => (
            <Card
              key={response.id}
              className="p-4 border-2 hover:border-blue-200 transition-colors"
            >
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h4 className="font-semibold text-lg">
                    {response.affiliate_name || "Unknown Affiliate"}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    {(response as any).expiry_time
                      ? `Submitted ${formatDistanceToNow(new Date((response as any).expiry_time))} ago`
                      : "Recently submitted"}
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-bold text-2xl text-green-600">
                    ${(response as any).rate_amount}
                  </p>
                  <Badge
                    variant={
                      response.status === "accepted"
                        ? "default"
                        : response.status === "rejected"
                          ? "destructive"
                          : "secondary"
                    }
                  >
                    {response.status.toUpperCase()}
                  </Badge>
                </div>
              </div>

              {(response as any).description && (
                <p className="text-sm text-muted-foreground mb-3">
                  {(response as any).description}
                </p>
              )}

              {response.status === "pending" && (
                <div className="flex gap-2 mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRejectResponse(response)}
                    className="flex-1"
                  >
                    <X className="h-4 w-4 mr-1" />
                    Decline
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => handleAcceptResponse(response)}
                    className="flex-1 bg-green-600 hover:bg-green-700"
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Accept & Book
                  </Button>
                </div>
              )}
            </Card>
          ))
        )}
      </div>
    );
  };

  // Admin-specific quote response interface
  const renderAdminQuoteResponses = () => {
    if (loadingResponse) {
      return (
        <div className="flex items-center justify-center h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4" />
            <p className="text-muted-foreground">Loading responses...</p>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {responses.map((response: QuoteResponse) => (
          <div key={response.id} className="rounded-lg border p-4">
            <div className="flex items-center justify-between mb-2">
              <div>
                <h4 className="font-medium">
                  {response.affiliate_name || "Unknown Affiliate"}
                </h4>
                <p className="text-sm text-gray-500">
                  {(response as any).expiry_time
                    ? `Responded ${formatDistanceToNow(new Date((response as any).expiry_time))} ago`
                    : "Recently responded"}
                </p>
              </div>
              <div className="text-right">
                <p className="font-medium text-lg">
                  ${(response as any).rate_amount}
                </p>
                <Badge
                  variant={
                    response.status === "accepted" ? "default" : "secondary"
                  }
                >
                  {response.status}
                </Badge>
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleRejectResponse(response)}
                disabled={response.status !== "pending"}
              >
                Reject
              </Button>
              <Button
                size="sm"
                onClick={() => handleAcceptResponse(response)}
                disabled={response.status !== "pending"}
              >
                Accept
              </Button>
            </div>
          </div>
        ))}

        <Button
          variant="outline"
          className="w-full"
          onClick={() => onAction("send_to_more_affiliates")}
        >
          <Users className="h-4 w-4 mr-2" />
          Send to More Affiliates
        </Button>
      </div>
    );
  };

  // Main function that routes to appropriate interface
  const renderPendingQuoteContent = () => {
    // For Pure SaaS mode with client users, show client interface
    if (
      effectiveUserContext.workflowType === "pure_saas" &&
      userType === "customer"
    ) {
      return renderClientQuoteResponses();
    }

    // For admin users or TNC mode, show admin interface
    return renderAdminQuoteResponses();
  };

  const handleProcessQuote = () => {
    setShowAffiliateSelection(true);
    onAction("set_panel", "affiliate-selection");
  };

  // Handle permission-based CTA actions
  const handlePermissionBasedAction = (action: string, data?: any) => {
    switch (action) {
      case "edit_quote":
        setIsEditMode(true);
        break;
      case "send_to_affiliates":
        handleProcessQuote();
        break;
      case "set_fixed_rate":
        // Handle fixed rate setting
        onAction("set_fixed_rate", data);
        break;
      case "accept_quote":
        onAction("accept_quote", data);
        break;
      case "reject_quote":
        onAction("reject_quote", data);
        break;
      case "manual_assign":
        onAction("manual_assign", data);
        break;
      case "request_changes":
        onAction("request_changes", data);
        break;
      case "archive_quote":
        onAction("archive_quote", data);
        break;
      case "contact_customer":
        onAction("contact_customer", data);
        break;
      case "message_customer":
        onAction("message_customer", data);
        break;
      case "show_more_actions":
        // Toggle expanded actions view
        onAction("show_more_actions", data);
        break;
      default:
        onAction(action, data);
    }
  };

  // Handle quote save from edit panel
  const handleQuoteSave = async (updatedQuote: any) => {
    try {
      // Call the API to update the quote
      const response = await fetch(`/api/super-admin/quotes/${quote.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(updatedQuote),
      });

      if (!response.ok) {
        throw new Error("Failed to update quote");
      }

      // Close edit mode and refresh
      setIsEditMode(false);
      onAction("quote_updated", updatedQuote);

      toast({
        title: "Quote Updated",
        description: "Quote has been successfully updated.",
        variant: "default",
      });
    } catch (error) {
      console.error("Error updating quote:", error);
      toast({
        title: "Update Failed",
        description: "Failed to update the quote. Please try again.",
        variant: "destructive",
      });
    }
  };

  const renderPendingStatusContent = () => {
    return (
      <div className="space-y-8">
        {/* Top Row - Status Info */}
        <Card className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <Clock className="h-5 w-5 text-amber-500" />
            <h3 className="text-lg font-semibold">Pending Review</h3>
          </div>
          <p className="text-muted-foreground mb-4">
            This quote is pending review by an administrator. Review the details
            and process when ready.
          </p>

          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Submitted</span>
            <span>{new Date((quote as any).created_at).toLocaleString()}</span>
          </div>
        </Card>

        {/* Quote Details */}
        <Card className="p-4">
          <h3 className="text-lg font-semibold mb-3">Quote Details</h3>

          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Pickup</span>
              </div>
              <span>{quote.pickup_location}</span>
            </div>

            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Dropoff</span>
              </div>
              <span>{quote.dropoff_location}</span>
            </div>

            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Date</span>
              </div>
              <span>{(quote as any).date}</span>
            </div>

            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Time</span>
              </div>
              <span>{(quote as any).time}</span>
            </div>

            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <Car className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Vehicle</span>
              </div>
              <span>{quote.vehicle_type}</span>
            </div>
          </div>
        </Card>

        {/* Client's Affiliate Selections (Pure SaaS Mode) */}
        {effectiveUserContext.workflowType === "pure_saas" && (
          <Card className="p-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-500" />
              Client's Affiliate Selections
            </h3>

            {loadingClientSelections ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
                <span className="ml-2 text-sm text-muted-foreground">
                  Loading selections...
                </span>
              </div>
            ) : clientSelectedAffiliates.length > 0 ? (
              <div className="space-y-3">
                <p className="text-sm text-muted-foreground mb-3">
                  The client selected {clientSelectedAffiliates.length}{" "}
                  affiliate{clientSelectedAffiliates.length > 1 ? "s" : ""}{" "}
                  during booking:
                </p>
                {clientSelectedAffiliates.map((affiliate, index) => (
                  <div
                    key={affiliate.id}
                    className="flex items-center justify-between p-3 border rounded-lg bg-blue-50"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                        {index + 1}
                      </div>
                      <div>
                        <h4 className="font-medium">{affiliate.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {affiliate.city}, {affiliate.state}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      {(affiliate as any).offerAmount && (
                        <p className="font-medium text-lg">
                          ${(affiliate as any).offerAmount}
                        </p>
                      )}
                      <Badge variant="outline" className="text-xs">
                        {(affiliate as any).offerStatus || "PENDING"}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4">
                <Users className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">
                  No affiliate selections found
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  Client may not have selected affiliates during booking
                </p>
              </div>
            )}
          </Card>
        )}

        {/* Actions - Permission-based CTAs */}
        <div className="space-y-2">
          {/* Show Process Quote button only if user can send to affiliates */}
          {permissions.canSendToAffiliates && (
            <Button className="w-full" onClick={handleProcessQuote}>
              Process Quote
            </Button>
          )}

          {/* Show monitoring message for Pure SaaS Super Admin */}
          {effectiveUserContext.userRoles?.includes("SUPER_ADMIN") &&
            effectiveUserContext.workflowType === "pure_saas" &&
            !permissions.canSendToAffiliates && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center gap-2 text-blue-700">
                  <Info className="h-4 w-4" />
                  <span className="text-sm font-medium">Monitoring Mode</span>
                </div>
                <p className="text-xs text-blue-600 mt-1">
                  Client selected affiliates during booking. Quote processing is
                  automated.
                </p>
              </div>
            )}

          {/* Show Edit Quote button for Super Admin */}
          {permissions.canEdit && (
            <Button
              variant="outline"
              className="w-full"
              onClick={() => setIsEditMode(true)}
            >
              Edit Quote Details
            </Button>
          )}

          {permissions.canContact && (
            <Button
              variant="outline"
              className="w-full"
              onClick={() => onAction("contact_customer")}
            >
              Contact Customer
            </Button>
          )}
        </div>
      </div>
    );
  };

  // Add utility function to check if a quote has a trip
  const hasTripData = (quote: any) => {
    return (
      quote.status === "accepted" &&
      quote.trips &&
      Array.isArray(quote.trips) &&
      quote.trips.length > 0
    );
  };

  // Format date helper function
  const formatDate = (date: Date) => {
    try {
      return format(date, "PPP");
    } catch (error) {
      console.error("Error formatting date:", error);
      return "Invalid Date";
    }
  };

  const renderAcceptedQuoteContent = () => {
    // Check if this quote has associated trip data
    const tripExists = hasTripData(quote);
    const tripData =
      tripExists && (quote as any).trips && (quote as any).trips.length > 0
        ? (quote as any).trips[0]
        : null;

    return (
      <div className="space-y-4">
        <Card className="p-4 border-green-200">
          <div className="flex items-center gap-2 text-green-700">
            <CheckCircle className="h-4 w-4" />
            <span className="font-medium">Quote accepted</span>
          </div>

          {(quote as any).accepted_by && (
            <div className="mt-2 text-sm flex items-center gap-1">
              <span className="text-muted-foreground">Accepted by:</span>
              <span className="font-medium">{(quote as any).accepted_by}</span>
            </div>
          )}

          {(quote as any).accepted_at && (
            <div className="mt-1 text-sm flex items-center gap-1">
              <span className="text-muted-foreground">Accepted on:</span>
              <span className="font-medium">
                {formatDate(new Date((quote as any).accepted_at))}
              </span>
            </div>
          )}
        </Card>

        {tripData ? (
          <Card className="p-4">
            <h3 className="font-semibold mb-3">Trip Information</h3>
            <div className="space-y-2">
              {tripData.reference_number && (
                <div className="text-sm flex justify-between">
                  <span className="text-muted-foreground">Trip Reference:</span>
                  <span className="font-medium">
                    {tripData.reference_number}
                  </span>
                </div>
              )}

              {tripData.status && (
                <div className="text-sm flex justify-between">
                  <span className="text-muted-foreground">Trip Status:</span>
                  <Badge variant="outline">{tripData.status}</Badge>
                </div>
              )}

              {tripData.scheduled_pickup_date && (
                <div className="text-sm flex justify-between">
                  <span className="text-muted-foreground">
                    Scheduled Pickup:
                  </span>
                  <span className="font-medium">
                    {formatDate(new Date(tripData.scheduled_pickup_date))}
                    {tripData.scheduled_pickup_time &&
                      ` at ${tripData.scheduled_pickup_time}`}
                  </span>
                </div>
              )}

              {tripData.estimated_amount && (
                <div className="text-sm flex justify-between">
                  <span className="text-muted-foreground">
                    Estimated Amount:
                  </span>
                  <span className="font-medium">
                    ${tripData.estimated_amount.toFixed(2)}
                  </span>
                </div>
              )}
            </div>
          </Card>
        ) : (
          <Card className="p-4 border-blue-200">
            <div className="flex items-center gap-2 text-blue-700">
              <Info className="h-4 w-4" />
              <span className="font-medium">Trip will be created soon</span>
            </div>
            <p className="mt-2 text-sm text-muted-foreground">
              Now that the quote has been accepted, a trip will be created and
              assigned to the selected affiliate.
            </p>
          </Card>
        )}

        {/* Only show "Generate Trip" button for admin if no trip exists yet */}
        {!tripExists && userType === "admin" && (
          <Button className="w-full" onClick={() => onAction("generate_trip")}>
            Generate Trip
          </Button>
        )}
      </div>
    );
  };

  const renderRejectedQuoteContent = () => {
    return (
      <div className="space-y-4">
        <Card className="p-4 border-destructive">
          <div className="flex items-center gap-2 text-destructive">
            <AlertTriangle className="h-4 w-4" />
            <span className="font-medium">Quote was rejected</span>
          </div>
          {(quote as any).rejection_reason && (
            <p className="mt-2 text-sm text-muted-foreground">
              {(quote as any).rejection_reason}
            </p>
          )}
        </Card>

        <div className="space-y-2">
          <Button
            className="w-full"
            onClick={() => onAction("try_different_affiliate")}
          >
            Try Different Affiliate
          </Button>
          <Button
            variant="outline"
            className="w-full"
            onClick={() => onAction("create_new_quote")}
          >
            Create New Quote
          </Button>
        </div>
      </div>
    );
  };

  const getStatusConfig = () => {
    // For quotes with status 'pending' or 'pending_quote', treat them as "new"
    if (isNewQuote(quote.status)) {
      return {
        title: "Trip Details",
        description: "Review and assign trip to affiliates",
        content: showAffiliateSelection
          ? renderNewQuoteContent()
          : renderPendingStatusContent(),
      };
    }

    // Normalize status to lowercase for case-insensitive comparison
    const normalizedStatus = (
      quote.status || ""
    ).toLowerCase() as Quote["status"];

    const config = {
      new: {
        title: "Trip Details",
        description: "Review and assign trip to affiliates",
        content: showAffiliateSelection
          ? renderNewQuoteContent()
          : renderPendingStatusContent(),
      },
      rate_requested: {
        title: "Quote Details",
        description: "Review affiliate responses",
        content: renderPendingQuoteContent(),
      },
      fixed_offer: {
        title: "Quote Details",
        description: "Review affiliate responses",
        content: renderPendingQuoteContent(),
      },
      accepted: {
        title: "Quote Details",
        description: "Quote has been accepted",
        content: renderAcceptedQuoteContent(),
      },
      rejected: {
        title: "Quote Details",
        description: "Quote has been rejected",
        content: renderRejectedQuoteContent(),
      },
      pending: {
        title: "Pending Quote",
        description: "Quote is awaiting review",
        content: showAffiliateSelection
          ? renderNewQuoteContent()
          : renderPendingStatusContent(),
      },
      manually_sent: {
        title: "Manually Sent",
        description: "Quote was manually sent",
        content: renderNewQuoteContent(),
      },
    };

    return (
      (config as any)[normalizedStatus] || {
        title: "Quote Details",
        description: "View quote details",
        content: renderPendingQuoteContent(),
      }
    );
  };

  const statusConfig = getStatusConfig();

  const handleAffiliateClick = (clickedAffiliate: AffiliateWithUIState) => {
    setAffiliates((prev) =>
      prev.map((affiliate) => {
        if (affiliate.id === clickedAffiliate.id) {
          // If already selected, unselect it and reorder others
          if (affiliate.selected) {
            const order = affiliate.selectionOrder;
            return {
              ...affiliate,
              selected: false,
              selectionOrder: undefined,
            };
          }
          // If not selected, add it to sequence
          return {
            ...affiliate,
            selected: true,
            selectionOrder: selectedCount + 1,
          };
        }
        return affiliate;
      })
    );
    setSelectedCount((prev) =>
      clickedAffiliate.selected ? prev - 1 : prev + 1
    );
    onAction("update_sequence", affiliates);
  };

  const handleSkipAffiliate = (skipAffiliate: AffiliateWithUIState) => {
    setAffiliates((prev) =>
      prev.map((affiliate) => {
        if (affiliate.id === skipAffiliate.id) {
          return {
            ...affiliate,
            isSkipped: true,
            selected: false,
            selectionOrder: undefined,
          };
        }
        return affiliate;
      })
    );
    onAction("skip_affiliate", skipAffiliate.id);
  };

  // Render status history section
  const renderStatusHistory = () => {
    if (statusHistory.length === 0) {
      return (
        <div className="text-sm text-muted-foreground italic">
          No status history available
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {statusHistory.map((item, index) => (
          <div key={index} className="flex items-start gap-3">
            <div className="mt-0.5">
              <Clock className="h-4 w-4 text-muted-foreground" />
            </div>
            <div>
              <div className="flex items-center gap-2">
                <span className="font-medium">
                  {item.status.charAt(0).toUpperCase() +
                    item.status.slice(1).replace(/_/g, " ")}
                </span>
                {item.user && (
                  <span className="text-xs text-muted-foreground">
                    by {item.user}
                  </span>
                )}
              </div>
              <div className="text-xs text-muted-foreground">
                {new Date(item.timestamp).toLocaleString()}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Update the renderContent function
  const renderContent = () => {
    if (showAffiliateSelection) {
      return renderNewQuoteContent();
    }

    // For quotes that have been processed or are not pending
    if (hasBeenProcessed || !shouldShowProcessButton) {
      // Special case for Pure SaaS monitoring mode - show client selections instead of pending review
      if (
        effectiveUserContext.workflowType === "pure_saas" &&
        !shouldShowProcessButton &&
        quote.status === "pending"
      ) {
        return renderPendingQuoteContent(); // This includes client affiliate selections
      }

      switch (quote.status) {
        case "rate_requested":
        case "fixed_offer":
          return renderPendingQuoteContent();
        case "accepted":
          return renderAcceptedQuoteContent();
        case "rejected":
          return renderRejectedQuoteContent();
        case "manually_sent":
          return renderNewQuoteContent();
        default:
          return renderPendingQuoteContent();
      }
    }

    // For pending quotes that haven't been processed (TNC mode)
    return renderPendingStatusContent();
  };

  // Use the imported utility functions instead
  const formattedDate = safelyExtractDate((quote as any)?.date); // Safe date access
  const formattedTime = safelyExtractTime((quote as any)?.time); // Safe time access

  // Add response rendering section
  const renderResponses = () => {
    if (loadingResponse) {
      return (
        <div className="flex items-center justify-center p-4">
          <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
          <span className="ml-2 text-sm text-gray-500">
            Loading responses...
          </span>
        </div>
      );
    }

    if (error) {
      return (
        <div className="p-4 text-center">
          <XCircle className="h-6 w-6 text-red-500 mx-auto mb-2" />
          <p className="text-sm text-gray-500">{error}</p>
        </div>
      );
    }

    if (!responses?.length) {
      return (
        <div className="p-4 text-center">
          <Clock className="h-6 w-6 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-500">No responses yet</p>
          <p className="text-xs text-gray-400">
            Check back later for affiliate responses
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-4 p-4">
        {responses.map((response) => (
          <div key={response.id} className="rounded-lg border p-4">
            <div className="flex items-center justify-between mb-2">
              <div>
                <h4 className="font-medium">
                  {response.affiliate_name || "Unknown Affiliate"}
                </h4>
                <p className="text-sm text-gray-500">
                  {(response as any).expiry_time
                    ? `Responded ${formatDistanceToNow(new Date((response as any).expiry_time))} ago`
                    : "Recently responded"}
                </p>
              </div>
              <div className="text-right">
                <p className="font-medium text-lg">
                  ${(response as any).rate_amount}
                </p>
                <Badge
                  variant={
                    response.status === "accepted" ? "default" : "secondary"
                  }
                >
                  {response.status}
                </Badge>
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleRejectResponse(response)}
                disabled={response.status !== "pending"}
              >
                Reject
              </Button>
              <Button
                size="sm"
                onClick={() => handleAcceptResponse(response)}
                disabled={response.status !== "pending"}
              >
                Accept
              </Button>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Add the renderAffiliateSelection function
  const renderAffiliateSelection = () => {
    return renderNewQuoteContent();
  };

  // Add the renderManualEmail function
  const renderManualEmail = () => {
    return (
      <div className="space-y-4">
        <Card className="p-4">
          <h3 className="text-lg font-semibold mb-4">Send Manual Email</h3>
          <div className="space-y-4">
            <div>
              <Label htmlFor="affiliate-email">Affiliate Email</Label>
              <Input
                id="affiliate-email"
                type="email"
                placeholder="Enter affiliate email"
                value={affiliateEmail}
                onChange={(e) => setAffiliateEmail(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="affiliate-name">Affiliate Name</Label>
              <Input
                id="affiliate-name"
                type="text"
                placeholder="Enter affiliate name"
                value={affiliateName}
                onChange={(e) => setAffiliateName(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="email-message">Message</Label>
              <Textarea
                id="email-message"
                placeholder="Enter message"
                value={emailMessage}
                onChange={(e) => setEmailMessage(e.target.value)}
              />
            </div>
          </div>
        </Card>
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            onClick={() => onAction("set_panel", "process")}
          >
            Back
          </Button>
          <Button
            onClick={handleSendManualEmail}
            disabled={!affiliateEmail || !affiliateName}
          >
            Send Email
          </Button>
        </div>
      </div>
    );
  };

  return (
    <Sheet
      open={true}
      onOpenChange={() => {
        // Reset the affiliate selection state when closing
        setShowAffiliateSelection(false);
        onClose();
      }}
    >
      <SheetContent
        side="right"
        className="w-[800px] sm:max-w-[800px] overflow-y-auto"
      >
        <SheetHeader className="pb-6">
          <div className="flex items-center justify-between">
            <div>
              <SheetTitle className="text-2xl font-semibold">
                {isEditMode ? "Edit Quote" : statusConfig.title}
              </SheetTitle>
              <SheetDescription className="text-base mt-1">
                {isEditMode
                  ? "Modify quote details and settings"
                  : statusConfig.description}
              </SheetDescription>
            </div>
            {!isEditMode && getStatusBadge()}
          </div>
        </SheetHeader>

        {/* Render edit panel or normal content */}
        {isEditMode ? (
          <QuoteEditPanel
            quote={quote}
            isEmergencyEdit={effectiveUserContext.userRoles?.includes(
              "SUPER_ADMIN"
            )}
            onSave={handleQuoteSave}
            onCancel={() => setIsEditMode(false)}
            userRole={(effectiveUserContext.userRoles?.[0] as any) || "CLIENT"}
          />
        ) : (
          <div className="space-y-6">
            {/* Main content - CTAs are already in the row, no need to duplicate */}
            {renderContent()}
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
}
