"use client"

import { useState } from 'react'
import { But<PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Badge } from "@/app/components/ui/badge"
import { 
  Crown, 
  Shield, 
  Star, 
  CheckCircle, 
  Clock, 
  AlertTriangle, 
  ArrowRight,
  Loader2,
  MapPin,
  Users
} from "lucide-react"

interface AffiliateOffer {
  id: string
  affiliateId: string
  affiliateName: string
  affiliateTier: 'Elite' | 'Premium' | 'Standard'
  vehicleType: string
  vehicleModel: string
  capacity: number
  rating: number
  responseTime: string
  estimatedArrival: string
  features: string[]
  baseRate: number
  totalPrice: number
  availability: 'confirmed' | 'pending' | 'unavailable'
  notes?: string
}

interface SelectedAffiliate {
  id: string
  order: number
}

interface SophisticatedAffiliateSelectionProps {
  affiliates: any[]
  quoteRequest: any
  onSubmit: (selectedAffiliates: SelectedAffiliate[]) => void
  isLoading?: boolean
}

export function SophisticatedAffiliateSelection({
  affiliates,
  quoteRequest,
  onSubmit,
  isLoading = false
}: SophisticatedAffiliateSelectionProps) {
  const [selectedAffiliates, setSelectedAffiliates] = useState<SelectedAffiliate[]>([])

  // Convert affiliate data to offer format
  const offers: AffiliateOffer[] = affiliates.map((affiliate, index) => ({
    id: `offer-${affiliate.id}`,
    affiliateId: affiliate.id,
    affiliateName: affiliate.name || affiliate.company_name,
    affiliateTier: affiliate.tier || (affiliate.rating >= 4.5 ? 'Elite' : affiliate.rating >= 4.0 ? 'Premium' : 'Standard'),
    vehicleType: affiliate.vehicleType || 'Sedan',
    vehicleModel: affiliate.vehicleModel || `${affiliate.vehicleType || 'Sedan'} Vehicle`,
    capacity: affiliate.capacity || 4,
    rating: affiliate.rating || 4.5,
    responseTime: affiliate.avgResponseTime || '< 15 min',
    estimatedArrival: affiliate.estimatedArrival || '10-15 min',
    features: affiliate.features || ['Professional Driver', 'Clean Vehicle', 'On-time Service'],
    baseRate: affiliate.baseRate || affiliate.base_rate || 0,
    totalPrice: affiliate.totalRate || affiliate.total_price || affiliate.estimated_rate || affiliate.baseRate || 0,
    availability: affiliate.availability || 'confirmed',
    notes: affiliate.notes
  }))

  const handleAffiliateSelect = (affiliateId: string) => {
    setSelectedAffiliates(prev => {
      const isAlreadySelected = prev.some(a => a.id === affiliateId)
      
      if (isAlreadySelected) {
        // Remove from selection
        return prev.filter(a => a.id !== affiliateId)
          .map((a, index) => ({ ...a, order: index + 1 }))
      } else {
        // Add to selection
        return [...prev, { id: affiliateId, order: prev.length + 1 }]
      }
    })
  }

  const getTierBadgeVariant = (tier: string) => {
    switch (tier) {
      case 'Elite': return 'default'
      case 'Premium': return 'secondary'
      case 'Standard': return 'outline'
      default: return 'outline'
    }
  }

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'Elite': return <Crown className="h-4 w-4" />
      case 'Premium': return <Shield className="h-4 w-4" />
      case 'Standard': return <Star className="h-4 w-4" />
      default: return null
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Available Transportation Options</CardTitle>
          <CardDescription>
            Based on your request: {quoteRequest.pickup_location} → {quoteRequest.dropoff_location}
            on {quoteRequest.date} at {quoteRequest.time} for {quoteRequest.passenger_count} passengers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <div className="flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-800">Select Your Preferred Order</h4>
                <p className="text-sm text-blue-700 mt-1">
                  Click on affiliates below to select them in your preferred order.
                  Quote requests will be sent to affiliates in the order you select them.
                </p>
                {selectedAffiliates.length > 0 && (
                  <p className="text-sm text-blue-600 mt-2 font-medium">
                    Selected: {selectedAffiliates.length} affiliate{selectedAffiliates.length !== 1 ? 's' : ''}
                  </p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-3">
        {offers.map((offer) => {
          const isSelected = selectedAffiliates.some(a => a.id === offer.affiliateId)
          const selectionOrder = selectedAffiliates.find(a => a.id === offer.affiliateId)?.order

          return (
            <div
              key={offer.id}
              className={`relative border rounded-lg p-4 cursor-pointer transition-all ${
                isSelected
                  ? 'border-blue-500 bg-blue-50 shadow-md'
                  : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
              }`}
              onClick={() => handleAffiliateSelect(offer.affiliateId)}
            >
              {/* Selection Badge */}
              {isSelected && (
                <div className="absolute -top-2 -right-2 z-10 bg-blue-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center">
                  {selectionOrder}
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="font-semibold text-gray-900">{offer.affiliateName}</h3>
                    <Badge variant={getTierBadgeVariant(offer.affiliateTier)} className="text-xs">
                      {offer.affiliateTier}
                    </Badge>
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      <span className="text-xs font-medium">{offer.rating}</span>
                    </div>
                  </div>

                  <div className="text-sm text-gray-600 mb-2">
                    {offer.vehicleModel} • {offer.vehicleType} • {offer.capacity} passengers
                  </div>

                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <span>{offer.responseTime} response</span>
                    <span>{offer.estimatedArrival} arrival</span>
                    {offer.availability === 'confirmed' && (
                      <span className="text-green-600 font-medium">✓ Available</span>
                    )}
                  </div>
                </div>

                <div className="text-right ml-4">
                  <div className="text-xl font-bold text-blue-600">
                    ${offer.totalPrice}
                  </div>
                  <div className="text-xs text-gray-500">Total</div>
                </div>
              </div>

              {/* Features - compact display */}
              {offer.features && offer.features.length > 0 && (
                <div className="mt-3 flex flex-wrap gap-1">
                  {offer.features.slice(0, 3).map((feature, index) => (
                    <span key={index} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                      {feature}
                    </span>
                  ))}
                  {offer.features.length > 3 && (
                    <span className="text-xs text-gray-500">+{offer.features.length - 3} more</span>
                  )}
                </div>
              )}
            </div>
          )
        })}
      </div>



      {/* Submit Selected Affiliates */}
      {offers.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-2">Submit Quote Request</h3>
                <p className="text-muted-foreground">
                  {selectedAffiliates.length === 0
                    ? 'Select at least one affiliate to send your quote request'
                    : `Send quote request to ${selectedAffiliates.length} selected affiliate${selectedAffiliates.length !== 1 ? 's' : ''} in order`
                  }
                </p>
              </div>

              {selectedAffiliates.length > 0 && (
                <div className="flex flex-wrap justify-center gap-2 mb-4">
                  {selectedAffiliates
                    .sort((a, b) => a.order - b.order)
                    .map((selected, index) => {
                      const affiliate = offers.find(o => o.affiliateId === selected.id)
                      return (
                        <Badge key={selected.id} variant="outline" className="text-sm">
                          #{index + 1} {affiliate?.affiliateName}
                        </Badge>
                      )
                    })
                  }
                </div>
              )}

              <Button
                onClick={() => onSubmit(selectedAffiliates.sort((a, b) => a.order - b.order))}
                disabled={selectedAffiliates.length === 0 || isLoading}
                size="lg"
                className="px-8"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Submitting Quote...
                  </>
                ) : (
                  <>
                    <ArrowRight className="h-4 w-4 mr-2" />
                    Submit Quote Request
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {offers.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <AlertTriangle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No Options Available</h3>
              <p className="text-muted-foreground mb-4">
                No affiliates are available for your requested time and location.
                Try adjusting your requirements or request a custom quote.
              </p>
              <Button variant="outline">
                Modify Request
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
