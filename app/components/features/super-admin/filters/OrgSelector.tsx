"use client";

import React, { useEffect, useState } from "react";
import { useGlobalFilters } from "@/app/contexts/GlobalFilterContext";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import { getSupabaseClient } from "@/lib/supabase"; // Updated import
import { SupabaseClient } from "@supabase/supabase-js"; // Added for type

interface Org {
  id: string;
  name: string;
  industry?: string;
  tenant_id?: string;
}

export function OrgSelector() {
  const { selectedOrg, setSelectedOrg } = useGlobalFilters();
  const [orgs, setOrgs] = useState<Org[]>([]);
  const [loading, setLoading] = useState(true);
  const [supabase, setSupabase] = useState<SupabaseClient | null>(null); // Added client state

  useEffect(() => {
    const client = getSupabaseClient();
    setSupabase(client);
  }, []);

  useEffect(() => {
    if (!supabase) return; // Guard for supabase client

    const fetchOrgs = async () => {
      setLoading(true);
      // Fetch from 'organizations' table (client organizations, not affiliates)
      const { data, error } = await supabase
        .from("organizations")
        .select("id, name, industry, tenant_id");
      if (error) {
        console.error("Error fetching client organizations:", error);
      } else if (data) {
        // Map 'id' and 'name' directly
        const formattedOrgs = data.map((orgData) => ({
          id: orgData.id,
          name: orgData.name || "Unnamed Organization",
          industry: orgData.industry,
          tenant_id: orgData.tenant_id,
        }));
        setOrgs(formattedOrgs);
      }
      setLoading(false);
    };
    fetchOrgs();
  }, [supabase]); // Add supabase to dependency array

  const handleOrgChange = (value: string) => {
    if (value === "all") {
      setSelectedOrg("all");
    } else {
      const org = orgs.find((t) => t.id === value) || null;
      setSelectedOrg(org);
    }
  };

  let currentSelectedValue: string;
  if (selectedOrg === "all") {
    currentSelectedValue = "all";
  } else if (selectedOrg) {
    currentSelectedValue = selectedOrg.id;
  } else {
    currentSelectedValue = "";
  }

  if (loading) {
    return <p className="text-sm p-2">Loading orgs...</p>;
  }

  return (
    <div className="min-w-[200px]">
      <Select value={currentSelectedValue} onValueChange={handleOrgChange}>
        <SelectTrigger className="h-9 text-sm">
          <SelectValue placeholder="Select Org" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Orgs (God's View)</SelectItem>
          {orgs.map((org) => (
            <SelectItem key={org.id} value={org.id}>
              {org.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
