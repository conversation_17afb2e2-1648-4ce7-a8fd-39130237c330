"use client";

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/app/components/ui/avatar";
import { Button } from "@/app/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  CreditCard,
  LogOut,
  Settings,
  User as UserIcon,
  Building2,
} from "lucide-react";
import { useAuth } from "@/lib/auth/context";
import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { getSupabaseClient } from "@/lib/supabase";

export function UserNav() {
  const { user, logout } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [userRole, setUserRole] = useState<string[] | null>(null);
  const supabase = getSupabaseClient();

  // Check if we're in the affiliate portal
  const isAffiliatePortal = pathname?.startsWith("/affiliate");

  // Fetch user role
  useEffect(() => {
    const fetchUserRole = async () => {
      if (!user || !supabase) return;

      try {
        const { data: profiles, error } = await supabase
          .from("user_profiles")
          .select("roles")
          .eq("user_id", user.id);

        if (error) {
          console.error("Error fetching user role:", error);
          return;
        }

        // Handle case where no profile exists - create a default CLIENT profile
        if (!profiles || profiles.length === 0) {
          console.warn(
            "No profile found for user:",
            user.id,
            "- creating default CLIENT profile"
          );

          try {
            // Create a default CLIENT profile
            const { error: insertError } = await supabase
              .from("user_profiles")
              .insert({
                user_id: user.id,
                roles: ["CLIENT"],
              });

            if (insertError) {
              console.error("Error creating user profile:", insertError);
              setUserRole(null);
              return;
            }

            // Set the default role
            setUserRole(["CLIENT"]);
            return;
          } catch (error) {
            console.error("Error creating user profile:", error);
            setUserRole(null);
            return;
          }
        }

        if (profiles.length > 1) {
          console.warn("Multiple profiles found for user:", user.id);
        }

        setUserRole(profiles[0]?.roles || null);
      } catch (error) {
        console.error("Error:", error);
      }
    };

    fetchUserRole();
  }, [user, supabase]);

  // If there's no user, we'll show a sign-in button
  if (!user) {
    return (
      <Button variant="outline" onClick={() => router.push("/login")}>
        Sign In
      </Button>
    );
  }

  // Get user initials for avatar fallback
  const getInitials = () => {
    if (!user.email) return "U";
    return user.email.charAt(0).toUpperCase();
  };

  // Get display name
  const getDisplayName = () => {
    // If we have metadata with a name, use that
    if (user.user_metadata?.full_name) {
      return user.user_metadata.full_name;
    }

    // Fall back to email
    return user.email || "User";
  };

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      await logout();
      // Navigation will be handled by the auth provider
    } catch (error) {
      console.error("Logout error:", error);
      setIsLoggingOut(false);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-10 w-10 rounded-full">
          <Avatar className="h-10 w-10">
            <AvatarImage src="/avatars/01.svg" alt={getDisplayName()} />
            <AvatarFallback>{getInitials()}</AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">
              {getDisplayName()}
            </p>
            <p className="text-xs leading-none text-muted-foreground">
              {user.email}
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem asChild>
            <Link href="/profile">
              <UserIcon className="mr-2 h-4 w-4" />
              <span>Profile</span>
              <DropdownMenuShortcut>⇧⌘P</DropdownMenuShortcut>
            </Link>
          </DropdownMenuItem>
          {isAffiliatePortal && (
            <DropdownMenuItem asChild>
              <Link href="/affiliate/company">
                <Building2 className="mr-2 h-4 w-4" />
                <span>Company Profile</span>
                <DropdownMenuShortcut>⇧⌘C</DropdownMenuShortcut>
              </Link>
            </DropdownMenuItem>
          )}
          {isAffiliatePortal && !userRole?.includes("dispatcher") && (
            <DropdownMenuItem asChild>
              <Link href="/affiliate/billing">
                <CreditCard className="mr-2 h-4 w-4" />
                <span>Billing</span>
                <DropdownMenuShortcut>⌘B</DropdownMenuShortcut>
              </Link>
            </DropdownMenuItem>
          )}
          <DropdownMenuItem asChild>
            <Link
              href={isAffiliatePortal ? "/affiliate/settings" : "/settings"}
            >
              <Settings className="mr-2 h-4 w-4" />
              <span>Settings</span>
              <DropdownMenuShortcut>⌘S</DropdownMenuShortcut>
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleLogout} disabled={isLoggingOut}>
          <LogOut className="mr-2 h-4 w-4" />
          <span>{isLoggingOut ? "Signing out..." : "Sign out"}</span>
          <DropdownMenuShortcut>⇧⌘Q</DropdownMenuShortcut>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
