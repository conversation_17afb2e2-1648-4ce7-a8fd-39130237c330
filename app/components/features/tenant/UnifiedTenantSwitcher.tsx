"use client"

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from "@/app/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu"
import { Badge } from "@/app/components/ui/badge"
import { Building2, ChevronDown, Network, Globe, Crown } from "lucide-react"

interface Tenant {
  id: string
  name: string
  slug: string
  tenant_type: 'shared' | 'segregated' | 'white_label'
  status: 'active' | 'inactive' | 'suspended'
  domain?: string
}

interface UnifiedTenantSwitcherProps {
  className?: string
}

export function UnifiedTenantSwitcher({ className }: UnifiedTenantSwitcherProps) {
  const [tenants, setTenants] = useState<Tenant[]>([])
  const [currentTenant, setCurrentTenant] = useState<Tenant | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchTenants()
  }, [])

  const fetchTenants = async () => {
    try {
      setLoading(true)
      
      // Use the tenant API to get available networks
      const response = await fetch('/api/tenant/switch')
      if (!response.ok) {
        throw new Error('Failed to fetch tenants')
      }
      
      const data = await response.json()
      setTenants(data.availableTenants || [])
      
      // Set current tenant from API response or default to shared
      if (data.currentTenant) {
        const current = data.availableTenants?.find((t: Tenant) => t.id === data.currentTenant)
        if (current) {
          setCurrentTenant(current)
        }
      } else {
        // Set default tenant (TransFlow shared)
        const defaultTenant = data.availableTenants?.find((t: Tenant) => t.tenant_type === 'shared') || data.availableTenants?.[0]
        if (defaultTenant) {
          setCurrentTenant(defaultTenant)
        }
      }
    } catch (error) {
      console.error('Error fetching tenants:', error)
    } finally {
      setLoading(false)
    }
  }

  const switchTenant = async (tenant: Tenant) => {
    try {
      // Call the tenant switch API
      const response = await fetch('/api/tenant/switch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tenantId: tenant.id,
          organizationId: null // Network switching doesn't specify organization
        })
      })
      
      if (!response.ok) {
        throw new Error('Failed to switch tenant')
      }
      
      setCurrentTenant(tenant)
      
      // Store the selected tenant in session storage
      sessionStorage.setItem("currentTenant", JSON.stringify(tenant))
      
      // Refresh the page to apply the new tenant context
      window.location.reload()
    } catch (error) {
      console.error("Error switching tenant:", error)
    }
  }

  const getTenantIcon = (tenantType: string) => {
    switch (tenantType) {
      case "shared":
        return <Globe className="h-4 w-4" />
      case "segregated":
        return <Network className="h-4 w-4" />
      case "white_label":
        return <Crown className="h-4 w-4" />
      default:
        return <Building2 className="h-4 w-4" />
    }
  }

  const getTenantTypeLabel = (tenantType: string) => {
    switch (tenantType) {
      case "shared":
        return "Shared SaaS"
      case "segregated":
        return "TNC Network"
      case "white_label":
        return "White-Label"
      default:
        return "Unknown"
    }
  }

  const getTenantTypeBadgeVariant = (tenantType: string) => {
    switch (tenantType) {
      case "shared":
        return "default"
      case "segregated":
        return "secondary"
      case "white_label":
        return "outline"
      default:
        return "default"
    }
  }

  if (loading) {
    return (
      <Button variant="outline" size="sm" disabled className={className}>
        <Network className="h-4 w-4 mr-2" />
        Loading...
      </Button>
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className={className}>
          {currentTenant ? (
            getTenantIcon(currentTenant.tenant_type)
          ) : (
            <Network className="h-4 w-4" />
          )}
          <span className="ml-2 hidden sm:inline">
            {currentTenant?.name || "Select Network"}
          </span>
          <ChevronDown className="h-4 w-4 ml-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel>Switch Network Context</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {tenants.map((tenant) => (
          <DropdownMenuItem
            key={tenant.id}
            onClick={() => switchTenant(tenant)}
            className="flex items-center justify-between p-3 cursor-pointer"
          >
            <div className="flex items-center space-x-3">
              {getTenantIcon(tenant.tenant_type)}
              <div className="flex flex-col">
                <span className="font-medium">{tenant.name}</span>
                {tenant.domain && (
                  <span className="text-xs text-muted-foreground">
                    {tenant.domain}
                  </span>
                )}
              </div>
            </div>
            <div className="flex flex-col items-end space-y-1">
              <Badge
                variant={getTenantTypeBadgeVariant(tenant.tenant_type)}
                className="text-xs"
              >
                {getTenantTypeLabel(tenant.tenant_type)}
              </Badge>
              {currentTenant?.id === tenant.id && (
                <Badge variant="default" className="text-xs">
                  Current
                </Badge>
              )}
            </div>
          </DropdownMenuItem>
        ))}
        
        {tenants.length === 0 && (
          <DropdownMenuItem disabled>
            No networks available
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
