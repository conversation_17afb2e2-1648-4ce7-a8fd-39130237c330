"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/app/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu";
import { Badge } from "@/app/components/ui/badge";
import { Building2, ChevronDown, Network, Globe } from "lucide-react";
import { getSupabaseClient } from "@/lib/supabase";

interface Network {
  id: string;
  name: string;
  slug: string;
  tenant_type: "shared" | "segregated" | "white_label";
  status: "active" | "inactive" | "suspended";
  domain?: string;
}

interface NetworkSwitcherProps {
  className?: string;
}

export function NetworkSwitcher({ className }: NetworkSwitcherProps) {
  const [networks, setNetworks] = useState<Network[]>([]);
  const [currentNetwork, setCurrentNetwork] = useState<Network | null>(null);
  const [loading, setLoading] = useState(true);
  const supabase = getSupabaseClient();

  useEffect(() => {
    fetchNetworks();
  }, []);

  const fetchNetworks = async () => {
    try {
      setLoading(true);

      // Use the tenant API to get available networks
      const response = await fetch("/api/tenant/switch");
      if (!response.ok) {
        throw new Error("Failed to fetch networks");
      }

      const data = await response.json();
      setNetworks(data.availableTenants || []);

      // Set current network from API response or default to shared
      if (data.currentTenant) {
        const current = data.availableTenants?.find(
          (n: Network) => n.id === data.currentTenant
        );
        if (current) {
          setCurrentNetwork(current);
        }
      } else {
        // Set default network (TransFlow shared)
        const defaultNetwork =
          data.availableTenants?.find(
            (n: Network) => n.tenant_type === "shared"
          ) || data.availableTenants?.[0];
        if (defaultNetwork) {
          setCurrentNetwork(defaultNetwork);
        }
      }
    } catch (error) {
      console.error("Error fetching networks:", error);
    } finally {
      setLoading(false);
    }
  };

  const switchNetwork = async (network: Network) => {
    try {
      // Call the tenant switch API
      const response = await fetch("/api/tenant/switch", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          tenantId: network.id,
          organizationId: null, // Network switching doesn't specify organization
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to switch network");
      }

      setCurrentNetwork(network);

      // Store the selected network in session storage
      sessionStorage.setItem("currentNetwork", JSON.stringify(network));

      // Refresh the page to apply the new tenant context
      window.location.reload();
    } catch (error) {
      console.error("Error switching network:", error);
    }
  };

  const getNetworkIcon = (tenantType: string) => {
    switch (tenantType) {
      case "shared":
        return <Globe className="h-4 w-4" />;
      case "segregated":
        return <Network className="h-4 w-4" />;
      case "white_label":
        return <Building2 className="h-4 w-4" />;
      default:
        return <Building2 className="h-4 w-4" />;
    }
  };

  const getNetworkTypeLabel = (tenantType: string) => {
    switch (tenantType) {
      case "shared":
        return "Shared";
      case "segregated":
        return "TNC";
      case "white_label":
        return "White-Label";
      default:
        return "Unknown";
    }
  };

  const getNetworkTypeBadgeVariant = (tenantType: string) => {
    switch (tenantType) {
      case "shared":
        return "default";
      case "segregated":
        return "secondary";
      case "white_label":
        return "outline";
      default:
        return "default";
    }
  };

  if (loading) {
    return (
      <Button variant="outline" size="sm" disabled className={className}>
        <Network className="h-4 w-4 mr-2" />
        Loading...
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className={className}>
          {currentNetwork ? (
            getNetworkIcon(currentNetwork.tenant_type)
          ) : (
            <Network className="h-4 w-4" />
          )}
          <span className="ml-2 hidden sm:inline">
            {currentNetwork?.name || "Select Network"}
          </span>
          <ChevronDown className="h-4 w-4 ml-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel>Switch Network Context</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {networks.map((network) => (
          <DropdownMenuItem
            key={network.id}
            onClick={() => switchNetwork(network)}
            className="flex items-center justify-between p-3 cursor-pointer"
          >
            <div className="flex items-center space-x-3">
              {getNetworkIcon(network.tenant_type)}
              <div className="flex flex-col">
                <span className="font-medium">{network.name}</span>
                {network.domain && (
                  <span className="text-xs text-muted-foreground">
                    {network.domain}
                  </span>
                )}
              </div>
            </div>
            <div className="flex flex-col items-end space-y-1">
              <Badge
                variant={getNetworkTypeBadgeVariant(network.tenant_type)}
                className="text-xs"
              >
                {getNetworkTypeLabel(network.tenant_type)}
              </Badge>
              {currentNetwork?.id === network.id && (
                <Badge variant="default" className="text-xs">
                  Current
                </Badge>
              )}
            </div>
          </DropdownMenuItem>
        ))}

        {networks.length === 0 && (
          <DropdownMenuItem disabled>No networks available</DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
