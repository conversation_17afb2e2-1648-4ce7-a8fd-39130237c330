"use client";

import React, { useState } from "react";
import { useViewMode } from "@/app/contexts/ViewModeContext";
import { Card } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { But<PERSON> } from "@/app/components/ui/button";
import { cn } from "@/lib/utils";
import { Map } from "@/app/components/shared/map";
import { format, formatDistanceToNow } from "date-fns";
import { Avatar, AvatarFallback } from "@/app/components/ui/avatar";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/app/components/ui/tooltip";
import {
  Clock,
  MapPin,
  Car,
  Users,
  Building2,
  DollarSign,
  Calendar,
  Timer,
  CheckCircle2,
  XCircle,
  AlertTriangle,
  CalendarClock,
  Briefcase,
  User,
  ArrowDownToLine,
  ArrowUpFromLine,
  Building,
  ChevronDown,
  ChevronUp,
  Phone,
  MessageSquare,
  Baby,
  Plane,
  Crown,
  Edit,
  Archive,
  Send,
  RotateCcw,
  Eye,
  Trash2,
  MoreHorizontal,
} from "lucide-react";
import { Progress } from "@/app/components/ui/progress";
import { formatReferenceNumber } from "@/lib/utils/reference-generator";
import { useAuth } from "@/lib/auth/context";
import { useTenant } from "@/app/contexts/TenantContext";
import { hasRole } from "@/app/lib/auth";
import type { UserRole } from "@/src/types/roles";
import {
  getQuoteActionPermissions,
  getTenantButtonStyle,
  getActionLabels,
} from "@/app/utils/quote-permissions";

// Types
export interface QuoteRowData {
  id: string;
  reference_number?: string;
  customer_id: string;
  service_type: "airport" | "hourly" | "point";
  vehicle_type: string;
  pickup_location: string;
  dropoff_location: string;
  date: string;
  time: string;
  duration: string;
  distance: string;
  status: string;
  passenger_count: number | null;
  luggage_count: number | null;
  special_requests?: string;
  priority: "high" | "medium" | "low";
  total_amount: number | null;
  created_at: string;
  updated_at: string;
  offer_id?: string;
  customer?: {
    id: string;
    email: string;
    full_name: string;
    phone?: string;
    company_name?: string;
  };
  duration_hours?: number | null;
  is_multi_day?: boolean;
  flight_number?: string;
  is_return_trip?: boolean;
  return_date?: string;
  return_time?: string;
  return_flight_number?: string;
  car_seats_needed?: boolean;
  infant_seats?: number | null;
  toddler_seats?: number | null;
  booster_seats?: number | null;
  intermediate_stops?: Array<{
    location: string;
    order_number: number;
    latitude?: number | null;
    longitude?: number | null;
  }>;
  timeline?: Array<{
    timestamp: Date | string;
    action: string;
    user?: string;
    details?: string;
  }>;
  response_time?: string;
  affiliate_count?: number | null;
  expiry_time?: string;
  last_updated?: string;
  affiliate_responses?: Array<{
    status: string;
    affiliate_name?: string;
    rate_amount?: number | null;
    rate_currency?: string;
    type?: string;
  }>;
  rate_proposals?: Array<{
    status: string;
    company_id?: string;
    rate_amount?: number | null;
    rate_currency?: string;
  }>;
  accepted_by?: string;
  affiliate_selected?: string[];
  company?: string;
  is_vip?: boolean;
  cancellation_policy?: {
    free_until_hours: number;
    partial_charge_hours: number;
    partial_charge_percentage: number;
  };
  communications?: Array<{
    timestamp: Date | string;
    user: string;
    message: string;
  }>;
  city?: string | null;
  notes?: string;
  event_name?: string;
  pickup_latitude?: number | null;
  pickup_longitude?: number | null;
  dropoff_latitude?: number | null;
  dropoff_longitude?: number | null;
  gratuity?: number;
  admin_fee?: number;
  trips?: Array<{
    id: string;
    status: string;
    created_at: string;
    updated_at: string;
    driver_id?: string;
    company_id?: string;
    estimated_distance?: string;
    estimated_duration?: string;
    scheduled_pickup_date?: string;
    scheduled_pickup_time?: string;
    estimated_amount?: number;
    final_amount?: number;
    trip_notes?: string;
    reference_number?: string;
  }>;
}

interface QuoteRowProps {
  quote: QuoteRowData;
  isSelected?: boolean;
  onClick?: () => void;
  onAccept?: () => void;
  onReject?: () => void;
  onRequestChanges?: () => void;
  onSendToAffiliates?: () => void;
  onFixedRate?: () => void;
  onSubmitRate?: () => void;
  onCall?: () => void;
  onMessage?: () => void;
  onArchive?: () => void;
  userType?: "admin" | "customer" | "affiliate" | "driver";
  showActions?: boolean;
  expandable?: boolean;
}

// Helper Functions
const getStatusColor = (status: string) => {
  switch (status?.toLowerCase() || "") {
    case "accepted":
      return "text-green-600 bg-green-50";
    case "rejected":
      return "text-red-600 bg-red-50";
    case "new":
    case "pending":
    case "pending_quote":
      return "text-blue-600 bg-blue-50";
    case "fixed_offer":
    case "quote_ready":
    case "quote_assigned":
      return "text-purple-600 bg-purple-50";
    case "rate_requested":
    case "sent_to_affiliates":
      return "text-yellow-600 bg-yellow-50";
    case "change_requested":
      return "text-indigo-600 bg-indigo-50";
    default:
      return "text-gray-600 bg-gray-50";
  }
};

const getPriorityColor = (priority?: string) => {
  switch (priority) {
    case "high":
      return "bg-red-500";
    case "medium":
      return "bg-yellow-500";
    case "low":
      return "bg-blue-500";
    default:
      return "bg-gray-300";
  }
};

const formatDate = (date: string | Date | undefined) => {
  if (!date) return "";
  try {
    return format(new Date(date), "MMM d, yyyy");
  } catch (error) {
    console.error("Error formatting date:", error, date);
    return date.toString();
  }
};

const formatTime = (date: string | Date | undefined, time?: string) => {
  if (time) return time;
  if (!date) return "";
  try {
    return format(new Date(date), "HH:mm");
  } catch (error) {
    console.error("Error formatting time:", error, date);
    return "";
  }
};

const getStatusIcon = (status: string) => {
  switch (status.toLowerCase()) {
    case "accepted":
      return <CheckCircle2 className="h-3.5 w-3.5 text-green-600" />;
    case "rejected":
      return <XCircle className="h-3.5 w-3.5 text-red-600" />;
    case "new":
    case "pending":
    case "pending_quote":
      return <AlertTriangle className="h-3.5 w-3.5 text-blue-600" />;
    case "fixed_offer":
    case "quote_ready":
    case "quote_assigned":
      return <DollarSign className="h-3.5 w-3.5 text-purple-600" />;
    case "rate_requested":
    case "sent_to_affiliates":
      return <Timer className="h-3.5 w-3.5 text-yellow-600" />;
    case "change_requested":
      return <Edit className="h-3.5 w-3.5 text-indigo-600" />;
    default:
      return <Clock className="h-3.5 w-3.5 text-gray-600" />;
  }
};

const getShortLocation = (location: string) => {
  if (!location) return "";
  const parts = location.split(",");
  return parts.length > 0 ? parts[0].trim() : location;
};

// Main Component
const QuoteRowComponent = ({
  quote,
  isSelected,
  onClick,
  onAccept,
  onReject,
  onRequestChanges,
  onSendToAffiliates,
  onFixedRate,
  onSubmitRate,
  onCall,
  onMessage,
  onArchive,
  userType = "admin",
  showActions = true,
  expandable = true,
}: QuoteRowProps) => {
  const [expanded, setExpanded] = useState(false);
  const [stopsExpanded, setStopsExpanded] = useState(false);
  const [requestsExpanded, setRequestsExpanded] = useState(false);
  const { viewMode } = useViewMode();

  // Auth and tenant context
  const { user } = useAuth();
  const { currentTenant } = useTenant();

  // Get user roles from auth context
  const userRoles = user?.roles || [];

  // Debug logging
  React.useEffect(() => {
    console.log("QuoteRow rendering for quote ID:", quote.id, {
      userType,
      viewMode,
      pickup_location: quote.pickup_location || "Not provided",
      dropoff_location: quote.dropoff_location || "Not provided",
      vehicle_type: quote.vehicle_type || "Not specified",
      service_type: quote.service_type || "Not specified",
      customer: quote.customer?.full_name || "No customer info",
      status: quote.status || "No status",
      date: quote.date || "No date",
      time: quote.time || "No time",
      city: quote.city || "No city",
    });
  }, [quote, userType, viewMode]);

  // Sanitized quote data
  const safeQuote = {
    ...quote,
    pickup_location: quote.pickup_location || "Not provided",
    dropoff_location: quote.dropoff_location || "Not provided",
    vehicle_type: quote.vehicle_type || "Standard Vehicle",
    service_type: quote.service_type || "airport",
    date: quote.date || "",
    time: quote.time || "",
    status: quote.status || "pending",
    customer: quote.customer || {
      id: "no-id",
      email: "",
      full_name: "No Customer Name",
    },
    passenger_count: quote.passenger_count || 0,
    luggage_count: quote.luggage_count || 0,
    priority: quote.priority || "medium",
    total_amount: quote.total_amount || 0,
    created_at: quote.created_at || new Date().toISOString(),
    updated_at: quote.updated_at || new Date().toISOString(),
    city: quote.city || "",
    intermediate_stops: quote.intermediate_stops || [],
    affiliate_responses: quote.affiliate_responses || [],
    rate_proposals: quote.rate_proposals || [],
    trips: quote.trips || [],
    communications: quote.communications || [],
    timeline: quote.timeline || [],
  };

  // Role and status checks
  const isAdmin = userType === "admin";
  const isCustomer = userType === "customer";
  const isAffiliate = userType === "affiliate";
  const isPending = ["pending", "pending_quote", "new"].includes(
    safeQuote.status.toLowerCase()
  );
  const isRateRequested = ["rate_requested", "sent_to_affiliates"].includes(
    safeQuote.status.toLowerCase()
  );
  const isFixedOffer = [
    "fixed_offer",
    "quote_ready",
    "quote_assigned",
  ].includes(safeQuote.status.toLowerCase());
  const isAccepted = safeQuote.status.toLowerCase() === "accepted";
  const isRejected = safeQuote.status.toLowerCase() === "rejected";
  const isChangeRequested =
    safeQuote.status.toLowerCase() === "change_requested";

  // Get role-based permissions with workflow context
  const permissions = getQuoteActionPermissions({
    userRoles,
    tenantType: currentTenant?.tenant_type,
    quoteStatus: safeQuote.status,
    userType,
    // NEW: Add workflow type context
    workflowType: "pure_saas", // Default to Pure SaaS for current implementation
    clientSelectedAffiliates:
      !!(safeQuote as any).selectedAffiliates ||
      !!(safeQuote as any).affiliate_id, // Check if client already selected affiliates
  });

  // Get tenant-specific action labels
  const actionLabels = getActionLabels(currentTenant?.tenant_type);

  // Action visibility based on permissions and showActions prop
  const showAcceptRejectButtons = showActions && permissions.canAccept;
  const showRateRequestButton = showActions && permissions.canSendToAffiliates;
  const showFixedRateButton = showActions && permissions.canFixedRate;
  const showSubmitRateButton = showActions && permissions.canSubmitRate;
  const showChangeRequestButton = showActions && permissions.canRequestChanges;
  const showContactButtons = showActions && permissions.canContact;
  const showArchiveButton = showActions && permissions.canArchive;

  // Get expiry display
  const getExpiryDisplay = () => {
    if (!safeQuote.expiry_time) return null;
    try {
      const expiry = new Date(safeQuote.expiry_time);
      const now = new Date();
      if (expiry < now) return { text: "Expired", className: "text-red-600" };
      const diffMs = expiry.getTime() - now.getTime();
      const diffHours = Math.round(diffMs / (1000 * 60 * 60));
      if (diffHours < 4)
        return { text: `${diffHours}h remaining`, className: "text-red-600" };
      if (diffHours < 12)
        return { text: `${diffHours}h remaining`, className: "text-amber-600" };
      return { text: `${diffHours}h remaining`, className: "text-green-600" };
    } catch (e) {
      return {
        text: safeQuote.expiry_time.toString(),
        className: "text-muted-foreground",
      };
    }
  };

  // Get response counts
  const getResponseCounts = () => {
    const responses = safeQuote.affiliate_responses || [];
    return {
      accepted: responses.filter((r) => r?.status === "accepted").length,
      rejected: responses.filter((r) => r?.status === "rejected").length,
      pending: responses.filter((r) => r?.status === "pending" || !r?.status)
        .length,
      total: responses.length,
    };
  };

  const expiryDisplay =
    isRateRequested || isFixedOffer ? getExpiryDisplay() : null;
  const responseCounts = getResponseCounts();

  // Tenant-specific styling helper
  const getButtonStyle = (
    variant: "primary" | "secondary" | "outline" = "primary"
  ) => {
    return getTenantButtonStyle(
      currentTenant?.tenant_type,
      currentTenant?.branding,
      variant
    );
  };

  // Event handlers
  const toggleExpand = (e: React.MouseEvent) => {
    if (expandable) {
      e.stopPropagation();
      setExpanded(!expanded);
    }
  };

  const handleAccept = (e: React.MouseEvent) => {
    e.stopPropagation();
    onAccept?.();
  };

  const handleReject = (e: React.MouseEvent) => {
    e.stopPropagation();
    onReject?.();
  };

  const handleRequestChanges = (e: React.MouseEvent) => {
    e.stopPropagation();
    onRequestChanges?.();
  };

  const handleSendToAffiliates = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSendToAffiliates?.();
  };

  const handleFixedRate = (e: React.MouseEvent) => {
    e.stopPropagation();
    onFixedRate?.();
  };

  const handleSubmitRate = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSubmitRate?.();
  };

  const handleCall = (e: React.MouseEvent) => {
    e.stopPropagation();
    onCall?.();
  };

  const handleMessage = (e: React.MouseEvent) => {
    e.stopPropagation();
    onMessage?.();
  };

  const handleArchive = (e: React.MouseEvent) => {
    e.stopPropagation();
    onArchive?.();
  };

  // Render functions
  const renderOriginalView = () => (
    <Card
      className={cn(
        "relative overflow-hidden cursor-pointer transition-all mb-2",
        isSelected && "bg-accent/10 border-primary shadow-md",
        !isSelected &&
          `border-l-4 border-l-${getPriorityColor(safeQuote.priority).replace("bg-", "")}`,
        !isSelected && isRateRequested && "border-l-yellow-500",
        !isSelected && isFixedOffer && "border-l-purple-500",
        !isSelected && isPending && "border-l-blue-500",
        !isSelected && isAccepted && "border-l-green-500",
        !isSelected && isRejected && "border-l-red-500",
        !isSelected && isChangeRequested && "border-l-indigo-500",
        "hover:shadow-md hover:border-primary/50"
      )}
      onClick={onClick}
    >
      <div className="p-4">
        <div className="grid grid-cols-12 gap-4 items-start">
          <div className="col-span-3 sm:col-span-3">
            <div className="flex items-center gap-3 mb-3">
              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center shadow-sm border border-primary/10">
                <User className="w-5 h-5 text-primary" />
              </div>
              <div>
                <div className="font-semibold truncate max-w-[170px] text-base">
                  {safeQuote.customer?.full_name || "No Customer"}
                </div>
                <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
                  {safeQuote.passenger_count > 0 && (
                    <div className="flex items-center gap-1">
                      <Users className="h-3.5 w-3.5" />
                      <span>
                        {safeQuote.passenger_count}{" "}
                        {safeQuote.passenger_count === 1
                          ? "passenger"
                          : "passengers"}
                      </span>
                    </div>
                  )}
                </div>
              </div>
              {(safeQuote.customer?.full_name?.toLowerCase().includes("vip") ||
                safeQuote.is_vip) && (
                <Badge
                  variant="outline"
                  className="px-1.5 py-0.5 border-yellow-300 bg-yellow-50/80"
                >
                  <Crown className="h-3 w-3 text-yellow-500 fill-yellow-500 mr-1" />
                  <span className="text-xs font-medium">VIP</span>
                </Badge>
              )}
            </div>
            <div className="mt-2 text-xs space-y-1.5">
              {safeQuote.reference_number && (
                <div className="flex items-center px-2 py-1 bg-primary/5 rounded-md">
                  <span className="font-medium">
                    #{safeQuote.reference_number}
                  </span>
                </div>
              )}
              {safeQuote.service_type && (
                <div className="flex items-center gap-1.5">
                  {safeQuote.service_type === "airport" && (
                    <Plane className="h-3.5 w-3.5 text-primary" />
                  )}
                  {safeQuote.service_type === "hourly" && (
                    <Clock className="h-3.5 w-3.5 text-primary" />
                  )}
                  {safeQuote.service_type === "point" && (
                    <MapPin className="h-3.5 w-3.5 text-primary" />
                  )}
                  <span className="font-medium capitalize">
                    {safeQuote.service_type === "airport"
                      ? "Airport Transfer"
                      : safeQuote.service_type === "hourly"
                        ? "By the Hour"
                        : "Point to Point"}
                  </span>
                </div>
              )}
              <div className="flex items-center gap-1.5">
                <Car className="h-3.5 w-3.5 text-muted-foreground" />
                <span>{safeQuote.vehicle_type}</span>
              </div>
            </div>
          </div>
          <div className="col-span-6 sm:col-span-6">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-3">
                <div className="bg-primary/5 px-2.5 py-1.5 rounded-md flex items-center">
                  <Calendar className="h-3.5 w-3.5 text-primary mr-1.5" />
                  <span className="text-sm font-medium">
                    {formatDate(safeQuote.date)}
                  </span>
                </div>
                <div className="bg-primary/5 px-2.5 py-1.5 rounded-md flex items-center">
                  <Clock className="h-3.5 w-3.5 text-primary mr-1.5" />
                  <span className="text-sm font-medium">
                    {formatTime(safeQuote.date, safeQuote.time)}
                  </span>
                </div>
              </div>
              <Badge
                className={cn(
                  "px-2 py-1 flex items-center gap-1.5",
                  getStatusColor(safeQuote.status)
                )}
              >
                {getStatusIcon(safeQuote.status)}
                <span className="capitalize">
                  {safeQuote.status.replace("_", " ")}
                </span>
              </Badge>
            </div>
            <div className="space-y-2 mb-3">
              <div className="flex items-start gap-2">
                <div className="mt-1 h-4 w-4 rounded-full bg-green-500 flex-shrink-0"></div>
                <div>
                  <div className="text-xs text-muted-foreground">Pickup</div>
                  <div className="text-sm font-medium">
                    {safeQuote.pickup_location}
                  </div>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="mt-1 h-4 w-4 rounded-full bg-red-500 flex-shrink-0"></div>
                <div>
                  <div className="text-xs text-muted-foreground">Dropoff</div>
                  <div className="text-sm font-medium">
                    {safeQuote.dropoff_location}
                  </div>
                </div>
              </div>
            </div>
            {safeQuote.special_requests && (
              <div className="mt-2 text-xs text-muted-foreground">
                <span className="font-medium">Special Requests:</span>{" "}
                {safeQuote.special_requests}
              </div>
            )}
          </div>
          <div className="col-span-3 sm:col-span-3 flex flex-col justify-between h-full">
            <div>
              {safeQuote.total_amount !== null && (
                <div className="text-right">
                  <div className="text-lg font-bold">
                    ${safeQuote.total_amount.toFixed(2)}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Total Amount
                  </div>
                </div>
              )}
              {isAdmin && isRateRequested && (
                <div className="mt-3 bg-muted/30 rounded-md p-2">
                  <div className="text-xs font-medium mb-1">
                    Affiliate Responses:
                  </div>
                  <div className="grid grid-cols-3 gap-1 text-xs">
                    <div className="flex items-center">
                      <div className="h-2 w-2 rounded-full bg-green-500 mr-1"></div>
                      <span>{responseCounts.accepted}</span>
                    </div>
                    <div className="flex items-center">
                      <div className="h-2 w-2 rounded-full bg-red-500 mr-1"></div>
                      <span>{responseCounts.rejected}</span>
                    </div>
                    <div className="flex items-center">
                      <div className="h-2 w-2 rounded-full bg-yellow-500 mr-1"></div>
                      <span>{responseCounts.pending}</span>
                    </div>
                  </div>
                </div>
              )}
              {expiryDisplay && (
                <div
                  className={cn(
                    "mt-2 text-right text-sm",
                    expiryDisplay.className
                  )}
                >
                  <CalendarClock className="inline-block h-3.5 w-3.5 mr-1" />
                  {expiryDisplay.text}
                </div>
              )}
            </div>
            <div className="mt-4 space-y-2">
              {/* Removed duplicate action buttons - actions are available in contextual slider panel */}
              {showContactButtons && (
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    className={cn(
                      "w-full",
                      getButtonStyle("outline").className
                    )}
                    style={getButtonStyle("outline").style}
                    onClick={handleCall}
                  >
                    <Phone className="h-4 w-4 mr-1" /> Call
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className={cn(
                      "w-full",
                      getButtonStyle("outline").className
                    )}
                    style={getButtonStyle("outline").style}
                    onClick={handleMessage}
                  >
                    <MessageSquare className="h-4 w-4 mr-1" /> Message
                  </Button>
                </div>
              )}
              {showArchiveButton && (
                <Button
                  size="sm"
                  variant="ghost"
                  className="w-full"
                  onClick={handleArchive}
                >
                  <Archive className="h-4 w-4 mr-1" /> Archive
                </Button>
              )}
            </div>
          </div>
        </div>
        {expandable && (
          <div className="mt-3 pt-3 border-t border-border">
            <Button
              variant="ghost"
              size="sm"
              className="w-full flex items-center justify-center"
              onClick={toggleExpand}
            >
              {expanded ? (
                <>
                  <ChevronUp className="h-4 w-4 mr-1" /> Show Less
                </>
              ) : (
                <>
                  <ChevronDown className="h-4 w-4 mr-1" /> Show More
                </>
              )}
            </Button>
            {expanded && (
              <div className="mt-3 space-y-4">
                <div className="bg-gradient-to-r from-slate-50 to-white rounded-xl border overflow-hidden shadow-sm">
                  <div className="p-5">
                    <div className="flex items-center justify-between mb-5">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center shadow">
                          <MapPin className="h-6 w-6 text-primary" />
                        </div>
                        <div>
                          <h3 className="text-lg font-bold">Trip Details</h3>
                          <p className="text-muted-foreground text-sm">
                            {formatDate(safeQuote.date)} •{" "}
                            {safeQuote.time || formatTime(safeQuote.date)}
                          </p>
                        </div>
                      </div>
                      {(safeQuote.customer?.full_name ||
                        safeQuote.customer?.email) && (
                        <div className="text-right">
                          <div className="text-xs uppercase tracking-wide text-muted-foreground font-medium">
                            CLIENT
                          </div>
                          <div className="text-base font-bold">
                            {safeQuote.customer?.full_name || "Unknown"}
                          </div>
                          {safeQuote.customer?.email && (
                            <div className="text-sm text-muted-foreground flex items-center justify-end gap-1.5">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="14"
                                height="14"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="text-primary"
                              >
                                <rect
                                  width="20"
                                  height="16"
                                  x="2"
                                  y="4"
                                  rx="2"
                                />
                                <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                              </svg>
                              {safeQuote.customer.email}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                    <div className="space-y-5">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                        <div className="space-y-2">
                          <div className="flex gap-3">
                            <div className="flex-shrink-0 w-7 h-7 rounded-full bg-primary flex items-center justify-center shadow-md">
                              <ArrowUpFromLine className="h-3.5 w-3.5 text-white" />
                            </div>
                            <div className="flex-1">
                              <div className="text-xs uppercase tracking-wide text-muted-foreground font-medium">
                                PICKUP
                              </div>
                              <div className="text-sm font-medium">
                                {safeQuote.pickup_location}
                              </div>
                            </div>
                          </div>
                          {safeQuote.intermediate_stops &&
                            safeQuote.intermediate_stops.length > 0 && (
                              <div>
                                <button
                                  className="ml-10 text-sm text-primary font-medium flex items-center"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setStopsExpanded(!stopsExpanded);
                                  }}
                                >
                                  {safeQuote.intermediate_stops.length}{" "}
                                  {safeQuote.intermediate_stops.length === 1
                                    ? "stop"
                                    : "stops"}
                                  {stopsExpanded ? (
                                    <ChevronUp className="h-3.5 w-3.5 ml-1" />
                                  ) : (
                                    <ChevronDown className="h-3.5 w-3.5 ml-1" />
                                  )}
                                </button>
                                {stopsExpanded && (
                                  <div className="ml-10 mt-2 space-y-2">
                                    {safeQuote.intermediate_stops.map(
                                      (stop, i) => (
                                        <div
                                          key={i}
                                          className="flex items-center gap-2"
                                        >
                                          <div className="w-1.5 h-1.5 rounded-full bg-primary/60 flex-shrink-0"></div>
                                          <div className="text-sm">
                                            {stop.location}
                                          </div>
                                        </div>
                                      )
                                    )}
                                  </div>
                                )}
                              </div>
                            )}
                          <div className="flex gap-3">
                            <div className="flex-shrink-0 w-7 h-7 rounded-full bg-primary flex items-center justify-center shadow-md">
                              <ArrowDownToLine className="h-3.5 w-3.5 text-white" />
                            </div>
                            <div className="flex-1">
                              <div className="text-xs uppercase tracking-wide text-muted-foreground font-medium">
                                DROPOFF
                              </div>
                              <div className="text-sm font-medium">
                                {safeQuote.dropoff_location}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <div className="grid grid-cols-2 gap-3">
                            <div className="rounded-lg bg-slate-50 p-3">
                              <div className="text-xs uppercase tracking-wide text-muted-foreground font-medium mb-1">
                                TYPE
                              </div>
                              <div className="flex items-center">
                                {safeQuote.service_type === "airport" && (
                                  <Plane className="h-4 w-4 mr-1.5 text-primary" />
                                )}
                                {safeQuote.service_type === "hourly" && (
                                  <Clock className="h-4 w-4 mr-1.5 text-primary" />
                                )}
                                {safeQuote.service_type === "point" && (
                                  <MapPin className="h-4 w-4 mr-1.5 text-primary" />
                                )}
                                <span className="font-medium text-sm">
                                  {safeQuote.service_type === "airport"
                                    ? "Airport Transfer"
                                    : safeQuote.service_type === "hourly"
                                      ? "By the Hour"
                                      : "Point to Point"}
                                </span>
                              </div>
                            </div>
                            <div className="rounded-lg bg-slate-50 p-3">
                              <div className="text-xs uppercase tracking-wide text-muted-foreground font-medium mb-1">
                                VEHICLE
                              </div>
                              <div className="flex items-center">
                                <Car className="h-4 w-4 mr-1.5 text-primary" />
                                <span className="font-medium text-sm">
                                  {safeQuote.vehicle_type}
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="grid grid-cols-2 gap-3">
                            <div className="rounded-lg bg-slate-50 p-3">
                              <div className="text-xs uppercase tracking-wide text-muted-foreground font-medium mb-1">
                                PASSENGERS
                              </div>
                              <div className="flex items-center">
                                <Users className="h-4 w-4 mr-1.5 text-primary" />
                                <span className="font-medium text-sm">
                                  {safeQuote.passenger_count || "0"}
                                </span>
                                {safeQuote.luggage_count &&
                                  safeQuote.luggage_count > 0 && (
                                    <span className="ml-3 flex items-center">
                                      <Briefcase className="h-3.5 w-3.5 mr-1 text-primary" />
                                      {safeQuote.luggage_count}
                                    </span>
                                  )}
                              </div>
                            </div>
                            <div className="rounded-lg bg-slate-50 p-3">
                              <div className="text-xs uppercase tracking-wide text-muted-foreground font-medium mb-1">
                                EST. TRIP
                              </div>
                              <div className="text-sm space-x-2">
                                {safeQuote.duration && (
                                  <span className="inline-flex items-center">
                                    <Timer className="h-3.5 w-3.5 mr-1 text-primary" />
                                    {safeQuote.duration}
                                  </span>
                                )}
                                {safeQuote.distance && (
                                  <span className="inline-flex items-center">
                                    <MapPin className="h-3.5 w-3.5 mr-1 text-primary" />
                                    {safeQuote.distance}
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      {safeQuote.special_requests && (
                        <div className="mt-4">
                          <div className="flex items-center">
                            <div className="text-xs uppercase tracking-wide text-muted-foreground font-medium mr-2">
                              SPECIAL REQUESTS
                            </div>
                            <button
                              className="text-primary text-xs"
                              onClick={(e) => {
                                e.stopPropagation();
                                setRequestsExpanded(!requestsExpanded);
                              }}
                            >
                              {requestsExpanded ? (
                                <ChevronUp className="h-3.5 w-3.5" />
                              ) : (
                                <ChevronDown className="h-3.5 w-3.5" />
                              )}
                            </button>
                          </div>
                          {requestsExpanded && (
                            <div className="text-sm p-3 bg-slate-50 rounded-lg mt-2">
                              {safeQuote.special_requests}
                            </div>
                          )}
                        </div>
                      )}
                      <div className="flex flex-wrap gap-2 mt-3">
                        {safeQuote.flight_number && (
                          <Badge
                            variant="outline"
                            className="bg-blue-50 text-blue-700 border-blue-200"
                          >
                            <Plane className="h-3 w-3 mr-1" />
                            Flight: {safeQuote.flight_number}
                          </Badge>
                        )}
                        {safeQuote.is_return_trip && (
                          <Badge
                            variant="outline"
                            className="bg-purple-50 text-purple-700 border-purple-200"
                          >
                            <CalendarClock className="h-3 w-3 mr-1" />
                            Round Trip
                          </Badge>
                        )}
                        {(safeQuote.infant_seats ||
                          safeQuote.toddler_seats ||
                          safeQuote.booster_seats) && (
                          <Badge
                            variant="outline"
                            className="bg-indigo-50 text-indigo-700 border-indigo-200"
                          >
                            <Baby className="h-3 w-3 mr-1" />
                            Child Seats
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                {(isAdmin || isCustomer) &&
                  safeQuote.total_amount &&
                  safeQuote.total_amount > 0 && (
                    <div className="bg-white rounded-xl border p-5 shadow-sm">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-10 h-10 rounded-full bg-green-50 flex items-center justify-center">
                          <DollarSign className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <div className="text-xs uppercase tracking-wide text-muted-foreground font-medium">
                            COST
                          </div>
                          <div className="text-2xl font-bold text-green-700">
                            ${safeQuote.total_amount.toFixed(2)}
                          </div>
                        </div>
                      </div>
                      <div className="space-y-1">
                        <div className="flex justify-between py-2 border-b text-sm">
                          <span className="text-muted-foreground">
                            Base Rate
                          </span>
                          <span className="font-medium">
                            ${safeQuote.total_amount.toFixed(2)}
                          </span>
                        </div>
                        {safeQuote.gratuity && (
                          <div className="flex justify-between py-2 border-b text-sm">
                            <span className="text-muted-foreground">
                              Gratuity
                            </span>
                            <span className="font-medium">
                              ${safeQuote.gratuity.toFixed(2)}
                            </span>
                          </div>
                        )}
                        {safeQuote.admin_fee && (
                          <div className="flex justify-between py-2 border-b text-sm">
                            <span className="text-muted-foreground">
                              Admin Fee
                            </span>
                            <span className="font-medium">
                              ${safeQuote.admin_fee.toFixed(2)}
                            </span>
                          </div>
                        )}
                      </div>
                      {safeQuote.cancellation_policy && (
                        <div className="mt-4 pt-4 border-t">
                          <div className="text-xs uppercase tracking-wide text-muted-foreground font-medium mb-2">
                            Cancellation Policy
                          </div>
                          <div className="space-y-2 text-xs">
                            <div className="flex items-center text-green-700">
                              <CheckCircle2 className="h-3.5 w-3.5 mr-1.5" />
                              Free up to{" "}
                              {safeQuote.cancellation_policy.free_until_hours}h
                              before
                            </div>
                            <div className="flex items-center text-yellow-700">
                              <AlertTriangle className="h-3.5 w-3.5 mr-1.5" />
                              {
                                safeQuote.cancellation_policy
                                  .partial_charge_percentage
                              }
                              % within{" "}
                              {
                                safeQuote.cancellation_policy
                                  .partial_charge_hours
                              }
                              h
                            </div>
                            <div className="flex items-center text-red-700">
                              <XCircle className="h-3.5 w-3.5 mr-1.5" />
                              Full charge for no-shows
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                {isAdmin &&
                  ((safeQuote.affiliate_responses &&
                    safeQuote.affiliate_responses.length > 0) ||
                    (safeQuote.rate_proposals &&
                      safeQuote.rate_proposals.length > 0)) && (
                    <div className="bg-white rounded-xl border p-5 shadow-sm">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                          <Building2 className="h-5 w-5 text-primary" />
                        </div>
                        <div>
                          <div className="text-xs uppercase tracking-wide text-muted-foreground font-medium">
                            AFFILIATES
                          </div>
                          <div className="text-base font-bold">
                            {responseCounts.total} Responses
                          </div>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-2 mb-3">
                        <div className="text-center p-2 rounded-lg bg-green-50">
                          <div className="text-2xl font-bold text-green-700">
                            {responseCounts.accepted}
                          </div>
                          <div className="text-xs text-green-700 font-medium">
                            Accepted
                          </div>
                        </div>
                        <div className="text-center p-2 rounded-lg bg-yellow-50">
                          <div className="text-2xl font-bold text-yellow-700">
                            {responseCounts.pending}
                          </div>
                          <div className="text-xs text-yellow-700 font-medium">
                            Pending
                          </div>
                        </div>
                        <div className="text-center p-2 rounded-lg bg-red-50">
                          <div className="text-2xl font-bold text-red-700">
                            {responseCounts.rejected}
                          </div>
                          <div className="text-xs text-red-700 font-medium">
                            Rejected
                          </div>
                        </div>
                      </div>
                      {safeQuote.rate_proposals &&
                        safeQuote.rate_proposals.length > 0 && (
                          <div className="mt-3">
                            <div className="text-xs uppercase tracking-wide text-muted-foreground font-medium mb-2">
                              Top Proposals
                            </div>
                            <div className="space-y-2">
                              {safeQuote.rate_proposals
                                .slice(0, 3)
                                .map((proposal, index) => (
                                  <div
                                    key={index}
                                    className="flex items-center justify-between p-2 rounded-lg bg-slate-50"
                                  >
                                    <div className="flex items-center gap-2">
                                      <div
                                        className={`w-2 h-2 rounded-full ${
                                          proposal.status === "accepted"
                                            ? "bg-green-500"
                                            : proposal.status === "rejected"
                                              ? "bg-red-500"
                                              : "bg-yellow-500"
                                        }`}
                                      ></div>
                                      <span className="text-sm font-medium truncate max-w-[100px]">
                                        {proposal.company_id ||
                                          `Company ${index + 1}`}
                                      </span>
                                    </div>
                                    <Badge
                                      variant={
                                        proposal.status === "accepted"
                                          ? "default"
                                          : "outline"
                                      }
                                    >
                                      ${proposal.rate_amount?.toFixed(2)}
                                    </Badge>
                                  </div>
                                ))}
                            </div>
                          </div>
                        )}
                    </div>
                  )}
                <div className="mt-5">
                  <div className="bg-white rounded-xl border shadow-sm overflow-hidden">
                    <div className="flex items-center justify-between p-3 border-b bg-slate-50">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-primary" />
                        <h3 className="font-bold">Route Map</h3>
                      </div>
                    </div>
                    <div className="aspect-[21/9] bg-slate-100">
                      {safeQuote.pickup_latitude &&
                      safeQuote.pickup_longitude &&
                      safeQuote.dropoff_latitude &&
                      safeQuote.dropoff_longitude ? (
                        <div className="w-full h-full">
                          <Map
                            pickupLocation={{
                              lat: Number(safeQuote.pickup_latitude),
                              lng: Number(safeQuote.pickup_longitude),
                              address: safeQuote.pickup_location,
                            }}
                            dropoffLocation={{
                              lat: Number(safeQuote.dropoff_latitude),
                              lng: Number(safeQuote.dropoff_longitude),
                              address: safeQuote.dropoff_location,
                            }}
                            intermediateStops={
                              safeQuote.intermediate_stops
                                ?.filter(
                                  (stop) => stop.latitude && stop.longitude
                                )
                                .map((stop) => ({
                                  lat: Number(stop.latitude),
                                  lng: Number(stop.longitude),
                                  address: stop.location,
                                })) || []
                            }
                          />
                        </div>
                      ) : (
                        <div className="flex items-center justify-center h-full text-center text-muted-foreground">
                          <div>
                            <MapPin className="h-8 w-8 mx-auto mb-2" />
                            <p className="text-sm">
                              Route coordinates not available
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                {(safeQuote.timeline || safeQuote.communications) && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                    {safeQuote.timeline && safeQuote.timeline.length > 0 && (
                      <div className="bg-white rounded-xl border shadow-sm overflow-hidden">
                        <div className="p-4 border-b bg-slate-50">
                          <div className="flex items-center gap-3">
                            <CalendarClock className="h-5 w-5 text-primary" />
                            <h3 className="font-bold">Timeline</h3>
                          </div>
                        </div>
                        <div className="p-4 max-h-[300px] overflow-y-auto">
                          <div className="relative pl-5 before:absolute before:left-1.5 before:top-2 before:bottom-2 before:w-0.5 before:bg-slate-200">
                            {safeQuote.timeline.map((item, index) => (
                              <div key={index} className="mb-3 pb-3 relative">
                                <div className="absolute left-[-5px] top-1.5 w-2.5 h-2.5 rounded-full bg-primary"></div>
                                <div>
                                  <div className="flex justify-between text-sm">
                                    <span className="font-medium">
                                      {item.action}
                                    </span>
                                    <span className="text-xs text-muted-foreground">
                                      {typeof item.timestamp === "string"
                                        ? item.timestamp
                                        : format(
                                            item.timestamp,
                                            "MMM d, HH:mm"
                                          )}
                                    </span>
                                  </div>
                                  {item.user && (
                                    <div className="text-xs text-primary mt-1">
                                      <User className="h-3 w-3 inline mr-1" />
                                      {item.user}
                                    </div>
                                  )}
                                  {item.details && (
                                    <div className="text-sm text-muted-foreground mt-1.5 bg-slate-50 p-2 rounded-md">
                                      {item.details}
                                    </div>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}
                    {safeQuote.communications &&
                      safeQuote.communications.length > 0 && (
                        <div className="bg-white rounded-xl border shadow-sm overflow-hidden">
                          <div className="p-4 border-b bg-slate-50">
                            <div className="flex items-center gap-3">
                              <MessageSquare className="h-5 w-5 text-primary" />
                              <h3 className="font-bold">Communications</h3>
                            </div>
                          </div>
                          <div className="p-4 max-h-[300px] overflow-y-auto">
                            <div className="space-y-3">
                              {safeQuote.communications.map((comm, index) => (
                                <div
                                  key={index}
                                  className="flex items-start gap-3"
                                >
                                  <div className="w-8 h-8 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center shadow-sm border border-primary/10 flex-shrink-0 mt-1">
                                    <User className="w-4 h-4 text-primary" />
                                  </div>
                                  <div className="flex-1">
                                    <div className="flex justify-between items-center mb-1">
                                      <div className="text-sm font-medium">
                                        {comm.user}
                                      </div>
                                      <div className="text-xs text-muted-foreground">
                                        {typeof comm.timestamp === "string"
                                          ? comm.timestamp
                                          : format(
                                              comm.timestamp,
                                              "MMM d, HH:mm"
                                            )}
                                      </div>
                                    </div>
                                    <div className="bg-slate-50 p-3 rounded-lg text-sm">
                                      {comm.message}
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                  </div>
                )}
              </div>
            )}
          </div>
        )}
        {safeQuote.city && (
          <div className="absolute bottom-2 left-4 flex items-center gap-1.5 text-sm text-primary">
            <Building className="h-4 w-4" />
            <span className="font-medium text-xl">{safeQuote.city}</span>
          </div>
        )}
      </div>
    </Card>
  );

  const renderModernView = () => (
    <Card
      className={cn(
        "w-full mb-2 overflow-hidden transition-all",
        isSelected ? "ring-2 ring-primary" : "hover:bg-accent/50",
        "shadow-sm"
      )}
    >
      <div className="flex flex-col md:flex-row">
        <div className="w-full md:w-1/4 p-4 bg-muted/30">
          <div className="flex flex-col h-full justify-between">
            <div>
              <div className="flex items-center space-x-2 mb-2">
                {safeQuote.priority && (
                  <div
                    className={cn(
                      "w-2 h-2 rounded-full",
                      getPriorityColor(safeQuote.priority)
                    )}
                  />
                )}
                <h3 className="font-medium">
                  {safeQuote.reference_number
                    ? formatReferenceNumber(safeQuote.reference_number)
                    : `Quote #${safeQuote.id.slice(0, 8)}`}
                </h3>
              </div>
              <Badge
                variant="outline"
                className={cn("mb-2", getStatusColor(safeQuote.status))}
              >
                {getStatusIcon(safeQuote.status)}
                <span className="ml-1 capitalize">
                  {safeQuote.status.replace(/_/g, " ")}
                </span>
              </Badge>
              <div className="text-sm mb-4">
                {safeQuote.customer?.full_name || "No customer name"}
                {safeQuote.customer?.company_name && (
                  <div className="text-xs text-muted-foreground">
                    {safeQuote.customer.company_name}
                  </div>
                )}
              </div>
            </div>
            <div className="text-xs text-muted-foreground">
              Created{" "}
              {formatDistanceToNow(new Date(safeQuote.created_at), {
                addSuffix: true,
              })}
            </div>
          </div>
        </div>
        <div className="w-full md:w-3/4 p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="flex items-center space-x-2">
              <div className="bg-primary/10 p-2 rounded-full">
                <MapPin className="h-4 w-4 text-primary" />
              </div>
              <div className="flex flex-col">
                <span className="text-xs text-muted-foreground">Pickup</span>
                <span
                  className="text-sm font-medium truncate"
                  title={safeQuote.pickup_location}
                >
                  {safeQuote.pickup_location}
                </span>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <div className="bg-primary/10 p-2 rounded-full">
                <MapPin className="h-4 w-4 text-primary" />
              </div>
              <div className="flex flex-col">
                <span className="text-xs text-muted-foreground">Dropoff</span>
                <span
                  className="text-sm font-medium truncate"
                  title={safeQuote.dropoff_location}
                >
                  {safeQuote.dropoff_location}
                </span>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <div className="bg-primary/10 p-2 rounded-full">
                <Calendar className="h-4 w-4 text-primary" />
              </div>
              <div className="flex flex-col">
                <span className="text-xs text-muted-foreground">
                  Date & Time
                </span>
                <span className="text-sm font-medium">
                  {formatDate(safeQuote.date)}{" "}
                  {formatTime(safeQuote.date, safeQuote.time)}
                </span>
              </div>
            </div>
          </div>
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <div className="bg-primary/10 p-2 rounded-full">
                <Car className="h-4 w-4 text-primary" />
              </div>
              <div className="flex flex-col">
                <span className="text-xs text-muted-foreground">Vehicle</span>
                <span className="text-sm font-medium">
                  {safeQuote.vehicle_type}
                  {safeQuote.passenger_count &&
                    safeQuote.passenger_count > 0 && (
                      <span className="ml-1 text-xs text-muted-foreground">
                        ({safeQuote.passenger_count} pax)
                      </span>
                    )}
                </span>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {showAcceptRejectButtons && (
                <>
                  <Button
                    size="sm"
                    variant="outline"
                    className={getButtonStyle("outline").className}
                    style={getButtonStyle("outline").style}
                    onClick={handleReject}
                  >
                    Reject
                  </Button>
                  <Button
                    size="sm"
                    className={getButtonStyle("primary").className}
                    style={getButtonStyle("primary").style}
                    onClick={handleAccept}
                  >
                    Accept
                  </Button>
                </>
              )}
              {showRateRequestButton && (
                <Button
                  size="sm"
                  className={getButtonStyle("primary").className}
                  style={getButtonStyle("primary").style}
                  onClick={handleSendToAffiliates}
                >
                  {actionLabels.requestRates}
                </Button>
              )}
              {showFixedRateButton && (
                <Button
                  size="sm"
                  variant="outline"
                  className={getButtonStyle("outline").className}
                  style={getButtonStyle("outline").style}
                  onClick={handleFixedRate}
                >
                  {actionLabels.fixedRate}
                </Button>
              )}
              {showSubmitRateButton && (
                <Button
                  size="sm"
                  className={getButtonStyle("primary").className}
                  style={getButtonStyle("primary").style}
                  onClick={handleSubmitRate}
                >
                  {actionLabels.submitRate}
                </Button>
              )}
              {showChangeRequestButton && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleRequestChanges}
                >
                  Request Changes
                </Button>
              )}
              {showContactButtons && (
                <>
                  <Button
                    size="sm"
                    variant="outline"
                    className={getButtonStyle("outline").className}
                    style={getButtonStyle("outline").style}
                    onClick={handleCall}
                  >
                    <Phone className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className={getButtonStyle("outline").className}
                    style={getButtonStyle("outline").style}
                    onClick={handleMessage}
                  >
                    <MessageSquare className="h-4 w-4" />
                  </Button>
                </>
              )}
              {expandable && (
                <Button variant="ghost" size="sm" onClick={toggleExpand}>
                  {expanded ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
              )}
            </div>
          </div>
          {expanded && (
            <div className="mt-4 pt-4 border-t">
              <div className="text-sm">Additional details coming soon...</div>
            </div>
          )}
        </div>
      </div>
    </Card>
  );

  const renderCompactView = () => (
    <Card
      className={cn(
        "w-full mb-2 overflow-hidden transition-all",
        isSelected ? "ring-2 ring-primary" : "hover:bg-accent/50",
        "shadow-sm"
      )}
    >
      <div className="p-3">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            {safeQuote.priority && (
              <div
                className={cn(
                  "w-2 h-2 rounded-full",
                  getPriorityColor(safeQuote.priority)
                )}
              />
            )}
            <span className="font-medium text-sm">
              {safeQuote.reference_number
                ? formatReferenceNumber(safeQuote.reference_number)
                : `#${safeQuote.id.slice(0, 6)}`}
            </span>
            <Badge
              variant="outline"
              className={cn(getStatusColor(safeQuote.status))}
            >
              {getStatusIcon(safeQuote.status)}
              <span className="ml-1 capitalize text-xs">
                {safeQuote.status.replace(/_/g, " ")}
              </span>
            </Badge>
          </div>
          <div className="flex items-center space-x-1">
            {/* Removed duplicate action buttons - actions are available in contextual slider panel */}
            {showContactButtons && (
              <>
                <Button size="sm" variant="outline" onClick={handleCall}>
                  <Phone className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="outline" onClick={handleMessage}>
                  <MessageSquare className="h-4 w-4" />
                </Button>
              </>
            )}
            {expandable && (
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0"
                onClick={toggleExpand}
              >
                {expanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            )}
          </div>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2 text-xs">
          <div className="flex items-center">
            <MapPin className="h-3 w-3 mr-1 text-muted-foreground" />
            <span className="truncate" title={safeQuote.pickup_location}>
              {getShortLocation(safeQuote.pickup_location)}
            </span>
          </div>
          <div className="flex items-center">
            <MapPin className="h-3 w-3 mr-1 text-muted-foreground" />
            <span className="truncate" title={safeQuote.dropoff_location}>
              {getShortLocation(safeQuote.dropoff_location)}
            </span>
          </div>
          <div className="flex items-center">
            <Calendar className="h-3 w-3 mr-1 text-muted-foreground" />
            <span>
              {formatDate(safeQuote.date)}{" "}
              {formatTime(safeQuote.date, safeQuote.time)}
            </span>
          </div>
          <div className="flex items-center">
            <Car className="h-3 w-3 mr-1 text-muted-foreground" />
            <span>{safeQuote.vehicle_type}</span>
            {safeQuote.passenger_count && safeQuote.passenger_count > 0 && (
              <span className="ml-1 text-muted-foreground">
                ({safeQuote.passenger_count})
              </span>
            )}
          </div>
        </div>
        {expanded && (
          <div className="mt-4 pt-4 border-t">
            <div className="text-sm">Additional details coming soon...</div>
          </div>
        )}
      </div>
    </Card>
  );

  const renderMinimalView = () => (
    <div
      className={cn(
        "w-full mb-1 p-2 border rounded-md overflow-hidden transition-all",
        isSelected
          ? "ring-1 ring-primary border-primary"
          : "hover:bg-accent/30",
        "shadow-sm"
      )}
    >
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          {safeQuote.priority && (
            <div
              className={cn(
                "w-1.5 h-1.5 rounded-full",
                getPriorityColor(safeQuote.priority)
              )}
            />
          )}
          <span className="text-xs font-medium">
            {safeQuote.reference_number
              ? formatReferenceNumber(safeQuote.reference_number)
              : `#${safeQuote.id.slice(0, 6)}`}
          </span>
          <Badge
            variant="outline"
            className={cn(
              "text-xs px-1 py-0 h-5",
              getStatusColor(safeQuote.status)
            )}
          >
            {getStatusIcon(safeQuote.status)}
            <span className="ml-0.5 capitalize text-xs">
              {safeQuote.status.replace(/_/g, " ")}
            </span>
          </Badge>
        </div>
        <div className="flex items-center space-x-1">
          {/* Removed duplicate action buttons - actions are available in contextual slider panel */}
          {showContactButtons && (
            <>
              <Button size="sm" variant="outline" onClick={handleCall}>
                <Phone className="h-4 w-4" />
              </Button>
              <Button size="sm" variant="outline" onClick={handleMessage}>
                <MessageSquare className="h-4 w-4" />
              </Button>
            </>
          )}
        </div>
      </div>
      <div className="flex justify-between mt-1 text-xs">
        <div className="flex items-center space-x-2">
          <div className="flex items-center">
            <MapPin className="h-3 w-3 mr-0.5 text-muted-foreground" />
            <span
              className="truncate max-w-[100px]"
              title={safeQuote.pickup_location}
            >
              {getShortLocation(safeQuote.pickup_location)}
            </span>
          </div>
          <span className="text-muted-foreground">→</span>
          <div className="flex items-center">
            <MapPin className="h-3 w-3 mr-0.5 text-muted-foreground" />
            <span
              className="truncate max-w-[100px]"
              title={safeQuote.dropoff_location}
            >
              {getShortLocation(safeQuote.dropoff_location)}
            </span>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <div className="flex items-center">
            <Calendar className="h-3 w-3 mr-0.5 text-muted-foreground" />
            <span>{formatDate(safeQuote.date)}</span>
          </div>
          <div className="flex items-center">
            <Car className="h-3 w-3 mr-0.5 text-muted-foreground" />
            <span>{safeQuote.vehicle_type.split(" ")[0]}</span>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div
      className={cn(
        "cursor-pointer",
        isSelected ? "opacity-100" : "opacity-90 hover:opacity-100"
      )}
      onClick={onClick}
    >
      {viewMode === "original" && renderOriginalView()}
      {viewMode === "modern" && renderModernView()}
      {viewMode === "compact" && renderCompactView()}
      {viewMode === "minimal" && renderMinimalView()}
      {!["original", "modern", "compact", "minimal"].includes(viewMode) &&
        renderOriginalView()}
    </div>
  );
};

// Memo comparison function
const areEqual = (prevProps: QuoteRowProps, nextProps: QuoteRowProps) => {
  return (
    prevProps.quote.id === nextProps.quote.id &&
    prevProps.quote.status === nextProps.quote.status &&
    prevProps.isSelected === nextProps.isSelected &&
    prevProps.quote.last_updated === nextProps.quote.last_updated &&
    (prevProps as any).viewMode === (nextProps as any).viewMode
  );
};

// Export memoized component
export const QuoteRow = React.memo(QuoteRowComponent, areEqual);
