"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTitle,
  SheetDescription,
} from "@/app/components/ui/sheet";
import { Button } from "@/app/components/ui/button";
import { Badge } from "@/app/components/ui/badge";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";
import { Textarea } from "@/app/components/ui/textarea";
import { Separator } from "@/app/components/ui/separator";
import { Alert, AlertDescription } from "@/app/components/ui/alert";
import { useToast } from "@/app/components/ui/use-toast";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import {
  CheckCircle2,
  XCircle,
  Clock,
  AlertTriangle,
  MessageSquare,
  Building2,
  Ban,
  Timer,
  DollarSign,
  MapPin,
  Users,
  Car,
  Calendar,
  Phone,
  Mail,
  FileText,
  TrendingUp,
  Info,
} from "lucide-react";
import { formatDistanceToNow, format, parseISO } from "date-fns";

// Extended interface for affiliate offers
interface AffiliateOffer {
  id: string;
  quote_id: string;
  company_id: string;
  status: "sent" | "pending" | "accepted" | "rejected" | "expired";
  rate_amount: number;
  rate_currency: string;
  timeout_at: string;
  created_at: string;
  updated_at: string;
  quote?: {
    id: string;
    reference_number: string;
    customer_id: string;
    service_type: "airport" | "hourly" | "point";
    vehicle_type: string;
    pickup_location: string;
    dropoff_location: string;
    date: string;
    time: string;
    passenger_count: number;
    luggage_count: number;
    special_requests?: string | null;
    distance: string;
    duration: string;
    priority: "high" | "medium" | "low";
    created_at: string;
    updated_at: string;
    total_amount: number | null;
    is_multi_day: boolean;
    flight_number: string | null;
    is_return_trip: boolean;
    return_date: string | null;
    return_time: string | null;
    return_flight_number: string | null;
    car_seats_needed: boolean;
    infant_seats?: number | null;
    toddler_seats?: number | null;
    booster_seats?: number | null;
    intermediate_stops: any[] | null;
    duration_hours?: number | null;
    customer?: {
      id: string;
      name?: string;
      full_name?: string;
      email?: string;
      phone?: string;
      company_name?: string;
    };
    contact_name?: string | null;
    contact_email?: string | null;
    contact_phone?: string | null;
    city?: string;
  };
}

interface AffiliateQuoteActionPanelProps {
  offer?: AffiliateOffer;
  isOpen?: boolean;
  onClose: () => void;
  onAction: (action: string, data?: any) => void;
  canRespond?: boolean;
  isApproved?: boolean;
}

export function AffiliateQuoteActionPanel({
  offer,
  isOpen = false,
  onClose,
  onAction,
  canRespond = false,
  isApproved = false,
}: AffiliateQuoteActionPanelProps) {
  const { toast } = useToast();
  const [comment, setComment] = useState("");
  const [counterOffer, setCounterOffer] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [excludingVehicle, setExcludingVehicle] = useState(false);

  // Reset form when offer changes
  useEffect(() => {
    setComment("");
    setCounterOffer("");
    setError(null);
  }, [offer?.id]);

  if (!offer || !offer.quote) {
    return (
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent
          side="right"
          className="w-[600px] sm:max-w-[600px] overflow-y-auto"
        >
          <div className="flex items-center justify-center h-[400px]">
            <div className="text-center">
              <AlertTriangle className="h-8 w-8 text-amber-500 mx-auto mb-4" />
              <p className="text-muted-foreground">No offer selected</p>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  const quote = offer.quote;
  const isPending = offer.status === "pending" || offer.status === "sent";
  const isAccepted = offer.status === "accepted";
  const isRejected = offer.status === "rejected";
  const isExpired = offer.status === "expired";

  // Check if offer is about to expire (within 2 hours) - with proper date validation
  const timeoutDate = offer.timeout_at ? new Date(offer.timeout_at) : null;
  const isValidTimeoutDate = timeoutDate && !isNaN(timeoutDate.getTime());
  const now = new Date();
  const timeUntilExpiry = isValidTimeoutDate
    ? timeoutDate.getTime() - now.getTime()
    : 0;
  const isExpiringSoon =
    isValidTimeoutDate &&
    timeUntilExpiry > 0 &&
    timeUntilExpiry < 2 * 60 * 60 * 1000; // 2 hours

  const getStatusBadge = () => {
    const statusConfig = {
      pending: {
        label: "Pending Response",
        variant: "secondary" as const,
        className: "bg-yellow-100 text-yellow-800",
        icon: <Clock className="h-3 w-3 mr-1" />,
      },
      sent: {
        label: "Pending Response",
        variant: "secondary" as const,
        className: "bg-yellow-100 text-yellow-800",
        icon: <Clock className="h-3 w-3 mr-1" />,
      },
      accepted: {
        label: "Accepted",
        variant: "outline" as const,
        className: "bg-green-100 text-green-800 border-green-200",
        icon: <CheckCircle2 className="h-3 w-3 mr-1" />,
      },
      rejected: {
        label: "Rejected",
        variant: "destructive" as const,
        icon: <XCircle className="h-3 w-3 mr-1" />,
      },
      expired: {
        label: "Expired",
        variant: "secondary" as const,
        className: "bg-gray-100 text-gray-800",
        icon: <Timer className="h-3 w-3 mr-1" />,
      },
    };

    const config = statusConfig[offer.status] || statusConfig.pending;

    return (
      <Badge
        variant={(config as any).variant}
        className={(config as any).className}
      >
        <div className="flex items-center">
          {config.icon}
          {config.label}
        </div>
      </Badge>
    );
  };

  const handleAccept = async () => {
    if (!canRespond) {
      toast({
        title: "Action Not Allowed",
        description:
          "Your company must be approved before you can respond to offers.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      await onAction("accept_offer", {
        offer_id: offer.id,
        quote_id: offer.quote_id,
        comment: comment.trim(),
      });
      toast({
        title: "Offer Accepted",
        description: "You have successfully accepted this offer.",
        variant: "default",
      });
      onClose();
    } catch (error) {
      setError("Failed to accept offer. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleReject = async () => {
    if (!canRespond) {
      toast({
        title: "Action Not Allowed",
        description:
          "Your company must be approved before you can respond to offers.",
        variant: "destructive",
      });
      return;
    }

    if (!comment.trim()) {
      setError("Please provide a reason for rejecting this offer.");
      return;
    }

    setLoading(true);
    try {
      await onAction("reject_offer", {
        offer_id: offer.id,
        quote_id: offer.quote_id,
        comment: comment.trim(),
      });
      toast({
        title: "Offer Rejected",
        description: "You have rejected this offer.",
        variant: "default",
      });
      onClose();
    } catch (error) {
      setError("Failed to reject offer. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleCounterOffer = async () => {
    if (!canRespond) {
      toast({
        title: "Action Not Allowed",
        description:
          "Your company must be approved before you can respond to offers.",
        variant: "destructive",
      });
      return;
    }

    if (!counterOffer.trim() || isNaN(Number(counterOffer))) {
      setError("Please enter a valid counter offer amount.");
      return;
    }

    setLoading(true);
    try {
      await onAction("submit_rate", {
        quote_id: offer.quote_id,
        rate: Number(counterOffer),
        notes: comment.trim(),
      });
      toast({
        title: "Counter Offer Submitted",
        description: "Your counter offer has been submitted for review.",
        variant: "default",
      });
      onClose();
    } catch (error) {
      setError("Failed to submit counter offer. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleExcludeVehicle = async () => {
    if (!quote.vehicle_type) {
      setError("Vehicle type not specified in this quote.");
      return;
    }

    setExcludingVehicle(true);
    try {
      // Create a deactivated vehicle entry to exclude this vehicle type
      const response = await fetch("/api/affiliate/vehicles", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          type: quote.vehicle_type,
          make: "Excluded",
          model: "Vehicle Type",
          year: new Date().getFullYear(),
          capacity: 0,
          status: "deactivated",
          license_plate: "N/A",
          notes: "We don't carry this vehicle type in our fleet",
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to exclude vehicle type");
      }

      toast({
        title: "Vehicle Type Excluded",
        description: `You will no longer receive offers for ${quote.vehicle_type} vehicles. You can reactivate this in your Fleet & Rates page.`,
        variant: "default",
      });

      // Also reject this current offer
      await handleReject();
    } catch (error) {
      console.error("Error excluding vehicle type:", error);
      toast({
        title: "Error",
        description: "Failed to exclude vehicle type. Please try again.",
        variant: "destructive",
      });
    } finally {
      setExcludingVehicle(false);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent
        side="right"
        className="w-[600px] sm:max-w-[600px] overflow-y-auto"
      >
        <SheetHeader className="pb-6">
          <div className="flex items-center justify-between">
            <div>
              <SheetTitle className="text-2xl font-semibold">
                Quote {quote.reference_number}
              </SheetTitle>
              <SheetDescription className="text-base">
                Review and respond to this quote request
              </SheetDescription>
            </div>
            {getStatusBadge()}
          </div>
        </SheetHeader>

        <div className="space-y-6">
          {/* Expiry Warning */}
          {isPending && isExpiringSoon && isValidTimeoutDate && (
            <Alert className="border-orange-200 bg-orange-50">
              <Timer className="h-4 w-4 text-orange-600" />
              <AlertDescription className="text-orange-800">
                This offer expires{" "}
                {formatDistanceToNow(timeoutDate, { addSuffix: true })}
              </AlertDescription>
            </Alert>
          )}

          {/* Approval Warning */}
          {!canRespond && (
            <Alert className="border-blue-200 bg-blue-50">
              <Info className="h-4 w-4 text-blue-600" />
              <AlertDescription className="text-blue-800">
                Your company must be approved before you can respond to offers.
                Complete your onboarding and wait for approval.
              </AlertDescription>
            </Alert>
          )}

          {/* Quote Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Trip Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-start gap-2">
                    <MapPin className="h-4 w-4 text-green-600 mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-sm text-muted-foreground">Pickup</p>
                      <p className="font-medium">
                        {quote.pickup_location || "Not specified"}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <MapPin className="h-4 w-4 text-red-600 mt-1 flex-shrink-0" />
                    <div>
                      <p className="text-sm text-muted-foreground">Dropoff</p>
                      <p className="font-medium">
                        {quote.dropoff_location || "Not specified"}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Date & Time
                      </p>
                      <p className="font-medium">
                        {quote.date && quote.time
                          ? `${quote.date} at ${quote.time}`
                          : quote.date || quote.time || "Not specified"}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Car className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Vehicle</p>
                      <p className="font-medium">
                        {quote.vehicle_type || "Not specified"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <Users className="h-4 w-4 text-muted-foreground mx-auto mb-1" />
                  <p className="text-sm text-muted-foreground">Passengers</p>
                  <p className="font-medium">{quote.passenger_count || 0}</p>
                </div>
                <div className="text-center">
                  <FileText className="h-4 w-4 text-muted-foreground mx-auto mb-1" />
                  <p className="text-sm text-muted-foreground">Luggage</p>
                  <p className="font-medium">{quote.luggage_count || 0}</p>
                </div>
                <div className="text-center">
                  <MapPin className="h-4 w-4 text-muted-foreground mx-auto mb-1" />
                  <p className="text-sm text-muted-foreground">Distance</p>
                  <p className="font-medium">{quote.distance || "N/A"}</p>
                </div>
                <div className="text-center">
                  <Clock className="h-4 w-4 text-muted-foreground mx-auto mb-1" />
                  <p className="text-sm text-muted-foreground">Duration</p>
                  <p className="font-medium">{quote.duration || "N/A"}</p>
                </div>
              </div>

              {quote.special_requests && (
                <>
                  <Separator />
                  <div>
                    <p className="text-sm text-muted-foreground mb-2">
                      Special Requests
                    </p>
                    <p className="text-sm bg-muted p-3 rounded-md">
                      {quote.special_requests}
                    </p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Customer Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Name</p>
                  <p className="font-medium">
                    {quote.customer?.full_name ||
                      quote.contact_name ||
                      "Not provided"}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Company</p>
                  <p className="font-medium">
                    {quote.customer?.company_name || "Not provided"}
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Email</p>
                    <p className="font-medium">
                      {quote.customer?.email ||
                        quote.contact_email ||
                        "Not provided"}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Phone</p>
                    <p className="font-medium">
                      {quote.customer?.phone ||
                        quote.contact_phone ||
                        "Not provided"}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Offer Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Offer Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                <div>
                  <p className="text-sm text-muted-foreground">Offered Rate</p>
                  <p className="text-2xl font-bold">
                    ${(offer.rate_amount || 0).toLocaleString()}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {offer.rate_currency || "USD"}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-muted-foreground">Expires</p>
                  {isValidTimeoutDate ? (
                    <>
                      <p className="font-medium">
                        {formatDistanceToNow(timeoutDate, { addSuffix: true })}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {format(timeoutDate, "MMM dd, yyyy HH:mm")}
                      </p>
                    </>
                  ) : (
                    <p className="font-medium text-muted-foreground">
                      Not specified
                    </p>
                  )}
                </div>
              </div>

              {quote.service_type === "airport" && quote.flight_number && (
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <p className="text-sm font-medium text-blue-800">
                    Flight Information
                  </p>
                  <p className="text-sm text-blue-700">
                    Flight: {quote.flight_number}
                  </p>
                  {quote.is_return_trip && quote.return_flight_number && (
                    <p className="text-sm text-blue-700">
                      Return Flight: {quote.return_flight_number}
                    </p>
                  )}
                </div>
              )}

              {quote.car_seats_needed && (
                <div className="p-3 bg-orange-50 rounded-lg border border-orange-200">
                  <p className="text-sm font-medium text-orange-800">
                    Car Seats Required
                  </p>
                  <div className="text-sm text-orange-700 space-y-1">
                    {quote.infant_seats && (
                      <p>Infant seats: {quote.infant_seats}</p>
                    )}
                    {quote.toddler_seats && (
                      <p>Toddler seats: {quote.toddler_seats}</p>
                    )}
                    {quote.booster_seats && (
                      <p>Booster seats: {quote.booster_seats}</p>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Action Section */}
          {isPending && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Respond to Offer
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="comment">Comment (Optional)</Label>
                  <Textarea
                    id="comment"
                    placeholder="Add any comments or questions about this trip..."
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="counter-offer">
                    Counter Offer (Optional)
                  </Label>
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="counter-offer"
                        type="number"
                        placeholder="Enter your rate"
                        value={counterOffer}
                        onChange={(e) => setCounterOffer(e.target.value)}
                        className="pl-10"
                        min="0"
                        step="0.01"
                      />
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Leave blank to accept the offered rate of $
                    {offer.rate_amount.toLocaleString()}
                  </p>
                </div>

                <Separator />

                <div className="flex gap-3">
                  <Button
                    variant="outline"
                    className="flex-1"
                    onClick={handleReject}
                    disabled={loading || !canRespond}
                  >
                    <XCircle className="mr-2 h-4 w-4" />
                    Reject
                  </Button>

                  {counterOffer.trim() ? (
                    <Button
                      className="flex-1"
                      onClick={handleCounterOffer}
                      disabled={loading || !canRespond}
                    >
                      <TrendingUp className="mr-2 h-4 w-4" />
                      Submit Counter Offer
                    </Button>
                  ) : (
                    <Button
                      className="flex-1"
                      onClick={handleAccept}
                      disabled={loading || !canRespond}
                    >
                      <CheckCircle2 className="mr-2 h-4 w-4" />
                      Accept Offer
                    </Button>
                  )}
                </div>

                {/* Vehicle Exclusion Option */}
                <div className="pt-2 border-t">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full text-muted-foreground hover:text-destructive"
                    onClick={handleExcludeVehicle}
                    disabled={excludingVehicle || loading}
                  >
                    <Ban className="mr-2 h-4 w-4" />
                    {excludingVehicle
                      ? "Excluding..."
                      : `We don't carry ${quote.vehicle_type} vehicles`}
                  </Button>
                  <p className="text-xs text-muted-foreground text-center mt-1">
                    This will prevent future offers for this vehicle type
                  </p>
                </div>

                {!canRespond && (
                  <p className="text-xs text-muted-foreground text-center">
                    Complete your company onboarding and get approved to respond
                    to offers
                  </p>
                )}
              </CardContent>
            </Card>
          )}

          {/* Status Messages */}
          {isAccepted && (
            <Alert className="bg-green-50 text-green-800 border-green-200">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                <strong>Offer Accepted!</strong> You have successfully accepted
                this offer. The customer will be notified and you should receive
                trip details soon.
              </AlertDescription>
            </Alert>
          )}

          {isRejected && (
            <Alert className="bg-red-50 text-red-800 border-red-200">
              <XCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                <strong>Offer Rejected.</strong> You have declined this offer.
                The system will look for other available affiliates.
              </AlertDescription>
            </Alert>
          )}

          {isExpired && (
            <Alert className="bg-gray-50 text-gray-800 border-gray-200">
              <Timer className="h-4 w-4 text-gray-600" />
              <AlertDescription className="text-gray-800">
                <strong>Offer Expired.</strong> This offer has expired and is no
                longer available for response.
              </AlertDescription>
            </Alert>
          )}

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
