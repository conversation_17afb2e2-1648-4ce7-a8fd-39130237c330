-- Seed data for WWMS testing
-- This file creates test data for the affiliate portal testing

-- Disable triggers temporarily to avoid conflicts
SET session_replication_role = 'replica';

-- Clean up existing test data first
DELETE FROM quote_affiliate_offers WHERE quote_id IN (
  SELECT id FROM quotes WHERE contact_email IN ('<EMAIL>', '<EMAIL>')
);

DELETE FROM quotes WHERE contact_email IN ('<EMAIL>', '<EMAIL>');

DELETE FROM rate_cards WHERE company_id IN (
  SELECT id FROM affiliate_companies WHERE contact_email = '<EMAIL>'
);

DELETE FROM vehicles WHERE company_id IN (
  SELECT id FROM affiliate_companies WHERE contact_email = '<EMAIL>'
);

DELETE FROM affiliate_user_companies WHERE affiliate_id IN (
  SELECT id FROM affiliate_companies WHERE contact_email = '<EMAIL>'
);

DELETE FROM affiliate_companies WHERE contact_email = '<EMAIL>';

DELETE FROM profiles WHER<PERSON> email IN ('<EMAIL>', '<EMAIL>');

-- Note: We don't delete from auth.users as that might cause issues

-- Create the actual seed data
DO $$
DECLARE
    test_user_id UUID;
    test_client_user_id UUID;
    test_company_id UUID;
    test_quote_id UUID;
    test_vehicle_id UUID;
BEGIN
    -- Get or create the test affiliate user
    SELECT id INTO test_user_id FROM auth.users WHERE email = '<EMAIL>';

    IF test_user_id IS NULL THEN
        -- Create the test user in auth.users
        INSERT INTO auth.users (
            id,
            instance_id,
            email,
            encrypted_password,
            email_confirmed_at,
            created_at,
            updated_at,
            raw_app_meta_data,
            raw_user_meta_data,
            is_super_admin,
            role
        ) VALUES (
            gen_random_uuid(),
            '00000000-0000-0000-0000-000000000000',
            '<EMAIL>',
            '$2b$10$Ssmr.Y41Vll46Kf7.OPwhOhHEpgnBtNdgyjDPu.jcXRp3pJjhyr0y', -- password123
            NOW(),
            NOW(),
            NOW(),
            '{"provider": "email", "providers": ["email"]}',
            '{"first_name": "Test", "last_name": "Affiliate", "full_name": "Test Affiliate", "user_type": "affiliate"}',
            false,
            'authenticated'
        ) RETURNING id INTO test_user_id;
    END IF;

    -- Create or update profile for affiliate user
    INSERT INTO profiles (
        id,
        email,
        first_name,
        last_name,
        full_name,
        role,
        created_at,
        updated_at
    ) VALUES (
        test_user_id,
        '<EMAIL>',
        'Test',
        'Affiliate',
        'Test Affiliate',
        'AFFILIATE',
        NOW(),
        NOW()
    ) ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        full_name = EXCLUDED.full_name,
        role = EXCLUDED.role,
        updated_at = NOW();

    -- Create affiliate company in Miami
    INSERT INTO affiliate_companies (
        id,
        name,
        dba,
        owner_name,
        year_established,
        federal_tax_id,
        contact_email,
        contact_phone,
        website,
        address_line_1,
        address_line_2,
        city,
        state_province,
        postal_code,
        country,
        owner_id,
        status,
        application_status,
        created_at,
        updated_at
    ) VALUES (
        gen_random_uuid(),
        'Miami Elite Transportation',
        'Elite Transport',
        'Test Affiliate',
        '2020',
        '12-3456789',
        '<EMAIL>',
        '******-555-0123',
        'https://miamielitetransport.com',
        '1234 Biscayne Blvd',
        'Suite 100',
        'Miami',
        'FL',
        '33132',
        'USA',
        test_user_id,
        'active',
        'approved',
        NOW(),
        NOW()
    ) RETURNING id INTO test_company_id;

    -- Link user to company
    INSERT INTO affiliate_user_companies (
        user_id,
        affiliate_id,
        role,
        status,
        created_at,
        updated_at
    ) VALUES (
        test_user_id,
        test_company_id,
        'OWNER',
        'ACTIVE',
        NOW(),
        NOW()
    );

    -- Create a sedan vehicle
    INSERT INTO vehicles (
        id,
        company_id,
        type,
        make,
        model,
        year,
        license_plate,
        capacity,
        status,
        images,
        insurance_policy_number,
        created_at,
        updated_at
    ) VALUES (
        gen_random_uuid(),
        test_company_id,
        'LUXURY SEDAN',
        'Mercedes-Benz',
        'S580',
        2024,
        'MIA-001',
        4,
        'active',
        '{"exterior": "/vehicles/s-class-exterior.jpg", "interior": "/vehicles/s-class-interior.jpg"}',
        'INS-POL-123456',
        NOW(),
        NOW()
    ) RETURNING id INTO test_vehicle_id;

    -- Create rate card for sedan
    INSERT INTO rate_cards (
        id,
        company_id,
        vehicle_type,
        pricing_model_type,
        p2p_point_to_point_rate,
        p2p_extra_hour_rate,
        dt_base_fee,
        dt_per_mile_rate,
        dt_per_hour_rate,
        dt_min_miles,
        dt_min_hours,
        airport_transfer_flat_rate,
        charter_hourly_rate,
        charter_min_hours,
        gratuity_percentage,
        status,
        created_at,
        updated_at
    ) VALUES (
        gen_random_uuid(),
        test_company_id,
        'LUXURY SEDAN',
        'P2P',
        150.00,
        75.00,
        50.00,
        3.50,
        65.00,
        10,
        3,
        85.00,
        75.00,
        3,
        20.0,
        'approved',
        NOW(),
        NOW()
    );

    -- Create a test client user for creating quotes
    SELECT id INTO test_client_user_id FROM auth.users WHERE email = '<EMAIL>';

    IF test_client_user_id IS NULL THEN
        INSERT INTO auth.users (
            id,
            instance_id,
            email,
            encrypted_password,
            email_confirmed_at,
            created_at,
            updated_at,
            raw_app_meta_data,
            raw_user_meta_data,
            is_super_admin,
            role
        ) VALUES (
            gen_random_uuid(),
            '00000000-0000-0000-0000-000000000000',
            '<EMAIL>',
            '$2b$10$Ssmr.Y41Vll46Kf7.OPwhOhHEpgnBtNdgyjDPu.jcXRp3pJjhyr0y', -- password123
            NOW(),
            NOW(),
            NOW(),
            '{"provider": "email", "providers": ["email"]}',
            '{"first_name": "Test", "last_name": "Client", "full_name": "Test Client", "user_type": "client"}',
            false,
            'authenticated'
        ) RETURNING id INTO test_client_user_id;
    END IF;

    -- Create profile for client user
    INSERT INTO profiles (
        id,
        email,
        first_name,
        last_name,
        full_name,
        role,
        created_at,
        updated_at
    ) VALUES (
        test_client_user_id,
        '<EMAIL>',
        'Test',
        'Client',
        'Test Client',
        'CLIENT',
        NOW(),
        NOW()
    ) ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        full_name = EXCLUDED.full_name,
        role = EXCLUDED.role,
        updated_at = NOW();

    -- Create a test quote in Miami for a sedan
    INSERT INTO quotes (
        id,
        reference_number,
        customer_id,
        service_type,
        vehicle_type,
        pickup_location,
        dropoff_location,
        date,
        time,
        passenger_count,
        luggage_count,
        special_requests,
        distance,
        duration,
        priority,
        total_amount,
        is_multi_day,
        flight_number,
        is_return_trip,
        return_date,
        return_time,
        return_flight_number,
        car_seats_needed,
        infant_seats,
        toddler_seats,
        booster_seats,
        intermediate_stops,
        duration_hours,
        contact_name,
        contact_email,
        contact_phone,
        city,
        created_at,
        updated_at
    ) VALUES (
        gen_random_uuid(),
        'QT-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-001',
        test_client_user_id,
        'airport',
        'LUXURY SEDAN',
        'Miami International Airport (MIA)',
        'Four Seasons Hotel Miami',
        CURRENT_DATE + INTERVAL '2 days',
        '14:30:00',
        2,
        2,
        ARRAY['VIP client - please arrive 15 minutes early'],
        '12.5 miles',
        '35 minutes',
        'high',
        150.00,
        false,
        'AA1234',
        false,
        NULL,
        NULL,
        NULL,
        false,
        0,
        0,
        0,
        '[]',
        1,
        'John Smith',
        '<EMAIL>',
        '******-123-4567',
        'Miami',
        NOW(),
        NOW()
    ) RETURNING id INTO test_quote_id;

    -- Create an affiliate offer for this quote
    INSERT INTO quote_affiliate_offers (
        id,
        quote_id,
        company_id,
        status,
        rate_amount,
        currency,
        expires_at,
        created_by,
        updated_by,
        created_at,
        updated_at
    ) VALUES (
        gen_random_uuid(),
        test_quote_id,
        test_company_id,
        'PENDING',
        150.00,
        'USD',
        NOW() + INTERVAL '24 hours',
        test_user_id,
        test_user_id,
        NOW(),
        NOW()
    );

    RAISE NOTICE 'Seed data created successfully!';
    RAISE NOTICE 'Test affiliate user: <EMAIL> (password: password123)';
    RAISE NOTICE 'Test client user: <EMAIL> (password: password123)';
    RAISE NOTICE 'Company: Miami Elite Transportation';
    RAISE NOTICE 'Vehicle: 2024 Mercedes-Benz S580';
    RAISE NOTICE 'Quote: Airport transfer from MIA to Four Seasons';

END $$;

-- Re-enable triggers
SET session_replication_role = 'origin';

-- Verify the data was created
SELECT 'Seed data verification:' as message;
SELECT 'Companies:' as section, name, contact_email, status FROM affiliate_companies WHERE contact_email = '<EMAIL>';
SELECT 'Vehicles:' as section, type, make, model, status FROM vehicles WHERE company_id IN (SELECT id FROM affiliate_companies WHERE contact_email = '<EMAIL>');
SELECT 'Quotes:' as section, reference_number, service_type, vehicle_type, pickup_location FROM quotes WHERE contact_email = '<EMAIL>';
SELECT 'Offers:' as section, status, rate_amount FROM quote_affiliate_offers WHERE company_id IN (SELECT id FROM affiliate_companies WHERE contact_email = '<EMAIL>');