-- Migration to fix network tenant organizations confusion
-- Remove network tenant organizations from the organizations table
-- Network tenants should only exist in the public.tenants table, not organizations table

BEGIN;

-- Remove the incorrectly created network tenant organizations
-- These should not be in the organizations table as they are network tenants, not client organizations
DELETE FROM organizations 
WHERE id IN (
  'aaaaaaaa-1111-1111-1111-111111111111', -- TransFlow Default Network
  'bbbbbbbb-2222-2222-2222-222222222222', -- TransFlow Shared Network  
  'cccccccc-3333-3333-3333-333333333333'  -- WWLIMO TNC Network
);

-- Remove the user-organization associations for network tenant organizations
DELETE FROM user_organizations 
WHERE organization_id IN (
  'aaaaaaaa-1111-1111-1111-111111111111',
  'bbbbbbbb-2222-2222-2222-222222222222', 
  'cccccccc-3333-3333-3333-333333333333'
);

-- Network tenant users should be associated with the SaaS tenant system directly
-- They don't need organization associations since they manage network tenants, not client organizations

-- Add a comment to clarify the distinction
COMMENT ON TABLE organizations IS 'Client tenant organizations (e.g., City Tours LLC, Acme Transportation) - NOT network tenants';
COMMENT ON TABLE tenants IS 'Network tenants (e.g., TransFlow Default, TransFlow Shared, WWLIMO TNC) - managed by Network Switcher';

COMMIT;
