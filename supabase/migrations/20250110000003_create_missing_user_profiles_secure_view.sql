-- Create missing user_profiles_secure view
-- This view is referenced in the codebase but doesn't exist

-- First, check if user_profiles table exists, if not create a simple mapping to profiles
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
    -- Create user_profiles table as a view or mapping to profiles
    CREATE VIEW user_profiles AS
    SELECT 
      id as user_id,
      roles,
      created_at,
      updated_at
    FROM profiles;
  END IF;
END $$;

-- Create the user_profiles_secure view that the code is expecting
CREATE OR REPLACE VIEW user_profiles_secure AS
SELECT 
  p.id as user_id,
  p.roles,
  p.created_at,
  p.updated_at
FROM profiles p
WHERE p.id = auth.uid() OR EXISTS (
  SELECT 1 FROM profiles admin_profile 
  WHERE admin_profile.id = auth.uid() 
  AND 'SUPER_ADMIN' = ANY(admin_profile.roles)
);

-- Grant permissions on the view
GRANT SELECT ON user_profiles_secure TO authenticated, service_role;

-- Add comment for documentation
COMMENT ON VIEW user_profiles_secure IS 'Secure view for user profiles that allows users to see their own profile and super admins to see all profiles';
