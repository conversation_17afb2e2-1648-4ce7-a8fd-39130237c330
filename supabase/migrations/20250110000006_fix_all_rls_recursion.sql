-- Fix all RLS recursion issues across affiliate tables
-- The problem is circular dependencies between policies that query the same tables they're protecting

-- First, let's create safe functions that use JWT claims instead of querying tables
CREATE OR REPLACE FUNCTION get_user_role_safe()
RETURNS text
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT COALESCE(get_my_claim('role'), 'CLIENT');
$$;

CREATE OR REPLACE FUNCTION is_admin_safe()
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT get_user_role_safe() IN ('SUPER_ADMIN', 'ADMIN');
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_user_role_safe() TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION is_admin_safe() TO authenticated, service_role;

-- Fix affiliate_user_companies policies
-- Drop problematic policies
DROP POLICY IF EXISTS "Affiliate owners can manage users in their companies." ON affiliate_user_companies;

-- Create new safe policies
CREATE POLICY "Safe affiliate owners can manage users" ON affiliate_user_companies
  FOR ALL
  TO public
  USING (
    is_admin_safe() 
    OR user_id = auth.uid()
    OR (
      -- Check if user is owner of the company without recursion
      -- We'll use a direct check on affiliate_companies table
      EXISTS (
        SELECT 1 FROM affiliate_companies ac 
        WHERE ac.id = affiliate_user_companies.affiliate_id 
        AND ac.owner_id = auth.uid()
      )
    )
  )
  WITH CHECK (
    is_admin_safe() 
    OR user_id = auth.uid()
    OR (
      -- Check if user is owner of the company without recursion
      EXISTS (
        SELECT 1 FROM affiliate_companies ac 
        WHERE ac.id = affiliate_user_companies.affiliate_id 
        AND ac.owner_id = auth.uid()
      )
    )
  );

-- Fix affiliate_companies policies that reference affiliate_user_companies
-- Drop problematic policies
DROP POLICY IF EXISTS "Allow ACTIVE OWNERs and ADMINs to UPDATE affiliate_companies" ON affiliate_companies;
DROP POLICY IF EXISTS "Allow ACTIVE members and ADMINs to SELECT affiliate_companies" ON affiliate_companies;
DROP POLICY IF EXISTS "Admins can view all affiliate companies" ON affiliate_companies;
DROP POLICY IF EXISTS "Companies are viewable by their owners and admins" ON affiliate_companies;
DROP POLICY IF EXISTS "Companies can be updated by their owners and admins" ON affiliate_companies;

-- Create new safe policies for affiliate_companies
CREATE POLICY "Safe companies select policy" ON affiliate_companies
  FOR SELECT
  TO public
  USING (
    is_admin_safe()
    OR owner_id = auth.uid()
  );

CREATE POLICY "Safe companies update policy" ON affiliate_companies
  FOR UPDATE
  TO public
  USING (
    is_admin_safe()
    OR owner_id = auth.uid()
  )
  WITH CHECK (
    is_admin_safe()
    OR owner_id = auth.uid()
  );

CREATE POLICY "Safe companies insert policy" ON affiliate_companies
  FOR INSERT
  TO public
  WITH CHECK (
    is_admin_safe()
    OR owner_id = auth.uid()
  );

CREATE POLICY "Safe companies delete policy" ON affiliate_companies
  FOR DELETE
  TO public
  USING (
    is_admin_safe()
    OR owner_id = auth.uid()
  );

-- Add comments for documentation
COMMENT ON FUNCTION get_user_role_safe() IS 'Safe user role check using JWT claims to avoid RLS recursion';
COMMENT ON FUNCTION is_admin_safe() IS 'Safe admin check using JWT claims to avoid RLS recursion';
