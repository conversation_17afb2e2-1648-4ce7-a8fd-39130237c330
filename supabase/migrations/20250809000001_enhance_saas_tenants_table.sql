-- Create ENUM types if they don't already exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tenant_type_enum') THEN
        CREATE TYPE public.tenant_type_enum AS ENUM ('shared', 'segregated', 'white_label');
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tenant_status_enum') THEN
        CREATE TYPE public.tenant_status_enum AS ENUM ('active', 'inactive', 'suspended');
    END IF;
END$$;

-- Alter saas_tenants.tenants table to add new columns (initially nullable for backfill)
ALTER TABLE saas_tenants.tenants
ADD COLUMN IF NOT EXISTS slug VARCHAR(100),
ADD COLUMN IF NOT EXISTS parent_tenant_id UUID REFERENCES saas_tenants.tenants(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS tenant_type public.tenant_type_enum,
ADD COLUMN IF NOT EXISTS status public.tenant_status_enum NOT NULL DEFAULT 'active', -- Default 'active' is fine
ADD COLUMN IF NOT EXISTS branding JSONB;

-- Backfill 'slug' and 'tenant_type' for existing rows before applying NOT NULL constraints
-- This ensures that existing tenants get a valid, unique slug and a default tenant_type.
-- Assumes 'name' column is NOT NULL in saas_tenants.tenants.
UPDATE saas_tenants.tenants
SET
    slug = CASE
        WHEN slug IS NULL THEN lower(regexp_replace(name, '[^a-zA-Z0-9_]+', '-', 'g')) || '-' || substring(id::text from 1 for 8)
        ELSE slug
    END,
    tenant_type = COALESCE(tenant_type, 'shared'::public.tenant_type_enum)
WHERE slug IS NULL OR tenant_type IS NULL; -- Only update rows that actually need backfilling

-- Add NOT NULL constraints now that data is backfilled
ALTER TABLE saas_tenants.tenants
ALTER COLUMN slug SET NOT NULL,
ALTER COLUMN tenant_type SET NOT NULL;

-- Add UNIQUE constraint for slug.
-- This must be after backfilling unique slugs. If the backfill logic didn't ensure uniqueness, this could fail.
-- The COALESCE for slug update and appending part of ID should make it highly likely to be unique.
ALTER TABLE saas_tenants.tenants
ADD CONSTRAINT tenants_slug_unique UNIQUE (slug);

-- Add comments for new columns
COMMENT ON COLUMN saas_tenants.tenants.slug IS 'Unique URL-friendly identifier for the tenant. Must be populated for all tenants.';
COMMENT ON COLUMN saas_tenants.tenants.parent_tenant_id IS 'Identifier of the parent tenant, for hierarchical structures. NULL for top-level tenants.';
COMMENT ON COLUMN saas_tenants.tenants.tenant_type IS 'Type of the tenant (shared, segregated, white_label). Must be populated.';
COMMENT ON COLUMN saas_tenants.tenants.status IS 'Current operational status of the tenant. Defaults to active.';
COMMENT ON COLUMN saas_tenants.tenants.branding IS 'JSONB object to store branding configurations (logo, colors, etc.).';
