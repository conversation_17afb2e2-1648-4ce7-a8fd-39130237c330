-- Create tenant_users junction table
CREATE TABLE IF NOT EXISTS saas_tenants.tenant_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES saas_tenants.tenants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    UNIQUE(tenant_id, user_id)
);

-- Add comments
COMMENT ON TABLE saas_tenants.tenant_users IS 'Junction table for many-to-many relationship between tenants and users with role assignment';
COMMENT ON COLUMN saas_tenants.tenant_users.role IS 'Role of the user within the tenant (e.g., ADMIN, MEMBER, etc.)';

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_tenant_users_tenant_id ON saas_tenants.tenant_users(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_users_user_id ON saas_tenants.tenant_users(user_id);

-- Enable RLS
ALTER TABLE saas_tenants.tenant_users ENABLE ROW LEVEL SECURITY;

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_tenant_users_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_tenant_users_updated_at
BEFORE UPDATE ON saas_tenants.tenant_users
FOR EACH ROW EXECUTE FUNCTION update_tenant_users_updated_at();

-- Create RLS policies
CREATE POLICY "Users can view their own tenant associations"
    ON saas_tenants.tenant_users
    FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Tenant admins can manage their tenant's users"
    ON saas_tenants.tenant_users
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM saas_tenants.tenant_users tu
            WHERE tu.tenant_id = saas_tenants.tenant_users.tenant_id
            AND tu.user_id = auth.uid()
            AND tu.role = 'ADMIN'
        )
    );

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON saas_tenants.tenant_users TO authenticated;
