-- Fix application_status constraint to include 'draft' status
-- This allows companies to be created in draft status before submission

-- Drop the existing constraint
ALTER TABLE public.affiliate_companies 
DROP CONSTRAINT IF EXISTS affiliate_companies_application_status_check;

-- Add the updated constraint with 'draft' included
ALTER TABLE public.affiliate_companies 
ADD CONSTRAINT affiliate_companies_application_status_check 
CHECK (application_status IN ('draft', 'pending', 'approved', 'rejected', 'resubmitted'));

-- Update the column comment
COMMENT ON COLUMN public.affiliate_companies.application_status IS 'Verification/approval status: draft, pending, approved, rejected, resubmitted';
