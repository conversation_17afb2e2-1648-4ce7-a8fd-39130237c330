-- Create missing tables for tenant infrastructure
-- This migration creates the organizations table and fixes tenant relationships

-- LEGACY: This migration may reference ADMIN for super-admin access. All new policies must use SUPER_ADMIN only. If ADMIN is referenced, update to SUPER_ADMIN or add a comment for backward compatibility.

BEGIN;

-- 1. Create organizations table (this is what's causing the 404 error)
CREATE TABLE IF NOT EXISTS public.organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    description TEXT,
    industry TEXT,
    logo_url TEXT,
    website TEXT,
    address TEXT,
    city TEXT,
    state TEXT,
    zip TEXT,
    country TEXT,
    phone TEXT,
    email TEXT,
    tenant_id UUID REFERENCES saas_tenants.tenants(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for organizations
CREATE INDEX IF NOT EXISTS idx_organizations_tenant_id ON public.organizations(tenant_id);
CREATE INDEX IF NOT EXISTS idx_organizations_slug ON public.organizations(slug);
CREATE INDEX IF NOT EXISTS idx_organizations_status ON public.organizations(status);

-- Enable RLS on organizations
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for organizations
CREATE POLICY "Users can view organizations in their tenant"
    ON public.organizations
    FOR SELECT
    USING (
        tenant_id IN (
            SELECT tenant_id FROM saas_tenants.tenant_users 
            WHERE user_id = auth.uid()
        )
        OR EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
        )
    );

CREATE POLICY "Admins can manage organizations in their tenant"
    ON public.organizations
    FOR ALL
    USING (
        tenant_id IN (
            SELECT tenant_id FROM saas_tenants.tenant_users 
            WHERE user_id = auth.uid() AND role IN ('ADMIN', 'SUPER_ADMIN')
        )
        OR EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
        )
    );

-- 2. Create user_organizations junction table for user-org relationships
CREATE TABLE IF NOT EXISTS public.user_organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    role TEXT NOT NULL DEFAULT 'MEMBER',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, organization_id)
);

-- Add indexes for user_organizations
CREATE INDEX IF NOT EXISTS idx_user_organizations_user_id ON public.user_organizations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_organizations_org_id ON public.user_organizations(organization_id);

-- Enable RLS on user_organizations
ALTER TABLE public.user_organizations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for user_organizations
CREATE POLICY "Users can view their own organization associations"
    ON public.user_organizations
    FOR SELECT
    USING (
        user_id = auth.uid()
        OR EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
        )
    );

-- 3. Create tenant_organizations table to link organizations to tenants
CREATE TABLE IF NOT EXISTS public.tenant_organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES saas_tenants.tenants(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES public.organizations(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, organization_id)
);

-- Add indexes for tenant_organizations
CREATE INDEX IF NOT EXISTS idx_tenant_organizations_tenant_id ON public.tenant_organizations(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_organizations_org_id ON public.tenant_organizations(organization_id);

-- 4. Insert sample organizations for testing
INSERT INTO public.organizations (name, slug, description, industry, tenant_id)
SELECT 
    'Default Organization',
    'default-org',
    'Default organization for testing',
    'Transportation',
    t.id
FROM saas_tenants.tenants t
WHERE t.name = 'Default Tenant'
ON CONFLICT (slug) DO NOTHING;

-- 5. Create updated_at triggers
CREATE OR REPLACE FUNCTION update_organizations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_organizations_updated_at
BEFORE UPDATE ON public.organizations
FOR EACH ROW EXECUTE FUNCTION update_organizations_updated_at();

CREATE OR REPLACE FUNCTION update_user_organizations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_user_organizations_updated_at
BEFORE UPDATE ON public.user_organizations
FOR EACH ROW EXECUTE FUNCTION update_user_organizations_updated_at();

-- 6. Add comments for documentation
COMMENT ON TABLE public.organizations IS 'Organizations within tenants - companies, departments, or groups';
COMMENT ON TABLE public.user_organizations IS 'Junction table for user-organization relationships';
COMMENT ON TABLE public.tenant_organizations IS 'Junction table for tenant-organization relationships';

COMMIT;
