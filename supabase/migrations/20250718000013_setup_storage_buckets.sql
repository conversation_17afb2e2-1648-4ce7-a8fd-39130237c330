-- Set up storage buckets for company documents
BEGIN;

-- Create the company_documents bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('company_documents', 'company_documents', false)
ON CONFLICT (id) DO NOTHING;

-- Create RLS policies for the company_documents bucket
CREATE POLICY "Users can view their company's documents"
    ON storage.objects FOR SELECT
    USING (
        bucket_id = 'company_documents'
        AND (
            -- Check if the user is associated with the company
            EXISTS (
                SELECT 1 FROM public.affiliate_user_companies auc
                WHERE auc.affiliate_id::text = (storage.foldername(name))[1]
                AND auc.user_id = auth.uid()
            )
            OR
            -- Allow super admins to view all documents
            EXISTS (
                SELECT 1 FROM public.profiles
                WHERE id = auth.uid()
                AND 'SUPER_ADMIN' = ANY(roles)
            )
        )
    );

CREATE POLICY "Users can upload documents for their company"
    ON storage.objects FOR INSERT
    WITH CHECK (
        bucket_id = 'company_documents'
        AND (
            -- Check if the user is associated with the company and has appropriate role
            EXISTS (
                SELECT 1 FROM public.affiliate_user_companies auc
                WHERE auc.affiliate_id::text = (storage.foldername(name))[1]
                AND auc.user_id = auth.uid()
                AND auc.role = 'OWNER'
            )
            OR
            -- Allow super admins to upload documents
            EXISTS (
                SELECT 1 FROM public.profiles
                WHERE id = auth.uid()
                AND 'SUPER_ADMIN' = ANY(roles)
            )
        )
    );

CREATE POLICY "Users can update their company's documents"
    ON storage.objects FOR UPDATE
    USING (
        bucket_id = 'company_documents'
        AND (
            -- Check if the user is associated with the company and has appropriate role
            EXISTS (
                SELECT 1 FROM public.affiliate_user_companies auc
                WHERE auc.affiliate_id::text = (storage.foldername(name))[1]
                AND auc.user_id = auth.uid()
                AND auc.role = 'OWNER'
            )
            OR
            -- Allow super admins to update documents
            EXISTS (
                SELECT 1 FROM public.profiles
                WHERE id = auth.uid()
                AND 'SUPER_ADMIN' = ANY(roles)
            )
        )
    )
    WITH CHECK (
        bucket_id = 'company_documents'
        AND (
            -- Check if the user is associated with the company and has appropriate role
            EXISTS (
                SELECT 1 FROM public.affiliate_user_companies auc
                WHERE auc.affiliate_id::text = (storage.foldername(name))[1]
                AND auc.user_id = auth.uid()
                AND auc.role = 'OWNER'
            )
            OR
            -- Allow super admins to update documents
            EXISTS (
                SELECT 1 FROM public.profiles
                WHERE id = auth.uid()
                AND 'SUPER_ADMIN' = ANY(roles)
            )
        )
    );

CREATE POLICY "Users can delete their company's documents"
    ON storage.objects FOR DELETE
    USING (
        bucket_id = 'company_documents'
        AND (
            -- Check if the user is associated with the company and has appropriate role
            EXISTS (
                SELECT 1 FROM public.affiliate_user_companies auc
                WHERE auc.affiliate_id::text = (storage.foldername(name))[1]
                AND auc.user_id = auth.uid()
                AND auc.role = 'OWNER'
            )
            OR
            -- Allow super admins to delete documents
            EXISTS (
                SELECT 1 FROM public.profiles
                WHERE id = auth.uid()
                AND 'SUPER_ADMIN' = ANY(roles)
            )
        )
    );

-- Create function to handle document deletion from storage when record is deleted
CREATE OR REPLACE FUNCTION public.handle_company_document_deletion()
RETURNS TRIGGER AS $$
BEGIN
    -- Delete the file from storage
    DELETE FROM storage.objects
    WHERE bucket_id = 'company_documents'
    AND name = OLD.file_path;
    
    RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for document deletion
CREATE TRIGGER handle_company_document_deletion_trigger
    AFTER DELETE ON public.company_documents
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_company_document_deletion();

COMMIT; 