-- Fix the list_tenants_rpc function to match the actual table structure
CREATE OR REPLACE FUNCTION saas_tenants.list_tenants_rpc(
    p_status TEXT DEFAULT NULL,
    p_page INTEGER DEFAULT 1,
    p_page_size INTEGER DEFAULT 10,
    p_sort_by TEXT DEFAULT 'name',
    p_sort_order TEXT DEFAULT 'asc'
)
RETURNS TABLE (
    id UUID,
    name TEXT,
    domain TEXT,
    settings JSONB,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    total_count BIGINT
)
LANGUAGE plpgsql
STABLE
AS $$
DECLARE
    _offset INTEGER;
    _query TEXT;
    _sort_direction TEXT;
    _safe_sort_by TEXT;
BEGIN
    _offset := (GREATEST(p_page, 1) - 1) * GREATEST(p_page_size, 1);

    IF lower(p_sort_order) = 'desc' THEN
        _sort_direction := 'DESC';
    ELSE
        _sort_direction := 'ASC';
    END IF;

    -- Validate p_sort_by to prevent SQL injection and ensure it's a valid column
    SELECT CASE p_sort_by
        WHEN 'name' THEN 'name'
        WHEN 'domain' THEN 'domain'
        WHEN 'created_at' THEN 'created_at'
        WHEN 'updated_at' THEN 'updated_at'
        ELSE 'name' -- Default to a safe column if input is not recognized
    END INTO _safe_sort_by;

    _query := format(
        'SELECT 
            t.id, 
            t.name, 
            t.domain, 
            t.settings, 
            t.created_at, 
            t.updated_at,
            COUNT(*) OVER() as total_count
         FROM saas_tenants.tenants t
         ORDER BY %I %s
         LIMIT %L OFFSET %L',
        _safe_sort_by, _sort_direction, -- order by
        GREATEST(p_page_size, 1), _offset -- limit and offset
    );

    RETURN QUERY EXECUTE _query;
END;
$$;

-- Update the comment to reflect the simplified function
COMMENT ON FUNCTION saas_tenants.list_tenants_rpc(TEXT, INTEGER, INTEGER, TEXT, TEXT)
IS 'Lists tenants from saas_tenants.tenants with pagination and sorting. Includes total_count for pagination.';

-- Update permissions
GRANT EXECUTE ON FUNCTION saas_tenants.list_tenants_rpc(TEXT, INTEGER, INTEGER, TEXT, TEXT) TO authenticated;
