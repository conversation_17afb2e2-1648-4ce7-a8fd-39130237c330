-- Simple Comprehensive Seed Data
-- Creates realistic test data demonstrating network segregation

BEGIN;

-- ============================================================================
-- 1. CREATE REALISTIC EVENTS WITH NETWORK SEGREGATION
-- ============================================================================

-- Global TransFlow Network Events (City Tours LLC)
INSERT INTO events (name, description, start_date, end_date, location, total_passengers, status, customer_id, tenant_id, created_at, updated_at)
VALUES
  ('Annual Corporate Retreat 2025', 'Executive team retreat with luxury transportation', '2025-02-15 08:00:00+00', '2025-02-17 18:00:00+00', 'Napa Valley, CA', 25, 'published', '2b2a2488-a406-49d1-ae51-1d1c35a2a813', '1e55bb92-1d57-4844-ae6c-645071365cac', NOW(), NOW()),
  ('Tech Conference Transportation', 'Multi-day conference shuttle services', '2025-03-10 06:00:00+00', '2025-03-12 22:00:00+00', 'San Francisco, CA', 150, 'draft', '2b2a2488-a406-49d1-ae51-1d1c35a2a813', '1e55bb92-1d57-4844-ae6c-645071365cac', NOW(), NOW()),
  ('Wedding Party Transportation', 'Luxury wedding transportation services', '2025-04-20 14:00:00+00', '2025-04-21 02:00:00+00', 'Sonoma County, CA', 40, 'draft', '2b2a2488-a406-49d1-ae51-1d1c35a2a813', '1e55bb92-1d57-4844-ae6c-645071365cac', NOW(), NOW()),

-- TNC Network Events (Medical Transport)
  ('Patient Transfer Program', 'Scheduled medical appointments and discharge transportation', '2025-02-01 06:00:00+00', '2025-02-28 20:00:00+00', 'Houston, TX', 200, 'published', '2b2a2488-a406-49d1-ae51-1d1c35a2a813', 'cef53f6d-16c8-4414-8db5-0b0bf927ec36', NOW(), NOW()),
  ('Emergency Dialysis Transport', 'Critical patient transportation for dialysis', '2025-02-10 05:00:00+00', '2025-02-10 22:00:00+00', 'Houston, TX', 8, 'published', '2b2a2488-a406-49d1-ae51-1d1c35a2a813', 'cef53f6d-16c8-4414-8db5-0b0bf927ec36', NOW(), NOW()),
  ('Wheelchair Accessible Transfers', 'ADA compliant transportation for mobility-impaired patients', '2025-03-01 08:00:00+00', '2025-03-31 18:00:00+00', 'Houston, TX', 50, 'draft', '2b2a2488-a406-49d1-ae51-1d1c35a2a813', 'cef53f6d-16c8-4414-8db5-0b0bf927ec36', NOW(), NOW()),

-- TransFlow Shared Network Events (Enhanced Features)
  ('Global Network Conference', 'Cross-network transportation coordination', '2025-03-20 08:00:00+00', '2025-03-22 18:00:00+00', 'Las Vegas, NV', 300, 'draft', '2b2a2488-a406-49d1-ae51-1d1c35a2a813', 'eb94587b-5d73-430b-8022-d28d23e9cdf0', NOW(), NOW());

-- Events will be created with auto-generated UUIDs

-- Add comments for documentation
COMMENT ON TABLE events IS 'Events demonstrating network segregation: Global TransFlow, TNC Network, and TransFlow Shared';
COMMENT ON COLUMN events.tenant_id IS 'Network tenant ID showing proper segregation across different network environments';

COMMIT;
