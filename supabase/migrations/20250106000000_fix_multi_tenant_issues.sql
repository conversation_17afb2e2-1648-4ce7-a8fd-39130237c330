-- Fix Multi-Tenant Issues Migration
-- This migration fixes the current issues with user profiles, RLS policies, and affiliate approval

-- 1. Fix profiles table RLS policies to allow profile creation
DROP POLICY IF EXISTS "Users can view profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Service role can manage profiles" ON public.profiles;

-- Create more permissive policies for profile management
CREATE POLICY "Users can view own profile" ON public.profiles
FOR SELECT TO authenticated
USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles
FOR INSERT TO authenticated
WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
FOR UPDATE TO authenticated
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

-- Allow service role to manage all profiles
CREATE POLICY "Service role can manage all profiles" ON public.profiles
FOR ALL TO service_role
USING (true)
WITH CHECK (true);

-- Allow super admins to manage all profiles
CREATE POLICY "Super admins can manage all profiles" ON public.profiles
FOR ALL TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles p
    WHERE p.id = auth.uid()
    AND ('SUPER_ADMIN' = ANY(p.roles) OR 'ADMIN' = ANY(p.roles))
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles p
    WHERE p.id = auth.uid()
    AND ('SUPER_ADMIN' = ANY(p.roles) OR 'ADMIN' = ANY(p.roles))
  )
);

-- 2. Create the missing set_config function
CREATE OR REPLACE FUNCTION public.set_config(setting_name text, new_value text, is_local boolean DEFAULT false)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- This function allows setting configuration parameters
  -- Used for tenant context switching
  PERFORM set_config(setting_name, new_value, is_local);
  RETURN new_value;
END;
$$;

-- 3. Create tenant context functions
CREATE OR REPLACE FUNCTION public.get_current_tenant_id()
RETURNS UUID
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT COALESCE(
    (current_setting('app.current_tenant_id', true))::UUID,
    NULL
  );
$$;

CREATE OR REPLACE FUNCTION public.set_current_tenant_id(tenant_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  PERFORM set_config('app.current_tenant_id', tenant_id::text, false);
END;
$$;

-- 4. Create helper function to check if user is super admin
CREATE OR REPLACE FUNCTION public.is_super_admin()
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = auth.uid()
    AND ('SUPER_ADMIN' = ANY(roles) OR 'ADMIN' = ANY(roles))
  );
$$;

-- 5. Fix affiliate_companies RLS policies for super admin access
DROP POLICY IF EXISTS "tenant_isolation_affiliate_companies" ON public.affiliate_companies;

CREATE POLICY "affiliate_companies_access" ON public.affiliate_companies
FOR ALL TO authenticated
USING (
  -- Super admins can access all affiliates
  is_super_admin()
  OR
  -- Regular users can only access affiliates in their tenant
  (tenant_id IS NULL OR tenant_id = get_current_tenant_id())
)
WITH CHECK (
  -- Super admins can modify all affiliates
  is_super_admin()
  OR
  -- Regular users can only modify affiliates in their tenant
  (tenant_id IS NULL OR tenant_id = get_current_tenant_id())
);

-- 6. Ensure all existing users have profiles
INSERT INTO public.profiles (id, email, role, roles, created_at, updated_at)
SELECT 
  u.id,
  u.email,
  COALESCE(u.raw_app_meta_data->>'role', 'CLIENT'),
  ARRAY[COALESCE(u.raw_app_meta_data->>'role', 'CLIENT')]::TEXT[],
  NOW(),
  NOW()
FROM auth.users u
WHERE NOT EXISTS (
  SELECT 1 FROM public.profiles p WHERE p.id = u.id
)
ON CONFLICT (id) DO NOTHING;

-- 7. Create default tenant if none exists
INSERT INTO public.tenants (id, name, slug, tenant_type, status, created_at, updated_at)
VALUES (
  gen_random_uuid(),
  'TransFlow Default',
  'transflow-default',
  'shared',
  'active',
  NOW(),
  NOW()
)
ON CONFLICT (slug) DO NOTHING;

-- 8. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO service_role;
GRANT EXECUTE ON FUNCTION public.set_config TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_current_tenant_id TO authenticated;
GRANT EXECUTE ON FUNCTION public.set_current_tenant_id TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_super_admin TO authenticated;
