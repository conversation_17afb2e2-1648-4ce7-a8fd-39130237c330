-- supabase/migrations/20250809000007_update_tenant_rpc.sql

CREATE OR REPLACE FUNCTION saas_tenants.update_tenant_rpc(
    p_tenant_id UUID,
    p_updates JSONB
)
RETURNS SETOF saas_tenants.tenants -- Returns the updated tenant record
LANGUAGE plpgsql
AS $$
DECLARE
    v_name TEXT;
    v_slug VARCHAR(100);
    v_domain TEXT;
    v_tenant_type public.tenant_type_enum;
    v_status public.tenant_status_enum;
    v_parent_tenant_id UUID;
    v_branding JSONB;
    v_settings JSONB;
    updated_tenant saas_tenants.tenants%ROWTYPE; -- Variable to hold the updated row
BEGIN
    -- Extract values from JSONB, only if they exist
    v_name := p_updates ->> 'name';
    v_slug := p_updates ->> 'slug';
    v_domain := p_updates ->> 'domain';
    
    IF p_updates ? 'tenant_type' THEN
        v_tenant_type := (p_updates ->> 'tenant_type')::public.tenant_type_enum;
    END IF;

    IF p_updates ? 'status' THEN
        v_status := (p_updates ->> 'status')::public.tenant_status_enum;
    END IF;
    
    IF p_updates ? 'parent_tenant_id' THEN
      IF (p_updates ->> 'parent_tenant_id') IS NOT NULL AND (p_updates ->> 'parent_tenant_id') <> '' THEN
        v_parent_tenant_id := (p_updates ->> 'parent_tenant_id')::UUID;
      ELSE
        v_parent_tenant_id := NULL; -- Allow unsetting parent_tenant_id
      END IF;
    END IF;

    v_branding := p_updates -> 'branding'; -- Keep as JSONB
    v_settings := p_updates -> 'settings'; -- Keep as JSONB

    UPDATE saas_tenants.tenants
    SET
        name = COALESCE(v_name, name),
        slug = COALESCE(v_slug, slug),
        domain = COALESCE(v_domain, domain),
        tenant_type = COALESCE(v_tenant_type, tenant_type),
        status = COALESCE(v_status, status),
        parent_tenant_id = CASE WHEN p_updates ? 'parent_tenant_id' THEN v_parent_tenant_id ELSE parent_tenant_id END, -- Special handling for NULL
        branding = COALESCE(v_branding, branding),
        settings = COALESCE(v_settings, settings),
        updated_at = NOW()
    WHERE id = p_tenant_id
    RETURNING * INTO updated_tenant;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Tenant with ID % not found.', p_tenant_id USING ERRCODE = 'P0002'; -- P0002: no_data_found
    END IF;

    -- Return the updated tenant
    RETURN QUERY SELECT * FROM saas_tenants.tenants WHERE id = updated_tenant.id;

EXCEPTION
    WHEN OTHERS THEN
        RAISE;
END;
$$;

COMMENT ON FUNCTION saas_tenants.update_tenant_rpc(UUID, JSONB)
IS 'Updates an existing tenant in saas_tenants.tenants. Allows partial updates based on the provided JSONB.';

GRANT EXECUTE ON FUNCTION saas_tenants.update_tenant_rpc(UUID, JSONB) TO authenticated;
