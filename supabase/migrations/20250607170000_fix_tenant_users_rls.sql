-- Fix RLS recursion issues in tenant_users table
-- This migration addresses the infinite recursion in the tenant_users RLS policies
-- by using a security definer function instead of RLS policies

-- 1. Disable RLS on tenant_users to prevent any issues during the update
ALTER TABLE saas_tenants.tenant_users DISABLE ROW LEVEL SECURITY;

-- 2. Drop existing policies if they exist
DROP POLICY IF EXISTS tenant_users_tenant_isolation_policy ON saas_tenants.tenant_users;
DROP POLICY IF EXISTS tenant_users_admin_management_policy ON saas_tenants.tenant_users;

-- 3. Create a security definer function to check tenant access
CREATE OR REPLACE FUNCTION saas_tenants.check_tenant_access(
    p_user_id UUID,
    p_tenant_id UUID
) RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_is_super_admin BOOLEAN;
    v_is_tenant_admin BOOLEAN;
BEGIN
    -- Check if user is super admin
    SELECT EXISTS (
        SELECT 1 
        FROM auth.users 
        WHERE id = p_user_id 
        AND raw_user_meta_data->>'role' = 'SUPER_ADMIN'
    ) INTO v_is_super_admin;
    
    IF v_is_super_admin THEN
        RETURN TRUE;
    END IF;
    
    -- Check if user is a tenant admin for the given tenant
    SELECT EXISTS (
        SELECT 1 
        FROM auth.users
        WHERE id = p_user_id
        AND raw_user_meta_data->'app_metadata'->'tenant_roles' ? (p_tenant_id::text)
        AND raw_user_meta_data->'app_metadata'->'tenant_roles'->>(p_tenant_id::text) = 'admin'
    ) INTO v_is_tenant_admin;
    
    RETURN v_is_tenant_admin OR (p_user_id = auth.uid());
END;
$$;

-- 4. Create a view with security definer to access tenant_users
CREATE OR REPLACE VIEW saas_tenants.tenant_users_secure AS
SELECT tu.*
FROM saas_tenants.tenant_users tu
WHERE saas_tenants.check_tenant_access(auth.uid(), tu.tenant_id);

-- 5. Grant permissions on the view
GRANT SELECT, INSERT, UPDATE, DELETE ON saas_tenants.tenant_users_secure TO authenticated, service_role;

-- 6. Create a function to handle inserts through the view
CREATE OR REPLACE FUNCTION saas_tenants.handle_tenant_user_insert()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    INSERT INTO saas_tenants.tenant_users (user_id, tenant_id, role, created_at, updated_at)
    VALUES (NEW.user_id, NEW.tenant_id, NEW.role, COALESCE(NEW.created_at, NOW()), COALESCE(NEW.updated_at, NOW()));
    RETURN NEW;
END;
$$;

-- 7. Create a trigger for inserts
DROP TRIGGER IF EXISTS on_tenant_user_insert ON saas_tenants.tenant_users_secure;
CREATE TRIGGER on_tenant_user_insert
    INSTEAD OF INSERT ON saas_tenants.tenant_users_secure
    FOR EACH ROW EXECUTE FUNCTION saas_tenants.handle_tenant_user_insert();

-- 8. Create a function to handle updates through the view
CREATE OR REPLACE FUNCTION saas_tenants.handle_tenant_user_update()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE saas_tenants.tenant_users
    SET 
        role = NEW.role,
        updated_at = NOW()
    WHERE user_id = NEW.user_id AND tenant_id = NEW.tenant_id;
    RETURN NEW;
END;
$$;

-- 9. Create a trigger for updates
DROP TRIGGER IF EXISTS on_tenant_user_update ON saas_tenants.tenant_users_secure;
CREATE TRIGGER on_tenant_user_update
    INSTEAD OF UPDATE ON saas_tenants.tenant_users_secure
    FOR EACH ROW EXECUTE FUNCTION saas_tenants.handle_tenant_user_update();

-- 10. Create a function to handle deletes through the view
CREATE OR REPLACE FUNCTION saas_tenants.handle_tenant_user_delete()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    DELETE FROM saas_tenants.tenant_users
    WHERE user_id = OLD.user_id AND tenant_id = OLD.tenant_id;
    RETURN OLD;
END;
$$;

-- 11. Create a trigger for deletes
DROP TRIGGER IF EXISTS on_tenant_user_delete ON saas_tenants.tenant_users_secure;
CREATE TRIGGER on_tenant_user_delete
    INSTEAD OF DELETE ON saas_tenants.tenant_users_secure
    FOR EACH ROW EXECUTE FUNCTION saas_tenants.handle_tenant_user_delete();

-- 12. Create a function to get current user's tenants
CREATE OR REPLACE FUNCTION saas_tenants.get_user_tenants()
RETURNS TABLE (
    tenant_id UUID,
    role TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT tu.tenant_id, tu.role, tu.created_at, tu.updated_at
    FROM saas_tenants.tenant_users tu
    WHERE tu.user_id = auth.uid()
    OR EXISTS (
        SELECT 1 
        FROM auth.users u
        WHERE u.id = auth.uid()
        AND u.raw_user_meta_data->>'role' = 'SUPER_ADMIN'
    )
    OR EXISTS (
        SELECT 1 
        FROM auth.users u
        WHERE u.id = auth.uid()
        AND u.raw_user_meta_data->'app_metadata'->'tenant_roles' ? (tu.tenant_id::text)
        AND u.raw_user_meta_data->'app_metadata'->'tenant_roles'->>(tu.tenant_id::text) = 'admin'
    );
END;
$$;

-- 13. Grant execute on the function
GRANT EXECUTE ON FUNCTION saas_tenants.get_user_tenants() TO authenticated, service_role;

-- 14. Keep RLS disabled on the base table
-- ALTER TABLE saas_tenants.tenant_users ENABLE ROW LEVEL SECURITY;

-- 15. Add a comment to document the change
COMMENT ON VIEW saas_tenants.tenant_users_secure IS 
    'Secure view for tenant_users that enforces access control without RLS recursion. Users can see their own tenant associations. Tenant admins can see/modify users in their tenant. Super admins can see/modify all tenant users.';

-- 16. Verify the view was created
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.views 
        WHERE table_schema = 'saas_tenants' 
        AND table_name = 'tenant_users_secure'
    ) THEN
        RAISE EXCEPTION 'Failed to create tenant_users_secure view';
    END IF;
    
    RAISE NOTICE 'Successfully applied tenant_users security fix using security definer functions';
END
$$;
