-- LEGACY: This migration may reference ADMIN for super-admin access. All new policies must use SUPER_ADMIN only. If ADMIN is referenced, update to SUPER_ADMIN or add a comment for backward compatibility.

-- Fix RLS recursion issues in user_profiles table
-- This migration addresses the infinite recursion in RLS policies by using security definer functions

-- 1. Disable RLS on user_profiles to prevent recursion during the update
ALTER TABLE public.user_profiles DISABLE ROW LEVEL SECURITY;

-- 2. Drop the existing policy
DROP POLICY IF EXISTS user_profiles_tenant_isolation_policy ON public.user_profiles;

-- 3. Create a security definer function to check access
CREATE OR REPLACE FUNCTION public.check_user_profile_access(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_current_user_id UUID;
    v_is_super_admin BOOLEAN;
    v_user_tenant_id UUID;
    v_target_tenant_id UUID;
BEGIN
    -- Get the current user ID
    v_current_user_id := auth.uid();
    
    -- If user is viewing their own profile, allow access
    IF v_current_user_id = p_user_id THEN
        RETURN TRUE;
    END IF;
    
    -- Check if user is super admin
    SELECT EXISTS (
        SELECT 1 
        FROM auth.users 
        WHERE id = v_current_user_id 
        AND raw_user_meta_data->>'role' = 'SUPER_ADMIN'
    ) INTO v_is_super_admin;
    
    IF v_is_super_admin THEN
        RETURN TRUE;
    END IF;
    
    -- Get the current user's tenant ID
    SELECT tenant_id INTO v_user_tenant_id
    FROM saas_tenants.tenant_users_secure
    WHERE user_id = v_current_user_id
    LIMIT 1;
    
    -- Get the target user's tenant ID
    SELECT tenant_id INTO v_target_tenant_id
    FROM saas_tenants.tenant_users_secure
    WHERE user_id = p_user_id
    LIMIT 1;
    
    -- If both users are in the same tenant, allow access
    IF v_user_tenant_id IS NOT NULL AND v_target_tenant_id IS NOT NULL 
       AND v_user_tenant_id = v_target_tenant_id THEN
        RETURN TRUE;
    END IF;
    
    -- Default deny
    RETURN FALSE;
END;
$$;

-- 4. Create a secure view for user_profiles
CREATE OR REPLACE VIEW public.user_profiles_secure AS
SELECT up.*
FROM public.user_profiles up
WHERE public.check_user_profile_access(up.user_id);

-- 5. Grant permissions on the view
GRANT SELECT, INSERT, UPDATE, DELETE ON public.user_profiles_secure TO authenticated, service_role;

-- 6. Create a function to handle inserts through the view
CREATE OR REPLACE FUNCTION public.handle_user_profile_insert()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    INSERT INTO public.user_profiles (
        user_id, roles
    )
    VALUES (
        NEW.user_id,
        COALESCE(NEW.roles, ARRAY['CLIENT']::TEXT[])
    )
    RETURNING * INTO NEW;
    RETURN NEW;
END;
$$;

-- 7. Create a trigger for inserts
DROP TRIGGER IF EXISTS on_user_profile_insert ON public.user_profiles_secure;
CREATE TRIGGER on_user_profile_insert
    INSTEAD OF INSERT ON public.user_profiles_secure
    FOR EACH ROW EXECUTE FUNCTION public.handle_user_profile_insert();

-- 8. Create a function to handle updates through the view
CREATE OR REPLACE FUNCTION public.handle_user_profile_update()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE public.user_profiles
    SET 
        roles = COALESCE(NEW.roles, user_profiles.roles)
    WHERE user_id = NEW.user_id;
    RETURN NEW;
END;
$$;

-- 9. Create a trigger for updates
DROP TRIGGER IF EXISTS on_user_profile_update ON public.user_profiles_secure;
CREATE TRIGGER on_user_profile_update
    INSTEAD OF UPDATE ON public.user_profiles_secure
    FOR EACH ROW EXECUTE FUNCTION public.handle_user_profile_update();

-- 10. Keep RLS disabled on the base table
-- ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- 11. Add a comment to document the change
COMMENT ON VIEW public.user_profiles_secure IS 
    'Secure view for user_profiles that enforces access control without RLS recursion. Users can see their own profile. Users in the same tenant can see each other''s profiles. Super admins can see all profiles.';

-- 12. Create a function to get the current user''s profile
CREATE OR REPLACE FUNCTION public.get_my_user_profile()
RETURNS SETOF public.user_profiles
LANGUAGE sql
SECURITY DEFINER
AS $$
    SELECT * FROM public.user_profiles WHERE user_id = auth.uid();
$$;

-- 13. Grant execute on the function
GRANT EXECUTE ON FUNCTION public.get_my_user_profile() TO authenticated, service_role;

-- 14. Verify the view was created
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.views 
        WHERE table_schema = 'public' 
        AND table_name = 'user_profiles_secure'
    ) THEN
        RAISE EXCEPTION 'Failed to create user_profiles_secure view';
    END IF;
    
    RAISE NOTICE 'Successfully applied user_profiles security fix using security definer functions';
END
$$;
