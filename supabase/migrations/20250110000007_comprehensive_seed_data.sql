-- Comprehensive seed data for events, quote offers, and trips
-- This creates realistic relationships between existing quotes, affiliate companies, and new events
-- Temporarily disable <PERSON><PERSON> for data insertion

-- Disable R<PERSON> for seed data insertion
ALTER TABLE events DISABLE ROW LEVEL SECURITY;
ALTER TABLE quote_offers DISABLE ROW LEVEL SECURITY;
ALTER TABLE trips DISABLE ROW LEVEL SECURITY;

DO $$
DECLARE
  -- Existing data IDs
  quote1_id UUID := '0e5cbb6e-2213-4173-b064-7ece309eace3';
  quote2_id UUID := 'd8ea9dc2-6dd6-4a55-bf07-9227c76e6f4e';
  quote3_id UUID := 'eac72b02-3335-467d-9561-386c535c5e08';
  customer_id UUID := '2b2a2488-a406-49d1-ae51-1d1c35a2a813';
  tenant_id UUID := 'cb6a185d-0b6a-4ef7-b8cb-8abd8c872ff9';
  affiliate1_id UUID := 'b89d2e3f-1c68-4d44-9f25-a8c45c2e1234'; -- Luxury Limo NYC
  affiliate2_id UUID := '27f06a8a-25e6-4cdd-9e6d-eed68bd48f48'; -- Elite Transportation
  affiliate3_id UUID := '3980df15-6ce2-4ca3-bd39-3649c32c2ee0'; -- Test Affiliate Company
  
  -- New IDs for events and trips
  event1_id UUID := gen_random_uuid();
  event2_id UUID := gen_random_uuid();
  event3_id UUID := gen_random_uuid();
  trip1_id UUID := gen_random_uuid();
  trip2_id UUID := gen_random_uuid();
  trip3_id UUID := gen_random_uuid();
BEGIN

  -- Create realistic events linked to existing quotes
  INSERT INTO events (
    id, name, description, customer_id, manager_id, start_date, end_date, 
    location, status, total_passengers, tenant_id, created_at, updated_at
  ) VALUES 
  (
    event1_id,
    'Corporate Executive Airport Transfer',
    'VIP airport transfer for board meeting attendees from JFK to Manhattan office',
    customer_id,
    customer_id, -- manager is same as customer for now
    NOW() + INTERVAL '2 days',
    NOW() + INTERVAL '2 days' + INTERVAL '3 hours',
    'JFK Airport Terminal 4 to Times Square, New York, NY',
    'published',
    4,
    tenant_id,
    NOW(),
    NOW()
  ),
  (
    event2_id,
    'Client Meeting Transportation',
    'Executive transportation for important client meeting in Financial District',
    customer_id,
    customer_id,
    NOW() + INTERVAL '5 days',
    NOW() + INTERVAL '5 days' + INTERVAL '2 hours',
    'Newark Airport to Wall Street, New York, NY',
    'draft',
    2,
    tenant_id,
    NOW(),
    NOW()
  ),
  (
    event3_id,
    'Multi-Stop City Tour Event',
    'Corporate team building event with multiple NYC location visits',
    customer_id,
    customer_id,
    NOW() + INTERVAL '7 days',
    NOW() + INTERVAL '7 days' + INTERVAL '8 hours',
    'Central Park to Various NYC locations',
    'draft',
    12,
    tenant_id,
    NOW(),
    NOW()
  );

  -- Create quote offers from different affiliate companies
  INSERT INTO quote_offers (
    id, quote_id, company_id, rate_amount, rate_currency, status, notes, timeout_at, created_at, updated_at
  ) VALUES
  -- Offers for Quote 1 (JFK to Times Square)
  (
    gen_random_uuid(),
    quote1_id,
    affiliate1_id,
    150.00,
    'USD',
    'accepted',
    'Premium sedan service with meet & greet included',
    NOW() + INTERVAL '24 hours',
    NOW() - INTERVAL '1 hour',
    NOW() - INTERVAL '30 minutes'
  ),
  (
    gen_random_uuid(),
    quote1_id,
    affiliate2_id,
    175.00,
    'USD',
    'rejected',
    'Luxury SUV with complimentary refreshments',
    NOW() + INTERVAL '24 hours',
    NOW() - INTERVAL '2 hours',
    NOW() - INTERVAL '1 hour'
  ),
  
  -- Offers for Quote 2 (Newark to Wall Street)
  (
    gen_random_uuid(),
    quote2_id,
    affiliate2_id,
    125.00,
    'USD',
    'pending',
    'Executive sedan with professional driver',
    NOW() + INTERVAL '12 hours',
    NOW() - INTERVAL '30 minutes',
    NOW() - INTERVAL '30 minutes'
  ),
  (
    gen_random_uuid(),
    quote2_id,
    affiliate3_id,
    135.00,
    'USD',
    'sent',
    'Premium vehicle with flight tracking',
    NOW() + INTERVAL '12 hours',
    NOW() - INTERVAL '45 minutes',
    NOW() - INTERVAL '45 minutes'
  ),
  
  -- Offers for Quote 3 (Multi-stop tour)
  (
    gen_random_uuid(),
    quote3_id,
    affiliate1_id,
    450.00,
    'USD',
    'sent',
    'Luxury coach for group transportation with tour guide',
    NOW() + INTERVAL '48 hours',
    NOW() - INTERVAL '15 minutes',
    NOW() - INTERVAL '15 minutes'
  ),
  (
    gen_random_uuid(),
    quote3_id,
    affiliate2_id,
    425.00,
    'USD',
    'pending',
    'Premium van service with multiple stops included',
    NOW() + INTERVAL '48 hours',
    NOW() - INTERVAL '10 minutes',
    NOW() - INTERVAL '10 minutes'
  );

  -- Create trips from accepted quotes
  INSERT INTO trips (
    id, quote_id, driver_id, company_id, status, created_at, updated_at
  ) VALUES
  -- Trip for accepted quote 1
  (
    trip1_id,
    quote1_id,
    NULL, -- driver will be assigned later
    affiliate1_id,
    'assigned',
    NOW() - INTERVAL '30 minutes',
    NOW() - INTERVAL '15 minutes'
  ),
  
  -- Additional trips for demonstration
  (
    trip2_id,
    quote2_id,
    NULL,
    affiliate2_id,
    'pending',
    NOW() - INTERVAL '10 minutes',
    NOW() - INTERVAL '10 minutes'
  ),
  
  (
    trip3_id,
    quote3_id,
    NULL,
    affiliate1_id,
    'pending',
    NOW() - INTERVAL '5 minutes',
    NOW() - INTERVAL '5 minutes'
  );

  RAISE NOTICE 'Successfully created comprehensive seed data:';
  RAISE NOTICE '- 3 Events linked to existing quotes';
  RAISE NOTICE '- 6 Quote offers from 3 affiliate companies';
  RAISE NOTICE '- 3 Trips with realistic statuses';
  RAISE NOTICE 'Seed data represents complete quote-to-trip lifecycle';

EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error creating comprehensive seed data: %', SQLERRM;
END $$;

-- Re-enable RLS after seed data insertion
ALTER TABLE events ENABLE ROW LEVEL SECURITY;
ALTER TABLE quote_offers ENABLE ROW LEVEL SECURITY;
ALTER TABLE trips ENABLE ROW LEVEL SECURITY;
