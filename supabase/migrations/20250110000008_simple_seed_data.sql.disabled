-- Simple seed data creation - step by step approach
-- Disable <PERSON><PERSON> temporarily for seed data insertion

-- Disable RLS
ALTER TABLE events DISABLE ROW LEVEL SECURITY;
ALTER TABLE quote_offers DISABLE ROW LEVEL SECURITY;
ALTER TABLE trips DISABLE ROW LEVEL SECURITY;

-- Create events first
INSERT INTO events (
  name, description, customer_id, manager_id, start_date, end_date, 
  location, status, total_passengers, tenant_id
) VALUES 
(
  'Corporate Executive Airport Transfer',
  'VIP airport transfer for board meeting attendees from JFK to Manhattan office',
  '2b2a2488-a406-49d1-ae51-1d1c35a2a813',
  '2b2a2488-a406-49d1-ae51-1d1c35a2a813',
  NOW() + INTERVAL '2 days',
  NOW() + INTERVAL '2 days' + INTERVAL '3 hours',
  'JFK Airport Terminal 4 to Times Square, New York, NY',
  'published',
  4,
  'cb6a185d-0b6a-4ef7-b8cb-8abd8c872ff9'
),
(
  'Client Meeting Transportation',
  'Executive transportation for important client meeting in Financial District',
  '2b2a2488-a406-49d1-ae51-1d1c35a2a813',
  '2b2a2488-a406-49d1-ae51-1d1c35a2a813',
  NOW() + INTERVAL '5 days',
  NOW() + INTERVAL '5 days' + INTERVAL '2 hours',
  'Newark Airport to Wall Street, New York, NY',
  'draft',
  2,
  'cb6a185d-0b6a-4ef7-b8cb-8abd8c872ff9'
),
(
  'Multi-Stop City Tour Event',
  'Corporate team building event with multiple NYC location visits',
  '2b2a2488-a406-49d1-ae51-1d1c35a2a813',
  '2b2a2488-a406-49d1-ae51-1d1c35a2a813',
  NOW() + INTERVAL '7 days',
  NOW() + INTERVAL '7 days' + INTERVAL '8 hours',
  'Central Park to Various NYC locations',
  'draft',
  12,
  'cb6a185d-0b6a-4ef7-b8cb-8abd8c872ff9'
);

-- Create quote offers
INSERT INTO quote_offers (
  quote_id, company_id, rate_amount, rate_currency, status, notes
) VALUES
-- Offers for Quote 1 (JFK to Times Square)
(
  '0e5cbb6e-2213-4173-b064-7ece309eace3',
  'b89d2e3f-1c68-4d44-9f25-a8c45c2e1234',
  150.00,
  'USD',
  'accepted',
  'Premium sedan service with meet & greet included'
),
(
  '0e5cbb6e-2213-4173-b064-7ece309eace3',
  '27f06a8a-25e6-4cdd-9e6d-eed68bd48f48',
  175.00,
  'USD',
  'rejected',
  'Luxury SUV with complimentary refreshments'
),

-- Offers for Quote 2 (Newark to Wall Street)
(
  'd8ea9dc2-6dd6-4a55-bf07-9227c76e6f4e',
  '27f06a8a-25e6-4cdd-9e6d-eed68bd48f48',
  125.00,
  'USD',
  'pending',
  'Executive sedan with professional driver'
),
(
  'd8ea9dc2-6dd6-4a55-bf07-9227c76e6f4e',
  '3980df15-6ce2-4ca3-bd39-3649c32c2ee0',
  135.00,
  'USD',
  'sent',
  'Premium vehicle with flight tracking'
),

-- Offers for Quote 3 (Multi-stop tour)
(
  'eac72b02-3335-467d-9561-386c535c5e08',
  'b89d2e3f-1c68-4d44-9f25-a8c45c2e1234',
  450.00,
  'USD',
  'sent',
  'Luxury coach for group transportation with tour guide'
),
(
  'eac72b02-3335-467d-9561-386c535c5e08',
  '27f06a8a-25e6-4cdd-9e6d-eed68bd48f48',
  425.00,
  'USD',
  'pending',
  'Premium van service with multiple stops included'
);

-- Create trips from accepted quotes
INSERT INTO trips (
  quote_id, company_id, status
) VALUES
-- Trip for accepted quote 1
(
  '0e5cbb6e-2213-4173-b064-7ece309eace3',
  'b89d2e3f-1c68-4d44-9f25-a8c45c2e1234',
  'assigned'
),

-- Additional trips for demonstration
(
  'd8ea9dc2-6dd6-4a55-bf07-9227c76e6f4e',
  '27f06a8a-25e6-4cdd-9e6d-eed68bd48f48',
  'pending'
),

(
  'eac72b02-3335-467d-9561-386c535c5e08',
  'b89d2e3f-1c68-4d44-9f25-a8c45c2e1234',
  'pending'
);

-- Re-enable RLS
ALTER TABLE events ENABLE ROW LEVEL SECURITY;
ALTER TABLE quote_offers ENABLE ROW LEVEL SECURITY;
ALTER TABLE trips ENABLE ROW LEVEL SECURITY;

-- Show results
SELECT 'Events created' as result, COUNT(*) as count FROM events
UNION ALL
SELECT 'Quote offers created', COUNT(*) FROM quote_offers
UNION ALL
SELECT 'Trips created', COUNT(*) FROM trips;
