-- Fix the update_affiliate_analytics trigger function
-- The issue is that it's trying to reference 'amount' column that doesn't exist in trips table
-- The amount should come from the related quote_offers table

CREATE OR REPLACE FUNCTION update_affiliate_analytics()
RETURNS TRIGGER AS $$
BEGIN
  -- Update or insert analytics record for the day
  INSERT INTO affiliate_analytics (
    company_id,
    date,
    total_trips,
    total_revenue,
    completion_rate,
    average_rating
  )
  SELECT
    NEW.company_id,
    DATE(NEW.created_at),
    COUNT(*),
    COALESCE(SUM(qo.rate_amount), 0), -- Get amount from quote_offers table
    (COUNT(*) FILTER (WHERE t.status = 'completed')::DECIMAL / NULLIF(COUNT(*)::DECIMAL, 0)) * 100,
    4.5 -- Default rating since customer_feedback column doesn't exist yet
  FROM trips t
  LEFT JOIN quote_offers qo ON t.quote_id = qo.quote_id AND t.company_id = qo.company_id AND qo.status = 'accepted'
  WHERE t.company_id = NEW.company_id
    AND DATE(t.created_at) = DATE(NEW.created_at)
  GROUP BY t.company_id, DATE(t.created_at)
  ON CONFLICT (company_id, date)
  DO UPDATE SET
    total_trips = EXCLUDED.total_trips,
    total_revenue = EXCLUDED.total_revenue,
    completion_rate = EXCLUDED.completion_rate,
    average_rating = EXCLUDED.average_rating,
    updated_at = TIMEZONE('utc'::text, NOW());

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
