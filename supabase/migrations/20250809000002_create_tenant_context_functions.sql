-- Function to set the current tenant context for the session
CREATE OR REPLACE FUNCTION saas_tenants.set_current_tenant(tenant_id_to_set UUID)
RETURNS TEXT AS $$
DECLARE
    current_user_id UUID := auth.uid();
    is_super_admin BOOLEAN := false;
BEGIN
    -- Check if the current user is a super admin
    SELECT EXISTS (
        SELECT 1
        FROM public.profiles
        WHERE id = current_user_id AND ('SUPER_ADMIN' = ANY(roles) OR 'ADMIN' = ANY(roles)) -- Using roles from public.profiles
    ) INTO is_super_admin;

    IF NOT is_super_admin THEN
        RAISE EXCEPTION 'User % is not authorized to set tenant context.', current_user_id;
    END IF;

    IF tenant_id_to_set IS NULL THEN
        -- Clear the tenant context
        PERFORM set_config('app.current_tenant_id', NULL, false);
        RETURN 'Tenant context cleared.';
    ELSE
        -- Verify the tenant_id_to_set actually exists in saas_tenants.tenants
        IF NOT EXISTS (SELECT 1 FROM saas_tenants.tenants WHERE id = tenant_id_to_set) THEN
            RAISE EXCEPTION 'Tenant ID % does not exist.', tenant_id_to_set;
        END IF;
        PERFORM set_config('app.current_tenant_id', tenant_id_to_set::TEXT, false);
        RETURN 'Tenant context set to ' || tenant_id_to_set::TEXT;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION saas_tenants.set_current_tenant(UUID) IS
'Sets a session-level configuration variable ''app.current_tenant_id'' for RLS. Restricted to super admins. Pass NULL to clear context.';

GRANT EXECUTE ON FUNCTION saas_tenants.set_current_tenant(UUID) TO authenticated;

-- Optional: A helper function to get the current tenant_id, primarily for debugging or server-side logic
CREATE OR REPLACE FUNCTION saas_tenants.get_current_tenant_context()
RETURNS UUID AS $$
BEGIN
    RETURN current_setting('app.current_tenant_id', true)::UUID; -- true makes it return NULL if not set, instead of erroring
EXCEPTION
    WHEN OTHERS THEN -- Catches 'unrecognized configuration parameter' if not set
        RETURN NULL;
END;
$$ LANGUAGE plpgsql STABLE;

COMMENT ON FUNCTION saas_tenants.get_current_tenant_context() IS
'Retrieves the current session''s tenant_id from ''app.current_tenant_id''. Returns NULL if not set.';
GRANT EXECUTE ON FUNCTION saas_tenants.get_current_tenant_context() TO authenticated;
