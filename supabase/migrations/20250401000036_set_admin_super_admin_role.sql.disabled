-- Ensure user_profiles table exists (it should, but for safety)
CREATE TABLE IF NOT EXISTS public.user_profiles (
  user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  -- Add other columns if they don't exist, e.g.:
  -- email VARCHAR(255) UNIQUE,
  -- full_name TEXT,
  -- avatar_url TEXT,
  roles TEXT[] -- Assuming roles is an array of text; adjust if it's JSONB or other type
  -- created_at TIMESTAMPTZ DEFAULT NOW(),
  -- updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add roles column if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS(
    SELECT 1 FROM information_schema.columns
    WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'roles'
  ) THEN
    ALTER TABLE public.user_profiles ADD COLUMN roles TEXT[];
    RAISE NOTICE 'Column roles added to user_profiles.';
  ELSE
    RAISE NOTICE 'Column roles already exists in user_profiles.';
  END IF;
END;
$$;

-- Update the roles for <NAME_EMAIL> user
-- User <NAME_EMAIL> is '2b2a2488-a406-49d1-ae51-1d1c35a2a813'
INSERT INTO public.user_profiles (user_id, roles)
VALUES ('2b2a2488-a406-49d1-ae51-1d1c35a2a813', ARRAY['SUPER_ADMIN'])
ON CONFLICT (user_id)
DO UPDATE SET roles = ARRAY['SUPER_ADMIN'];

SELECT 'Migration applied: <EMAIL> now has SUPER_ADMIN role in user_profiles.';

-- Enable RLS and add a basic policy for user_profiles
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own user_profile"
  ON public.user_profiles
  FOR SELECT
  USING (auth.uid() = user_id); 