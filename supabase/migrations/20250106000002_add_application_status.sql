-- Add application_status column to affiliate_companies
-- This column tracks the verification/approval status separately from operational status

ALTER TABLE public.affiliate_companies 
ADD COLUMN IF NOT EXISTS application_status TEXT DEFAULT 'pending' 
CHECK (application_status IN ('pending', 'approved', 'rejected', 'resubmitted'));

-- Update existing records to have a default application_status based on current status
UPDATE public.affiliate_companies 
SET application_status = CASE 
  WHEN status = 'active' THEN 'approved'
  WHEN status = 'inactive' THEN 'rejected'
  ELSE 'pending'
END
WHERE application_status IS NULL;

-- Add comment for documentation
COMMENT ON COLUMN public.affiliate_companies.application_status IS 'Verification/approval status: pending, approved, rejected, resubmitted';
