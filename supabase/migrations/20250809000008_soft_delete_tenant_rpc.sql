-- supabase/migrations/20250809000008_soft_delete_tenant_rpc.sql

-- Create a function to soft delete a tenant
CREATE OR REPLACE FUNCTION saas_tenants.soft_delete_tenant_rpc(
    p_tenant_id UUID
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Update the tenant's status to 'DELETED' and set deleted_at timestamp
    UPDATE saas_tenants.tenants
    SET 
        status = 'DELETED'::public.tenant_status_enum,
        deleted_at = NOW(),
        updated_at = NOW()
    WHERE id = p_tenant_id
    AND status != 'DELETED'; -- Prevent redundant updates
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Tenant with ID % not found or already deleted', p_tenant_id 
        USING ERRCODE = 'P0002'; -- no_data_found
    END IF;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION saas_tenants.soft_delete_tenant_rpc(UUID) TO authenticated;

-- Add a comment
COMMENT ON FUNCTION saas_tenants.soft_delete_tenant_rpc(UUID) 
IS 'Soft deletes a tenant by setting its status to DELETED and setting the deleted_at timestamp.';
