-- Fix RLS recursion issues in profiles table
-- This migration addresses the infinite recursion in the profiles <PERSON><PERSON> policies
-- by using a security definer function instead of direct RLS policies

-- 1. Disable RLS on profiles to prevent any issues during the update
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- 2. Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own profile (TEMP DEBUG)" ON public.profiles;

-- 3. Create a security definer function to check profile access
CREATE OR REPLACE FUNCTION public.check_profile_access(p_profile_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_user_id UUID;
    v_is_super_admin BOOLEAN;
    v_tenant_id UUID;
    v_is_tenant_admin BOOLEAN;
BEGIN
    -- Get the current user ID
    v_user_id := auth.uid();
    
    -- If user is viewing their own profile, allow access
    IF v_user_id = p_profile_id THEN
        RETURN TRUE;
    END IF;
    
    -- Check if user is super admin
    SELECT EXISTS (
        SELECT 1 
        FROM auth.users 
        WHERE id = v_user_id 
        AND raw_user_meta_data->>'role' = 'SUPER_ADMIN'
    ) INTO v_is_super_admin;
    
    IF v_is_super_admin THEN
        RETURN TRUE;
    END IF;
    
    -- For tenant admins, check if they share a tenant with the profile
    -- Get the tenant_id of the profile
    SELECT tenant_id INTO v_tenant_id
    FROM public.profiles
    WHERE id = p_profile_id;
    
    -- Check if user is a tenant admin for the profile's tenant
    IF v_tenant_id IS NOT NULL THEN
        SELECT EXISTS (
            SELECT 1 
            FROM auth.users u
            WHERE u.id = v_user_id
            AND u.raw_user_meta_data->'app_metadata'->'tenant_roles' ? (v_tenant_id::text)
            AND u.raw_user_meta_data->'app_metadata'->'tenant_roles'->>(v_tenant_id::text) = 'admin'
        ) INTO v_is_tenant_admin;
        
        IF v_is_tenant_admin THEN
            RETURN TRUE;
        END IF;
    END IF;
    
    -- Default deny
    RETURN FALSE;
END;
$$;

-- 4. Create a view with security definer to access profiles
CREATE OR REPLACE VIEW public.profiles_secure AS
SELECT p.*
FROM public.profiles p
WHERE public.check_profile_access(p.id);

-- 5. Grant permissions on the view
GRANT SELECT, INSERT, UPDATE, DELETE ON public.profiles_secure TO authenticated, service_role;

-- 6. Create a function to handle inserts through the view
CREATE OR REPLACE FUNCTION public.handle_profile_insert()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    INSERT INTO public.profiles (
        id, username, full_name, avatar_url, website, updated_at, 
        company_name, phone_number, address, city, state, country, 
        postal_code, timezone, preferences, metadata, tenant_id, 
        role, roles, status, last_login_at, email_verified, phone_verified
    )
    VALUES (
        NEW.id, 
        COALESCE(NEW.username, split_part(NEW.email, '@', 1) || '_' || substr(md5(random()::text), 1, 8)),
        COALESCE(NEW.full_name, split_part(NEW.email, '@', 1)),
        NEW.avatar_url,
        NEW.website,
        COALESCE(NEW.updated_at, NOW()),
        NEW.company_name,
        NEW.phone_number,
        NEW.address,
        NEW.city,
        NEW.state,
        NEW.country,
        NEW.postal_code,
        COALESCE(NEW.timezone, 'UTC'),
        COALESCE(NEW.preferences, '{}'::jsonb),
        COALESCE(NEW.metadata, '{}'::jsonb),
        NEW.tenant_id,
        COALESCE(NEW.role, 'user'),
        COALESCE(NEW.roles, ARRAY[COALESCE(NEW.role, 'user')]::text[]),
        COALESCE(NEW.status, 'active'),
        NEW.last_login_at,
        COALESCE(NEW.email_verified, FALSE),
        COALESCE(NEW.phone_verified, FALSE)
    )
    RETURNING *;
    RETURN NEW;
END;
$$;

-- 7. Create a trigger for inserts
DROP TRIGGER IF EXISTS on_profile_insert ON public.profiles_secure;
CREATE TRIGGER on_profile_insert
    INSTEAD OF INSERT ON public.profiles_secure
    FOR EACH ROW EXECUTE FUNCTION public.handle_profile_insert();

-- 8. Create a function to handle updates through the view
CREATE OR REPLACE FUNCTION public.handle_profile_update()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE public.profiles
    SET 
        username = COALESCE(NEW.username, profiles.username),
        full_name = COALESCE(NEW.full_name, profiles.full_name),
        avatar_url = COALESCE(NEW.avatar_url, profiles.avatar_url),
        website = COALESCE(NEW.website, profiles.website),
        updated_at = NOW(),
        company_name = COALESCE(NEW.company_name, profiles.company_name),
        phone_number = COALESCE(NEW.phone_number, profiles.phone_number),
        address = COALESCE(NEW.address, profiles.address),
        city = COALESCE(NEW.city, profiles.city),
        state = COALESCE(NEW.state, profiles.state),
        country = COALESCE(NEW.country, profiles.country),
        postal_code = COALESCE(NEW.postal_code, profiles.postal_code),
        timezone = COALESCE(NEW.timezone, profiles.timezone, 'UTC'),
        preferences = COALESCE(NEW.preferences, profiles.preferences, '{}'::jsonb),
        metadata = COALESCE(NEW.metadata, profiles.metadata, '{}'::jsonb),
        tenant_id = COALESCE(NEW.tenant_id, profiles.tenant_id),
        role = COALESCE(NEW.role, profiles.role, 'user'),
        roles = COALESCE(NEW.roles, profiles.roles, ARRAY[COALESCE(NEW.role, profiles.role, 'user')]::text[]),
        status = COALESCE(NEW.status, profiles.status, 'active'),
        last_login_at = COALESCE(NEW.last_login_at, profiles.last_login_at),
        email_verified = COALESCE(NEW.email_verified, profiles.email_verified, FALSE),
        phone_verified = COALESCE(NEW.phone_verified, profiles.phone_verified, FALSE)
    WHERE id = NEW.id;
    RETURN NEW;
END;
$$;

-- 9. Create a trigger for updates
DROP TRIGGER IF EXISTS on_profile_update ON public.profiles_secure;
CREATE TRIGGER on_profile_update
    INSTEAD OF UPDATE ON public.profiles_secure
    FOR EACH ROW EXECUTE FUNCTION public.handle_profile_update();

-- 10. Create a function to handle deletes through the view
CREATE OR REPLACE FUNCTION public.handle_profile_delete()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    DELETE FROM public.profiles
    WHERE id = OLD.id;
    RETURN OLD;
END;
$$;

-- 11. Create a trigger for deletes
DROP TRIGGER IF EXISTS on_profile_delete ON public.profiles_secure;
CREATE TRIGGER on_profile_delete
    INSTEAD OF DELETE ON public.profiles_secure
    FOR EACH ROW EXECUTE FUNCTION public.handle_profile_delete();

-- 12. Keep RLS disabled on the base table
-- ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 13. Add a comment to document the change
COMMENT ON VIEW public.profiles_secure IS 
    'Secure view for profiles that enforces access control without RLS recursion. Users can see their own profile. Tenant admins can see profiles in their tenant. Super admins can see all profiles.';

-- 14. Create a function to get the current user's profile
CREATE OR REPLACE FUNCTION public.get_my_profile()
RETURNS SETOF public.profiles
LANGUAGE sql
SECURITY DEFINER
AS $$
    SELECT * FROM public.profiles WHERE id = auth.uid();
$$;

-- 15. Grant execute on the function
GRANT EXECUTE ON FUNCTION public.get_my_profile() TO authenticated, service_role;

-- 16. Verify the view was created
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.views 
        WHERE table_schema = 'public' 
        AND table_name = 'profiles_secure'
    ) THEN
        RAISE EXCEPTION 'Failed to create profiles_secure view';
    END IF;
    
    RAISE NOTICE 'Successfully applied profiles security fix using security definer functions';
END
$$;
