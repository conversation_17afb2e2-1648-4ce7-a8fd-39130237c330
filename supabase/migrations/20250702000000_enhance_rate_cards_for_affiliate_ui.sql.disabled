ALTER TABLE public.rate_cards
  ADD COLUMN pricing_model_type TEXT, -- e.g., 'P2P', 'DT', 'HOURLY_CHARTER'
  ADD COLUMN p2p_point_to_point_rate NUMERIC,
  ADD COLUMN p2p_extra_hour_rate NUMERIC,
  ADD COLUMN dt_base_fee NUMERIC, -- Base fee for Distance + Time model
  ADD COLUMN dt_per_mile_rate NUMERIC, -- Replaces per_mile_rate for clarity if used only for DT
  ADD COLUMN dt_per_hour_rate NUMERIC, -- Replaces per_hour_rate for clarity if used only for DT
  ADD COLUMN dt_min_miles NUMERIC,
  ADD COLUMN dt_min_hours INTEGER, -- Replaces minimum_hours for clarity if used only for DT
  ADD COLUMN airport_transfer_flat_rate NUMERIC,
  ADD COLUMN charter_hourly_rate NUMERIC,
  ADD COLUMN charter_min_hours INTEGER;

-- Make original generic rate columns nullable as their use becomes more specific or superseded
ALTER TABLE public.rate_cards
  ALTER COLUMN base_rate DROP NOT NULL; -- Will be used as dt_base_fee or p2p_point_to_point_rate or superseded

ALTER TABLE public.rate_cards
  ALTER COLUMN per_mile_rate DROP NOT NULL; -- Will be dt_per_mile_rate

ALTER TABLE public.rate_cards
  ALTER COLUMN per_hour_rate DROP NOT NULL; -- Will be dt_per_hour_rate

ALTER TABLE public.rate_cards
  ALTER COLUMN minimum_hours DROP NOT NULL; -- Will be dt_min_hours or charter_min_hours

-- It might be cleaner to rename them if they are truly being replaced by the DT specific ones.
-- For now, making them nullable and adding specific ones.
-- Example of renaming (if preferred later):
-- ALTER TABLE public.rate_cards RENAME COLUMN per_mile_rate TO old_per_mile_rate;
-- ALTER TABLE public.rate_cards RENAME COLUMN per_hour_rate TO old_per_hour_rate;
-- ALTER TABLE public.rate_cards RENAME COLUMN minimum_hours TO old_minimum_hours;
-- ALTER TABLE public.rate_cards RENAME COLUMN base_rate TO old_base_rate;

-- Add comments to clarify new column usage
COMMENT ON COLUMN public.rate_cards.pricing_model_type IS 'The pricing model selected by the affiliate for this vehicle type (e.g., P2P, DT, HOURLY_CHARTER).';
COMMENT ON COLUMN public.rate_cards.p2p_point_to_point_rate IS 'Flat rate for Point-to-Point, if pricing_model_type is P2P.';
COMMENT ON COLUMN public.rate_cards.p2p_extra_hour_rate IS 'Rate for extra hours for Point-to-Point, if pricing_model_type is P2P.';
COMMENT ON COLUMN public.rate_cards.dt_base_fee IS 'Base fee for Distance + Time model, if pricing_model_type is DT.';
COMMENT ON COLUMN public.rate_cards.dt_per_mile_rate IS 'Per mile rate for Distance + Time model, if pricing_model_type is DT.';
COMMENT ON COLUMN public.rate_cards.dt_per_hour_rate IS 'Per hour rate for Distance + Time model, if pricing_model_type is DT.';
COMMENT ON COLUMN public.rate_cards.dt_min_miles IS 'Minimum miles for Distance + Time model, if pricing_model_type is DT.';
COMMENT ON COLUMN public.rate_cards.dt_min_hours IS 'Minimum hours for Distance + Time model, if pricing_model_type is DT.';
COMMENT ON COLUMN public.rate_cards.airport_transfer_flat_rate IS 'Specific flat rate for airport transfers.';
COMMENT ON COLUMN public.rate_cards.charter_hourly_rate IS 'Hourly rate for simple charter, if pricing_model_type is HOURLY_CHARTER.';
COMMENT ON COLUMN public.rate_cards.charter_min_hours IS 'Minimum hours for simple charter, if pricing_model_type is HOURLY_CHARTER.';

-- Add unique constraint for upsert functionality
ALTER TABLE public.rate_cards
  ADD CONSTRAINT rate_cards_company_vehicle_type_unique UNIQUE (company_id, vehicle_type); 