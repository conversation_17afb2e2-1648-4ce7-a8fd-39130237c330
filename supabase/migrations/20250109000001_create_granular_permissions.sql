-- LEGACY: This migration may reference ADMIN for super-admin access. All new policies must use SUPER_ADMIN only. If ADMIN is referenced, update to SUPER_ADMIN or add a comment for backward compatibility.

-- Create granular permissions system for super admin user management
-- This enables per-user, per-organization, and per-tenant permission customization

-- Permission templates for quick assignment
CREATE TABLE permission_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  template_type TEXT CHECK (template_type IN ('subscription_tier', 'role_based', 'custom')) NOT NULL,
  permissions JSONB NOT NULL DEFAULT '{}',
  ui_customizations JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User permission overrides (account-level permissions)
CREATE TABLE user_permission_overrides (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  affiliate_company_id UUID REFERENCES affiliate_companies(id) ON DELETE CASCADE,

  -- Permission categories
  feature_permissions JSONB NOT NULL DEFAULT '{}', -- e.g., {"quotes.create": true, "analytics.advanced": false}
  ui_customizations JSONB NOT NULL DEFAULT '{}',   -- e.g., {"sidebar.affiliates": "hidden", "page.analytics": "restricted"}
  access_controls JSONB NOT NULL DEFAULT '{}',     -- e.g., {"data_scope": "organization", "white_label": true}

  -- Metadata
  granted_by UUID REFERENCES auth.users(id), -- Super admin who granted this
  template_id UUID REFERENCES permission_templates(id), -- Template used (if any)
  notes TEXT,

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Ensure unique permission set per user/tenant/company combination
  UNIQUE(user_id, tenant_id, affiliate_company_id)
);

-- Tenant-level feature permissions (for TNC tenants)
CREATE TABLE tenant_feature_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  feature_key VARCHAR(100) NOT NULL, -- 'quotes.view', 'events.create', 'analytics.access'
  is_enabled BOOLEAN DEFAULT true,
  custom_config JSONB DEFAULT '{}', -- Feature-specific configuration
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(tenant_id, feature_key)
);

-- Tenant UI customizations (for white label and branding)
CREATE TABLE tenant_ui_customizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  ui_element VARCHAR(100) NOT NULL, -- 'sidebar.affiliates', 'page.analytics', 'branding.logo'
  visibility TEXT CHECK (visibility IN ('visible', 'hidden', 'restricted')) DEFAULT 'visible',
  custom_properties JSONB DEFAULT '{}', -- Element-specific customization (colors, text, etc.)
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(tenant_id, ui_element)
);

-- Permission audit log
CREATE TABLE permission_audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  target_user_id UUID REFERENCES auth.users(id),
  tenant_id UUID REFERENCES tenants(id),
  affiliate_company_id UUID REFERENCES affiliate_companies(id),
  action TEXT NOT NULL, -- 'granted', 'revoked', 'modified'
  permission_type TEXT NOT NULL, -- 'feature', 'ui', 'access_control'
  permission_key VARCHAR(100),
  old_value JSONB,
  new_value JSONB,
  reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default permission templates
INSERT INTO permission_templates (name, description, template_type, permissions, ui_customizations) VALUES
(
  'Fully Managed Client',
  'View-only access with hidden affiliate and financial data',
  'role_based',
  '{
    "quotes.view": true,
    "quotes.create": false,
    "quotes.edit": false,
    "trips.view": true,
    "trips.manage": false,
    "affiliates.view": false,
    "affiliates.manage": false,
    "analytics.basic": true,
    "analytics.advanced": false,
    "financial.view_costs": true,
    "financial.view_rates": false,
    "white_label": false
  }',
  '{
    "sidebar.affiliates": "hidden",
    "sidebar.financial_reports": "hidden",
    "page.affiliate_management": "hidden",
    "section.affiliate_rates": "hidden",
    "section.cost_breakdown": "hidden"
  }'
),
(
  'Self-Service Client',
  'Full booking management with limited financial access',
  'role_based',
  '{
    "quotes.view": true,
    "quotes.create": true,
    "quotes.edit": true,
    "trips.view": true,
    "trips.manage": true,
    "affiliates.view": true,
    "affiliates.select": true,
    "affiliates.manage": false,
    "analytics.basic": true,
    "analytics.advanced": false,
    "financial.view_costs": true,
    "financial.view_rates": false,
    "white_label": false
  }',
  '{
    "sidebar.affiliate_rates": "hidden",
    "page.affiliate_management": "restricted",
    "section.rate_negotiation": "hidden"
  }'
),
(
  'TNC Full Access',
  'Complete TNC operations with white label option',
  'role_based',
  '{
    "quotes.view": true,
    "quotes.create": true,
    "quotes.edit": true,
    "quotes.delete": true,
    "trips.view": true,
    "trips.manage": true,
    "affiliates.view": true,
    "affiliates.manage": true,
    "analytics.basic": true,
    "analytics.advanced": true,
    "financial.view_costs": true,
    "financial.view_rates": true,
    "customers.register": true,
    "white_label": true
  }',
  '{
    "branding.custom": "visible",
    "page.customer_registration": "visible"
  }'
),
(
  'Medical Transport Specialist',
  'HIPAA-compliant medical transport features',
  'role_based',
  '{
    "quotes.view": true,
    "quotes.create": true,
    "quotes.edit": true,
    "trips.view": true,
    "trips.manage": true,
    "affiliates.view": true,
    "affiliates.medical_only": true,
    "analytics.medical": true,
    "compliance.hipaa": true,
    "white_label": true
  }',
  '{
    "sidebar.commercial_features": "hidden",
    "page.general_affiliates": "hidden",
    "section.hipaa_compliance": "visible"
  }'
);

-- Create indexes for performance
CREATE INDEX idx_user_permission_overrides_user_id ON user_permission_overrides(user_id);
CREATE INDEX idx_user_permission_overrides_tenant_id ON user_permission_overrides(tenant_id);
CREATE INDEX idx_user_permission_overrides_affiliate_company_id ON user_permission_overrides(affiliate_company_id);
CREATE INDEX idx_tenant_feature_permissions_tenant_id ON tenant_feature_permissions(tenant_id);
CREATE INDEX idx_tenant_ui_customizations_tenant_id ON tenant_ui_customizations(tenant_id);
CREATE INDEX idx_permission_audit_log_user_id ON permission_audit_log(user_id);
CREATE INDEX idx_permission_audit_log_target_user_id ON permission_audit_log(target_user_id);

-- Enable RLS
ALTER TABLE permission_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_permission_overrides ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenant_feature_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenant_ui_customizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE permission_audit_log ENABLE ROW LEVEL SECURITY;

-- RLS Policies (Super admin access only)
CREATE POLICY "Super admins can manage permission templates" ON permission_templates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND 'SUPER_ADMIN' = ANY(profiles.roles)
    )
  );

CREATE POLICY "Super admins can manage user permission overrides" ON user_permission_overrides
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND 'SUPER_ADMIN' = ANY(profiles.roles)
    )
  );

CREATE POLICY "Super admins can manage tenant feature permissions" ON tenant_feature_permissions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND 'SUPER_ADMIN' = ANY(profiles.roles)
    )
  );

CREATE POLICY "Super admins can manage tenant UI customizations" ON tenant_ui_customizations
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND 'SUPER_ADMIN' = ANY(profiles.roles)
    )
  );

CREATE POLICY "Super admins can view permission audit log" ON permission_audit_log
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND 'SUPER_ADMIN' = ANY(profiles.roles)
    )
  );
