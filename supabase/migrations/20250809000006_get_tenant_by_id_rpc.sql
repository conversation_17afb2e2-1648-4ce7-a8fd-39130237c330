-- supabase/migrations/20250809000006_get_tenant_by_id_rpc.sql

CREATE OR REPLACE FUNCTION saas_tenants.get_tenant_by_id_rpc(
    p_tenant_id UUID
)
RETURNS TABLE (
    id UUID,
    name TEXT,
    slug VARCHAR(100),
    domain TEXT,
    tenant_type public.tenant_type_enum,
    status public.tenant_status_enum,
    parent_tenant_id UUID,
    branding JSONB,
    settings JSONB,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
)
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
    RETURN QUERY
    SELECT
        t.id,
        t.name,
        t.slug,
        t.domain,
        t.tenant_type,
        t.status,
        t.parent_tenant_id,
        t.branding,
        t.settings,
        t.created_at,
        t.updated_at
    FROM saas_tenants.tenants t
    WHERE t.id = p_tenant_id;
END;
$$;

COMMENT ON FUNCTION saas_tenants.get_tenant_by_id_rpc(UUID)
IS 'Retrieves a single tenant from saas_tenants.tenants by its ID.';

GRANT EXECUTE ON FUNCTION saas_tenants.get_tenant_by_id_rpc(UUID) TO authenticated;
