-- Drop the existing function if it exists
DROP FUNCTION IF EXISTS saas_tenants.list_tenants_rpc(TEXT, INTEGER, INTEGER, TEXT, TEXT);

-- Create the updated list_tenants_rpc function
CREATE FUNCTION saas_tenants.list_tenants_rpc(
    p_status TEXT DEFAULT NULL,
    p_page INTEGER DEFAULT 1,
    p_page_size INTEGER DEFAULT 10,
    p_sort_by TEXT DEFAULT 'name',
    p_sort_order TEXT DEFAULT 'asc'
)
RETURNS TABLE (
    id UUID,
    name TEXT,
    slug TEXT,
    domain TEXT,
    tenant_type TEXT,
    status TEXT,
    settings JSONB,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    total_count BIGINT
)
LANGUAGE plpgsql
STABLE
AS $$
DECLARE
    _offset INTEGER;
    _query TEXT;
    _sort_direction TEXT;
    _safe_sort_by TEXT;
    _where_conditions TEXT[] := '{}';
    _where_clause TEXT := '';
BEGIN
    _offset := (GREATEST(p_page, 1) - 1) * GREATEST(p_page_size, 1);
    _sort_direction := CASE WHEN lower(p_sort_order) = 'desc' THEN 'DESC' ELSE 'ASC' END;

    -- Validate p_sort_by to prevent SQL injection and ensure it's a valid column
    SELECT CASE p_sort_by
        WHEN 'name' THEN 'name'
        WHEN 'domain' THEN 'domain'
        WHEN 'created_at' THEN 'created_at'
        WHEN 'updated_at' THEN 'updated_at'
        WHEN 'status' THEN 'status'
        WHEN 'tenant_type' THEN 'tenant_type'
        ELSE 'name' -- Default to a safe column if input is not recognized
    END INTO _safe_sort_by;

    -- Add status filter if provided
    IF p_status IS NOT NULL THEN
        _where_conditions := array_append(_where_conditions, format('t.status = %L', p_status));
    END IF;

    -- Build WHERE clause if we have conditions
    IF array_length(_where_conditions, 1) > 0 THEN
        _where_clause := ' WHERE ' || array_to_string(_where_conditions, ' AND ');
    END IF;

    _query := format(
        'SELECT 
            t.id, 
            t.name,
            t.slug,
            t.domain, 
            t.tenant_type,
            t.status,
            t.settings, 
            t.created_at, 
            t.updated_at,
            COUNT(*) OVER() as total_count
         FROM saas_tenants.tenants t
         %s
         ORDER BY %I %s
         LIMIT %L OFFSET %L',
        _where_clause,
        _safe_sort_by, _sort_direction,
        GREATEST(p_page_size, 1), _offset
    );

    RETURN QUERY EXECUTE _query;
END;
$$;

-- Update permissions
GRANT EXECUTE ON FUNCTION saas_tenants.list_tenants_rpc(TEXT, INTEGER, INTEGER, TEXT, TEXT) TO authenticated;

-- Update the comment to reflect the function's purpose
COMMENT ON FUNCTION saas_tenants.list_tenants_rpc(TEXT, INTEGER, INTEGER, TEXT, TEXT)
IS 'Lists tenants from saas_tenants.tenants with filtering by status, pagination, and sorting. Includes total_count for pagination and all required tenant fields.';
