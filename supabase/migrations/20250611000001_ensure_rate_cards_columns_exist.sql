-- Ensure all required rate_cards columns exist
-- This migration is idempotent and safe to run multiple times

-- Add pricing_model_type column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'rate_cards' 
        AND column_name = 'pricing_model_type'
    ) THEN
        ALTER TABLE public.rate_cards ADD COLUMN pricing_model_type TEXT;
        COMMENT ON COLUMN public.rate_cards.pricing_model_type IS 'The pricing model selected by the affiliate for this vehicle type (e.g., P2P, DT, HOURLY_CHARTER).';
    END IF;
END $$;

-- Add p2p_point_to_point_rate column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'rate_cards' 
        AND column_name = 'p2p_point_to_point_rate'
    ) THEN
        ALTER TABLE public.rate_cards ADD COLUMN p2p_point_to_point_rate NUMERIC;
        COMMENT ON COLUMN public.rate_cards.p2p_point_to_point_rate IS 'Flat rate for Point-to-Point, if pricing_model_type is P2P.';
    END IF;
END $$;

-- Add p2p_extra_hour_rate column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'rate_cards' 
        AND column_name = 'p2p_extra_hour_rate'
    ) THEN
        ALTER TABLE public.rate_cards ADD COLUMN p2p_extra_hour_rate NUMERIC;
        COMMENT ON COLUMN public.rate_cards.p2p_extra_hour_rate IS 'Rate for extra hours for Point-to-Point, if pricing_model_type is P2P.';
    END IF;
END $$;

-- Add other essential columns
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'rate_cards' 
        AND column_name = 'dt_base_fee'
    ) THEN
        ALTER TABLE public.rate_cards ADD COLUMN dt_base_fee NUMERIC;
        COMMENT ON COLUMN public.rate_cards.dt_base_fee IS 'Base fee for Distance + Time model, if pricing_model_type is DT.';
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'rate_cards' 
        AND column_name = 'dt_per_mile_rate'
    ) THEN
        ALTER TABLE public.rate_cards ADD COLUMN dt_per_mile_rate NUMERIC;
        COMMENT ON COLUMN public.rate_cards.dt_per_mile_rate IS 'Per mile rate for Distance + Time model, if pricing_model_type is DT.';
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'rate_cards' 
        AND column_name = 'dt_per_hour_rate'
    ) THEN
        ALTER TABLE public.rate_cards ADD COLUMN dt_per_hour_rate NUMERIC;
        COMMENT ON COLUMN public.rate_cards.dt_per_hour_rate IS 'Per hour rate for Distance + Time model, if pricing_model_type is DT.';
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'rate_cards' 
        AND column_name = 'dt_min_miles'
    ) THEN
        ALTER TABLE public.rate_cards ADD COLUMN dt_min_miles NUMERIC;
        COMMENT ON COLUMN public.rate_cards.dt_min_miles IS 'Minimum miles for Distance + Time model, if pricing_model_type is DT.';
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'rate_cards' 
        AND column_name = 'dt_min_hours'
    ) THEN
        ALTER TABLE public.rate_cards ADD COLUMN dt_min_hours NUMERIC;
        COMMENT ON COLUMN public.rate_cards.dt_min_hours IS 'Minimum hours for Distance + Time model, if pricing_model_type is DT.';
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'rate_cards' 
        AND column_name = 'airport_transfer_flat_rate'
    ) THEN
        ALTER TABLE public.rate_cards ADD COLUMN airport_transfer_flat_rate NUMERIC;
        COMMENT ON COLUMN public.rate_cards.airport_transfer_flat_rate IS 'Specific flat rate for airport transfers.';
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'rate_cards' 
        AND column_name = 'charter_hourly_rate'
    ) THEN
        ALTER TABLE public.rate_cards ADD COLUMN charter_hourly_rate NUMERIC;
        COMMENT ON COLUMN public.rate_cards.charter_hourly_rate IS 'Hourly rate for simple charter, if pricing_model_type is HOURLY_CHARTER.';
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'rate_cards' 
        AND column_name = 'charter_min_hours'
    ) THEN
        ALTER TABLE public.rate_cards ADD COLUMN charter_min_hours NUMERIC;
        COMMENT ON COLUMN public.rate_cards.charter_min_hours IS 'Minimum hours for simple charter, if pricing_model_type is HOURLY_CHARTER.';
    END IF;
END $$;

-- Add check constraint for pricing_model_type if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'rate_cards_pricing_model_type_check'
    ) THEN
        ALTER TABLE public.rate_cards
        ADD CONSTRAINT rate_cards_pricing_model_type_check
        CHECK (pricing_model_type IN ('P2P', 'DT', 'HOURLY_CHARTER', 'AIRPORT_TRANSFER', 'NONE'));
    END IF;
END $$;

-- Set default pricing_model_type for existing records
UPDATE public.rate_cards 
SET pricing_model_type = 'P2P' 
WHERE pricing_model_type IS NULL;
