-- Seed Data Part 2: Events, Trips, Quotes, and Passengers
-- Creates realistic operational data demonstrating network segregation

BEGIN;

-- ============================================================================
-- 4. CREATE REALISTIC EVENTS WITH PROPER CUSTOMER ASSOCIATIONS
-- ============================================================================

-- City Tours LLC Events (Global TransFlow Network)
INSERT INTO events (id, name, description, start_date, end_date, location, total_passengers, status, customer_id, tenant_id, created_at, updated_at)
VALUES 
  ('event-city-tours-001', 'Annual Corporate Retreat 2025', 'Executive team retreat with airport transfers and local transportation', '2025-02-15 08:00:00+00', '2025-02-17 18:00:00+00', 'Napa Valley, CA', 25, 'confirmed', 'client001-1111-1111-1111-111111111111', '1e55bb92-1d57-4844-ae6c-645071365cac', NOW(), NOW()),
  ('event-city-tours-002', 'Tech Conference Transportation', 'Multi-day conference with shuttle services and VIP transfers', '2025-03-10 06:00:00+00', '2025-03-12 22:00:00+00', 'San Francisco, CA', 150, 'planning', 'client002-2222-2222-2222-222222222222', '1e55bb92-1d57-4844-ae6c-645071365cac', NOW(), NOW()),
  ('event-city-tours-003', 'Wedding Party Transportation', 'Luxury transportation for wedding party and guests', '2025-04-20 14:00:00+00', '2025-04-21 02:00:00+00', 'Sonoma County, CA', 40, 'quoted', 'client001-1111-1111-1111-111111111111', '1e55bb92-1d57-4844-ae6c-645071365cac', NOW(), NOW()),

-- Corporate Travel Solutions Events (Global Network)
  ('event-corp-travel-001', 'Board Meeting Transportation', 'Executive board meeting with airport pickups and hotel transfers', '2025-02-28 07:00:00+00', '2025-03-01 20:00:00+00', 'New York, NY', 12, 'confirmed', 'client003-3333-3333-3333-333333333333', '1e55bb92-1d57-4844-ae6c-645071365cac', NOW(), NOW()),
  ('event-corp-travel-002', 'Sales Team Offsite', 'Quarterly sales meeting with group transportation', '2025-03-15 09:00:00+00', '2025-03-16 17:00:00+00', 'Chicago, IL', 35, 'planning', 'client004-4444-4444-4444-444444444444', '1e55bb92-1d57-4844-ae6c-645071365cac', NOW(), NOW()),

-- St. Mary Hospital Events (TNC Network - Medical Transport)
  ('event-hospital-001', 'Patient Transfer Program', 'Scheduled medical appointments and discharge transportation', '2025-02-01 06:00:00+00', '2025-02-28 20:00:00+00', 'Houston, TX', 200, 'active', 'client005-5555-5555-5555-555555555555', 'cef53f6d-16c8-4414-8db5-0b0bf927ec36', NOW(), NOW()),
  ('event-hospital-002', 'Emergency Dialysis Transport', 'Critical patient transportation for dialysis treatments', '2025-02-10 05:00:00+00', '2025-02-10 22:00:00+00', 'Houston, TX', 8, 'confirmed', 'client006-6666-6666-6666-666666666666', 'cef53f6d-16c8-4414-8db5-0b0bf927ec36', NOW(), NOW()),
  ('event-hospital-003', 'Wheelchair Accessible Transfers', 'ADA compliant transportation for mobility-impaired patients', '2025-03-01 08:00:00+00', '2025-03-31 18:00:00+00', 'Houston, TX', 50, 'planning', 'client005-5555-5555-5555-555555555555', 'cef53f6d-16c8-4414-8db5-0b0bf927ec36', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  description = EXCLUDED.description,
  start_date = EXCLUDED.start_date,
  end_date = EXCLUDED.end_date,
  location = EXCLUDED.location,
  total_passengers = EXCLUDED.total_passengers,
  status = EXCLUDED.status,
  updated_at = NOW();

-- ============================================================================
-- 5. CREATE PASSENGERS LINKED TO EVENTS
-- ============================================================================

-- Passengers for City Tours LLC Events
INSERT INTO passengers (id, event_id, first_name, last_name, email, phone, special_requirements, created_at, updated_at)
VALUES 
  -- Annual Corporate Retreat passengers
  ('pass-retreat-001', 'event-city-tours-001', 'John', 'Smith', '<EMAIL>', '******-2001', NULL, NOW(), NOW()),
  ('pass-retreat-002', 'event-city-tours-001', 'Emily', 'Johnson', '<EMAIL>', '******-2002', 'Vegetarian meal preference', NOW(), NOW()),
  ('pass-retreat-003', 'event-city-tours-001', 'Michael', 'Brown', '<EMAIL>', '******-2003', NULL, NOW(), NOW()),
  ('pass-retreat-004', 'event-city-tours-001', 'Sarah', 'Davis', '<EMAIL>', '******-2004', 'Wheelchair accessible vehicle needed', NOW(), NOW()),
  
  -- Tech Conference passengers
  ('pass-tech-conf-001', 'event-city-tours-002', 'David', 'Wilson', '<EMAIL>', '******-3001', NULL, NOW(), NOW()),
  ('pass-tech-conf-002', 'event-city-tours-002', 'Lisa', 'Anderson', '<EMAIL>', '******-3002', 'Early departure needed', NOW(), NOW()),
  ('pass-tech-conf-003', 'event-city-tours-002', 'James', 'Taylor', '<EMAIL>', '******-3003', NULL, NOW(), NOW()),
  
  -- Wedding Party passengers
  ('pass-wedding-001', 'event-city-tours-003', 'Robert', 'Miller', '<EMAIL>', '******-4001', 'Groom - priority pickup', NOW(), NOW()),
  ('pass-wedding-002', 'event-city-tours-003', 'Jennifer', 'Miller', '<EMAIL>', '******-4002', 'Bride - priority pickup', NOW(), NOW()),
  ('pass-wedding-003', 'event-city-tours-003', 'Thomas', 'Wilson', '<EMAIL>', '******-4003', 'Best man', NOW(), NOW()),

-- Passengers for Corporate Travel Solutions Events
  ('pass-board-001', 'event-corp-travel-001', 'William', 'Garcia', '<EMAIL>', '******-5001', 'CEO - VIP service required', NOW(), NOW()),
  ('pass-board-002', 'event-corp-travel-001', 'Maria', 'Rodriguez', '<EMAIL>', '******-5002', 'CFO - VIP service required', NOW(), NOW()),
  ('pass-board-003', 'event-corp-travel-001', 'Christopher', 'Martinez', '<EMAIL>', '******-5003', 'Board member', NOW(), NOW()),

-- Passengers for Hospital Events (Medical Transport)
  ('pass-medical-001', 'event-hospital-001', 'Patient', 'Confidential', '<EMAIL>', '******-6001', 'HIPAA protected - wheelchair accessible required', NOW(), NOW()),
  ('pass-medical-002', 'event-hospital-001', 'Patient', 'Confidential', '<EMAIL>', '******-6002', 'HIPAA protected - oxygen tank accommodation', NOW(), NOW()),
  ('pass-medical-003', 'event-hospital-002', 'Emergency', 'Patient', '<EMAIL>', '******-6003', 'CRITICAL - dialysis patient, time sensitive', NOW(), NOW()),
  ('pass-medical-004', 'event-hospital-003', 'Mobility', 'Patient', '<EMAIL>', '******-6004', 'Wheelchair accessible vehicle required', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  first_name = EXCLUDED.first_name,
  last_name = EXCLUDED.last_name,
  email = EXCLUDED.email,
  phone = EXCLUDED.phone,
  special_requirements = EXCLUDED.special_requirements,
  updated_at = NOW();

-- ============================================================================
-- 6. CREATE QUOTES WITH NETWORK-SPECIFIC SCENARIOS
-- ============================================================================

-- Quotes for Global TransFlow Network Events
INSERT INTO quotes (id, event_id, customer_id, pickup_location, dropoff_location, pickup_datetime, service_type, passenger_count, status, total_amount, currency, special_requirements, created_at, updated_at, tenant_id)
VALUES 
  -- City Tours LLC Quotes (Global Network)
  ('quote-retreat-001', 'event-city-tours-001', 'client001-1111-1111-1111-111111111111', 'San Francisco International Airport (SFO)', 'Auberge du Soleil, Napa Valley', '2025-02-15 10:00:00+00', 'luxury_suv', 8, 'accepted', 1200.00, 'USD', 'Premium service for executives', NOW(), NOW(), '1e55bb92-1d57-4844-ae6c-645071365cac'),
  ('quote-retreat-002', 'event-city-tours-001', 'client001-1111-1111-1111-111111111111', 'Auberge du Soleil, Napa Valley', 'San Francisco International Airport (SFO)', '2025-02-17 15:00:00+00', 'luxury_suv', 8, 'pending', 1200.00, 'USD', 'Return trip for executives', NOW(), NOW(), '1e55bb92-1d57-4844-ae6c-645071365cac'),
  
  ('quote-tech-conf-001', 'event-city-tours-002', 'client002-2222-2222-2222-222222222222', 'San Francisco Hotels', 'Moscone Center', '2025-03-10 07:00:00+00', 'shuttle_bus', 50, 'quoted', 2500.00, 'USD', 'Conference shuttle service', NOW(), NOW(), '1e55bb92-1d57-4844-ae6c-645071365cac'),
  
  ('quote-wedding-001', 'event-city-tours-003', 'client001-1111-1111-1111-111111111111', 'Bride and Groom Residence', 'Sonoma County Vineyard', '2025-04-20 15:00:00+00', 'luxury_sedan', 2, 'quoted', 800.00, 'USD', 'Wedding day transportation for bride and groom', NOW(), NOW(), '1e55bb92-1d57-4844-ae6c-645071365cac'),

-- Quotes for Corporate Travel Solutions (Global Network)
  ('quote-board-001', 'event-corp-travel-001', 'client003-3333-3333-3333-333333333333', 'JFK International Airport', 'Manhattan Corporate Office', '2025-02-28 08:00:00+00', 'executive_sedan', 4, 'accepted', 150.00, 'USD', 'Executive board member pickup', NOW(), NOW(), '1e55bb92-1d57-4844-ae6c-645071365cac'),
  
-- Quotes for TNC Network (Medical Transport)
  ('quote-medical-001', 'event-hospital-001', 'client005-5555-5555-5555-555555555555', 'St. Mary Hospital', 'Dialysis Center Downtown', '2025-02-10 06:00:00+00', 'medical_transport', 1, 'accepted', 75.00, 'USD', 'HIPAA compliant, wheelchair accessible', NOW(), NOW(), 'cef53f6d-16c8-4414-8db5-0b0bf927ec36'),
  ('quote-medical-002', 'event-hospital-002', 'client006-6666-6666-6666-666666666666', 'Patient Residence', 'St. Mary Hospital Emergency', '2025-02-10 14:00:00+00', 'medical_transport', 1, 'accepted', 120.00, 'USD', 'Emergency transport, oxygen tank required', NOW(), NOW(), 'cef53f6d-16c8-4414-8db5-0b0bf927ec36'),
  ('quote-medical-003', 'event-hospital-003', 'client005-5555-5555-5555-555555555555', 'Rehabilitation Center', 'St. Mary Hospital', '2025-03-01 09:00:00+00', 'wheelchair_accessible', 1, 'pending', 85.00, 'USD', 'ADA compliant vehicle required', NOW(), NOW(), 'cef53f6d-16c8-4414-8db5-0b0bf927ec36')
ON CONFLICT (id) DO UPDATE SET
  pickup_location = EXCLUDED.pickup_location,
  dropoff_location = EXCLUDED.dropoff_location,
  pickup_datetime = EXCLUDED.pickup_datetime,
  service_type = EXCLUDED.service_type,
  passenger_count = EXCLUDED.passenger_count,
  status = EXCLUDED.status,
  total_amount = EXCLUDED.total_amount,
  special_requirements = EXCLUDED.special_requirements,
  updated_at = NOW();

COMMIT;
