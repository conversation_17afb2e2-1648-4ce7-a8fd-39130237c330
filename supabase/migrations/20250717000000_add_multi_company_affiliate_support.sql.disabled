-- LEGACY: This migration may reference ADMIN for super-admin access. All new policies must use SUPER_ADMIN only. If ADMIN is referenced, update to SUPER_ADMIN or add a comment for backward compatibility.

-- Supabase Migration: Add Multi-Company Affiliate Support

-- 1. Define ENUM types for affiliate user role and status
-- Force drop and recreate to ensure correct definition, overriding any older versions.
DROP TYPE IF EXISTS public.affiliate_user_role CASCADE;
CREATE TYPE public.affiliate_user_role AS ENUM ('OWNER', 'DISPATCHER', 'DRIVER', 'STAFF');

DROP TYPE IF EXISTS public.affiliate_user_status CASCADE;
CREATE TYPE public.affiliate_user_status AS ENUM ('INVITED', 'ACTIVE', 'PENDING_VERIFICATION', 'DISABLED', 'REMOVED');

-- Define is_super_admin() function
CREATE OR REPLACE FUNCTION public.is_super_admin()
RETURNS boolean AS $$
BEGIN
  -- Check if the 'role' in app_metadata is 'SUPER_ADMIN'
  -- Ensure that the claim 'app_metadata' and 'role' exist and are configured correctly in your Supabase JWT.
  -- You might need to adjust this logic based on how S<PERSON>ER_ADMIN is actually identified in your system.
  RETURN (
    auth.jwt() IS NOT NULL AND
    auth.jwt() -> 'app_metadata' IS NOT NULL AND
    auth.jwt() -> 'app_metadata' ->> 'role' = 'SUPER_ADMIN'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute on the function to relevant roles
GRANT EXECUTE ON FUNCTION public.is_super_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_super_admin() TO service_role;

-- Make sure this function is available before it's used in RLS policies below.

-- Helper function to check if the current user is an OWNER of a specific company
CREATE OR REPLACE FUNCTION public.is_current_user_active_owner_of_company(p_company_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  is_owner BOOLEAN;
BEGIN
  -- This internal query will be subject to RLS.
  -- It relies on the policy "Users can view their own company associations"
  -- (USING (auth.uid() = user_id)) to allow the current user to see their
  -- own record in affiliate_user_companies, which should prevent recursion here.
  SELECT EXISTS (
    SELECT 1
    FROM public.affiliate_user_companies
    WHERE user_id = auth.uid()
      AND affiliate_id = p_company_id
      AND role = 'OWNER'
      AND status = 'ACTIVE'
  ) INTO is_owner;
  RETURN is_owner;
END;
$$ LANGUAGE plpgsql STABLE SECURITY INVOKER; -- STABLE as it doesn't modify DB and returns same result for same input in a transaction. SECURITY INVOKER is crucial.

-- Grant execute on the new helper function
GRANT EXECUTE ON FUNCTION public.is_current_user_active_owner_of_company(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_current_user_active_owner_of_company(UUID) TO service_role;

-- 2. Create the affiliate_user_companies table
CREATE TABLE IF NOT EXISTS public.affiliate_user_companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    affiliate_id UUID REFERENCES public.affiliate_companies(id) ON DELETE CASCADE NOT NULL,
    role public.affiliate_user_role NOT NULL,
    status public.affiliate_user_status NOT NULL DEFAULT 'INVITED',
    created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
    CONSTRAINT unique_user_company_role UNIQUE (user_id, affiliate_id) -- A user has one primary link/role per company. If multiple roles are needed, this constraint might change or roles become an array.
);

-- Ensure 'role' column exists and is of the correct type
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'affiliate_user_companies' AND column_name = 'role'
    ) THEN
        ALTER TABLE public.affiliate_user_companies ADD COLUMN role public.affiliate_user_role;
    ELSE
        ALTER TABLE public.affiliate_user_companies ALTER COLUMN role TYPE public.affiliate_user_role USING role::text::public.affiliate_user_role;
    END IF;
    -- Ensure NOT NULL constraint is present
    ALTER TABLE public.affiliate_user_companies ALTER COLUMN role SET NOT NULL;
END $$;

-- Ensure 'status' column exists and is of the correct type
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'affiliate_user_companies' AND column_name = 'status'
    ) THEN
        ALTER TABLE public.affiliate_user_companies ADD COLUMN status public.affiliate_user_status;
    ELSE
        ALTER TABLE public.affiliate_user_companies ALTER COLUMN status TYPE public.affiliate_user_status USING status::text::public.affiliate_user_status;
    END IF;
    -- Ensure NOT NULL constraint and default
    ALTER TABLE public.affiliate_user_companies ALTER COLUMN status SET NOT NULL;
    ALTER TABLE public.affiliate_user_companies ALTER COLUMN status SET DEFAULT 'INVITED';
END $$;

COMMENT ON TABLE public.affiliate_user_companies IS 'Links users to affiliate companies with specific roles and statuses, enabling multi-company management.';
COMMENT ON COLUMN public.affiliate_user_companies.role IS 'Role of the user within this specific affiliate company (e.g., OWNER, DISPATCHER, DRIVER).';
COMMENT ON COLUMN public.affiliate_user_companies.status IS 'Status of the user''s association with this company (e.g., INVITED, ACTIVE, DISABLED).';

-- 3. Add RLS policies for affiliate_user_companies
ALTER TABLE public.affiliate_user_companies ENABLE ROW LEVEL SECURITY;

-- Drop the old specific super admin policy as its logic is merged.
DROP POLICY IF EXISTS "Super Admins have full access to affiliate user companies" ON public.affiliate_user_companies;
DROP POLICY IF EXISTS "Affiliate Owners/Admins can manage their company users" ON public.affiliate_user_companies; -- Drop the old one

-- Create the main management policy (FOR ALL: INSERT, UPDATE, DELETE)
CREATE POLICY "Affiliate users can manage their company associations"
ON public.affiliate_user_companies FOR ALL
USING (
    -- Users to whom this policy applies for operating on existing rows:
    public.is_super_admin() OR -- Super Admins
    public.is_current_user_active_owner_of_company(affiliate_user_companies.affiliate_id) -- Active Owners for the company of the row
)
WITH CHECK (
    -- Conditions for allowing an INSERT or UPDATE:
    public.is_super_admin() OR -- Super Admins can do anything
    ( 
      -- Condition 1: An existing Owner of the company (affiliate_id of the row being inserted/updated)
      -- is performing the operation. This allows them to add/manage other users.
      public.is_current_user_active_owner_of_company(affiliate_user_companies.affiliate_id)
    ) OR
    ( 
      -- Condition 2: The user is inserting themselves (user_id of row = current user) 
      -- into a company (affiliate_id of row) with the 'OWNER' role.
      -- This is for the initial company setup by the first affiliate user.
      affiliate_user_companies.user_id = auth.uid() AND 
      affiliate_user_companies.role = 'OWNER'::public.affiliate_user_role AND
      NOT public.is_current_user_active_owner_of_company(affiliate_user_companies.affiliate_id) -- Crucially, they are NOT already an owner of this company
                                                                                                -- This prevents existing owners from re-assigning themselves as owner if other logic should prevent that.
                                                                                                -- More specifically, it ensures this clause is for the *initial* owner setup.
    )
);

-- Policy: Users can view their own associations with companies (FOR SELECT)
-- This policy should remain as is, as it's for SELECT operations by any user for their own records.
DROP POLICY IF EXISTS "Users can view their own company associations" ON public.affiliate_user_companies; -- ensure it's dropped if we are redefining
CREATE POLICY "Users can view their own company associations"
ON public.affiliate_user_companies FOR SELECT
USING (auth.uid() = user_id OR public.is_super_admin()); -- Super admins should also be able to see all associations

-- Trigger to update "updated_at" timestamp
CREATE OR REPLACE FUNCTION public.set_current_timestamp_updated_at()
RETURNS TRIGGER AS $$
DECLARE
  _new record;
BEGIN
  _new := NEW;
  _new."updated_at" = timezone('utc'::text, now());
  RETURN _new;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_affiliate_user_companies_updated_at
BEFORE UPDATE ON public.affiliate_user_companies
FOR EACH ROW
EXECUTE FUNCTION public.set_current_timestamp_updated_at();

-- 4. Data Migration from old `affiliate_details` table (Conceptual - uncomment and modify as needed)
-- This assumes `affiliate_details` has `user_id` (FK to auth.users) and `id` (which is the affiliate_company_id)
-- And that existing users in `affiliate_details` should become 'OWNER' and 'ACTIVE' in the new table.

-- Example:
-- INSERT INTO public.affiliate_user_companies (user_id, affiliate_id, role, status, created_at, updated_at)
-- SELECT
--     ad.user_id,
--     ad.id AS affiliate_id, -- Assuming the PK of affiliate_details was the affiliate_company_id
--     'OWNER'::public.affiliate_user_role,
--     'ACTIVE'::public.affiliate_user_status,
--     COALESCE(ad.created_at, timezone('utc'::text, now())), -- Use existing created_at if available
--     COALESCE(ad.updated_at, timezone('utc'::text, now()))  -- Use existing updated_at if available
-- FROM public.affiliate_details ad
-- WHERE ad.user_id IS NOT NULL -- Ensure user_id exists
--   AND EXISTS (SELECT 1 FROM public.affiliate_companies ac WHERE ac.id = ad.id) -- Ensure the company exists
--   AND EXISTS (SELECT 1 FROM auth.users u WHERE u.id = ad.user_id) -- Ensure the user exists
-- ON CONFLICT (user_id, affiliate_id) DO NOTHING; -- Avoid errors if a mapping somehow already exists

-- After successful migration, you might consider dropping or archiving `affiliate_details`:
-- DROP TABLE IF EXISTS public.affiliate_details;


-- 5. Update RLS policies on other affiliate-related tables
-- This is crucial. Any table that was previously secured by checking `affiliate_details`
-- or by assuming a direct link from `auth.users.id` to an affiliate context needs to be updated.
-- Example for a `vehicles` table (assuming it has a `company_id` FK to `affiliate_companies`):

-- DROP POLICY IF EXISTS "Allow affiliated users to read vehicles" ON public.vehicles; -- Example old policy name
-- CREATE POLICY "Users can access vehicles of their active affiliated companies"
-- ON public.vehicles FOR SELECT
-- USING (
--     EXISTS (
--         SELECT 1
--         FROM public.affiliate_user_companies auc
--         WHERE auc.user_id = auth.uid()
--           AND auc.affiliate_id = vehicles.company_id
--           AND auc.status = 'ACTIVE'
--     )
--     OR public.is_super_admin() -- Allow super admins
-- );

-- CREATE POLICY "Affiliate Owners can manage vehicles in their companies"
-- ON public.vehicles FOR ALL -- INSERT, UPDATE, DELETE
-- USING (
--     EXISTS (
--         SELECT 1
--         FROM public.affiliate_user_companies auc
--         WHERE auc.user_id = auth.uid()
--           AND auc.affiliate_id = vehicles.company_id
--           AND auc.role = 'OWNER'
--           AND auc.status = 'ACTIVE'
--     )
--     OR public.is_super_admin()
-- )
-- WITH CHECK (
--    EXISTS (
--         SELECT 1
--         FROM public.affiliate_user_companies auc
--         WHERE auc.user_id = auth.uid()
--           AND auc.affiliate_id = vehicles.company_id
--           AND auc.role = 'OWNER'
--           AND auc.status = 'ACTIVE'
--     )
--     OR public.is_super_admin()
-- );

-- Repeat similar RLS updates for:
-- - rate_cards
-- - affiliate-specific settings tables
-- - any other table that stores data specific to an affiliate company

-- 6. Add Indexes
CREATE INDEX IF NOT EXISTS idx_affiliate_user_companies_user_id ON public.affiliate_user_companies(user_id);
CREATE INDEX IF NOT EXISTS idx_affiliate_user_companies_affiliate_id ON public.affiliate_user_companies(affiliate_id);
CREATE INDEX IF NOT EXISTS idx_affiliate_user_companies_role ON public.affiliate_user_companies(role);
CREATE INDEX IF NOT EXISTS idx_affiliate_user_companies_status ON public.affiliate_user_companies(status);

GRANT SELECT ON public.affiliate_user_companies TO authenticated;
GRANT INSERT, UPDATE, DELETE ON public.affiliate_user_companies TO supabase_auth_admin; -- Or a more specific role if needed for management

-- Note: Ensure the `is_super_admin()` function and `get_my_claim()` functions are defined and working correctly in your database.
-- The `is_super_admin()` function typically checks a custom claim or a specific role in `auth.users` metadata.
-- e.g. CREATE OR REPLACE FUNCTION public.is_super_admin()
-- RETURNS boolean AS $$
-- BEGIN
--   RETURN (auth.jwt() -> 'app_metadata' ->> 'role' = 'SUPER_ADMIN'); -- Adjust claim as per your setup
-- END;
-- $$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute on helper functions if they are new or permissions need adjustment
-- GRANT EXECUTE ON FUNCTION public.is_super_admin() TO authenticated; 