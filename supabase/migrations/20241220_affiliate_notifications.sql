-- Create affiliate_notifications table for tracking email notifications
CREATE TABLE IF NOT EXISTS affiliate_notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  affiliate_id UUID NOT NULL REFERENCES affiliate_companies(id) ON DELETE CASCADE,
  type VARCHAR(50) NOT NULL CHECK (type IN ('approval', 'rejection', 'update_request', 'general')),
  subject TEXT NOT NULL,
  content TEXT NOT NULL,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'bounced')),
  sent_at TIMESTAMP WITH TIME ZONE,
  sent_by UUID REFERENCES auth.users(id),
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_affiliate_notifications_affiliate_id ON affiliate_notifications(affiliate_id);
CREATE INDEX IF NOT EXISTS idx_affiliate_notifications_type ON affiliate_notifications(type);
CREATE INDEX IF NOT EXISTS idx_affiliate_notifications_status ON affiliate_notifications(status);
CREATE INDEX IF NOT EXISTS idx_affiliate_notifications_sent_at ON affiliate_notifications(sent_at);

-- Add RLS policies
ALTER TABLE affiliate_notifications ENABLE ROW LEVEL SECURITY;

-- Super admins can see all notifications
CREATE POLICY "Super admins can view all affiliate notifications" ON affiliate_notifications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles p
      WHERE p.id = auth.uid()
      AND ('SUPER_ADMIN' = ANY(p.roles) OR 'ADMIN' = ANY(p.roles))
    )
  );

-- Super admins can insert notifications
CREATE POLICY "Super admins can create affiliate notifications" ON affiliate_notifications
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles p
      WHERE p.id = auth.uid()
      AND ('SUPER_ADMIN' = ANY(p.roles) OR 'ADMIN' = ANY(p.roles))
    )
  );

-- Super admins can update notifications
CREATE POLICY "Super admins can update affiliate notifications" ON affiliate_notifications
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM profiles p
      WHERE p.id = auth.uid()
      AND ('SUPER_ADMIN' = ANY(p.roles) OR 'ADMIN' = ANY(p.roles))
    )
  );

-- Affiliates can view their own notifications
CREATE POLICY "Affiliates can view their own notifications" ON affiliate_notifications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM affiliate_companies ac
      JOIN profiles p ON p.id = auth.uid()
      WHERE ac.id = affiliate_notifications.affiliate_id
      AND 'AFFILIATE' = ANY(p.roles)
      AND ac.owner_id = auth.uid()
    )
  );

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_affiliate_notifications_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_affiliate_notifications_updated_at
  BEFORE UPDATE ON affiliate_notifications
  FOR EACH ROW
  EXECUTE FUNCTION update_affiliate_notifications_updated_at();

-- Add comments for documentation
COMMENT ON TABLE affiliate_notifications IS 'Stores email notifications sent to affiliates about status changes and updates';
COMMENT ON COLUMN affiliate_notifications.type IS 'Type of notification: approval, rejection, update_request, general';
COMMENT ON COLUMN affiliate_notifications.status IS 'Email delivery status: pending, sent, failed, bounced';
COMMENT ON COLUMN affiliate_notifications.content IS 'HTML content of the email notification';
