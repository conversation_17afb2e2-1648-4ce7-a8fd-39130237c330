-- Fix the <NAME_EMAIL> user to have SUPER_ADMIN role
-- This user exists in auth.users with SUPER_ADMIN in JWT but may not have correct profile

-- First, let's ensure the profile exists with the correct role
INSERT INTO public.profiles (id, email, role, roles, full_name, created_at, updated_at)
SELECT 
    u.id,
    u.email,
    'SUPER_ADMIN' as role,
    ARRAY['SUPER_ADMIN']::TEXT[] as roles,
    COALESCE(u.raw_user_meta_data->>'full_name', 'Super Admin') as full_name,
    u.created_at,
    NOW() as updated_at
FROM auth.users u
WHERE u.email = '<EMAIL>'
ON CONFLICT (id) DO UPDATE SET
    role = 'SUPER_ADMIN',
    roles = ARRAY['SUPER_ADMIN']::TEXT[],
    updated_at = NOW();

-- Also update the handle_new_user function to properly handle admin888 specifically
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
BEGIN
  -- Delete any existing profile with the same ID
  DELETE FROM public.profiles WHERE id = NEW.id;
  
  -- Determine role based on email and metadata
  DECLARE
    user_role TEXT;
    user_email TEXT := LOWER(NEW.email);
    user_roles TEXT[];
  BEGIN
    -- First check user_metadata for roles array
    IF NEW.raw_user_meta_data ? 'roles' THEN
      user_roles := ARRAY(SELECT jsonb_array_elements_text(NEW.raw_user_meta_data->'roles'));
      user_role := user_roles[1]; -- Use first role as primary
    -- Then check app_metadata
    ELSIF NEW.raw_app_meta_data ? 'role' THEN
      user_role := NEW.raw_app_meta_data->>'role';
      user_roles := ARRAY[user_role];
    -- Finally determine from email
    ELSE
      IF user_email = '<EMAIL>' THEN
        user_role := 'SUPER_ADMIN';
      ELSIF user_email LIKE '%affiliate%' THEN
        user_role := 'AFFILIATE';
      ELSIF user_email LIKE '%admin%' THEN
        user_role := 'ADMIN';
      ELSIF user_email LIKE '%event%' THEN
        user_role := 'EVENT_MANAGER';
      ELSE
        user_role := 'CUSTOMER';
      END IF;
      user_roles := ARRAY[user_role];
    END IF;
    
    -- Insert a new profile with both role and roles array
    INSERT INTO public.profiles (id, email, role, roles, full_name)
    VALUES (
      NEW.id, 
      NEW.email, 
      user_role, 
      user_roles,
      COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1))
    );
  END;

  RETURN NEW;
END;
$function$;

-- Log the fix
INSERT INTO public.logs (level, message, context, created_at)
VALUES (
  'info',
  'Fixed <EMAIL> profile to have SUPER_ADMIN role',
  jsonb_build_object(
    'email', '<EMAIL>',
    'action', 'profile_role_fix'
  ),
  NOW()
);
