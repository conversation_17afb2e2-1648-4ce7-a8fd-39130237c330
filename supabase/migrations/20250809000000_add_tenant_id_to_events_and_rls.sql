BEGIN;

-- Add tenant_id column to public.events
ALTER TABLE public.events ADD COLUMN IF NOT EXISTS tenant_id UUID;

-- Add foreign key constraint to saas_tenants.tenants
-- Ensure saas_tenants.tenants table and id column exist before running this
ALTER TABLE public.events
ADD CONSTRAINT fk_events_tenant_id FOREIGN KEY (tenant_id)
REFERENCES saas_tenants.tenants(id) ON DELETE SET NULL;

-- Enable Row Level Security on public.events
ALTER TABLE public.events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.events FORCE ROW LEVEL SECURITY;

-- Drop existing RLS policies that do not consider tenant_id
DROP POLICY IF EXISTS "Clients can view their own events" ON public.events;
DROP POLICY IF EXISTS "Client coordinators can view events they manage" ON public.events;
DROP POLICY IF EXISTS "Clients can create events" ON public.events;
DROP POLICY IF EXISTS "Client coordinators can update events they manage" ON public.events;
DROP POLICY IF EXISTS "Admins have full access to events" ON public.events;

-- Drop the old view as it's not tenant-aware in its previous form
DROP VIEW IF EXISTS client_coordinator_events;

-- RLS Policies for Multi-Tenancy

-- Policy for SELECT operations
CREATE POLICY "Allow tenant members and admins to read events" 
ON public.events
FOR SELECT
USING (
  saas_tenants.is_system_admin() OR 
  (events.tenant_id = saas_tenants.get_current_tenant_id())
);

-- Policy for INSERT operations
CREATE POLICY "Allow tenant members and admins to insert events"
ON public.events
FOR INSERT
WITH CHECK (
  saas_tenants.is_system_admin() OR 
  (events.tenant_id = saas_tenants.get_current_tenant_id())
);

-- Policy for UPDATE operations
CREATE POLICY "Allow tenant members and admins to update events"
ON public.events
FOR UPDATE
USING (
  saas_tenants.is_system_admin() OR 
  (events.tenant_id = saas_tenants.get_current_tenant_id())
)
WITH CHECK (
  saas_tenants.is_system_admin() OR 
  (events.tenant_id = saas_tenants.get_current_tenant_id())
);

-- Policy for DELETE operations
CREATE POLICY "Allow tenant members and admins to delete events"
ON public.events
FOR DELETE
USING (
  saas_tenants.is_system_admin() OR 
  (events.tenant_id = saas_tenants.get_current_tenant_id())
);

COMMIT;
