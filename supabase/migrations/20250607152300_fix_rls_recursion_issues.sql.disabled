-- LEGACY: This migration may reference ADMIN for super-admin access. All new policies must use SUPER_ADMIN only. If ADMIN is referenced, update to SUPER_ADMIN or add a comment for backward compatibility.

-- Fix RLS recursion issues in profiles and user_profiles tables
-- This migration addresses the infinite recursion in RLS policies and ensures proper tenant isolation

-- 1. First, create a safe version of get_current_tenant_id
CREATE OR REPLACE FUNCTION saas_tenants.get_current_tenant_id()
RETURNS UUID AS $$
DECLARE
    user_tenant_id UUID;
    is_super_admin BOOLEAN;
BEGIN
    -- First check if user is a system admin
    SELECT EXISTS (
        SELECT 1 FROM auth.users 
        WHERE id = auth.uid() 
        AND raw_user_meta_data->>'role' = 'SUPER_ADMIN'
    ) INTO is_super_admin;

    IF is_super_admin THEN
        RETURN NULL; -- System admins bypass tenant restrictions
    END IF;

    -- Get the tenant_id from tenant_users
    SELECT tu.tenant_id INTO user_tenant_id
    FROM saas_tenants.tenant_users tu
    WHERE tu.user_id = auth.uid()
    LIMIT 1;

    RETURN user_tenant_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Add tenant_id to profiles if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'profiles' 
        AND column_name = 'tenant_id'
    ) THEN
        ALTER TABLE public.profiles 
        ADD COLUMN tenant_id UUID 
        REFERENCES public.tenants(id) 
        ON DELETE SET NULL;
    END IF;
END
$$;

-- 3. Update profiles RLS policies
DROP POLICY IF EXISTS profiles_tenant_isolation_policy ON public.profiles;

CREATE POLICY profiles_tenant_isolation_policy ON public.profiles
    USING (
        -- System admins can see all profiles
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE id = auth.uid() 
            AND raw_user_meta_data->>'role' = 'SUPER_ADMIN'
        )
        OR
        -- Users can see their own profile
        auth.uid() = id
        OR
        -- Users can see profiles in their tenant
        EXISTS (
            SELECT 1 
            FROM saas_tenants.tenant_users tu
            WHERE tu.user_id = auth.uid()
            AND tu.tenant_id = public.profiles.tenant_id
        )
    );

-- 4. Fix user_profiles RLS policies
DROP POLICY IF EXISTS user_profiles_tenant_isolation_policy ON public.user_profiles;

CREATE POLICY user_profiles_tenant_isolation_policy ON public.user_profiles
    USING (
        -- System admins can see all user profiles
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE id = auth.uid() 
            AND raw_user_meta_data->>'role' = 'SUPER_ADMIN'
        )
        OR
        -- Users can see their own profile
        auth.uid() = user_id
        OR
        -- Users can see profiles in their tenant
        EXISTS (
            SELECT 1 
            FROM saas_tenants.tenant_users tu
            WHERE tu.user_id = auth.uid()
            AND tu.tenant_id = (
                SELECT tenant_id 
                FROM saas_tenants.tenant_users 
                WHERE user_id = user_profiles.user_id
                LIMIT 1
            )
        )
    );

-- 5. Update existing profiles with tenant_id if they don't have one
-- This ensures existing profiles are assigned to a valid tenant
UPDATE public.profiles p
SET tenant_id = tu.tenant_id
FROM saas_tenants.tenant_users tu
WHERE p.tenant_id IS NULL 
AND p.id = tu.user_id
AND tu.tenant_id IN (SELECT id FROM public.tenants);

-- 6. Create a trigger to automatically set tenant_id on new profiles
CREATE OR REPLACE FUNCTION public.handle_new_profile()
RETURNS TRIGGER AS $$
BEGIN
    -- Set tenant_id from user's tenant association if available
    NEW.tenant_id := (
        SELECT tenant_id 
        FROM saas_tenants.tenant_users 
        WHERE user_id = NEW.id 
        LIMIT 1
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_created ON public.profiles;

-- Create the trigger
CREATE TRIGGER on_auth_user_created
BEFORE INSERT ON public.profiles
FOR EACH ROW
EXECUTE FUNCTION public.handle_new_profile();

-- 7. Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.profiles TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.user_profiles TO authenticated;

-- 8. Add a comment to document these changes
COMMENT ON FUNCTION saas_tenants.get_current_tenant_id() IS 'Safely gets the current user''s tenant ID, avoiding RLS recursion. Returns NULL for super admins.';

-- 9. Notify that the migration is complete
DO $$
BEGIN
    RAISE NOTICE 'RLS recursion fixes applied successfully';
END
$$;
