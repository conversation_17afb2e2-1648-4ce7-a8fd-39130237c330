-- Comprehensive Audit System for Affiliate Management
-- This migration creates tables and functions for complete audit trail

BEGIN;

-- 1. Create audit_logs table if it doesn't exist, or add missing columns
CREATE TABLE IF NOT EXISTS public.audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id),
    action TEXT NOT NULL,
    resource_type TEXT NOT NULL,
    resource_id UUID,
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Add missing columns if they don't exist
ALTER TABLE public.audit_logs
ADD COLUMN IF NOT EXISTS resource_type TEXT,
ADD COLUMN IF NOT EXISTS resource_id UUID,
ADD COLUMN IF NOT EXISTS ip_address INET,
ADD COLUMN IF NOT EXISTS user_agent TEXT;

-- 2. Create affiliate_approval_history table for tracking approval workflow
CREATE TABLE IF NOT EXISTS public.affiliate_approval_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    affiliate_id UUID REFERENCES public.affiliate_companies(id) ON DELETE CASCADE NOT NULL,
    action TEXT NOT NULL CHECK (action IN ('submitted', 'approved', 'rejected', 'resubmitted', 'updated')),
    previous_status TEXT,
    new_status TEXT,
    admin_user_id UUID REFERENCES auth.users(id),
    rejection_reasons TEXT[],
    rejection_notes TEXT,
    approval_notes TEXT,
    created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- 3. Create notification_templates table for structured notifications
CREATE TABLE IF NOT EXISTS public.notification_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type TEXT NOT NULL UNIQUE CHECK (type IN ('approval', 'rejection', 'update_request', 'resubmission_request')),
    subject_template TEXT NOT NULL,
    content_template TEXT NOT NULL,
    variables JSONB, -- Available template variables
    created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- 4. Add missing columns to affiliate_companies for better tracking
ALTER TABLE public.affiliate_companies 
ADD COLUMN IF NOT EXISTS application_status TEXT DEFAULT 'pending' CHECK (application_status IN ('pending', 'approved', 'rejected', 'resubmitted')),
ADD COLUMN IF NOT EXISTS rejection_reasons TEXT[],
ADD COLUMN IF NOT EXISTS rejection_notes TEXT,
ADD COLUMN IF NOT EXISTS approval_notes TEXT,
ADD COLUMN IF NOT EXISTS approved_by UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS approved_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS rejected_by UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS rejected_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS resubmission_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_resubmitted_at TIMESTAMPTZ;

-- 5. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON public.audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource ON public.audit_logs(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON public.audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_affiliate_approval_history_affiliate_id ON public.affiliate_approval_history(affiliate_id);
CREATE INDEX IF NOT EXISTS idx_affiliate_approval_history_created_at ON public.affiliate_approval_history(created_at);
CREATE INDEX IF NOT EXISTS idx_affiliate_companies_application_status ON public.affiliate_companies(application_status);

-- 6. Insert default notification templates
INSERT INTO public.notification_templates (type, subject_template, content_template, variables) VALUES
('approval', 'Welcome to TransFlow - Your Application Has Been Approved!', 
 'Dear {{affiliate_name}},

Congratulations! Your application to join the TransFlow affiliate network has been approved.

You can now:
- Access your affiliate dashboard
- Start receiving quote requests
- Manage your fleet and rates
- View performance analytics

{{#approval_notes}}
Admin Notes: {{approval_notes}}
{{/approval_notes}}

Welcome to the TransFlow family!

Best regards,
The TransFlow Team', 
 '{"affiliate_name": "Company name", "approval_notes": "Optional admin notes"}'),

('rejection', 'TransFlow Application Update - Action Required', 
 'Dear {{affiliate_name}},

Thank you for your interest in joining the TransFlow affiliate network. After reviewing your application, we need you to address the following items before we can proceed:

{{#rejection_reasons}}
- {{.}}
{{/rejection_reasons}}

{{#rejection_notes}}
Additional Details:
{{rejection_notes}}
{{/rejection_notes}}

Please log into your account and update the required information. Once completed, your application will be automatically resubmitted for review.

If you have any questions, please contact our support team.

Best regards,
The TransFlow Team', 
 '{"affiliate_name": "Company name", "rejection_reasons": "Array of rejection reasons", "rejection_notes": "Optional admin notes"}'),

('resubmission_request', 'TransFlow - Please Resubmit Your Application', 
 'Dear {{affiliate_name}},

We have reviewed the updates to your application. Please resubmit your application for final review.

Thank you for your patience.

Best regards,
The TransFlow Team', 
 '{"affiliate_name": "Company name"}')
ON CONFLICT (type) DO UPDATE SET
    subject_template = EXCLUDED.subject_template,
    content_template = EXCLUDED.content_template,
    variables = EXCLUDED.variables,
    updated_at = timezone('utc'::text, now());

-- 7. Create function to log affiliate actions
CREATE OR REPLACE FUNCTION public.log_affiliate_action(
    p_affiliate_id UUID,
    p_action TEXT,
    p_admin_user_id UUID DEFAULT NULL,
    p_previous_status TEXT DEFAULT NULL,
    p_new_status TEXT DEFAULT NULL,
    p_rejection_reasons TEXT[] DEFAULT NULL,
    p_rejection_notes TEXT DEFAULT NULL,
    p_approval_notes TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    history_id UUID;
BEGIN
    INSERT INTO public.affiliate_approval_history (
        affiliate_id,
        action,
        previous_status,
        new_status,
        admin_user_id,
        rejection_reasons,
        rejection_notes,
        approval_notes
    ) VALUES (
        p_affiliate_id,
        p_action,
        p_previous_status,
        p_new_status,
        p_admin_user_id,
        p_rejection_reasons,
        p_rejection_notes,
        p_approval_notes
    ) RETURNING id INTO history_id;
    
    RETURN history_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. Enable RLS on new tables
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.affiliate_approval_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_templates ENABLE ROW LEVEL SECURITY;

-- 9. Create RLS policies
CREATE POLICY "Super admins can view all audit logs" ON public.audit_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE profiles.id = auth.uid()
            AND 'SUPER_ADMIN' = ANY(profiles.roles)
        )
    );

CREATE POLICY "Super admins can view all approval history" ON public.affiliate_approval_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE profiles.id = auth.uid()
            AND 'SUPER_ADMIN' = ANY(profiles.roles)
        )
    );

CREATE POLICY "Affiliates can view their own approval history" ON public.affiliate_approval_history
    FOR SELECT USING (
        affiliate_id IN (
            SELECT id FROM public.affiliate_companies
            WHERE owner_id = auth.uid()
        )
    );

CREATE POLICY "Super admins can view notification templates" ON public.notification_templates
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE profiles.id = auth.uid()
            AND 'SUPER_ADMIN' = ANY(profiles.roles)
        )
    );

COMMIT;
