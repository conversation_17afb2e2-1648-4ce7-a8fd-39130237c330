-- Fix RLS recursion issues in profiles table - Version 2
-- This migration addresses the infinite recursion in the profiles RLS policy

-- 1. Drop the existing policy if it exists
DROP POLICY IF EXISTS profiles_tenant_isolation_policy ON public.profiles;

-- 2. Disable RLS to prevent any issues during the update
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- 3. Create a safer version of the policy that avoids recursion
CREATE POLICY profiles_tenant_isolation_policy ON public.profiles
    USING (
        -- System admins can see all profiles
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE id = auth.uid() 
            AND raw_user_meta_data->>'role' = 'SUPER_ADMIN'
        )
        OR
        -- Users can see their own profile
        auth.uid() = id
        OR
        -- Users can see profiles in their tenant
        EXISTS (
            SELECT 1 
            FROM saas_tenants.tenant_users tu
            JOIN saas_tenants.tenant_users current_user_tu 
                ON current_user_tu.user_id = auth.uid()
                AND tu.tenant_id = current_user_tu.tenant_id
            WHERE tu.user_id = profiles.id
        )
    );

-- 4. Re-enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 5. Add a comment to document the change
COMMENT ON POLICY profiles_tenant_isolation_policy ON public.profiles IS 
    'Controls access to profiles based on tenant membership. Users can see their own profile and profiles within their tenant. Super admins can see all profiles.';

-- 6. Verify the policy was created
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_policies 
        WHERE tablename = 'profiles' 
        AND policyname = 'profiles_tenant_isolation_policy'
    ) THEN
        RAISE EXCEPTION 'Failed to create profiles_tenant_isolation_policy';
    END IF;
    
    RAISE NOTICE 'Successfully applied RLS recursion fix v2';
END
$$;
