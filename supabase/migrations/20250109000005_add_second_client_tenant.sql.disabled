-- Migration: Add second client tenant for ORG filtering testing
-- This creates Metro Medical Transport organization and assigns events to it

-- First, check if Metro Medical Transport organization already exists
-- If not, create it
INSERT INTO organizations (id, name, slug, description, industry, tenant_id, status, created_at, updated_at)
VALUES (
  'b8f23e3d-174d-5bcd-c894-f283383e866d',
  'Metro Medical Transport',
  'metro-medical-transport',
  'Specialized medical transportation and emergency services',
  'Healthcare & Medical',
  '88888888-8888-8888-8888-888888888888',
  'active',
  NOW(),
  NOW()
)
ON CONFLICT (id) DO NOTHING;

-- Create a user for Metro Medical Transport if it doesn't exist
-- We'll use an existing user and associate them with the new organization
INSERT INTO user_organizations (user_id, organization_id, role, created_at, updated_at)
VALUES (
  '11111111-1111-1111-1111-111111111111', -- Use existing user
  'b8f23e3d-174d-5bcd-c894-f283383e866d', -- Metro Medical Transport
  'admin',
  NOW(),
  NOW()
)
ON CONFLICT (user_id, organization_id) DO NOTHING;

-- Create some events for Metro Medical Transport to test ORG filtering
INSERT INTO events (name, description, start_date, end_date, location, total_passengers, status, customer_id, tenant_id, created_at, updated_at)
VALUES 
  ('Emergency Medical Conference', 'Annual emergency medical services conference with transportation', '2025-03-05 07:00:00+00', '2025-03-07 19:00:00+00', 'Dallas, TX', 80, 'published', 'f5d4b9f4-27d4-49f7-bbc5-dd17bb154455', '1e55bb92-1d57-4844-ae6c-645071365cac', NOW(), NOW()),
  ('Hospital Staff Shuttle', 'Daily shuttle service for hospital staff during construction', '2025-02-20 05:00:00+00', '2025-04-30 23:00:00+00', 'Houston, TX', 120, 'published', 'f5d4b9f4-27d4-49f7-bbc5-dd17bb154455', '1e55bb92-1d57-4844-ae6c-645071365cac', NOW(), NOW()),
  ('Medical Equipment Transport', 'Specialized transport for medical equipment and personnel', '2025-03-15 08:00:00+00', '2025-03-15 18:00:00+00', 'Austin, TX', 15, 'draft', 'f5d4b9f4-27d4-49f7-bbc5-dd17bb154455', '1e55bb92-1d57-4844-ae6c-645071365cac', NOW(), NOW());

-- Note: These events are currently assigned to the same tenant_id as the City Tours events
-- This is because the events table has tenant_id (network tenant) not organization_id
-- The organization filtering will be done through the customer_id -> user_organizations -> organization relationship
