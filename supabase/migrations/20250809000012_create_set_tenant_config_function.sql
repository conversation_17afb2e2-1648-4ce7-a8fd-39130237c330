-- Create the missing set_tenant_config function that the frontend is trying to call
-- This function is used for setting tenant context in RLS policies

CREATE OR REPLACE FUNCTION public.set_tenant_config(
    setting_name TEXT,
    setting_value TEXT,
    is_local BOOLEAN DEFAULT false
)
R<PERSON><PERSON>NS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Set the configuration parameter for the current session
    -- This is used by RLS policies to filter data by tenant
    PERFORM set_config(setting_name, setting_value, is_local);
    RETURN setting_value;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.set_tenant_config(TEXT, TEXT, BOOLEAN) TO authenticated;

-- Add comment explaining the function's purpose
COMMENT ON FUNCTION public.set_tenant_config(TEXT, TEXT, BOOLEAN) 
IS 'Sets configuration parameters for tenant context in RLS policies. Used by the frontend to establish tenant context for database queries.';
