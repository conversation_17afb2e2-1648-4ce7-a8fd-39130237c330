-- Drop the old check constraint if it exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM pg_constraint
        WHERE conname = 'notification_templates_type_check'
    ) THEN
        ALTER TABLE notification_templates DROP CONSTRAINT notification_templates_type_check;
    END IF;
END
$$;

-- Add a new check constraint with all allowed types
ALTER TABLE notification_templates
ADD CONSTRAINT notification_templates_type_check
CHECK (type = ANY (ARRAY['approval'::text, 'rejection'::text, 'update_request'::text, 'resubmission_request'::text, 'quote_confirmation'::text, 'reset_password'::text]));

-- Ensure tenant_branding has an email_templates column
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'tenant_branding' AND column_name = 'email_templates') THEN
        ALTER TABLE tenant_branding ADD COLUMN email_templates JSONB DEFAULT '{}';
    END IF;
END
$$;

-- Insert default email templates
INSERT INTO notification_templates (type, subject_template, content_template, variables)
VALUES
    (
        'quote_confirmation',
        '{{pickupCity}} Quote Request - {{formattedDate}} - {{vehicleName}} - {{customerName}}',
        '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Confirmation</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .header { background-color: #f4f4f4; padding: 10px 20px; text-align: center; border-bottom: 1px solid #ddd; }
        .content { padding: 20px; }
        .footer { background-color: #f4f4f4; padding: 10px 20px; text-align: center; border-top: 1px solid #ddd; font-size: 0.9em; color: #777; }
        .button { display: inline-block; background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Quote Confirmation</h1>
        </div>
        <div class="content">
            <p>Dear {{customerName}},</p>
            <p>Thank you for your recent quote request for a {{vehicleName}}.</p>
            <p>Here are the details of your request:</p>
            <ul>
                <li><strong>Pickup Location:</strong> {{pickupLocation}}</li>
                <li><strong>Dropoff Location:</strong> {{dropoffLocation}}</li>
                <li><strong>Pickup Date:</strong> {{pickupDate}}</li>
                <li><strong>Pickup Time:</strong> {{pickupTime}}</li>
                <li><strong>Passengers:</strong> Adults: {{adults}}{{#if children}}, Children: {{children}}{{/if}}</li>
                {{#if specialRequests}}<li><strong>Special Requests:</strong> {{specialRequests}}</li>{{/if}}
                {{#if dealId}}<li><strong>Reference ID:</strong> {{dealId}}</li>{{/if}}
            </ul>
            <p>We will review your request and get back to you with a detailed quote shortly.</p>
            <p>If you have any questions, please do not hesitate to contact us.</p>
            <p>Best regards,</p>
            <p>The {{tenantName}} Team</p>
        </div>
        <div class="footer">
            <p>&copy; {{year}} {{tenantName}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>',
        '{"pickupCity": "string", "formattedDate": "string", "vehicleName": "string", "customerName": "string", "pickupLocation": "string", "dropoffLocation": "string", "pickupDate": "string", "pickupTime": "string", "adults": "number", "children": "number", "specialRequests": "string", "dealId": "string", "tenantName": "string", "year": "number"}'::jsonb
    ),
    (
        'approval',
        '🎉 Your Affiliate Application Has Been Approved!',
        '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Affiliate Application Approved</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .header { background-color: #f4f4f4; padding: 10px 20px; text-align: center; border-bottom: 1px solid #ddd; }
        .content { padding: 20px; }
        .footer { background-color: #f4f4f4; padding: 10px 20px; text-align: center; border-top: 1px solid #ddd; font-size: 0.9em; color: #777; }
        .button { display: inline-block; background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Application Approved!</h1>
        </div>
        <div class="content">
            <p>Dear {{affiliateName}},</p>
            <p>We are thrilled to inform you that your affiliate application has been approved!</p>
            <p>Welcome to our affiliate network. We are excited to have you on board and look forward to a successful partnership.</p>
            {{#if approvalNotes}}<p><strong>Notes:</strong> {{approvalNotes}}</p>{{/if}}
            <p>You can now log in to your dashboard and start managing your account.</p>
            <p><a href="{{loginUrl}}" class="button">Go to Dashboard</a></p>
            <p>If you have any questions, please feel free to contact our support team.</p>
            <p>Best regards,</p>
            <p>The {{tenantName}} Team</p>
        </div>
        <div class="footer">
            <p>&copy; {{year}} {{tenantName}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>',
        '{"affiliateName": "string", "approvalNotes": "string", "loginUrl": "string", "tenantName": "string", "year": "number"}'::jsonb
    ),
    (
        'reset_password',
        'Password Reset Request',
        '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .header { background-color: #f4f4f4; padding: 10px 20px; text-align: center; border-bottom: 1px solid #ddd; }
        .content { padding: 20px; }
        .footer { background-color: #f4f4f4; padding: 10px 20px; text-align: center; border-top: 1px solid #ddd; font-size: 0.9em; color: #777; }
        .button { display: inline-block; background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Password Reset Request</h1>
        </div>
        <div class="content">
            <p>Dear {{userName}},</p>
            <p>We received a request to reset your password for your account with {{tenantName}}.</p>
            <p>If you initiated this request, please click the button below to set a new password:</p>
            <p><a href="{{resetUrl}}" class="button">Reset Password</a></p>
            <p>If you did not request a password reset, please ignore this email.</p>
            <p>This link will expire in a few hours for security reasons.</p>
            <p>Best regards,</p>
            <p>The {{tenantName}} Team</p>
        </div>
        <div class="footer">
            <p>&copy; {{year}} {{tenantName}}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>',
        '{"userName": "string", "resetUrl": "string", "tenantName": "string", "year": "number"}'::jsonb
    )
ON CONFLICT (type) DO UPDATE SET subject_template = EXCLUDED.subject_template, content_template = EXCLUDED.content_template, variables = EXCLUDED.variables;
