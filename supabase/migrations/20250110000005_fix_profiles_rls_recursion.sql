-- LEGACY: This migration may reference ADMIN for super-admin access. All new policies must use SUPER_ADMIN only. If ADMIN is referenced, update to SUPER_ADMIN or add a comment for backward compatibility.

-- Fix infinite recursion in profiles RLS policies
-- The issue is that policies are checking the profiles table to determine access to the profiles table

-- Drop the problematic policy that causes recursion
DROP POLICY IF EXISTS "Super admins can manage all profiles" ON profiles;

-- Create a new policy that uses JWT claims instead of querying profiles table
-- This avoids the circular dependency
CREATE POLICY "Super admins can manage all profiles" ON profiles
  FOR ALL
  TO authenticated
  USING (
    -- Use JWT claims to check role instead of querying profiles table
    get_my_claim('role') IN ('SUPER_ADMIN', 'ADMIN')
    OR 
    -- Allow users to access their own profile
    auth.uid() = id
  )
  WITH CHECK (
    -- Use JWT claims to check role instead of querying profiles table
    get_my_claim('role') IN ('SUPER_ADMIN', 'ADMIN')
    OR 
    -- Allow users to update their own profile
    auth.uid() = id
  );

-- Also check if there are any other problematic functions and create a safe admin check
CREATE OR REPLACE FUNCTION is_super_admin_safe()
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  -- Use JWT claims instead of querying profiles table to avoid recursion
  SELECT get_my_claim('role') IN ('SUPER_ADMIN', 'ADMIN');
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION is_super_admin_safe() TO authenticated, service_role;

-- Add comment for documentation
COMMENT ON FUNCTION is_super_admin_safe() IS 'Safe admin check that uses JWT claims instead of querying profiles table to avoid RLS recursion';
