-- Seed Data Part 3: Affiliate Offers, Rate Cards, and Network Scenarios
-- Demonstrates network segregation and affiliate participation

BEGIN;

-- ============================================================================
-- 7. CREATE QUOTE AFFILIATE OFFERS (Network-Specific Responses)
-- ============================================================================

-- Global TransFlow Network Affiliate Responses
INSERT INTO quote_affiliate_offers (id, quote_id, company_id, status, rate_amount, currency, response_time_minutes, notes, created_at, updated_at, expires_at)
VALUES 
  -- Elite Limo responses (Global Network)
  ('offer-elite-retreat-001', 'quote-retreat-001', 'aff-elite-limo-service', 'accepted', 1150.00, 'USD', 15, 'Premium executive service with complimentary refreshments', NOW(), NOW(), NOW() + INTERVAL '24 hours'),
  ('offer-elite-wedding-001', 'quote-wedding-001', 'aff-elite-limo-service', 'quoted', 750.00, 'USD', 8, 'Wedding package includes red carpet service and champagne', NOW(), NOW(), NOW() + INTERVAL '48 hours'),
  
  -- Luxury Rides responses (Global Network)
  ('offer-luxury-retreat-001', 'quote-retreat-001', 'aff-luxury-rides-llc', 'quoted', 1200.00, 'USD', 22, 'Luxury SUV fleet with professional chauffeurs', NOW(), NOW(), NOW() + INTERVAL '24 hours'),
  ('offer-luxury-tech-001', 'quote-tech-conf-001', 'aff-luxury-rides-llc', 'quoted', 2400.00, 'USD', 18, 'Conference shuttle with WiFi and charging stations', NOW(), NOW(), NOW() + INTERVAL '72 hours'),
  ('offer-luxury-board-001', 'quote-board-001', 'aff-luxury-rides-llc', 'declined', NULL, 'USD', 45, 'Unable to accommodate due to scheduling conflict', NOW(), NOW(), NULL),
  
  -- Premium Transport (Multi-Network) responses to Global quotes
  ('offer-premium-retreat-001', 'quote-retreat-001', 'aff-premium-transport', 'quoted', 1100.00, 'USD', 12, 'Best rate guarantee with luxury amenities', NOW(), NOW(), NOW() + INTERVAL '24 hours'),
  ('offer-premium-board-001', 'quote-board-001', 'aff-premium-transport', 'accepted', 140.00, 'USD', 5, 'Executive sedan with meet and greet service', NOW(), NOW(), NOW() + INTERVAL '12 hours'),

-- TNC Network Exclusive Affiliate Responses (Medical Transport)
  -- Medical Transport Solutions responses (TNC Exclusive)
  ('offer-medical-dialysis-001', 'quote-medical-001', 'aff-medical-transport', 'accepted', 70.00, 'USD', 8, 'HIPAA compliant with certified medical transport driver', NOW(), NOW(), NOW() + INTERVAL '6 hours'),
  ('offer-medical-emergency-001', 'quote-medical-002', 'aff-medical-transport', 'accepted', 115.00, 'USD', 3, 'Emergency response with oxygen-equipped vehicle', NOW(), NOW(), NOW() + INTERVAL '2 hours'),
  
  -- Accessible Rides responses (TNC Exclusive)
  ('offer-accessible-wheelchair-001', 'quote-medical-003', 'aff-accessible-rides', 'quoted', 80.00, 'USD', 12, 'ADA compliant vehicle with wheelchair lift', NOW(), NOW(), NOW() + INTERVAL '24 hours'),
  ('offer-accessible-dialysis-001', 'quote-medical-001', 'aff-accessible-rides', 'quoted', 75.00, 'USD', 15, 'Wheelchair accessible with medical equipment space', NOW(), NOW(), NOW() + INTERVAL '6 hours'),
  
  -- Premium Transport (Multi-Network) responses to TNC quotes
  ('offer-premium-medical-001', 'quote-medical-001', 'aff-premium-transport', 'quoted', 72.00, 'USD', 10, 'Medical certified drivers with wheelchair accessibility', NOW(), NOW(), NOW() + INTERVAL '6 hours'),
  ('offer-premium-wheelchair-001', 'quote-medical-003', 'aff-premium-transport', 'accepted', 82.00, 'USD', 7, 'Premium medical transport with comfort amenities', NOW(), NOW(), NOW() + INTERVAL '24 hours')
ON CONFLICT (id) DO UPDATE SET
  status = EXCLUDED.status,
  rate_amount = EXCLUDED.rate_amount,
  response_time_minutes = EXCLUDED.response_time_minutes,
  notes = EXCLUDED.notes,
  updated_at = NOW();

-- ============================================================================
-- 8. CREATE RATE CARDS FOR DIFFERENT NETWORK SCENARIOS
-- ============================================================================

-- Global TransFlow Network Rate Cards
INSERT INTO rate_cards (id, company_id, service_type, base_rate, per_mile_rate, per_hour_rate, minimum_charge, currency, effective_date, expiry_date, is_active, service_area, created_at, updated_at)
VALUES 
  -- Elite Limo Service (Global Network)
  ('rate-elite-luxury-suv', 'aff-elite-limo-service', 'luxury_suv', 150.00, 3.50, 85.00, 150.00, 'USD', '2025-01-01', '2025-12-31', true, 'San Francisco Bay Area, Napa Valley', NOW(), NOW()),
  ('rate-elite-executive-sedan', 'aff-elite-limo-service', 'executive_sedan', 100.00, 2.75, 65.00, 100.00, 'USD', '2025-01-01', '2025-12-31', true, 'San Francisco Bay Area', NOW(), NOW()),
  ('rate-elite-luxury-sedan', 'aff-elite-limo-service', 'luxury_sedan', 120.00, 3.00, 75.00, 120.00, 'USD', '2025-01-01', '2025-12-31', true, 'San Francisco Bay Area, Wine Country', NOW(), NOW()),
  
  -- Luxury Rides LLC (Global Network)
  ('rate-luxury-shuttle-bus', 'aff-luxury-rides-llc', 'shuttle_bus', 200.00, 2.50, 95.00, 200.00, 'USD', '2025-01-01', '2025-12-31', true, 'Los Angeles, Orange County', NOW(), NOW()),
  ('rate-luxury-executive-sedan', 'aff-luxury-rides-llc', 'executive_sedan', 110.00, 2.85, 70.00, 110.00, 'USD', '2025-01-01', '2025-12-31', true, 'Los Angeles Metro Area', NOW(), NOW()),
  ('rate-luxury-suv', 'aff-luxury-rides-llc', 'luxury_suv', 160.00, 3.75, 90.00, 160.00, 'USD', '2025-01-01', '2025-12-31', true, 'Los Angeles, Beverly Hills', NOW(), NOW()),

-- TNC Network Exclusive Rate Cards (Medical Transport)
  -- Medical Transport Solutions (TNC Exclusive)
  ('rate-medical-transport', 'aff-medical-transport', 'medical_transport', 50.00, 2.25, 45.00, 50.00, 'USD', '2025-01-01', '2025-12-31', true, 'Houston Metro Area', NOW(), NOW()),
  ('rate-medical-wheelchair', 'aff-medical-transport', 'wheelchair_accessible', 60.00, 2.50, 50.00, 60.00, 'USD', '2025-01-01', '2025-12-31', true, 'Houston, Medical Center', NOW(), NOW()),
  ('rate-medical-emergency', 'aff-medical-transport', 'emergency_transport', 80.00, 3.00, 65.00, 80.00, 'USD', '2025-01-01', '2025-12-31', true, 'Houston Emergency Services Area', NOW(), NOW()),
  
  -- Accessible Rides Inc (TNC Exclusive)
  ('rate-accessible-wheelchair', 'aff-accessible-rides', 'wheelchair_accessible', 55.00, 2.40, 48.00, 55.00, 'USD', '2025-01-01', '2025-12-31', true, 'Chicago Metro Area', NOW(), NOW()),
  ('rate-accessible-medical', 'aff-accessible-rides', 'medical_transport', 65.00, 2.60, 52.00, 65.00, 'USD', '2025-01-01', '2025-12-31', true, 'Chicago, Medical District', NOW(), NOW()),

-- Premium Transport Group (Multi-Network) Rate Cards
  -- Global Network Rates
  ('rate-premium-global-suv', 'aff-premium-transport', 'luxury_suv', 140.00, 3.25, 80.00, 140.00, 'USD', '2025-01-01', '2025-12-31', true, 'Miami-Dade, Broward County', NOW(), NOW()),
  ('rate-premium-global-sedan', 'aff-premium-transport', 'executive_sedan', 95.00, 2.65, 62.00, 95.00, 'USD', '2025-01-01', '2025-12-31', true, 'South Florida', NOW(), NOW()),
  
  -- TNC Network Rates (Medical)
  ('rate-premium-tnc-medical', 'aff-premium-transport', 'medical_transport', 52.00, 2.30, 47.00, 52.00, 'USD', '2025-01-01', '2025-12-31', true, 'Miami Medical Centers', NOW(), NOW()),
  ('rate-premium-tnc-wheelchair', 'aff-premium-transport', 'wheelchair_accessible', 58.00, 2.45, 49.00, 58.00, 'USD', '2025-01-01', '2025-12-31', true, 'South Florida ADA Services', NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  base_rate = EXCLUDED.base_rate,
  per_mile_rate = EXCLUDED.per_mile_rate,
  per_hour_rate = EXCLUDED.per_hour_rate,
  minimum_charge = EXCLUDED.minimum_charge,
  effective_date = EXCLUDED.effective_date,
  expiry_date = EXCLUDED.expiry_date,
  is_active = EXCLUDED.is_active,
  service_area = EXCLUDED.service_area,
  updated_at = NOW();

-- ============================================================================
-- 9. CREATE TRIPS FROM ACCEPTED QUOTES
-- ============================================================================

-- Create trips for accepted quotes to demonstrate the trip management system
INSERT INTO trips (id, quote_id, driver_id, vehicle_id, status, scheduled_pickup, actual_pickup, scheduled_dropoff, actual_dropoff, distance_miles, duration_minutes, created_at, updated_at)
VALUES 
  -- Global Network Trips
  ('trip-retreat-001', 'quote-retreat-001', NULL, NULL, 'scheduled', '2025-02-15 10:00:00+00', NULL, '2025-02-15 12:30:00+00', NULL, 65.5, 150, NOW(), NOW()),
  ('trip-board-001', 'quote-board-001', NULL, NULL, 'in_progress', '2025-02-28 08:00:00+00', '2025-02-28 08:05:00+00', '2025-02-28 08:45:00+00', NULL, 12.3, 45, NOW(), NOW()),
  
  -- TNC Network Trips (Medical)
  ('trip-medical-001', 'quote-medical-001', NULL, NULL, 'completed', '2025-02-10 06:00:00+00', '2025-02-10 05:58:00+00', '2025-02-10 06:35:00+00', '2025-02-10 06:32:00+00', 8.2, 35, NOW(), NOW()),
  ('trip-medical-002', 'quote-medical-002', NULL, NULL, 'completed', '2025-02-10 14:00:00+00', '2025-02-10 13:55:00+00', '2025-02-10 14:25:00+00', '2025-02-10 14:22:00+00', 5.7, 25, NOW(), NOW()),
  ('trip-wheelchair-001', 'quote-medical-003', NULL, NULL, 'scheduled', '2025-03-01 09:00:00+00', NULL, '2025-03-01 09:40:00+00', NULL, 11.4, 40, NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  status = EXCLUDED.status,
  scheduled_pickup = EXCLUDED.scheduled_pickup,
  actual_pickup = EXCLUDED.actual_pickup,
  scheduled_dropoff = EXCLUDED.scheduled_dropoff,
  actual_dropoff = EXCLUDED.actual_dropoff,
  distance_miles = EXCLUDED.distance_miles,
  duration_minutes = EXCLUDED.duration_minutes,
  updated_at = NOW();

-- Add final comments
COMMENT ON TABLE quote_affiliate_offers IS 'Affiliate responses to quotes showing network segregation: Global vs TNC exclusive vs Multi-network';
COMMENT ON TABLE rate_cards IS 'Service pricing by affiliate companies with different rates for Global vs TNC networks';
COMMENT ON TABLE trips IS 'Actual transportation trips derived from accepted quotes, showing operational data';

COMMIT;
