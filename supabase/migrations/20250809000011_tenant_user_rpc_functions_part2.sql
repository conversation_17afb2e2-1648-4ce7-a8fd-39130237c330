-- Function to add or update a user in a tenant
CREATE OR REPLACE FUNCTION saas_tenants.upsert_tenant_user_rpc(
    p_tenant_id UUID,
    p_user_id UUID,
    p_role TEXT,
    p_actor_id UUID DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_result JSONB;
    v_tenant_exists BOOLEAN;
    v_user_exists BOOLEAN;
BEGIN
    -- Check if tenant exists and is active
    SELECT EXISTS (
        SELECT 1 FROM saas_tenants.tenants 
        WHERE id = p_tenant_id AND status = 'ACTIVE'
    ) INTO v_tenant_exists;
    
    IF NOT v_tenant_exists THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Tenant not found or inactive',
            'code', 'TENANT_NOT_FOUND'
        );
    END IF;
    
    -- Check if user exists
    SELECT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) INTO v_user_exists;
    IF NOT v_user_exists THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'User not found',
            'code', 'USER_NOT_FOUND'
        );
    END IF;
    
    -- Insert or update the tenant user
    INSERT INTO saas_tenants.tenant_users (
        tenant_id,
        user_id,
        role,
        created_by
    )
    VALUES (
        p_tenant_id,
        p_user_id,
        p_role,
        COALESCE(p_actor_id, p_user_id)
    )
    ON CONFLICT (tenant_id, user_id) 
    DO UPDATE SET 
        role = EXCLUDED.role,
        updated_at = NOW()
    RETURNING 
        jsonb_build_object(
            'id', id,
            'tenant_id', tenant_id,
            'user_id', user_id,
            'role', role,
            'created_at', created_at,
            'updated_at', updated_at
        ) INTO v_result;
    
    RETURN jsonb_build_object('success', true, 'data', v_result);
    
EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object(
        'success', false,
        'error', SQLERRM,
        'code', SQLSTATE
    );
END;
$$;

-- Function to remove a user from a tenant
CREATE OR REPLACE FUNCTION saas_tenants.remove_tenant_user_rpc(
    p_tenant_id UUID,
    p_user_id UUID,
    p_actor_id UUID DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_deleted_count INTEGER;
    v_tenant_exists BOOLEAN;
    v_user_exists BOOLEAN;
BEGIN
    -- Check if tenant exists
    SELECT EXISTS (SELECT 1 FROM saas_tenants.tenants WHERE id = p_tenant_id) INTO v_tenant_exists;
    IF NOT v_tenant_exists THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'Tenant not found',
            'code', 'TENANT_NOT_FOUND'
        );
    END IF;
    
    -- Check if user exists
    SELECT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) INTO v_user_exists;
    IF NOT v_user_exists THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'User not found',
            'code', 'USER_NOT_FOUND'
        );
    END IF;
    
    -- Delete the tenant user association
    DELETE FROM saas_tenants.tenant_users
    WHERE tenant_id = p_tenant_id AND user_id = p_user_id
    RETURNING 1 INTO v_deleted_count;
    
    IF v_deleted_count = 0 THEN
        RETURN jsonb_build_object(
            'success', false,
            'error', 'User is not associated with this tenant',
            'code', 'ASSOCIATION_NOT_FOUND'
        );
    END IF;
    
    RETURN jsonb_build_object('success', true);
    
EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object(
        'success', false,
        'error', SQLERRM,
        'code', SQLSTATE
    );
END;
$$;

-- Function to get users for a tenant
CREATE OR REPLACE FUNCTION saas_tenants.get_tenant_users_rpc(
    p_tenant_id UUID,
    p_page INT DEFAULT 1,
    p_page_size INT DEFAULT 10,
    p_search TEXT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_result JSONB;
    v_total_count BIGINT;
    v_offset INT;
    v_filter TEXT;
    v_query TEXT;
BEGIN
    -- Input validation
    IF p_page < 1 THEN
        p_page := 1;
    END IF;
    
    IF p_page_size < 1 OR p_page_size > 100 THEN
        p_page_size := 10;
    END IF;
    
    v_offset := (p_page - 1) * p_page_size;
    
    -- Build the filter condition
    v_filter := 'WHERE tu.tenant_id = $1';
    
    IF p_search IS NOT NULL AND p_search != '' THEN
        v_filter := v_filter || ' AND (u.email ILIKE $3 OR u.raw_user_meta_data->>''full_name'' ILIKE $3)';
    END IF;
    
    -- Get total count
    EXECUTE format('
        SELECT COUNT(*)::BIGINT
        FROM saas_tenants.tenant_users tu
        JOIN auth.users u ON u.id = tu.user_id
        %s',
        v_filter
    )
    USING p_tenant_id, p_search || '%', '%' || p_search || '%'
    INTO v_total_count;
    
    -- Get paginated results
    EXECUTE format('
        SELECT jsonb_build_object(
            ''data'', COALESCE(jsonb_agg(
                jsonb_build_object(
                    ''id'', tu.id,
                    ''user_id'', u.id,
                    ''email'', u.email,
                    ''full_name'', u.raw_user_meta_data->>''full_name'',
                    ''role'', tu.role,
                    ''created_at'', tu.created_at,
                    ''updated_at'', tu.updated_at
                )
                ORDER BY u.email
            ), ''[]''::jsonb),
            ''pagination'', jsonb_build_object(
                ''total'', $1,
                ''page'', $2,
                ''page_size'', $3,
                ''total_pages'', CEIL($1::FLOAT / NULLIF($3, 0))
            )
        )
        FROM (
            SELECT tu.*, u.email, u.raw_user_meta_data
            FROM saas_tenants.tenant_users tu
            JOIN auth.users u ON u.id = tu.user_id
            %s
            ORDER BY u.email
            LIMIT $4 OFFSET $5
        ) tu',
        v_filter
    )
    USING v_total_count, p_page, p_page_size, p_page_size, v_offset, p_search || '%', '%' || p_search || '%'
    INTO v_result;
    
    RETURN jsonb_build_object('success', true, 'data', v_result);
    
EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object(
        'success', false,
        'error', SQLERRM,
        'code', SQLSTATE
    );
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION saas_tenants.upsert_tenant_user_rpc(UUID, UUID, TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION saas_tenants.remove_tenant_user_rpc(UUID, UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION saas_tenants.get_tenant_users_rpc(UUID, INT, INT, TEXT) TO authenticated;
