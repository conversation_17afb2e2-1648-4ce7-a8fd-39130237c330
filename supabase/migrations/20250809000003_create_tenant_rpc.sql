-- supabase/migrations/20250809000003_create_tenant_rpc.sql

CREATE OR REPLACE FUNCTION saas_tenants.create_tenant_rpc(
    p_name TEXT,
    p_tenant_type public.tenant_type_enum,
    p_slug TEXT DEFAULT NULL,
    p_domain TEXT DEFAULT NULL,
    p_parent_tenant_id UUID DEFAULT NULL,
    p_status public.tenant_status_enum DEFAULT 'active', -- Default from table definition
    p_branding JSONB DEFAULT '{}',
    p_settings JSONB DEFAULT '{}'
)
RETURNS saas_tenants.tenants -- Returns the whole tenant record
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    final_slug TEXT;
    created_tenant saas_tenants.tenants;
BEGIN
    -- Ensure caller is authorized (API layer should primarily handle this,
    -- but an additional check here for direct DB calls could be added if needed)
    -- Example: IF NOT saas_tenants.is_system_admin() THEN RAISE EXCEPTION 'Unauthorized'; END IF;
    -- For now, relying on API layer's requireRole(['SUPER_ADMIN'])

    IF p_name IS NULL OR p_name = '' THEN
        RAISE EXCEPTION 'Tenant name cannot be empty.';
    END IF;

    IF p_tenant_type IS NULL THEN
        RAISE EXCEPTION 'Tenant type cannot be empty.';
    END IF;

    IF p_slug IS NULL OR p_slug = '' THEN
        -- Generate a unique slug based on name and a random suffix
        final_slug := lower(regexp_replace(p_name, '[^a-zA-Z0-9_]+', '-', 'g')) || '-' || substring(md5(random()::text || clock_timestamp()::text) from 1 for 8);
    ELSE
        final_slug := p_slug;
    END IF;

    -- Validate slug format (simple version)
    IF NOT (final_slug ~ '^[a-z0-9]+(?:-[a-z0-9]+)*$') THEN
        RAISE EXCEPTION 'Invalid slug format: "%". Slugs can only contain lowercase alphanumeric characters and hyphens, and cannot start or end with a hyphen.', final_slug;
    END IF;
    
    -- Validate parent_tenant_id if provided
    IF p_parent_tenant_id IS NOT NULL AND NOT EXISTS (SELECT 1 FROM saas_tenants.tenants WHERE id = p_parent_tenant_id) THEN
        RAISE EXCEPTION 'Parent tenant ID % does not exist.', p_parent_tenant_id;
    END IF;

    INSERT INTO saas_tenants.tenants (
        name, slug, domain, tenant_type, parent_tenant_id, status, branding, settings
    ) VALUES (
        p_name, final_slug, p_domain, p_tenant_type, p_parent_tenant_id, p_status, p_branding, p_settings
    ) RETURNING * INTO created_tenant;

    RETURN created_tenant;
EXCEPTION
    WHEN unique_violation THEN
        -- Catch unique constraint violations (e.g., for slug)
        RAISE EXCEPTION 'Failed to create tenant. Slug "%" may already exist or another unique constraint was violated.', final_slug;
    WHEN OTHERS THEN
        -- Catch any other errors during insert
        RAISE EXCEPTION 'An unexpected error occurred while creating tenant: %', SQLERRM;
END;
$$;

COMMENT ON FUNCTION saas_tenants.create_tenant_rpc(TEXT, public.tenant_type_enum, TEXT, TEXT, UUID, public.tenant_status_enum, JSONB, JSONB)
IS 'Creates a new tenant in saas_tenants.tenants. Intended to be called by Super Admins via an API endpoint.';

-- Grant execute permission to authenticated users, as the API will use an authenticated client.
-- The function itself is SECURITY DEFINER, but API layer restricts to SUPER_ADMIN.
GRANT EXECUTE ON FUNCTION saas_tenants.create_tenant_rpc(TEXT, public.tenant_type_enum, TEXT, TEXT, UUID, public.tenant_status_enum, JSONB, JSONB) TO authenticated;
