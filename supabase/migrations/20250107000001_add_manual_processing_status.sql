-- Add manual_processing status to quotes table
-- This status is used when no affiliates are found and the quote needs manual handling

BEGIN;

-- Drop the existing constraint
ALTER TABLE public.quotes DROP CONSTRAINT IF EXISTS quotes_status_check;

-- Add the new constraint with manual_processing status
ALTER TABLE public.quotes ADD CONSTRAINT quotes_status_check 
CHECK (status = ANY (ARRAY[
  'new'::text, 
  'rate_requested'::text, 
  'fixed_offer'::text, 
  'accepted'::text, 
  'rejected'::text, 
  'pending'::text, 
  'quote_ready'::text, 
  'quote_assigned'::text, 
  'sent_to_affiliates'::text,
  'manual_processing'::text
]));

-- Add manual_processing_reason column if it doesn't exist
ALTER TABLE public.quotes 
ADD COLUMN IF NOT EXISTS manual_processing_reason TEXT;

-- Add comment to explain the new status
COMMENT ON COLUMN public.quotes.status IS 'Quote status: new, rate_requested, fixed_offer, accepted, rejected, pending, quote_ready, quote_assigned, sent_to_affiliates, manual_processing';
COMMENT ON COLUMN public.quotes.manual_processing_reason IS 'Reason why quote requires manual processing (e.g., no_affiliates_found, special_requirements)';

COMMIT;
