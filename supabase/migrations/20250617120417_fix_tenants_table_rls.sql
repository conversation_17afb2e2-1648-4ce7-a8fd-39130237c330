-- Enable RLS on the public.tenants table
ALTER TABLE public.tenants ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist to prevent conflicts
DROP POLICY IF EXISTS "tenants_access" ON public.tenants;

-- Policy for SELECT: Allow SUPER_ADMIN to select all tenants
CREATE POLICY "Allow SUPER_ADMIN to select tenants"
ON public.tenants FOR SELECT
TO authenticated
USING (
    (SELECT public.get_my_claim('role'::text))::text = 'SUPER_ADMIN'
);

-- Policy for INSERT: Allow SUPER_ADMIN to insert new tenants
CREATE POLICY "Allow SUPER_ADMIN to insert tenants"
ON public.tenants FOR INSERT
TO authenticated
WITH CHECK (
    (SELECT public.get_my_claim('role'::text))::text = 'SUPER_ADMIN'
);

-- Policy for UPDATE: Allow SUPER_ADMIN to update tenants, including branding
CREATE POLICY "Allow SUPER_ADMIN to update tenants"
ON public.tenants FOR UPDATE
TO authenticated
USING (
    (SELECT public.get_my_claim('role'::text))::text = 'SUPER_ADMIN'
)
WITH CHECK (
    (SELECT public.get_my_claim('role'::text))::text = 'SUPER_ADMIN'
);
