-- Add a default tenant for testing
INSERT INTO saas_tenants.tenants (id, name, domain, settings, created_at, updated_at)
VALUES (
    '00000000-0000-0000-0000-000000000000',
    'Default Tenant',
    'default.localhost',
    '{"theme": {"primaryColor": "#3b82f6"}}'::jsonb,
    NOW(),
    NOW()
)
ON CONFLICT (id) DO NOTHING;

-- Update existing users to be associated with the default tenant
INSERT INTO saas_tenants.tenant_users (tenant_id, user_id, role, created_at)
SELECT 
    '00000000-0000-0000-0000-000000000000',
    id,
    'admin',
    NOW()
FROM auth.users
WHERE id NOT IN (SELECT user_id FROM saas_tenants.tenant_users)
ON CONFLICT (tenant_id, user_id) DO NOTHING;
