-- Create passengers table for event management
-- This migration creates the passengers table that's referenced in the event manager APIs

BEGIN;

-- Create passengers table
CREATE TABLE IF NOT EXISTS public.passengers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    email TEXT NOT NULL,
    phone_number TEXT,
    passenger_type TEXT DEFAULT 'guest' CHECK (passenger_type IN ('guest', 'vip', 'staff', 'adult', 'child')),
    company TEXT,
    dietary_restrictions TEXT,
    special_requirements TEXT,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    tenant_id UUID REFERENCES saas_tenants.tenants(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for passengers
CREATE INDEX IF NOT EXISTS idx_passengers_created_by ON public.passengers(created_by);
CREATE INDEX IF NOT EXISTS idx_passengers_tenant_id ON public.passengers(tenant_id);
CREATE INDEX IF NOT EXISTS idx_passengers_email ON public.passengers(email);
CREATE INDEX IF NOT EXISTS idx_passengers_type ON public.passengers(passenger_type);

-- Enable RLS on passengers
ALTER TABLE public.passengers ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for passengers
CREATE POLICY "Users can view passengers in their tenant"
    ON public.passengers
    FOR SELECT
    USING (
        tenant_id IN (
            SELECT tenant_id FROM saas_tenants.tenant_users 
            WHERE user_id = auth.uid()
        )
        OR created_by = auth.uid()
        OR EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
        )
    );

CREATE POLICY "Users can create passengers in their tenant"
    ON public.passengers
    FOR INSERT
    WITH CHECK (
        created_by = auth.uid()
        AND (
            tenant_id IN (
                SELECT tenant_id FROM saas_tenants.tenant_users 
                WHERE user_id = auth.uid()
            )
            OR EXISTS (
                SELECT 1 FROM public.profiles 
                WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
            )
        )
    );

CREATE POLICY "Users can update passengers they created"
    ON public.passengers
    FOR UPDATE
    USING (
        created_by = auth.uid()
        OR EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
        )
    );

CREATE POLICY "Users can delete passengers they created"
    ON public.passengers
    FOR DELETE
    USING (
        created_by = auth.uid()
        OR EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
        )
    );

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_passengers_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_passengers_updated_at
BEFORE UPDATE ON public.passengers
FOR EACH ROW EXECUTE FUNCTION update_passengers_updated_at();

-- Add comments for documentation
COMMENT ON TABLE public.passengers IS 'Passengers for events and trips';
COMMENT ON COLUMN public.passengers.passenger_type IS 'Type of passenger: guest, vip, staff, adult, child';
COMMENT ON COLUMN public.passengers.dietary_restrictions IS 'Any dietary restrictions or allergies';
COMMENT ON COLUMN public.passengers.special_requirements IS 'Any special requirements or accessibility needs';

COMMIT;
