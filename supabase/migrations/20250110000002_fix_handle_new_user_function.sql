-- Fix handle_new_user function to use 'C<PERSON>IENT' instead of 'CUSTOMER'
-- The constraint allows 'CLIENT' but not 'CUSTOMER'

CREATE OR REPLACE FUNCTION handle_new_user()
R<PERSON><PERSON>NS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, roles, full_name, role)
  VALUES (
    NEW.id,
    ARRAY[COALESCE(NEW.raw_user_meta_data->>'role', 'CLIENT')],
    COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'role', 'CLIENT')
  );
  RETURN NEW;
EXCEPTION
  WHEN unique_violation THEN
    -- If profile already exists, ignore
    RETURN NEW;
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Error in handle_new_user: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
