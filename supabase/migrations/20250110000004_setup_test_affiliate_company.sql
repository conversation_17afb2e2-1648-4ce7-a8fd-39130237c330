-- Setup test affiliate company and user association
-- This <NAME_EMAIL> user has a company to work with

DO $$
DECLARE
  affiliate_user_id UUID;
  test_company_id UUID;
BEGIN
  -- Get the affiliate user ID
  SELECT id INTO affiliate_user_id 
  FROM profiles 
  WHERE email = '<EMAIL>' 
  LIMIT 1;

  IF affiliate_user_id IS NULL THEN
    RAISE NOTICE 'Affiliate user not found, skipping company setup';
    RETURN;
  END IF;

  -- Check if company already exists for this user
  SELECT id INTO test_company_id
  FROM affiliate_companies
  WHERE owner_id = affiliate_user_id
  LIMIT 1;

  -- Create a test affiliate company if it doesn't exist
  IF test_company_id IS NULL THEN
    INSERT INTO affiliate_companies (
      id,
      name,
      email,
      phone,
      address,
      city,
      state,
      zip,
      country,
      owner_id,
      status,
      application_status,
      created_at,
      updated_at
    ) VALUES (
      gen_random_uuid(),
      'Test Affiliate Company',
      '<EMAIL>',
      '************',
      '123 Test Street',
      'Austin',
      'TX',
      '78701',
      'USA',
      affiliate_user_id,
      'active',
      'draft',
      NOW(),
      NOW()
    )
    RETURNING id INTO test_company_id;
  END IF;

  -- Create user-company association
  INSERT INTO affiliate_user_companies (
    user_id,
    affiliate_id,
    role,
    status,
    created_at,
    updated_at
  ) VALUES (
    affiliate_user_id,
    test_company_id,
    'OWNER',
    'ACTIVE',
    NOW(),
    NOW()
  )
  ON CONFLICT (user_id, affiliate_id) DO UPDATE SET
    role = EXCLUDED.role,
    status = EXCLUDED.status,
    updated_at = NOW();

  RAISE NOTICE 'Successfully set up test affiliate company for user %', affiliate_user_id;

EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error setting up test affiliate company: %', SQLERRM;
END $$;
