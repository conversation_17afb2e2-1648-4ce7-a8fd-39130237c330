-- Migration to create Network Tenant user accounts and organizations
-- This creates login accounts for each network tenant so they can access their tenant directly

-- TEMPORARILY DISABLED DUE TO CONSTRAINT ISSUES
-- This migration will be re-enabled after fixing role constraints
-- BEGIN;

-- Create Network Tenant Users in auth.users
-- These users can log in directly to their respective network tenants
INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at, raw_app_meta_data, raw_user_meta_data, is_super_admin, role)
VALUES 
  ('********-1111-1111-1111-********1111', '<EMAIL>', crypt('network123', gen_salt('bf')), NOW(), NOW(), NOW(), '{"provider": "email", "providers": ["email"], "role": "SUPER_ADMIN"}', '{"full_name": "TransFlow Default Admin", "role": "SUPER_ADMIN"}', false, 'authenticated'),
  ('2222**************-2222-************', '<EMAIL>', crypt('network123', gen_salt('bf')), NOW(), NOW(), NOW(), '{"provider": "email", "providers": ["email"], "role": "SUPER_ADMIN"}', '{"full_name": "TransFlow Shared Admin", "role": "SUPER_ADMIN"}', false, 'authenticated'),
  ('3333**************-3333-************', '<EMAIL>', crypt('network123', gen_salt('bf')), NOW(), NOW(), NOW(), '{"provider": "email", "providers": ["email"], "role": "SUPER_ADMIN"}', '{"full_name": "WWLIMO TNC Admin", "role": "SUPER_ADMIN"}', false, 'authenticated')
ON CONFLICT (id) DO NOTHING;

-- Profiles will be created automatically by the handle_new_user trigger
-- But let's ensure they exist with correct data
INSERT INTO profiles (id, email, full_name, role, roles, created_at, updated_at)
VALUES
  ('********-1111-1111-1111-********1111', '<EMAIL>', 'TransFlow Default Admin', 'SUPER_ADMIN', ARRAY['SUPER_ADMIN'], NOW(), NOW()),
  ('2222**************-2222-************', '<EMAIL>', 'TransFlow Shared Admin', 'SUPER_ADMIN', ARRAY['SUPER_ADMIN'], NOW(), NOW()),
  ('3333**************-3333-************', '<EMAIL>', 'WWLIMO TNC Admin', 'SUPER_ADMIN', ARRAY['SUPER_ADMIN'], NOW(), NOW())
ON CONFLICT (id) DO UPDATE SET
  email = EXCLUDED.email,
  full_name = EXCLUDED.full_name,
  role = EXCLUDED.role,
  roles = EXCLUDED.roles,
  updated_at = NOW();

-- Create SaaS tenant organizations for network tenant admins
-- Network tenant admins need organizations in the SaaS tenant system to manage their operations
-- We'll use the default SaaS tenant for now, but in production each network would have their own SaaS tenant
DO $$
DECLARE
  default_saas_tenant_id UUID;
BEGIN
  -- Get the default SaaS tenant ID
  SELECT id INTO default_saas_tenant_id FROM saas_tenants.tenants WHERE slug = 'default-tenant';

  -- Create organizations for each network tenant admin under the default SaaS tenant
  INSERT INTO organizations (id, name, slug, tenant_id, created_at, updated_at)
  VALUES
    ('aaaaaaaa-1111-1111-1111-********1111', 'TransFlow Default Network', 'transflow-default-network', default_saas_tenant_id, NOW(), NOW()),
    ('bbbbbbbb-**************-************', 'TransFlow Shared Network', 'transflow-shared-network', default_saas_tenant_id, NOW(), NOW()),
    ('cccccccc-**************-************', 'WWLIMO TNC Network', 'wwlimo-tnc-network', default_saas_tenant_id, NOW(), NOW())
  ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    slug = EXCLUDED.slug,
    tenant_id = EXCLUDED.tenant_id,
    updated_at = NOW();

  -- Associate users with their respective organizations
  INSERT INTO user_organizations (user_id, organization_id, role, created_at, updated_at)
  VALUES
    ('********-1111-1111-1111-********1111', 'aaaaaaaa-1111-1111-1111-********1111', 'ADMIN', NOW(), NOW()),
    ('2222**************-2222-************', 'bbbbbbbb-**************-************', 'ADMIN', NOW(), NOW()),
    ('3333**************-3333-************', 'cccccccc-**************-************', 'ADMIN', NOW(), NOW())
  ON CONFLICT (user_id, organization_id) DO UPDATE SET
    role = EXCLUDED.role,
    updated_at = NOW();

  -- Also associate these users with the SaaS tenant system
  INSERT INTO saas_tenants.tenant_users (tenant_id, user_id, role, created_at)
  VALUES
    (default_saas_tenant_id, '********-1111-1111-1111-********1111', 'ADMIN', NOW()),
    (default_saas_tenant_id, '2222**************-2222-************', 'ADMIN', NOW()),
    (default_saas_tenant_id, '3333**************-3333-************', 'ADMIN', NOW())
  ON CONFLICT (tenant_id, user_id) DO UPDATE SET
    role = EXCLUDED.role;
END;
$$;

-- Add comments for documentation
COMMENT ON TABLE auth.users IS 'Extended to include network tenant admin accounts for direct login access';
COMMENT ON TABLE organizations IS 'Extended to include network tenant organizations for user association';

-- COMMIT;
