-- Migration: Fix schema issues for real-time quote system
-- This migration addresses the database schema mismatches causing errors

BEGIN;

-- 1. Fix events table - add created_by column as alias for customer_id
-- The API expects 'created_by' but the schema has 'customer_id'
ALTER TABLE public.events 
ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES auth.users(id);

-- Update existing events to set created_by = customer_id
UPDATE public.events 
SET created_by = customer_id 
WHERE created_by IS NULL AND customer_id IS NOT NULL;

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_events_created_by ON public.events(created_by);

-- 2. Fix rate_cards table - add is_active column based on status
-- The affiliate matching function expects 'is_active' but the schema uses 'status'
ALTER TABLE public.rate_cards 
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT false;

-- Update existing rate cards to set is_active based on status
UPDATE public.rate_cards 
SET is_active = CASE 
    WHEN status = 'approved' THEN true 
    ELSE false 
END
WHERE is_active IS NULL;

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_rate_cards_is_active ON public.rate_cards(is_active);
CREATE INDEX IF NOT EXISTS idx_rate_cards_company_vehicle_active ON public.rate_cards(company_id, vehicle_type, is_active);

-- 3. Fix affiliate_service_areas table - ensure is_active column exists
-- The matching function expects this column
ALTER TABLE public.affiliate_service_areas 
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- Update existing service areas
UPDATE public.affiliate_service_areas 
SET is_active = true 
WHERE is_active IS NULL;

-- 4. Create trigger to keep is_active in sync with status for rate_cards
CREATE OR REPLACE FUNCTION public.sync_rate_cards_is_active()
RETURNS TRIGGER AS $$
BEGIN
    -- Update is_active based on status changes
    NEW.is_active = CASE 
        WHEN NEW.status = 'approved' THEN true 
        ELSE false 
    END;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop trigger if it exists and recreate
DROP TRIGGER IF EXISTS trigger_sync_rate_cards_is_active ON public.rate_cards;
CREATE TRIGGER trigger_sync_rate_cards_is_active
    BEFORE INSERT OR UPDATE ON public.rate_cards
    FOR EACH ROW
    EXECUTE FUNCTION public.sync_rate_cards_is_active();

-- 5. Create trigger to keep created_by in sync with customer_id for events
CREATE OR REPLACE FUNCTION public.sync_events_created_by()
RETURNS TRIGGER AS $$
BEGIN
    -- Update created_by when customer_id changes
    IF NEW.customer_id IS NOT NULL THEN
        NEW.created_by = NEW.customer_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop trigger if it exists and recreate
DROP TRIGGER IF EXISTS trigger_sync_events_created_by ON public.events;
CREATE TRIGGER trigger_sync_events_created_by
    BEFORE INSERT OR UPDATE ON public.events
    FOR EACH ROW
    EXECUTE FUNCTION public.sync_events_created_by();

-- 6. Add missing columns to support push notifications
CREATE TABLE IF NOT EXISTS public.push_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    endpoint TEXT NOT NULL,
    p256dh_key TEXT NOT NULL,
    auth_key TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(user_id, endpoint)
);

-- Enable RLS on push_subscriptions
ALTER TABLE public.push_subscriptions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for push_subscriptions
CREATE POLICY "Users can manage their own push subscriptions"
    ON public.push_subscriptions
    FOR ALL
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

-- Create indexes for push_subscriptions
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_user_id ON public.push_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_endpoint ON public.push_subscriptions(endpoint);

-- 7. Create indexes for vehicles (table already has correct structure)
CREATE INDEX IF NOT EXISTS idx_vehicles_company_type_status ON public.vehicles(company_id, type, status);
CREATE INDEX IF NOT EXISTS idx_vehicles_type_status ON public.vehicles(type, status);

-- 8. Fix affiliate_companies table to ensure status column exists
ALTER TABLE public.affiliate_companies 
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'pending';

-- Update status for existing companies
UPDATE public.affiliate_companies 
SET status = 'active' 
WHERE status IS NULL;

-- Create index for affiliate_companies status
CREATE INDEX IF NOT EXISTS idx_affiliate_companies_status ON public.affiliate_companies(status);

-- 9. Create updated_at trigger for push_subscriptions
CREATE TRIGGER update_push_subscriptions_updated_at
    BEFORE UPDATE ON public.push_subscriptions
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- 10. Add comments for documentation
COMMENT ON COLUMN public.events.created_by IS 'User who created the event (synced with customer_id)';
COMMENT ON COLUMN public.rate_cards.is_active IS 'Whether the rate card is active (synced with status = approved)';
COMMENT ON TABLE public.push_subscriptions IS 'Stores push notification subscriptions for users';

-- 11. Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.push_subscriptions TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

COMMIT;

-- Log completion
SELECT 'Real-time schema fixes applied successfully' as result;
