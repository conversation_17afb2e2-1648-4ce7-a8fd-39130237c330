-- Fix immediate issues for multi-tenancy
-- This migration addresses the current errors without full multi-tenant implementation

-- 1. Create tenants table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.tenants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  tenant_type TEXT DEFAULT 'shared' CHECK (tenant_type IN ('shared', 'dedicated')),
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Add tenant_id column to affiliate_companies if it doesn't exist
ALTER TABLE public.affiliate_companies 
ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES public.tenants(id);

-- 3. Create default tenant
INSERT INTO public.tenants (id, name, slug, tenant_type, status, created_at, updated_at)
VALUES (
  gen_random_uuid(),
  'TransFlow Default',
  'transflow-default',
  'shared',
  'active',
  NOW(),
  NOW()
)
ON CONFLICT (slug) DO NOTHING;

-- 4. Update existing affiliate companies to use the default tenant
UPDATE public.affiliate_companies 
SET tenant_id = (SELECT id FROM public.tenants WHERE slug = 'transflow-default')
WHERE tenant_id IS NULL;

-- 5. Enable RLS on tenants table
ALTER TABLE public.tenants ENABLE ROW LEVEL SECURITY;

-- 6. Create RLS policies for tenants
CREATE POLICY "Super admins can manage all tenants" ON public.tenants
FOR ALL TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles p
    WHERE p.id = auth.uid()
    AND ('SUPER_ADMIN' = ANY(p.roles) OR 'ADMIN' = ANY(p.roles))
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.profiles p
    WHERE p.id = auth.uid()
    AND ('SUPER_ADMIN' = ANY(p.roles) OR 'ADMIN' = ANY(p.roles))
  )
);

-- 7. Grant permissions
GRANT ALL ON public.tenants TO service_role;
GRANT SELECT ON public.tenants TO authenticated;

-- 8. Add comments
COMMENT ON TABLE public.tenants IS 'Multi-tenant organization management';
COMMENT ON COLUMN public.tenants.tenant_type IS 'Type of tenant: shared (multi-tenant) or dedicated (single-tenant)';
COMMENT ON COLUMN public.tenants.status IS 'Tenant status: active, inactive, or suspended';
