-- Fix passengers RLS policies to avoid infinite recursion
-- This migration fixes the circular dependency issue with tenant_users table

BEGIN;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view passengers in their tenant" ON public.passengers;
DROP POLICY IF EXISTS "Users can create passengers in their tenant" ON public.passengers;
DROP POLICY IF EXISTS "Users can update passengers they created" ON public.passengers;
DROP POLICY IF EXISTS "Users can delete passengers they created" ON public.passengers;

-- Create simplified RLS policies that avoid circular dependencies
CREATE POLICY "Users can view passengers they created"
    ON public.passengers
    FOR SELECT
    USING (
        created_by = auth.uid()
        OR EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
        )
    );

CREATE POLICY "Users can create passengers"
    ON public.passengers
    FOR INSERT
    WITH CHECK (
        created_by = auth.uid()
    );

CREATE POLICY "Users can update passengers they created"
    ON public.passengers
    FOR UPDATE
    USING (
        created_by = auth.uid()
        OR EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
        )
    );

CREATE POLICY "Users can delete passengers they created"
    ON public.passengers
    FOR DELETE
    USING (
        created_by = auth.uid()
        OR EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'SUPER_ADMIN'
        )
    );

COMMIT;
