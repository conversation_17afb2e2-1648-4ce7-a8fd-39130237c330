-- Enable RLS on tenant_branding table
ALTER TABLE public.tenant_branding ENABLE ROW LEVEL SECURITY;

-- Policy for SELECT: Allow all authenticated users to select branding details
CREATE POLICY "Enable read access for authenticated users"
ON public.tenant_branding FOR SELECT
TO authenticated
USING (true);

-- Policy for INSERT: Allow SUPER_ADMIN to insert new branding details
CREATE POLICY "Allow SUPER_ADMIN to insert branding details"
ON public.tenant_branding FOR INSERT
TO authenticated
WITH CHECK (
    (SELECT public.get_my_claim('role'::text))::text = 'SUPER_ADMIN'
);

-- Policy for UPDATE: Allow SUPER_ADMIN to update branding details
CREATE POLICY "Allow SUPER_ADMIN to update branding details"
ON public.tenant_branding FOR UPDATE
TO authenticated
USING (
    (SELECT public.get_my_claim('role'::text))::text = 'SUPER_ADMIN'
)
WITH CHECK (
    (SELECT public.get_my_claim('role'::text))::text = 'SUPER_ADMIN'
);
