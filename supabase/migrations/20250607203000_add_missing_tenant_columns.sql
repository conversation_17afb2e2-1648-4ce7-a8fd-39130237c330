-- Add missing columns to saas_tenants.tenants table

-- First, create the enum types if they don't exist
DO $$
BEGIN
    -- Create tenant_type_enum if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tenant_type_enum') THEN
        CREATE TYPE public.tenant_type_enum AS ENUM ('shared', 'segregated', 'white_label');
    END IF;
    
    -- Create tenant_status_enum if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tenant_status_enum') THEN
        CREATE TYPE public.tenant_status_enum AS ENUM ('active', 'inactive', 'suspended');
    END IF;
END
$$;

-- Add the missing columns with appropriate constraints
ALTER TABLE saas_tenants.tenants
    ADD COLUMN IF NOT EXISTS slug TEXT UNIQUE,
    ADD COLUMN IF NOT EXISTS tenant_type public.tenant_type_enum NOT NULL DEFAULT 'shared'::public.tenant_type_enum,
    ADD COLUMN IF NOT EXISTS status public.tenant_status_enum NOT NULL DEFAULT 'active'::public.tenant_status_enum,
    ADD COLUMN IF NOT EXISTS parent_tenant_id UUID REFERENCES saas_tenants.tenants(id) ON DELETE SET NULL,
    ADD COLUMN IF NOT EXISTS branding JSONB;

-- Update existing rows with default values for required columns
UPDATE saas_tenants.tenants
SET 
    slug = 'default-tenant-' || substr(id::text, 1, 8),
    tenant_type = 'shared',
    status = 'active';

-- Add comments for the new columns
COMMENT ON COLUMN saas_tenants.tenants.slug IS 'A URL-friendly identifier for the tenant';
COMMENT ON COLUMN saas_tenants.tenants.tenant_type IS 'The type of tenant (shared, segregated, white_label)';
COMMENT ON COLUMN saas_tenants.tenants.status IS 'The current status of the tenant (active, inactive, suspended)';
COMMENT ON COLUMN saas_tenants.tenants.parent_tenant_id IS 'For hierarchical tenant structures, references the parent tenant';
COMMENT ON COLUMN saas_tenants.tenants.branding IS 'JSONB field for storing tenant-specific branding information';