-- Create affiliate tier system and performance tracking
-- Migration: 20250101000000_create_affiliate_tier_system.sql

-- Create affiliate performance metrics table
CREATE TABLE IF NOT EXISTS affiliate_performance_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID NOT NULL REFERENCES affiliate_companies(id) ON DELETE CASCADE,

  -- Core Performance Metrics
  compliance_score DECIMAL(5,2) DEFAULT 0 CHECK (compliance_score >= 0 AND compliance_score <= 100),
  customer_rating DECIMAL(3,2) DEFAULT 0 CHECK (customer_rating >= 0 AND customer_rating <= 5),
  quote_response_time_minutes DECIMAL(8,2) DEFAULT 0, -- Average response time in minutes
  driver_arrival_on_time_percentage DECIMAL(5,2) DEFAULT 0 CHECK (driver_arrival_on_time_percentage >= 0 AND driver_arrival_on_time_percentage <= 100),

  -- Trip Statistics
  total_trips INTEGER DEFAULT 0,
  completed_trips INTEGER DEFAULT 0,
  cancelled_trips INTEGER DEFAULT 0,

  -- Calculated Tier
  current_tier VARCHAR(20) DEFAULT 'Standard' CHECK (current_tier IN ('Standard', 'Premium', 'Elite')),
  tier_score DECIMAL(5,2) DEFAULT 0 CHECK (tier_score >= 0 AND tier_score <= 100),

  -- Timestamps
  last_calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create VIP services configuration table
CREATE TABLE IF NOT EXISTS affiliate_vip_services (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID NOT NULL REFERENCES affiliate_companies(id) ON DELETE CASCADE,

  -- Service Details
  service_type VARCHAR(50) NOT NULL, -- 'airport_greet', 'fast_track', 'champagne', 'flowers', etc.
  service_name VARCHAR(100) NOT NULL,
  description TEXT,

  -- Pricing
  base_price DECIMAL(10,2) NOT NULL DEFAULT 0,

  -- Availability
  is_active BOOLEAN DEFAULT true,
  available_vehicle_types TEXT[] DEFAULT '{}', -- Array of vehicle type IDs

  -- Service-specific configuration
  service_config JSONB DEFAULT '{}', -- Flexible config for different service types

  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create quote response tracking table
CREATE TABLE IF NOT EXISTS quote_response_tracking (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  quote_id UUID NOT NULL REFERENCES quotes(id) ON DELETE CASCADE,
  company_id UUID NOT NULL REFERENCES affiliate_companies(id) ON DELETE CASCADE,

  -- Response Timing
  sent_at TIMESTAMP WITH TIME ZONE NOT NULL,
  responded_at TIMESTAMP WITH TIME ZONE,
  response_time_minutes DECIMAL(8,2),

  -- Response Details
  response_type VARCHAR(20) CHECK (response_type IN ('accepted', 'rejected', 'counter_offer', 'expired')),

  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create driver arrival tracking table
CREATE TABLE IF NOT EXISTS driver_arrival_tracking (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  trip_id UUID, -- Will reference trips table when implemented
  driver_id UUID REFERENCES drivers(id) ON DELETE CASCADE,
  company_id UUID NOT NULL REFERENCES affiliate_companies(id) ON DELETE CASCADE,

  -- Arrival Timing
  scheduled_arrival_time TIMESTAMP WITH TIME ZONE NOT NULL,
  actual_arrival_time TIMESTAMP WITH TIME ZONE,
  minutes_difference DECIMAL(8,2), -- Positive = late, Negative = early
  is_on_time BOOLEAN DEFAULT false,

  -- Location Verification
  arrival_location_lat DECIMAL(10,8),
  arrival_location_lng DECIMAL(11,8),

  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create compliance document tracking table
CREATE TABLE IF NOT EXISTS compliance_document_tracking (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID NOT NULL REFERENCES affiliate_companies(id) ON DELETE CASCADE,

  -- Document Categories
  business_documents_score DECIMAL(5,2) DEFAULT 0 CHECK (business_documents_score >= 0 AND business_documents_score <= 100),
  vehicle_documents_score DECIMAL(5,2) DEFAULT 0 CHECK (vehicle_documents_score >= 0 AND vehicle_documents_score <= 100),
  driver_documents_score DECIMAL(5,2) DEFAULT 0 CHECK (driver_documents_score >= 0 AND driver_documents_score <= 100),

  -- Overall Compliance
  overall_compliance_score DECIMAL(5,2) DEFAULT 0 CHECK (overall_compliance_score >= 0 AND overall_compliance_score <= 100),

  -- Document Counts
  total_required_documents INTEGER DEFAULT 0,
  completed_documents INTEGER DEFAULT 0,
  expired_documents INTEGER DEFAULT 0,
  expiring_soon_documents INTEGER DEFAULT 0, -- Expiring within 30 days

  -- Last Updated
  last_calculated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_affiliate_performance_metrics_company_id ON affiliate_performance_metrics(company_id);
CREATE INDEX IF NOT EXISTS idx_affiliate_performance_metrics_tier ON affiliate_performance_metrics(current_tier);
CREATE INDEX IF NOT EXISTS idx_affiliate_vip_services_company_id ON affiliate_vip_services(company_id);
CREATE INDEX IF NOT EXISTS idx_affiliate_vip_services_type ON affiliate_vip_services(service_type);
CREATE INDEX IF NOT EXISTS idx_quote_response_tracking_company_id ON quote_response_tracking(company_id);
CREATE INDEX IF NOT EXISTS idx_quote_response_tracking_quote_id ON quote_response_tracking(quote_id);
CREATE INDEX IF NOT EXISTS idx_driver_arrival_tracking_company_id ON driver_arrival_tracking(company_id);
CREATE INDEX IF NOT EXISTS idx_driver_arrival_tracking_driver_id ON driver_arrival_tracking(driver_id);
CREATE INDEX IF NOT EXISTS idx_compliance_document_tracking_company_id ON compliance_document_tracking(company_id);

-- Create RLS policies
ALTER TABLE affiliate_performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE affiliate_vip_services ENABLE ROW LEVEL SECURITY;
ALTER TABLE quote_response_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE driver_arrival_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE compliance_document_tracking ENABLE ROW LEVEL SECURITY;

-- RLS Policies for affiliate_performance_metrics
CREATE POLICY "Affiliates can view their own performance metrics" ON affiliate_performance_metrics
  FOR SELECT USING (
    company_id IN (
      SELECT affiliate_id FROM affiliate_user_companies
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "System can manage performance metrics" ON affiliate_performance_metrics
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND role IN ('SUPER_ADMIN', 'SYSTEM')
    )
  );

-- RLS Policies for affiliate_vip_services
CREATE POLICY "Affiliates can manage their own VIP services" ON affiliate_vip_services
  FOR ALL USING (
    company_id IN (
      SELECT affiliate_id FROM affiliate_user_companies
      WHERE user_id = auth.uid()
      AND role IN ('OWNER', 'DISPATCHER')
    )
  );

CREATE POLICY "Super admins can view all VIP services" ON affiliate_vip_services
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND role = 'SUPER_ADMIN'
    )
  );

-- RLS Policies for quote_response_tracking
CREATE POLICY "Affiliates can view their own response tracking" ON quote_response_tracking
  FOR SELECT USING (
    company_id IN (
      SELECT affiliate_id FROM affiliate_user_companies
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "System can manage response tracking" ON quote_response_tracking
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND role IN ('SUPER_ADMIN', 'SYSTEM')
    )
  );

-- RLS Policies for driver_arrival_tracking
CREATE POLICY "Affiliates can view their own arrival tracking" ON driver_arrival_tracking
  FOR SELECT USING (
    company_id IN (
      SELECT affiliate_id FROM affiliate_user_companies
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "System can manage arrival tracking" ON driver_arrival_tracking
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND role IN ('SUPER_ADMIN', 'SYSTEM')
    )
  );

-- RLS Policies for compliance_document_tracking
CREATE POLICY "Affiliates can view their own compliance tracking" ON compliance_document_tracking
  FOR SELECT USING (
    company_id IN (
      SELECT affiliate_id FROM affiliate_user_companies
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "System can manage compliance tracking" ON compliance_document_tracking
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND role IN ('SUPER_ADMIN', 'SYSTEM')
    )
  );

-- Create function to calculate affiliate tier
CREATE OR REPLACE FUNCTION calculate_affiliate_tier(
  p_compliance_score DECIMAL,
  p_customer_rating DECIMAL,
  p_response_time_minutes DECIMAL,
  p_arrival_on_time_percentage DECIMAL,
  p_total_trips INTEGER
) RETURNS TABLE (
  tier_name VARCHAR(20),
  tier_score DECIMAL(5,2)
) AS $$
DECLARE
  v_tier_score DECIMAL(5,2) := 0;
  v_tier_name VARCHAR(20) := 'Standard';
  v_response_score DECIMAL(5,2);
  v_rating_score DECIMAL(5,2);
BEGIN
  -- Must have at least 3 trips to get a tier
  IF p_total_trips < 3 THEN
    RETURN QUERY SELECT 'Standard'::VARCHAR(20), 0::DECIMAL(5,2);
    RETURN;
  END IF;

  -- Convert response time to score (faster = better, max 10 minutes for perfect score)
  v_response_score := GREATEST(0, 100 - (p_response_time_minutes * 10));
  v_response_score := LEAST(100, v_response_score);

  -- Convert customer rating to 0-100 scale
  v_rating_score := (p_customer_rating / 5.0) * 100;

  -- Calculate weighted tier score (25% each metric)
  v_tier_score := (
    (p_compliance_score * 0.25) +
    (v_rating_score * 0.25) +
    (v_response_score * 0.25) +
    (p_arrival_on_time_percentage * 0.25)
  );

  -- Determine tier based on score
  IF v_tier_score >= 90 THEN
    v_tier_name := 'Elite';
  ELSIF v_tier_score >= 75 THEN
    v_tier_name := 'Premium';
  ELSE
    v_tier_name := 'Standard';
  END IF;

  RETURN QUERY SELECT v_tier_name, v_tier_score;
END;
$$ LANGUAGE plpgsql;

-- Create function to update affiliate performance metrics
CREATE OR REPLACE FUNCTION update_affiliate_performance_metrics(p_company_id UUID)
RETURNS VOID AS $$
DECLARE
  v_compliance_score DECIMAL(5,2) := 0;
  v_customer_rating DECIMAL(3,2) := 0;
  v_response_time DECIMAL(8,2) := 0;
  v_arrival_percentage DECIMAL(5,2) := 0;
  v_total_trips INTEGER := 0;
  v_completed_trips INTEGER := 0;
  v_cancelled_trips INTEGER := 0;
  v_tier_result RECORD;
BEGIN
  -- Get compliance score
  SELECT COALESCE(overall_compliance_score, 0) INTO v_compliance_score
  FROM compliance_document_tracking
  WHERE company_id = p_company_id
  ORDER BY last_calculated_at DESC
  LIMIT 1;

  -- Get average response time
  SELECT COALESCE(AVG(response_time_minutes), 0) INTO v_response_time
  FROM quote_response_tracking
  WHERE company_id = p_company_id
    AND responded_at IS NOT NULL
    AND created_at >= NOW() - INTERVAL '30 days';

  -- Get arrival on-time percentage
  SELECT COALESCE(AVG(CASE WHEN is_on_time THEN 100 ELSE 0 END), 0) INTO v_arrival_percentage
  FROM driver_arrival_tracking
  WHERE company_id = p_company_id
    AND actual_arrival_time IS NOT NULL
    AND created_at >= NOW() - INTERVAL '30 days';

  -- TODO: Get customer rating from trips/reviews when implemented
  -- For now, use a default value
  v_customer_rating := 4.0;

  -- TODO: Get trip counts from trips table when implemented
  -- For now, use quote response count as proxy
  SELECT COUNT(*) INTO v_total_trips
  FROM quote_response_tracking
  WHERE company_id = p_company_id
    AND response_type = 'accepted';

  v_completed_trips := v_total_trips; -- Assume all accepted are completed for now
  v_cancelled_trips := 0;

  -- Calculate tier
  SELECT tier_name, tier_score INTO v_tier_result
  FROM calculate_affiliate_tier(
    v_compliance_score,
    v_customer_rating,
    v_response_time,
    v_arrival_percentage,
    v_total_trips
  );

  -- Insert or update performance metrics
  INSERT INTO affiliate_performance_metrics (
    company_id,
    compliance_score,
    customer_rating,
    quote_response_time_minutes,
    driver_arrival_on_time_percentage,
    total_trips,
    completed_trips,
    cancelled_trips,
    current_tier,
    tier_score,
    last_calculated_at
  ) VALUES (
    p_company_id,
    v_compliance_score,
    v_customer_rating,
    v_response_time,
    v_arrival_percentage,
    v_total_trips,
    v_completed_trips,
    v_cancelled_trips,
    v_tier_result.tier_name,
    v_tier_result.tier_score,
    NOW()
  )
  ON CONFLICT (company_id) DO UPDATE SET
    compliance_score = EXCLUDED.compliance_score,
    customer_rating = EXCLUDED.customer_rating,
    quote_response_time_minutes = EXCLUDED.quote_response_time_minutes,
    driver_arrival_on_time_percentage = EXCLUDED.driver_arrival_on_time_percentage,
    total_trips = EXCLUDED.total_trips,
    completed_trips = EXCLUDED.completed_trips,
    cancelled_trips = EXCLUDED.cancelled_trips,
    current_tier = EXCLUDED.current_tier,
    tier_score = EXCLUDED.tier_score,
    last_calculated_at = EXCLUDED.last_calculated_at,
    updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Add unique constraint for company_id in performance metrics
ALTER TABLE affiliate_performance_metrics
ADD CONSTRAINT affiliate_performance_metrics_company_id_unique
UNIQUE (company_id);
