# Multi-Tenant Architecture Specification

## Overview

TransFlow operates as a three-tier multi-tenant platform supporting different levels of isolation and branding to serve various business models from shared SaaS to white-label solutions.

## Architecture Levels

### Level 1: Shared (Current State)

**Description**: Single tenant with organizational switching for management access.

**Use Case**: Traditional SaaS model where all customers share the same platform and branding.

**Customer Experience**:

- Access via `app.transflow.com`
- TransFlow branding throughout
- Shared affiliate network
- Transparent pricing from affiliates
- Cross-customer analytics visible to super admin

**User Roles**:

- **Clients (Event Managers)**: Manage events and quotes for their organization
- **Super Admin**: ORG switching to manage different client organizations
- **Affiliates**: Single portal, serve all TransFlow clients

**Example**: ABC Corporation uses TransFlow for corporate travel management.

---

### Level 2: Segregated (Multi-Brand Networks)

**Description**: Multiple isolated brands under TransFlow umbrella with separate customer bases and configurable affiliate networks.

**Use Case**: TransFlow operates multiple transportation brands (LIMO123, DallasLimo, etc.) with distinct market positioning.

**Customer Experience**:

- **LIMO123 Customers**: Access `app.limo123.com`

  - LIMO123 branding throughout
  - Manual commission pricing (hide real affiliate rates)
  - May not know about TransFlow network initially
  - Gradual upsell to TransFlow network benefits

- **DallasLimo Customers**: Access `app.dallaslimo.com`
  - Completely separate from LIMO123 customers
  - DallasLimo branding and pricing models
  - Independent customer database

**User Roles**:

- **Clients**: Brand-specific access, isolated customer bases
- **Super Admin**: Tenant switching between brands, cross-brand analytics
- **Affiliates**: Choose network participation (exclusive vs. global)

**Affiliate Network Participation**:

```typescript
interface AffiliateNetworkPreferences {
  participateInGlobalNetwork: boolean; // Join TransFlow network
  exclusiveToBrands: string[]; // ["LIMO123"] for exclusive
  rateVisibility: "transparent" | "brand-specific";
  preferredQuoteVolume: "maximum" | "selective";
}
```

**Customer Migration Strategy**:

1. **Initial Experience**: LIMO123 branded, exclusive affiliate network
2. **Value Discovery**: When customer needs exceed LIMO123 coverage
3. **Network Reveal**: "Access our partner network for this location?"
4. **Upsell Opportunity**: "Upgrade to TransFlow Premium for full network access"

**Example**: Customer books with LIMO123 in Austin, later needs service in Houston. System offers "LIMO123 partner network" access, gradually introducing TransFlow benefits.

---

### Level 3: White-Label (External Clients)

**Description**: Complete branding customization for external clients with full data isolation.

**Use Case**: Hotels, corporations, or other businesses want transportation booking under their own brand.

**Customer Experience**:

- **Marriott Downtown**: Access `transportation.marriott-downtown.com`
  - Full Marriott branding (logo, colors, domain)
  - Only Marriott guests/events visible
  - Marriott-specific pricing and workflows
  - No TransFlow branding visible
  - Custom email templates and communications

**User Roles**:

- **Clients**: Fully branded experience, complete data isolation
- **Super Admin**: White-label management, custom configurations
- **Affiliates**: Context-aware service (know they're serving Marriott guests)

**Example**: Marriott concierge books transportation for hotel guests through fully branded portal.

---

## Database Schema Changes

### Core Tables Modifications

```sql
-- Add tenant isolation (Note: customers and bookings tables don't exist in current schema)
-- ALTER TABLE customers ADD COLUMN tenant_id UUID REFERENCES tenants(id);
ALTER TABLE quotes ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
ALTER TABLE events ADD COLUMN IF NOT EXISTS tenant_id UUID REFERENCES tenants(id);
-- ALTER TABLE bookings ADD COLUMN tenant_id UUID REFERENCES tenants(id);

-- Tenant configuration (implemented in public schema)
CREATE TABLE IF NOT EXISTS public.tenants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  domain VARCHAR(255),
  parent_tenant_id UUID REFERENCES tenants(id),
  tenant_type TEXT CHECK (tenant_type IN ('shared', 'segregated', 'white_label')) NOT NULL DEFAULT 'shared',
  status TEXT CHECK (status IN ('active', 'inactive', 'suspended')) DEFAULT 'active',
  branding JSONB DEFAULT '{}',
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tenant branding configuration (implemented)
CREATE TABLE IF NOT EXISTS public.tenant_branding (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  logo_url VARCHAR(500),
  favicon_url VARCHAR(500),
  primary_color VARCHAR(7),
  secondary_color VARCHAR(7),
  background_color VARCHAR(7),
  custom_css TEXT,
  email_templates JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Affiliate network participation (implemented on affiliate_companies table)
ALTER TABLE affiliate_companies ADD COLUMN IF NOT EXISTS network_participation JSONB DEFAULT '{
  "global_network": false,
  "exclusive_brands": [],
  "rate_sharing": "transparent",
  "preferred_volume": "maximum"
}';

-- Tenant-specific affiliate relationships (implemented)
CREATE TABLE IF NOT EXISTS public.tenant_affiliates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  affiliate_id UUID REFERENCES affiliate_companies(id) ON DELETE CASCADE,
  relationship_type TEXT CHECK (relationship_type IN ('exclusive', 'shared', 'preferred')) NOT NULL DEFAULT 'shared',
  commission_override DECIMAL(5,2),
  priority_level INTEGER DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(tenant_id, affiliate_id)
);

-- Customer migration tracking (planned - customers table doesn't exist yet)
-- CREATE TABLE customer_migrations (
--   id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
--   customer_id UUID REFERENCES customers(id),
--   from_tenant_id UUID REFERENCES tenants(id),
--   to_tenant_id UUID REFERENCES tenants(id),
--   migration_type TEXT CHECK (migration_type IN ('upsell', 'transfer', 'merge')) NOT NULL,
--   migration_status TEXT CHECK (migration_status IN ('pending', 'completed', 'failed')) DEFAULT 'pending',
--   migration_data JSONB DEFAULT '{}',
--   created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
--   completed_at TIMESTAMP WITH TIME ZONE
-- );
```

### Row Level Security (RLS) Policies

```sql
-- Enable RLS on tenant-aware tables (implemented)
-- ALTER TABLE customers ENABLE ROW LEVEL SECURITY; -- customers table doesn't exist
ALTER TABLE quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE events ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenant_branding ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenant_affiliates ENABLE ROW LEVEL SECURITY;

-- Tenant isolation policy (implemented)
CREATE POLICY "tenant_isolation_quotes" ON quotes
  FOR ALL TO authenticated
  USING (
    tenant_id = COALESCE(
      (current_setting('app.current_tenant_id', true))::UUID,
      tenant_id
    )
  );

-- Super admin bypass policy (implemented via profiles table)
CREATE POLICY "super_admin_access_quotes" ON quotes
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid()
      AND 'SUPER_ADMIN' = ANY(roles)
    )
  );
```

## Current Implementation Status

### ✅ **Completed Features**

- **Tenant Infrastructure**: `public.tenants` table with three-tier support
- **Tenant Branding**: `public.tenant_branding` table for customization
- **Affiliate Network Management**: `affiliate_companies.network_participation` JSONB field
- **Tenant-Affiliate Relationships**: `public.tenant_affiliates` table
- **Row Level Security**: RLS policies for tenant isolation on quotes and events
- **Default Shared Tenant**: Created and assigned to existing records

### 🚧 **Partially Implemented**

- **Customer Migration System**: Schema designed but `customers` table doesn't exist yet
- **White-Label Engine**: Basic branding structure exists, UI implementation pending
- **Cross-Tenant Analytics**: Database structure ready, reporting UI pending

### 📋 **Planned Features**

- **Customer Management**: `customers` table and related migration tracking
- **Advanced Branding**: Dynamic CSS injection and subdomain routing
- **Subscription Management**: Feature gating based on tenant subscription tiers

### 🔧 **Current Database Schema Notes**

- Uses `TEXT` with `CHECK` constraints instead of `ENUM` types for PostgreSQL compatibility
- `affiliate_companies` table used instead of generic `affiliates` table
- Tenant isolation implemented via `tenant_id` columns with RLS policies
- Super admin access controlled via `profiles.roles` array containing `'SUPER_ADMIN'`

## Implementation Phases

### Phase 1: Tenant Infrastructure (Weeks 1-2)

- Create tenant tables and relationships
- Implement tenant middleware and context
- Add tenant_id to existing tables
- Create tenant switching UI for super admin

### Phase 2: Affiliate Network Management (Weeks 3-4)

- Implement affiliate network participation choices
- Create tenant-affiliate relationship management
- Build affiliate onboarding flow with network selection
- Implement quote distribution logic based on network preferences

### Phase 3: Customer Migration System (Weeks 5-6)

- Build customer migration tracking
- Implement upsell detection (coverage gaps, volume thresholds)
- Create migration workflows and UI
- Add analytics for migration success rates

### Phase 4: White-Label Engine (Weeks 7-8)

- Implement dynamic branding system
- Create subdomain routing
- Build custom CSS injection
- Implement branded email templates

### Phase 5: Advanced Features (Weeks 9-10)

- Cross-tenant analytics and reporting
- Advanced affiliate network optimization
- Customer lifecycle management
- Performance monitoring and optimization

## Business Logic Examples

### Quote Distribution Algorithm

```typescript
async function getEligibleAffiliates(quoteRequest: QuoteRequest) {
  const { tenant_id, location, service_type, customer_id } = quoteRequest;
  const tenant = await getTenant(tenant_id);

  // Get tenant-specific affiliates
  const exclusiveAffiliates = await getAffiliatesByTenant(
    tenant_id,
    "exclusive"
  );

  // Check if customer is eligible for network access
  const customerTier = await getCustomerTier(customer_id);
  const hasNetworkAccess =
    customerTier.includes("network") || tenant.settings.allowGlobalNetwork;

  // Get global network affiliates if eligible
  const globalAffiliates = hasNetworkAccess
    ? await getGlobalNetworkAffiliates(location, service_type)
    : [];

  return {
    exclusive: exclusiveAffiliates,
    network: globalAffiliates,
    upsellOpportunity: !hasNetworkAccess && globalAffiliates.length > 0,
  };
}
```

### Customer Upsell Detection

```typescript
async function detectUpsellOpportunity(
  customer_id: string,
  quote_request: QuoteRequest
) {
  const customer = await getCustomer(customer_id);
  const tenant = await getTenant(customer.tenant_id);

  // Scenarios for upsell
  const scenarios = {
    // No coverage in current network
    noCoverage: await checkCoverage(quote_request, tenant.exclusive_affiliates),

    // Better rates available in global network
    betterRates: await comparePricing(quote_request, tenant.id),

    // Customer booking frequency threshold
    highVolume: customer.monthly_bookings > tenant.settings.upsell_threshold,

    // Geographic expansion needs
    multiCity: await detectMultiCityNeeds(customer_id),
  };

  return {
    shouldUpsell: Object.values(scenarios).some(Boolean),
    scenarios,
    recommendedPlan: calculateRecommendedPlan(scenarios),
  };
}
```

## Security Considerations

### Data Isolation

- Strict tenant_id enforcement in all queries
- RLS policies prevent cross-tenant data access
- Encrypted tenant-specific data at rest

### Access Control

- Role-based permissions per tenant
- Super admin bypass with audit logging
- API rate limiting per tenant

### Compliance

- GDPR compliance with tenant-specific data handling
- SOC 2 compliance for white-label clients
- Audit trails for all cross-tenant operations

## Monitoring and Analytics

### Tenant-Level Metrics

- Customer acquisition and retention per tenant
- Revenue attribution across tenant hierarchy
- Affiliate performance by network participation
- Migration success rates and customer satisfaction

### Cross-Tenant Insights

- Network effects measurement
- Optimal affiliate distribution
- Customer lifecycle optimization
- Pricing strategy effectiveness

This architecture provides the flexibility to serve different business models while maintaining operational efficiency and clear upgrade paths for customers across all tenant levels.
