# TransFlow SaaS Platform - Software Requirements Specification

## 1. Executive Summary

TransFlow is a comprehensive three-tier multi-tenant SaaS platform for transportation management that connects corporate clients with transportation network providers. The platform operates across three distinct levels:

- **Level 1 (Shared)**: Traditional SaaS with organizational switching
- **Level 2 (Segregated)**: Multi-brand networks with isolated customer bases and configurable affiliate participation
- **Level 3 (White-Label)**: Fully branded solutions for external clients

The platform enables seamless quote management, event coordination, customer migration pathways, and trip operations across all tenant levels with sophisticated affiliate network management.

## 2. System Architecture

### 2.1 Three-Tier Multi-Tenant Architecture

#### Level 1: Shared Tenancy

- **Super Admin Portal**: Platform-wide management with ORG switching
- **Client Portal**: Shared TransFlow branding and affiliate network
- **Transparent Pricing**: Direct affiliate rates visible to clients

#### Level 2: Segregated Multi-Brand Networks

- **Brand Isolation**: Separate customer bases per brand (LIMO123, DallasLimo)
- **Affiliate Network Choice**: Affiliates choose exclusive vs. global network participation
- **Customer Migration**: Gradual upsell from brand-specific to network access
- **Tenant Switching**: Super admin management across multiple brands

#### Level 3: White-Label Solutions

- **Complete Branding**: Custom domains, logos, colors for external clients
- **Data Isolation**: Fully segregated customer and operational data
- **Custom Workflows**: Tailored processes for specific client needs

### 2.2 Core Technologies

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS, ShadCN UI
- **Backend**: Supabase (PostgreSQL, Auth, Real-time)
- **Authentication**: Supabase Auth with role-based access control
- **Real-time**: WebSocket integration for live updates

## 3. User Roles & Permissions

### 3.1 Super Admin (SUPER_ADMIN)

- **Platform Management**: Full access to all organizations and data
- **Organization Switching**: Context filtering via ORG selector
- **Affiliate Management**: Comprehensive onboarding, approval, compliance oversight with audit trails
- **System Configuration**: Platform settings, security, analytics
- **Tenant Management**: Create and manage multi-tenant configurations
- **White-Label Configuration**: Custom branding and domain management

### 3.2 Customer (CUSTOMER)

- **Quote Management**: Create, manage, and track transportation quotes
- **Event Planning**: Coordinate transportation for events
- **Trip Monitoring**: Real-time trip tracking and management
- **Passenger Management**: Handle passenger data and coordination
- **Multi-Tenant Access**: Access appropriate tenant context based on organization

### 3.3 Event Manager (EVENT_MANAGER)

- **Event Operations**: Specialized event transportation management with live tracking
- **Quote Processing**: Handle event-specific transportation requests
- **Trip Coordination**: Manage complex multi-trip events with God's View monitoring
- **Real-Time Tracking**: Professional-grade live trips map with emergency response capabilities

### 3.4 Affiliate (AFFILIATE)

- **Quote Responses**: Receive and respond to transportation requests
- **Fleet Management**: Manage vehicles and drivers with multi-company support
- **Trip Execution**: Handle assigned transportation services
- **Compliance**: Maintain required documentation and certifications
- **Network Participation**: Choose between exclusive brand service or global network access
- **Multi-Company Management**: Manage multiple affiliate companies with role-based access

## 4. Core Features

### 4.1 Quote Management System

- **Real-time Quote Processing**: Live status updates via WebSocket
- **Multi-tier Affiliate Distribution**: Elite, Premium, Standard tiers
- **Automated Matching**: City-based affiliate selection
- **Response Tracking**: Comprehensive quote lifecycle management

### 4.2 Organization Management (ORG Feature)

- **Multi-tenant Architecture**: Isolated data per organization
- **Context Switching**: Super admin can operate on behalf of any organization
- **God's View**: Platform-wide data visibility for super admins
- **API Integration Ready**: Support for corporate client and network provider APIs

### 4.3 Affiliate Onboarding & Management

- **Centralized Onboarding**: Comprehensive application management center
- **Duty of Care Compliance**: Insurance, licensing, safety verification
- **Performance Tracking**: Metrics, ratings, and operational data
- **Approval Workflow**: Multi-stage verification process

### 4.4 Event Management

- **Complex Event Planning**: Multi-trip coordination
- **Live Trip Monitoring**: Real-time map with trip tracking
- **Resource Allocation**: Vehicle and driver assignment
- **Client Communication**: Automated updates and notifications

### 4.5 Multi-Tenant Management

- **Tenant Isolation**: Complete data separation between tenant levels
- **Dynamic Branding**: Real-time theme and branding application
- **Customer Migration**: Automated upsell detection and migration workflows
- **Affiliate Network Management**: Configurable participation models
- **Cross-Tenant Analytics**: Performance insights across all tenant levels

### 4.6 Customer Migration System

- **Upsell Detection**: Automatic identification of upgrade opportunities
- **Coverage Gap Analysis**: Detection when brand-specific network is insufficient
- **Migration Workflows**: Seamless customer transition between tenant levels
- **Retention Analytics**: Track migration success and customer satisfaction

## 5. Navigation Structure

### 5.1 Super Admin Portal Navigation

#### Tenancy Menu (Top Row)

- **Dashboard**: Platform overview and key metrics
- **ORGs**: Organization management and subscriptions
- **Users**: User management across all organizations
- **Affiliate Config**: Onboarding management center
- **Coverage**: Geographic service area management
- **Analytics**: Platform-wide analytics and reporting
- **Settings**: System configuration and preferences
- **Security**: Security policies and access control
- **Documentation**: System documentation and help

#### Operations Menu (Second Row - ORG Filtered)

- **OPS Snapshot**: Operational dashboard for selected organization
- **Events**: Event management for selected organization
- **Quotes**: Quote management for selected organization
- **Trips**: Trip monitoring for selected organization
- **Passengers**: Passenger management for selected organization
- **Affiliate Operations**: Affiliate performance for selected organization

### 5.2 ORG Selector Functionality

- **Context Filtering**: Applies to operations menu items only
- **Organization Switching**: Seamless context changes
- **API Integration**: Supports external API quote processing
- **Data Isolation**: Ensures proper multi-tenant data separation

## 6. Recent Enhancements (Phase 3)

### 6.1 Enhanced Affiliate Management

- **Comprehensive Detail Pages**: 9-tab interface (Approval, Overview, Contact, Performance, Rates, Fleet, Service Area, Documents, Audit Log)
- **Centralized Approval Process**: Streamlined duty of care verification
- **Application Progress Tracking**: Visual progress indicators and status management
- **Real-time Activity Feed**: Live updates on affiliate activities

### 6.2 Improved Navigation Structure

- **Unified Affiliate Entry Point**: Single point of access for all affiliate data
- **Simplified Application Management**: Card-based interface for pending applications
- **Enhanced Visual Hierarchy**: Better organization of information and actions

### 6.3 Authentication Improvements

- **Professional Login/Register**: Half-split screen design with corporate styling
- **Consistent UI/UX**: ShadCN UI components throughout
- **Mobile-responsive**: Optimized for all device types

## 7. Technical Implementation

### 7.1 Database Architecture

- **Row-Level Security (RLS)**: Multi-tenant data isolation
- **Service Role Access**: Super admin bypass capabilities
- **Real-time Subscriptions**: Live data updates
- **Audit Logging**: Comprehensive activity tracking

### 7.2 API Design

- **RESTful Endpoints**: Standard HTTP methods and status codes
- **Real-time Updates**: WebSocket integration for live features
- **External Integration**: Support for corporate client and network provider APIs
- **Authentication**: JWT-based with role verification

### 7.3 Security Features

- **Role-based Access Control**: Granular permission system
- **Data Encryption**: At-rest and in-transit encryption
- **Audit Trails**: Comprehensive logging and monitoring
- **Compliance**: Industry-standard security practices

## 8. Development Status

### 8.1 Completed Features ✅

- **Phase 1**: Basic SaaS architecture and user roles
- **Phase 2**: Correct quote workflow implementation
- **Phase 3**: Enhanced affiliate management and navigation
- **Event Manager Portal**: Production-ready with live tracking and God's View monitoring
- **Authentication System**: Production-ready with role-based access control
- **Multi-tenant Architecture**: Core infrastructure implemented with tenant isolation
- **Affiliate Approval System**: Comprehensive workflows with audit trails and email notifications
- **Live Trips Map**: Professional-grade real-time tracking with emergency response capabilities
- **Multi-Company Affiliate Support**: Role-based access across multiple affiliate companies

### 8.2 Current Priorities

- **Real-time Quote Status Updates**: WebSocket integration for live affiliate responses
- **Enhanced Quote Details Modal**: Comprehensive client quote management interface
- **Mobile Optimization**: Continued mobile-first approach improvements

## 9. Future Enhancements

### 9.1 Advanced Features

- **AI-powered Matching**: Intelligent affiliate selection algorithms
- **Predictive Analytics**: Demand forecasting and capacity planning
- **Mobile Applications**: Native iOS and Android apps
- **Advanced Reporting**: Custom dashboards and data visualization

### 9.2 Integration Capabilities

- **Third-party APIs**: CRM, accounting, and logistics integrations
- **White-label Solutions**: Customizable branding for enterprise clients
- **Marketplace Features**: Public transportation provider directory

## 10. Quality Assurance

### 10.1 Testing Strategy

- **Manual Testing**: Comprehensive user journey testing
- **Component Testing**: Individual feature validation
- **Integration Testing**: End-to-end workflow verification
- **Performance Testing**: Load and stress testing

### 10.2 Production Readiness

- **Error Handling**: Comprehensive error management
- **Performance Optimization**: Efficient data loading and caching
- **Security Validation**: Regular security audits and updates
- **Documentation**: Complete technical and user documentation

---

**Document Version**: 3.0
**Last Updated**: January 2025
**Status**: Active Development - Phase 3
