"ID","Team","Title","Description","Status","Estimate","Priority","Project ID","Project","Creator","Assignee","Labels","Cycle Number","Cycle Name","Cycle Start","Cycle End","Created","Updated","Started","Triaged","Completed","Canceled","Archived","Due Date","Parent issue","Initiatives","Project Milestone ID","Project Milestone","SLA Status","Roadmaps"
"GUG-29","Gugga7","Tenant Infrastructure","This milestone covers the foundational work for the multi-tenant architecture.

### Tasks:

* Create tenant tables and relationships.
* Implement tenant middleware and context.
* Add tenant_id to existing tables.
* Create tenant switching UI for super admin.

# Multi-Tenant Architecture Specification

## Overview

TransFlow operates as a three-tier multi-tenant platform supporting different levels of isolation and branding to serve various business models from shared SaaS to white-label solutions.

## Architecture Levels

### Level 1: Shared (Current State)

**Description**: Single tenant with organizational switching for management access.

**Use Case**: Traditional SaaS model where all customers share the same platform and branding.

**Customer Experience**:

* Access via `app.transflow.com`
* TransFlow branding throughout
* Shared affiliate network
* Transparent pricing from affiliates
* Cross-customer analytics visible to super admin

**User Roles**:

* **Clients (Event Managers)**: Manage events and quotes for their organization
* **Super Admin**: ORG switching to manage different client organizations
* **Affiliates**: Single portal, serve all TransFlow clients

**Example**: ABC Corporation uses TransFlow for corporate travel management.

---

### Level 2: Transportation Network Providers (LIMO123, DallasLimo, etc.)

**Description**: Independent transportation networks using TransFlow's platform infrastructure with their own customers and affiliate networks.

**Use Case**: LIMO123 operates as a Transportation Network Company (TNC) using TransFlow's platform but maintaining complete operational independence.

**Customer Experience**:

* **LIMO123 Customers**: Access `app.limo123.com`
  * LIMO123 branding throughout
  * Register accounts to track quotes and reservations
  * No subscription model - direct booking and tracking
  * May not know about TransFlow network initially
  * Gradual upsell to TransFlow network benefits
* **DallasLimo Customers**: Access `app.dallaslimo.com`
  * Completely separate from LIMO123 customers
  * DallasLimo branding and pricing models
  * Independent customer database

**User Roles**:

* **LIMO123 Customers**: Direct customer registration and booking tracking
* **LIMO123 Admin**: Large OPS capabilities (similar to Master Super Admin but no tenancy management)
* **TransFlow Master Super Admin**: Network switching between TransFlow, LIMO123, DallasLimo
* **Affiliates**: Choose network participation (LIMO123-exclusive vs. LIMO123 + Global TransFlow)

**Affiliate Network Participation**:

```typescript
interface AffiliateNetworkPreferences {
  participateInGlobalNetwork: boolean;     // Join TransFlow network
  exclusiveToBrands: string[];            // [""LIMO123""] for exclusive
  rateVisibility: 'transparent' | 'brand-specific';
  preferredQuoteVolume: 'maximum' | 'selective';
}
```

**Key Architectural Differences**:

* **No ORG Switching**: LIMO123 Admin manages customers directly without organizational layers
* **No Subscription Management**: Customers register for tracking, not subscription billing
* **Network Independence**: LIMO123 operates independently but can access TransFlow affiliate network
* **Admin vs. Master Super Admin**: LIMO123 Admin has operational control, Master Super Admin has platform control

**Customer Migration Strategy**:

1. **Initial Experience**: LIMO123 branded, exclusive affiliate network
2. **Value Discovery**: When customer needs exceed LIMO123 coverage
3. **Network Reveal**: ""Access our partner network for this location?""
4. **Upsell Opportunity**: ""Upgrade to TransFlow Premium for full network access""

**Example**: Customer books with LIMO123 in Austin, later needs service in Houston. System offers ""LIMO123 partner network"" access, gradually introducing TransFlow benefits.

---

### Level 3: White-Label (External Clients)

**Description**: Complete branding customization for external clients with full data isolation.

**Use Case**: Hotels, corporations, or other businesses want transportation booking under their own brand.

**Customer Experience**:

* **Marriott Downtown**: Access `transportation.marriott-downtown.com`
  * Full Marriott branding (logo, colors, domain)
  * Only Marriott guests/events visible
  * Marriott-specific pricing and workflows
  * No TransFlow branding visible
  * Custom email templates and communications

**User Roles**:

* **Hotel/Corporate Customers**: Fully branded experience, complete data isolation
* **TransFlow Master Super Admin**: White-label management, custom configurations
* **Affiliates**: Context-aware service (know they're serving Marriott guests)

**Example**: Marriott concierge books transportation for hotel guests through fully branded portal.

---

## Database Schema Changes

### Core Tables Modifications

```sql
-- Add tenant isolation
ALTER TABLE customers ADD COLUMN tenant_id UUID REFERENCES tenants(id);
ALTER TABLE quotes ADD COLUMN tenant_id UUID REFERENCES tenants(id);
ALTER TABLE events ADD COLUMN tenant_id UUID REFERENCES tenants(id);
ALTER TABLE bookings ADD COLUMN tenant_id UUID REFERENCES tenants(id);

-- Tenant configuration
CREATE TABLE tenants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  domain VARCHAR(255),
  parent_tenant_id UUID REFERENCES tenants(id),
  tenant_type ENUM('shared', 'segregated', 'white_label') NOT NULL,
  status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
  branding JSONB,
  settings JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Tenant branding configuration
CREATE TABLE tenant_branding (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id),
  logo_url VARCHAR(500),
  favicon_url VARCHAR(500),
  primary_color VARCHAR(7),
  secondary_color VARCHAR(7),
  background_color VARCHAR(7),
  custom_css TEXT,
  email_templates JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Affiliate network participation
ALTER TABLE affiliates ADD COLUMN network_participation JSONB DEFAULT '{
  ""global_network"": false,
  ""exclusive_brands"": [],
  ""rate_sharing"": ""transparent"",
  ""preferred_volume"": ""maximum""
}';

-- Tenant-specific affiliate relationships
CREATE TABLE tenant_affiliates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id),
  affiliate_id UUID REFERENCES affiliates(id),
  relationship_type ENUM('exclusive', 'shared', 'preferred') NOT NULL,
  commission_override DECIMAL(5,2),
  priority_level INTEGER DEFAULT 1,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(tenant_id, affiliate_id)
);

-- Customer migration tracking
CREATE TABLE customer_migrations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id),
  from_tenant_id UUID REFERENCES tenants(id),
  to_tenant_id UUID REFERENCES tenants(id),
  migration_type ENUM('upsell', 'transfer', 'merge') NOT NULL,
  migration_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
  migration_data JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  completed_at TIMESTAMP
);
```

### Row Level Security (RLS) Policies

```sql
-- Enable RLS on tenant-aware tables
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE events ENABLE ROW LEVEL SECURITY;

-- Tenant isolation policy
CREATE POLICY tenant_isolation ON customers
  FOR ALL TO authenticated
  USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

-- Super admin bypass policy
CREATE POLICY super_admin_access ON customers
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_roles 
      WHERE user_id = auth.uid() 
      AND role = 'super_admin'
    )
  );
```

## Implementation Phases

### Phase 1: Tenant Infrastructure (Weeks 1-2)

* Create tenant tables and relationships
* Implement tenant middleware and context
* Add tenant_id to existing tables
* Create tenant switching UI for super admin

### Phase 2: Affiliate Network Management (Weeks 3-4)

* Implement affiliate network participation choices
* Create tenant-affiliate relationship management
* Build affiliate onboarding flow with network selection
* Implement quote distribution logic based on network preferences

### Phase 3: Customer Migration System (Weeks 5-6)

* Build customer migration tracking
* Implement upsell detection (coverage gaps, volume thresholds)
* Create migration workflows and UI
* Add analytics for migration success rates

### Phase 4: White-Label Engine (Weeks 7-8)

* Implement dynamic branding system
* Create subdomain routing
* Build custom CSS injection
* Implement branded email templates

### Phase 5: Advanced Features (Weeks 9-10)

* Cross-tenant analytics and reporting
* Advanced affiliate network optimization
* Customer lifecycle management
* Performance monitoring and optimization

## Business Logic Examples

### Quote Distribution Algorithm

```typescript
async function getEligibleAffiliates(quoteRequest: QuoteRequest) {
  const { tenant_id, location, service_type, customer_id } = quoteRequest;
  const tenant = await getTenant(tenant_id);
  
  // Get tenant-specific affiliates
  const exclusiveAffiliates = await getAffiliatesByTenant(tenant_id, 'exclusive');
  
  // Check if customer is eligible for network access
  const customerTier = await getCustomerTier(customer_id);
  const hasNetworkAccess = customerTier.includes('network') || 
                          tenant.settings.allowGlobalNetwork;
  
  // Get global network affiliates if eligible
  const globalAffiliates = hasNetworkAccess ? 
    await getGlobalNetworkAffiliates(location, service_type) : [];
  
  return {
    exclusive: exclusiveAffiliates,
    network: globalAffiliates,
    upsellOpportunity: !hasNetworkAccess && globalAffiliates.length > 0
  };
}
```

### Customer Upsell Detection

```typescript
async function detectUpsellOpportunity(customer_id: string, quote_request: QuoteRequest) {
  const customer = await getCustomer(customer_id);
  const tenant = await getTenant(customer.tenant_id);
  
  // Scenarios for upsell
  const scenarios = {
    // No coverage in current network
    noCoverage: await checkCoverage(quote_request, tenant.exclusive_affiliates),
    
    // Better rates available in global network
    betterRates: await comparePricing(quote_request, tenant.id),
    
    // Customer booking frequency threshold
    highVolume: customer.monthly_bookings > tenant.settings.upsell_threshold,
    
    // Geographic expansion needs
    multiCity: await detectMultiCityNeeds(customer_id)
  };
  
  return {
    shouldUpsell: Object.values(scenarios).some(Boolean),
    scenarios,
    recommendedPlan: calculateRecommendedPlan(scenarios)
  };
}
```

## Security Considerations

### Data Isolation

* Strict tenant_id enforcement in all queries
* RLS policies prevent cross-tenant data access
* Encrypted tenant-specific data at rest

### Access Control

* Role-based permissions per tenant
* Super admin bypass with audit logging
* API rate limiting per tenant

### Compliance

* GDPR compliance with tenant-specific data handling
* SOC 2 compliance for white-label clients
* Audit trails for all cross-tenant operations

## Monitoring and Analytics

### Tenant-Level Metrics

* Customer acquisition and retention per tenant
* Revenue attribution across tenant hierarchy
* Affiliate performance by network participation
* Migration success rates and customer satisfaction

### Cross-Tenant Insights

* Network effects measurement
* Optimal affiliate distribution
* Customer lifecycle optimization
* Pricing strategy effectiveness

This architecture provides the flexibility to serve different business models while maintaining operational efficiency and clear upgrade paths for customers across all tenant levels.","Backlog",,"Urgent","4b5894c8-521c-495d-a38d-d0cc36080880","transflow","<EMAIL>",,"",,,,,"2025-06-05T22:30:02.664Z","2025-06-06T05:08:48.365Z",,,,,,,,"","c73fd879-9410-40f4-9caf-c293e3b12826","MILESTONE 1 : Foundation",
"GUG-34","Gugga7","Parent: Testing Affiliate Critical Flows","This issue will serve as a parent task for tracking all testing related to the Affiliate user role's critical flows. Sub-tasks will be created for each specific flow to be tested.","Backlog",,"Urgent","4b5894c8-521c-495d-a38d-d0cc36080880","transflow","<EMAIL>",,"",,,,,"2025-06-05T22:45:01.446Z","2025-06-06T04:54:10.087Z",,,,,,,,"","f0f6b1fa-e1af-4bda-983e-2ac693cfa179","MILESTONE 4: Testing Suite",
"GUG-35","Gugga7","Test: Affiliate Onboarding and Profile Management","**Objective:** Verify that the affiliate onboarding process is seamless and that profile management functions as expected.

### Test Cases:

1. **Registration:**
   * An affiliate can successfully register for a new account.
   * Validate that all required fields are enforced.
   * Ensure the user is correctly assigned the 'AFFILIATE' role.
2. **Company Creation:**
   * After registration, the affiliate is prompted to create or join a company.
   * Verify that a new affiliate company can be created with all required information (name, address, contact info).
3. **Profile & Document Management:**
   * An affiliate can edit their company profile information.
   * Verify that documents (e.g., Business License, Insurance) can be uploaded.
   * Ensure the document status is updated accordingly.
4. **Multi-Company Support:**
   * Verify an affiliate user can be associated with multiple companies via the `affiliate_user_companies` table.
   * Ensure the user can switch context between their associated companies.","Backlog",,"Urgent","4b5894c8-521c-495d-a38d-d0cc36080880","transflow","<EMAIL>",,"",,,,,"2025-06-05T22:45:10.970Z","2025-06-06T05:02:24.018Z",,,,,,,,"","f0f6b1fa-e1af-4bda-983e-2ac693cfa179","MILESTONE 4: Testing Suite",
"GUG-36","Gugga7","Test: Affiliate Quote Request (Offer) Workflow","**Objective:** Ensure affiliates can correctly receive, review, and respond to quote offers.

### Test Cases:

1. **Offer Notification:**
   * Verify that a matching affiliate receives a notification (UI and/or email) for a new quote offer.
2. **Offer Review:**
   * The affiliate can view all details of the quote request.
3. **Offer Acceptance/Rejection:**
   * The affiliate can accept an offer. The quote status should be updated, and the client notified.
   * The affiliate can reject an offer. The quote should be released back to the pool or the client notified.
4. **Counter-Offers:**
   * The affiliate can submit a counter-offer with a new price and explanatory notes.
   * The client should be notified of the counter-offer and be able to review it.
5. **Status Updates:**
   * Test the real-time status updates throughout the offer workflow.","Backlog",,"Urgent","4b5894c8-521c-495d-a38d-d0cc36080880","transflow","<EMAIL>",,"",,,,,"2025-06-05T22:45:21.184Z","2025-06-06T05:02:33.330Z",,,,,,,,"","f0f6b1fa-e1af-4bda-983e-2ac693cfa179","MILESTONE 4: Testing Suite",
"GUG-37","Gugga7","Test: Affiliate Advanced Service Management (Fleet, Rates, Service Areas)","**Objective:** Test the advanced features that allow affiliates to manage their service offerings.

### Test Cases:

1. **Service Area Management:**
   * An affiliate can define a service area using a central point and a radius in miles.
   * Verify that the affiliate matching logic correctly uses this radius to determine eligibility for a quote.
2. **Date Block Management:**
   * An affiliate can create special event date blocks for specific cities.
   * Ensure that quotes falling within these date blocks trigger special pricing considerations or notifications.
3. **Fleet and Rate Management:**
   * An affiliate can add/edit vehicles in their fleet.
   * Verify that rates can be set for active vehicles.
   * The quote workflow should be able to pull and display these rates.
4. **Network Participation:**
   * An affiliate can choose between participating in the global network or remaining exclusive to a specific tenant (e.g., LIMO123).
   * Test that the quote distribution logic respects this preference.","Backlog",,"Urgent","4b5894c8-521c-495d-a38d-d0cc36080880","transflow","<EMAIL>",,"",,,,,"2025-06-05T22:45:31.013Z","2025-06-06T05:02:41.860Z",,,,,,,,"","f0f6b1fa-e1af-4bda-983e-2ac693cfa179","MILESTONE 4: Testing Suite",
"GUG-38","Gugga7","Parent: Testing Client (Event Manager) Critical Flows","This issue will serve as a parent task for tracking all testing related to the Client (Event Manager) user role's critical flows. Sub-tasks will be created for each specific flow to be tested.","Backlog",,"Urgent","4b5894c8-521c-495d-a38d-d0cc36080880","transflow","<EMAIL>",,"",,,,,"2025-06-05T22:45:38.608Z","2025-06-06T04:54:25.986Z",,,,,,,,"","f0f6b1fa-e1af-4bda-983e-2ac693cfa179","MILESTONE 4: Testing Suite",
"GUG-39","Gugga7","Test: Client Quote and Event Management","**Objective:** Ensure clients can seamlessly create quotes and manage their events.

### Test Cases:

1. **Quote Creation:**
   * A client can successfully create a new quote request with all necessary details (pickup, dropoff, time, vehicle type, etc.).
   * Test the affiliate matching logic to ensure the quote is sent to the correct affiliates based on service area, rates, etc.
   * Verify the system recommends the correct approach (Rate Request vs. Fixed Offer).
2. **Offer Review and Decision:**
   * The client receives and can review offers (including counter-offers) from affiliates.
   * The client can accept or reject an offer.
   * Test that all status changes are communicated in real-time.
3. **Event Management:**
   * A client can create and manage an event, associating multiple trips/quotes with it.
   * Verify the Event Details page provides a comprehensive overview of all related transportation.
4. **Passenger Management:**
   * A client can add, edit, and manage passenger information for trips.","Backlog",,"Urgent","4b5894c8-521c-495d-a38d-d0cc36080880","transflow","<EMAIL>",,"",,,,,"2025-06-05T22:45:49.548Z","2025-06-06T05:02:59.585Z",,,,,,,,"","f0f6b1fa-e1af-4bda-983e-2ac693cfa179","MILESTONE 4: Testing Suite",
"GUG-40","Gugga7","Test: Client Live Trip Tracking and ""God's View""","**Objective:** Verify the functionality and accuracy of the live trip tracking features for clients.

### Test Cases:

1. **Map Visualization:**
   * Active trips for an event are accurately displayed on the Live Trips Map.
   * Verify that vehicle positions are updated in real-time.
   * Test the route visualization, including the curved route lines.
2. **Filtering and Selection:**
   * A client can filter trips on the map by status (e.g., On the way, Arrived) and vehicle type.
   * Selecting a trip should highlight it and show relevant details (driver info, passenger, ETA).
   * A/B pins should only show for the selected trip.
3. **God's View Monitoring:**
   * Ensure the 'God's View' provides a comprehensive overview of all event transportation.
   * Test the performance analytics dashboard (on-time rates, delays).
4. **Emergency Features:**
   * Test the alert system and communication features.
   * Verify that VIP passengers are specially marked and tracked.","Backlog",,"Urgent","4b5894c8-521c-495d-a38d-d0cc36080880","transflow","<EMAIL>",,"",,,,,"2025-06-05T22:46:02.176Z","2025-06-06T05:03:12.402Z",,,,,,,,"","f0f6b1fa-e1af-4bda-983e-2ac693cfa179","MILESTONE 4: Testing Suite",
"GUG-41","Gugga7","Parent: Testing Super Admin Critical Flows","This issue will serve as a parent task for tracking all testing related to the Super Admin user role's critical flows. Sub-tasks will be created for each specific flow to be tested.","Backlog",,"Urgent","4b5894c8-521c-495d-a38d-d0cc36080880","transflow","<EMAIL>",,"",,,,,"2025-06-05T22:46:10.203Z","2025-06-06T05:01:44.769Z",,,,,,,,"","f0f6b1fa-e1af-4bda-983e-2ac693cfa179","MILESTONE 4: Testing Suite",
"GUG-42","Gugga7","Test: Super Admin Affiliate Onboarding and Approval Workflow","**Objective:** To test the super admin's ability to manage the entire affiliate lifecycle, from application to approval.

### Test Cases:

1. **Application Review:**
   * Super admin can view a list of pending affiliate applications in the Affiliate Config dashboard.
   * Verify all submitted information and documents are visible and accessible.
2. **Approval Process:**
   * Super admin can approve an affiliate application with one click.
   * The affiliate's status should change to 'Approved'.
   * An email notification should be sent to the affiliate.
   * Approved affiliates should now be eligible to receive quote offers.
3. **Rejection Process:**
   * Super admin can reject an application, selecting from predefined reasons and adding custom notes.
   * The affiliate's status should change to 'Rejected'.
   * A structured rejection email should be sent to the affiliate.
4. **Audit Trail:**
   * Every action (view, approve, reject, notify) taken by the super admin should be logged in the `affiliate_approval_history` table.
   * Verify the Audit Log viewer on the affiliate's detail page displays a complete and accurate history.","Backlog",,"Urgent","4b5894c8-521c-495d-a38d-d0cc36080880","transflow","<EMAIL>",,"",,,,,"2025-06-05T22:46:18.970Z","2025-06-06T05:03:20.862Z",,,,,,,,"","f0f6b1fa-e1af-4bda-983e-2ac693cfa179","MILESTONE 4: Testing Suite",
"GUG-43","Gugga7","Test: Super Admin Multi-Tenant Management","**Objective:** To verify the super admin's ability to manage the entire multi-tenant ecosystem.

### Test Cases:

1. **Tenant Creation:**
   * Super admin can create new tenants for each level (Shared, Segregated, White-Label).
   * Verify that tenant-specific configurations (branding, settings) can be applied.
2. **Context Switching (ORG & Network):**
   * **Level 1 (Shared):** Super admin can use the ORG switcher to operate on behalf of a specific client organization. Verify that operational data (quotes, events, etc.) is correctly filtered.
   * **Level 2 (Segregated):** Super admin can use the Network switcher to move between different TNCs (e.g., TransFlow, LIMO123).
3. **Data Isolation:**
   * Confirm that RLS policies effectively prevent data leakage between tenants. A super admin should only see a tenant's data when explicitly switched into that tenant's context.
4. **User Impersonation:**
   * Test the ability of a super admin to impersonate a client user for support purposes.
   * Ensure all actions taken while impersonating are logged to the audit trail with both super admin and impersonated user IDs.
5. **Global Analytics:**
   * Verify the super admin has access to cross-tenant analytics to monitor the health of the entire platform.","Backlog",,"Urgent","4b5894c8-521c-495d-a38d-d0cc36080880","transflow","<EMAIL>",,"",,,,,"2025-06-05T22:46:30.293Z","2025-06-06T05:03:27.692Z",,,,,,,,"","f0f6b1fa-e1af-4bda-983e-2ac693cfa179","MILESTONE 4: Testing Suite",
"GUG-44","Gugga7","Parent: Testing TNC Admin (Level 2 Tenant) Critical Flows","This issue will serve as a parent task for tracking all testing related to the TNC Admin user role's critical flows. Sub-tasks will be created for each specific flow to be tested. This covers the Level 2 (Segregated) tenant model.","Backlog",,"Urgent","4b5894c8-521c-495d-a38d-d0cc36080880","transflow","<EMAIL>",,"",,,,,"2025-06-05T22:46:37.641Z","2025-06-06T05:01:52.562Z",,,,,,,,"","f0f6b1fa-e1af-4bda-983e-2ac693cfa179","MILESTONE 4: Testing Suite",
"GUG-45","Gugga7","Test: TNC Admin Customer and Operations Management","**Objective:** To verify that a TNC Admin can manage their own customer base and operations independently.

### Test Cases:

1. **Customer Management:**
   * TNC Admin can manage their own set of customers directly.
   * Verify there is no ORG switching capability for the TNC Admin.
   * Test customer registration which is for tracking purposes, not subscription billing.
2. **Operational Control:**
   * TNC Admin has large-scale operational capabilities (managing quotes, events, trips) within their own tenant.
   * Verify that their view is restricted to their tenant's data only.
3. **Affiliate Network Management:**
   * TNC Admin can manage their relationships with affiliates (e.g., exclusive vs. shared).
   * Test the quote distribution to ensure it respects these relationships (e.g., an exclusive affiliate only gets quotes from that TNC).
4. **Customer Migration/Upsell:**
   * Test the scenario where a customer's request is outside the TNC's affiliate coverage.
   * Verify the system correctly identifies this as an upsell opportunity and offers access to the broader TransFlow partner network.","Backlog",,"Urgent","4b5894c8-521c-495d-a38d-d0cc36080880","transflow","<EMAIL>",,"",,,,,"2025-06-05T22:46:49.262Z","2025-06-06T05:03:34.641Z",,,,,,,,"","f0f6b1fa-e1af-4bda-983e-2ac693cfa179","MILESTONE 4: Testing Suite",
"GUG-46","Gugga7","Parent: Testing White Label (Level 3 Tenant) Critical Flows","This issue will serve as a parent task for tracking all testing related to the White Label (Level 3) tenant model. Sub-tasks will be created for each specific flow to be tested.","Backlog",,"Urgent","4b5894c8-521c-495d-a38d-d0cc36080880","transflow","<EMAIL>",,"",,,,,"2025-06-05T22:46:55.713Z","2025-06-06T05:02:05.068Z",,,,,,,,"","f0f6b1fa-e1af-4bda-983e-2ac693cfa179","MILESTONE 4: Testing Suite",
"GUG-47","Gugga7","Test: White Label Branding, Isolation, and User Experience","**Objective:** To ensure the white-label solution provides complete branding customization and data isolation.

### Test Cases:

1. **Branding and Theming:**
   * Set up a white-label tenant with a custom domain (e.g., `transportation.marriott-downtown.com`).
   * Verify that the logo, favicon, and color scheme are correctly applied throughout the portal.
   * Test that all user-facing pages reflect the white-label branding, with no mention of TransFlow.
2. **Custom Domain and Routing:**
   * Ensure the custom domain routes correctly to the tenant's instance of the application.
3. **Data Isolation:**
   * Create data (customers, quotes, events) within the white-label tenant.
   * Log in as a user from a different tenant (or as a super admin not in that context) and verify that this data is completely inaccessible.
   * Test RLS policies rigorously.
4. **Communications:**
   * Verify that all email notifications (e.g., quote confirmations, password resets) use the tenant's custom email templates and branding.
5. **Affiliate Context:**
   * When an affiliate serves a white-label tenant, ensure they have context that they are serving that specific brand (e.g., ""You are serving a Marriott guest"").","Backlog",,"Urgent","4b5894c8-521c-495d-a38d-d0cc36080880","transflow","<EMAIL>",,"",,,,,"2025-06-05T22:47:06.107Z","2025-06-06T05:03:47.055Z",,,,,,,,"","f0f6b1fa-e1af-4bda-983e-2ac693cfa179","MILESTONE 4: Testing Suite",