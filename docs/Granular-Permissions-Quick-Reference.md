# Granular Permissions System - Quick Reference

## 🚀 System Overview

The granular permissions system provides account-level permission management that works across all tenant levels. Super admins can configure detailed permissions for individual users, applying pre-configured templates or custom permission sets.

## 📍 Access Points

### Super Admin Interface
- **Main Access**: `/super-admin/users` - User management table
- **Permission Management**: `/super-admin/users/[userId]/permissions` - Individual user permissions
- **User Actions**: "Manage Permissions", "View User Details", "Edit User" from user table dropdown

### Network Tenant Access
- **Direct Login**: Network tenants can log in directly with their credentials
- **Context Switching**: Super admin can switch to network tenant context via Network Tenant switcher

## 🔐 Login Credentials

### Network Tenant Accounts
- **TransFlow Default**: `<EMAIL>` (password: `network123`)
- **TransFlow Shared**: `<EMAIL>` (password: `network123`)
- **WWLIMO TNC**: `<EMAIL>` (password: `network123`)

All accounts have SUPER_ADMIN role and full access to their respective network tenant context.

## 🎯 Permission Templates

### 1. Fully Managed Client
- **Use Case**: Clients who want view-only access with hidden affiliate/financial data
- **Features**: Basic quote viewing, trip monitoring, limited analytics
- **Restrictions**: No quote creation, hidden affiliate rates, no financial details
- **White Label**: Disabled

### 2. Self-Service Client
- **Use Case**: Clients who want full booking management with limited financial access
- **Features**: Full quote management, trip coordination, basic analytics
- **Restrictions**: Limited affiliate management, hidden detailed rates
- **White Label**: Disabled

### 3. TNC Full Access
- **Use Case**: Transportation Network Companies with complete operational control
- **Features**: Full platform access, affiliate management, advanced analytics
- **Permissions**: Customer registration, rate negotiation, financial reporting
- **White Label**: Enabled (optional)

### 4. Medical Transport Specialist
- **Use Case**: Healthcare facilities requiring HIPAA-compliant transportation
- **Features**: Medical-specific workflows, compliance tools, specialized analytics
- **Permissions**: HIPAA compliance features, medical affiliate network
- **White Label**: Enabled (for hospital branding)

## 🔧 Permission Categories

### Feature Permissions
- **Quotes & Bookings**: view, create, edit, delete, approve, export
- **Trip Management**: view, manage, assign drivers, track, complete
- **Event Management**: view, create, edit, delete, manage quotes
- **Passenger Management**: view, create, edit, delete, export
- **Affiliate Operations**: view, select, manage, negotiate, view rates
- **Analytics & Reports**: basic, advanced, financial, export, medical
- **Financial Access**: view costs, rates, profit, manage billing
- **System & Access**: customer registration, white label, HIPAA, admin

### UI Customizations
- **Sidebar Elements**: affiliates, financial reports, analytics, admin
- **Page Access**: affiliate management, advanced analytics, financial dashboard
- **Page Sections**: affiliate rates, cost breakdown, profit margins, HIPAA
- **Branding & Customization**: custom branding, logo, colors, white label

### Access Controls
- **White Label Access**: Granular per-user activation (as requested)
- **Cross-Tenant Access**: Access data across multiple tenants
- **Data Export**: Export capabilities and report generation
- **Data Scope**: user, organization, tenant, global access levels

## 🏢 Tenant Management

### Client Tenants vs Network Tenants
- **Client Tenants**: Organizations that pay for subscriptions (City Tours LLC, etc.)
- **Network Tenants**: Network environments (TransFlow Default, TransFlow Shared, WWLIMO TNC)

### Switchers
- **Client Tenant Switcher**: Filters operational data by client organization
- **Network Tenant Switcher**: Switches between network environments (searchable)

### Organization Filtering
All OPS menu items filter by selected client tenant:
- ✅ Events
- ✅ Trips  
- ✅ Passengers
- ✅ Affiliates (Affiliate Operations)
- ❌ Operations (system-level, correctly excluded)

## 🗄️ Database Architecture

### Core Tables
- `permission_templates` - Pre-configured permission sets
- `user_permission_overrides` - User-specific permission configurations
- `tenant_feature_permissions` - Tenant-level feature controls
- `tenant_ui_customizations` - Tenant-level UI customizations
- `permission_audit_log` - Complete audit trail

### Migration Files
- `20250109000001_create_granular_permissions.sql` - Core permissions system
- `20250109000002_create_network_tenant_users.sql` - Network tenant accounts

## 🔍 API Endpoints

- `GET /api/super-admin/users` - List all users with real data
- `GET /api/super-admin/users/[userId]/permissions` - Get user permissions
- `POST /api/super-admin/users/[userId]/permissions` - Update user permissions
- `GET /api/super-admin/permission-templates` - List permission templates
- `GET /api/super-admin/tenants` - List network tenants

## 📋 Linear Issues Completed

- **GUG-63**: Granular Permissions Skeleton - PRODUCTION READY
- **GUG-65**: Organization Filtering - COMPLETED
- **GUG-66**: Network Switcher Fix - COMPLETED
- **GUG-67**: Searchable Network Switcher - COMPLETED
- **GUG-68**: Implementation Status Summary - CREATED

## 🧪 Testing Checklist

### Permission Management
- [ ] Navigate to `/super-admin/users`
- [ ] Click "Manage Permissions" for any user
- [ ] Test template selection and application
- [ ] Configure granular permissions in tabs
- [ ] Test white-label per-user activation
- [ ] Verify audit trail logging

### Network Tenant Management
- [ ] Test Network Tenant switcher search functionality
- [ ] Login with network tenant credentials
- [ ] Verify same permissions as super admin context switching
- [ ] Test organization filtering across OPS pages

### Organization Filtering
- [ ] Use Client Tenant switcher dropdown
- [ ] Verify Events, Trips, Passengers, Affiliates filter correctly
- [ ] Test API responses with organization parameters

The granular permissions system is production-ready and provides comprehensive control over user access and capabilities across the multi-tenant platform.
