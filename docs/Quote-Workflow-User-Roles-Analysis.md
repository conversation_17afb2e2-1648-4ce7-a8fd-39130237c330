# Quote Workflow & User Roles Analysis

## Overview

This document provides a comprehensive analysis of the quote workflow capabilities and CTAs (Call-to-Actions) across different user roles in the TransFlow SaaS platform, based on the corrected business logic and multi-tenant architecture.

## Business Logic Principles

### Core Principle: Workflow Varies by Account Type

#### **Pure SaaS Workflow** (Current Implementation)

- **Client picks affiliates** directly in booking form during quote creation
- **Super Admin Role**: **MONITORING ONLY** - No quote orchestration or intermediation
- **Workflow**: Client → Selects Affiliates → Affiliates Respond → Client Chooses Winner
- **Super Admin CTAs**: View, Monitor, Handle Escalations (NO assignment actions)

#### **TNC Workflow** (Different Context)

- **Client submits quote** without affiliate selection (normal booking form)
- **TNC Admin Role**: **QUOTE ORCHESTRATOR** - Full workflow control
- **Workflow**: Client → TNC Admin → Assigns Affiliates → Affiliates Respond → TNC Admin Chooses → Client
- **TNC Admin CTAs**: Send to Affiliates, Assign Winner, Full orchestration

#### **Key Distinction**

- **Pure SaaS**: Client has control, Super Admin monitors
- **TNC**: TNC Admin has control, Client submits and receives

## User Role Definitions

### 🔴 Super Admin - "Platform Monitor" (Pure SaaS Context)

- **Primary Role**: Platform monitoring and network health oversight
- **Quote Interaction**: **VIEW ONLY** - Monitor status, handle escalations, NO orchestration
- **Portal**: `/super-admin/` with monitoring capabilities
- **Scope**: All networks, all tenants (monitoring access)
- **Key Limitation**: Cannot "Send to Affiliates" (client already chose in booking form)

### 🟡 TNC Admin - "Quote Orchestrator" (TNC Context)

- **Primary Role**: Full quote workflow orchestration within their network
- **Quote Interaction**: **FULL CONTROL** - Assign affiliates, manage responses, choose winners
- **Portal**: `/super-admin/` with orchestration capabilities
- **Scope**: Their affiliate network + their client network only
- **Key Power**: Receives quotes without affiliate selection, decides assignments

### 🟢 Super Client - "Empowered Client"

- **Primary Role**: Enhanced client with admin-style capabilities
- **Quote Interaction**: Configurable based on granular permissions
- **Portal**: `/super-admin/` with custom permission set
- **Scope**: Their organization with extended capabilities

### 🔵 Regular Client - "Standard User"

- **Primary Role**: Simple quote management for events
- **Quote Interaction**: Basic quote operations via platform
- **Portal**: `/event-manager/` with simplified interface
- **Scope**: Their organization with standard capabilities

## Quote CTA Capabilities Matrix

### Pure SaaS Context (Client Picks Affiliates in Booking Form)

| **Capability**         | **Super Admin (Monitor)** | **Super Client** | **Regular Client**        |
| ---------------------- | ------------------------- | ---------------- | ------------------------- |
| **Send to Affiliates** | ❌ **N/A** (Client chose) | 🔧 Configurable  | ❌ **N/A** (Client chose) |
| **Fixed Rate**         | 👁️ View Only              | 🔧 Configurable  | ❌ Request only           |
| **Accept Quote**       | 👁️ View Only              | 🔧 Configurable  | ✅ Final decision         |
| **Reject Quote**       | 👁️ View Only              | 🔧 Configurable  | ✅ Final decision         |
| **Archive Quote**      | ✅ Any quote              | 🔧 Configurable  | ✅ Org quotes             |
| **Monitor Status**     | ✅ All quotes             | ✅ Org quotes    | ✅ Org quotes             |
| **Handle Escalations** | ✅ Platform issues        | ❌ No access     | ❌ No access              |

### TNC Context (TNC Admin Orchestrates Workflow)

| **Capability**         | **TNC Admin (Orchestrator)** | **TNC Client**  |
| ---------------------- | ---------------------------- | --------------- |
| **Send to Affiliates** | ✅ **Core Function**         | ❌ TNC handles  |
| **Fixed Rate**         | ✅ Network rates             | ❌ Request only |
| **Accept Quote**       | ✅ **Assign Winner**         | 👁️ View result  |
| **Reject Quote**       | ✅ Network decision          | 👁️ View result  |
| **Archive Quote**      | ✅ Network quotes            | ❌ No access    |
| **Orchestrate Flow**   | ✅ **Full Control**          | ❌ Submit only  |

**Legend**: ✅ Full Access | ❌ No Access | 🔧 Configurable via Granular Permissions

## Portal Layout Strategy

### Admin-Style Layout (Super Admin, TNC Admin, Super Client)

- **Interface**: `/super-admin/` with full sidebar and advanced features
- **Quote Management**: Advanced quote row with expandable right slider panel
- **Capabilities**: Complete CTA set based on user permissions
- **Use Cases**: Platform management, network operations, enterprise clients

### Event Manager Layout (Regular Client)

- **Interface**: `/event-manager/` with simplified sidebar
- **Quote Management**: Streamlined quote creation and basic tracking
- **Capabilities**: Essential CTAs for event-based quote management
- **Use Cases**: Standard corporate clients, event planners

## White-Label Flexibility

Both layouts support white-label branding:

### Admin-Style White Label

- **Target**: Enterprise clients, hotel chains, large corporations
- **Features**: Full admin interface with custom branding
- **Permissions**: Granular control via Super Client permissions

### Event Manager White Label

- **Target**: Event planning companies, smaller corporations
- **Features**: Simplified interface with custom branding
- **Permissions**: Standard client permissions

## Granular Permissions for Super Clients

Super Clients can be granted specific permissions via the granular permissions system:

### Feature Permissions

```json
{
  "quotes.send_to_affiliates": true,
  "quotes.fixed_rate": true,
  "quotes.accept": true,
  "quotes.reject": true,
  "quotes.archive": true,
  "quotes.edit": true,
  "quotes.correct": false,
  "affiliates.manage": true,
  "affiliates.view_rates": true,
  "analytics.advanced": true,
  "analytics.profit_margins": false
}
```

### UI Customizations

```json
{
  "sidebar.affiliates": "visible",
  "sidebar.analytics": "visible",
  "sidebar.admin": "hidden",
  "page.affiliate_rates": "visible",
  "page.profit_margins": "restricted",
  "advanced_features": "visible"
}
```

### Access Controls

```json
{
  "white_label": true,
  "data_scope": "organization",
  "cross_tenant_access": false,
  "data_export": true
}
```

## Implementation Status

### ✅ Completed

- Multi-tenant architecture foundation
- Granular permissions system
- Super Admin full capabilities
- Basic role-based access control

### 🚧 In Progress

- TNC Admin network-scoped permissions
- Super Client granular permission enforcement
- Quote CTA conditional rendering

### 📋 Pending

- Regular Client simplified interface
- White-label layout customization
- Permission-based CTA display logic
- Event Manager portal enhancements

## Business Case Validation

This approach ensures:

1. **Operational Efficiency**: Each user type gets appropriate tools
2. **Scalability**: Platform operates without constant Super Admin intervention
3. **Flexibility**: Granular permissions allow custom client configurations
4. **Business Logic**: Clear separation of concerns and responsibilities
5. **Network Autonomy**: TNC operations remain independent
6. **Client Empowerment**: Super Clients get enterprise-grade capabilities when needed

## Next Steps

1. Implement permission-based CTA rendering in quote components
2. Enhance TNC Admin network-scoped filtering
3. Complete Super Client granular permission enforcement
4. Optimize Regular Client simplified interface
5. Test white-label functionality across both layouts
