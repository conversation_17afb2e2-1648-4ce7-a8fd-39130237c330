# Quote Workflow & User Roles Analysis

## Overview

This document provides a comprehensive analysis of the quote workflow capabilities and CTAs (Call-to-Actions) across different user roles in the TransFlow SaaS platform, based on the corrected business logic and multi-tenant architecture.

## Business Logic Principles

### Core Principle: Minimal Super Admin Intervention
- **Pure SaaS Mode**: Client ↔ Affiliate interactions happen directly without Super Admin intermediation
- **Super Admin Role**: Platform oversight and emergency intervention only
- **TNC Independence**: Full operational control within their network scope
- **Client Empowerment**: Appropriate tools based on their tier and permissions

## User Role Definitions

### 🔴 Super Admin - "Platform Overseer"
- **Primary Role**: Platform management and oversight
- **Quote Interaction**: Minimal intervention - only for corrections/escalations
- **Portal**: `/super-admin/` with full capabilities
- **Scope**: All networks, all tenants (emergency access)

### 🟡 TNC Admin - "Network Operator" 
- **Primary Role**: Full operational control within their network
- **Quote Interaction**: Active daily management of network quotes
- **Portal**: `/super-admin/` with network-scoped permissions
- **Scope**: Their affiliate network + their client network only

### 🟢 Super Client - "Empowered Client"
- **Primary Role**: Enhanced client with admin-style capabilities
- **Quote Interaction**: Configurable based on granular permissions
- **Portal**: `/super-admin/` with custom permission set
- **Scope**: Their organization with extended capabilities

### 🔵 Regular Client - "Standard User"
- **Primary Role**: Simple quote management for events
- **Quote Interaction**: Basic quote operations via platform
- **Portal**: `/event-manager/` with simplified interface
- **Scope**: Their organization with standard capabilities

## Quote CTA Capabilities Matrix

| **Capability** | **Super Admin** | **TNC Admin** | **Super Client** | **Regular Client** |
|---|---|---|---|---|
| **Send to Affiliates** | ✅ Emergency | ✅ Network | 🔧 Configurable | ❌ Platform handles |
| **Fixed Rate** | ✅ Override | ✅ Network rates | 🔧 Configurable | ❌ Request only |
| **Accept Quote** | ✅ Override | ✅ Customer decision | 🔧 Configurable | ✅ Final decision |
| **Reject Quote** | ✅ Override | ✅ Customer decision | 🔧 Configurable | ✅ Final decision |
| **Archive Quote** | ✅ Any quote | ✅ Network quotes | 🔧 Configurable | ✅ Org quotes |
| **Delete Quote** | ✅ Any quote | ❌ No delete | ❌ No delete | ❌ No delete |
| **Manage Affiliates** | ✅ All affiliates | ✅ Network mgmt | 🔧 Configurable | ❌ View only |
| **View Analytics** | ✅ Cross-tenant | ✅ Network metrics | 🔧 Configurable | ✅ Basic only |
| **Impersonate Users** | ✅ Any user | ❌ No access | ❌ No access | ❌ No access |
| **Correct Quote** | ✅ Any quote | ✅ Network quotes | 🔧 Configurable | ❌ No access |
| **Reorder Affiliates** | ✅ Any quote | ✅ Network quotes | 🔧 Configurable | ❌ No access |

**Legend**: ✅ Full Access | ❌ No Access | 🔧 Configurable via Granular Permissions

## Portal Layout Strategy

### Admin-Style Layout (Super Admin, TNC Admin, Super Client)
- **Interface**: `/super-admin/` with full sidebar and advanced features
- **Quote Management**: Advanced quote row with expandable right slider panel
- **Capabilities**: Complete CTA set based on user permissions
- **Use Cases**: Platform management, network operations, enterprise clients

### Event Manager Layout (Regular Client)
- **Interface**: `/event-manager/` with simplified sidebar
- **Quote Management**: Streamlined quote creation and basic tracking
- **Capabilities**: Essential CTAs for event-based quote management
- **Use Cases**: Standard corporate clients, event planners

## White-Label Flexibility

Both layouts support white-label branding:

### Admin-Style White Label
- **Target**: Enterprise clients, hotel chains, large corporations
- **Features**: Full admin interface with custom branding
- **Permissions**: Granular control via Super Client permissions

### Event Manager White Label  
- **Target**: Event planning companies, smaller corporations
- **Features**: Simplified interface with custom branding
- **Permissions**: Standard client permissions

## Granular Permissions for Super Clients

Super Clients can be granted specific permissions via the granular permissions system:

### Feature Permissions
```json
{
  "quotes.send_to_affiliates": true,
  "quotes.fixed_rate": true,
  "quotes.accept": true,
  "quotes.reject": true,
  "quotes.archive": true,
  "quotes.edit": true,
  "quotes.correct": false,
  "affiliates.manage": true,
  "affiliates.view_rates": true,
  "analytics.advanced": true,
  "analytics.profit_margins": false
}
```

### UI Customizations
```json
{
  "sidebar.affiliates": "visible",
  "sidebar.analytics": "visible", 
  "sidebar.admin": "hidden",
  "page.affiliate_rates": "visible",
  "page.profit_margins": "restricted",
  "advanced_features": "visible"
}
```

### Access Controls
```json
{
  "white_label": true,
  "data_scope": "organization",
  "cross_tenant_access": false,
  "data_export": true
}
```

## Implementation Status

### ✅ Completed
- Multi-tenant architecture foundation
- Granular permissions system
- Super Admin full capabilities
- Basic role-based access control

### 🚧 In Progress  
- TNC Admin network-scoped permissions
- Super Client granular permission enforcement
- Quote CTA conditional rendering

### 📋 Pending
- Regular Client simplified interface
- White-label layout customization
- Permission-based CTA display logic
- Event Manager portal enhancements

## Business Case Validation

This approach ensures:

1. **Operational Efficiency**: Each user type gets appropriate tools
2. **Scalability**: Platform operates without constant Super Admin intervention  
3. **Flexibility**: Granular permissions allow custom client configurations
4. **Business Logic**: Clear separation of concerns and responsibilities
5. **Network Autonomy**: TNC operations remain independent
6. **Client Empowerment**: Super Clients get enterprise-grade capabilities when needed

## Next Steps

1. Implement permission-based CTA rendering in quote components
2. Enhance TNC Admin network-scoped filtering
3. Complete Super Client granular permission enforcement
4. Optimize Regular Client simplified interface
5. Test white-label functionality across both layouts
