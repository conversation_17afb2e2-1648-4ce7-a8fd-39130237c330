# Portal Structure Analysis

## Overview
This document provides an analysis of the current portal structure in `app/(portals)` and the relationship between user roles and their respective portals.

## Current Portal Structure

### ✅ Active Portals

#### 1. `/event-manager` Portal
- **Users**: CLIENT, CLIENT_COORDINATOR roles
- **Purpose**: Event and transportation management
- **Key Features**:
  - Dashboard with organizational overview
  - Events management (create, edit, view)
  - Quotes and transportation requests
  - Trips tracking and management
  - Passenger management
- **Access Control**:
  - **CLIENT**: Full access to all organizational data
  - **CLIENT_COORDINATOR**: Filtered access to assigned events only

#### 2. `/super-admin` Portal  
- **Users**: SUPER_ADMIN role
- **Purpose**: Platform administration and management
- **Key Features**:
  - Platform analytics and reporting
  - Affiliate configuration and management
  - Organization (tenant) management
  - User management and permissions
  - Security and compliance monitoring
  - Subscription and billing management

#### 3. `/affiliate` Portal
- **Users**: AFFILIATE, AFFILIATE_DISPATCH roles
- **Purpose**: Transportation provider operations
- **Key Features**:
  - Company profile and fleet management
  - Rate requests and offer management
  - Live trips and duty of care
  - Driver management
  - Billing and financial tracking

### 🗄️ Archived Legacy Portals

#### 4. `/archive/admin-legacy` (formerly `/admin`)
- **Status**: ✅ ARCHIVED
- **Reason**: Replaced by `/super-admin` portal
- **Former Role**: ADMIN (now SUPER_ADMIN)

#### 5. `/archive/customer-legacy` (formerly `/customer`)
- **Status**: ✅ ARCHIVED  
- **Reason**: Replaced by `/event-manager` portal
- **Former Role**: CUSTOMER (now CLIENT)

### 🚨 Orphaned/Unclear Portals

#### 6. `/company`
- **Status**: ❓ UNCLEAR PURPOSE
- **Contents**: Profile and team management pages
- **Recommendation**: Evaluate if functionality should be merged into existing portals

#### 7. `/events`
- **Status**: ❓ UNCLEAR PURPOSE
- **Contents**: Standalone event pages
- **Recommendation**: Likely redundant with `/event-manager/events`

#### 8. `/components`
- **Status**: ❌ MISPLACED
- **Issue**: UI components should not be in `(portals)` directory
- **Recommendation**: Move to root `/components` directory

## Role-Portal Mapping

### Correct Architecture

```typescript
const ROLE_PORTAL_MAP = {
  'SUPER_ADMIN': '/super-admin',
  'CLIENT': '/event-manager',           // Full access
  'CLIENT_COORDINATOR': '/event-manager', // Filtered access
  'AFFILIATE': '/affiliate',
  'AFFILIATE_DISPATCH': '/affiliate',
  'PASSENGER': '/customer',             // Legacy, may be deprecated
  'DRIVER': '/driver'                   // Not yet implemented
};
```

### Data Access Control

#### CLIENT vs CLIENT_COORDINATOR
Both roles use the same `/event-manager` portal but with different data access:

- **CLIENT (Event Manager)**:
  - Full access to all organizational events, quotes, trips
  - Can create new events and assign coordinators
  - Administrative control over team and settings

- **CLIENT_COORDINATOR**:
  - Limited to events/trips specifically assigned by CLIENT
  - Cannot create new events, only manage assigned ones
  - Same UI interface but filtered data via RLS policies

## Implementation Details

### Database-Level Access Control
- **Row Level Security (RLS)** policies filter data based on user role
- **Event Assignment Tables** link coordinators to specific events
- **Tenant Isolation** ensures data separation between organizations

### Frontend Access Control
- **Conditional Rendering** based on user permissions
- **Route Protection** via middleware role validation
- **UI Filtering** to show/hide features based on role capabilities

## Recommendations

### Immediate Actions
1. **Move `/components`** to root directory structure
2. **Evaluate `/company` and `/events`** for consolidation or removal
3. **Document `/driver` portal** requirements for future implementation

### Future Considerations
1. **Deprecate `/customer` portal** in favor of integrated passenger management
2. **Standardize portal layouts** for consistent user experience
3. **Implement role-based feature flags** for granular access control

## Portal Statistics

- **Total Directories**: 155
- **Total Files**: 219
- **Active Portals**: 3 (event-manager, super-admin, affiliate)
- **Archived Portals**: 2 (admin-legacy, customer-legacy)
- **Orphaned Folders**: 3 (company, events, components)

## Conclusion

The portal structure has been successfully migrated from legacy role-based folders to the current SaaS architecture. The CLIENT and CLIENT_COORDINATOR roles sharing the `/event-manager` portal with differentiated access control represents a clean and scalable approach to multi-role portal management.
