# Cloud Database Migration Guide

This guide walks you through migrating your local WWMS database to Supabase Cloud for production use and remote agent connectivity.

## Why Migrate to Cloud?

### Benefits:
- ✅ **Remote Agent Access**: Agents can connect from anywhere
- ✅ **Better Performance**: No tunneling overhead
- ✅ **Production Ready**: Built-in backups, monitoring, scaling
- ✅ **Global CDN**: Faster access worldwide
- ✅ **Security**: Enterprise-grade security features

### vs. Local Tunneling (ngrok):
- ❌ **Slow**: Added latency from tunneling
- ❌ **Unreliable**: Connection drops and timeouts
- ❌ **Limited**: Bandwidth restrictions
- ❌ **Insecure**: Exposing local services

## Prerequisites

1. **Supabase Account**: Sign up at [supabase.com](https://supabase.com)
2. **Local Database Running**: Ensure your local Supabase is running
3. **Node.js & npm**: For running migration scripts
4. **PostgreSQL Client**: For database operations

## Step-by-Step Migration

### 1. Create Supabase Cloud Project

1. Go to [supabase.com](https://supabase.com) and create a new project
2. Choose a region close to your users
3. Set a strong database password
4. Wait for the project to be ready (2-3 minutes)

### 2. Get Your Cloud Credentials

From your Supabase project dashboard:

```bash
# Project Settings > API
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 3. Configure Environment

```bash
# Copy the template
cp .env.cloud.template .env.cloud

# Edit with your actual values
nano .env.cloud
```

Fill in your Supabase credentials:
```bash
CLOUD_SUPABASE_URL=https://your-project-ref.supabase.co
CLOUD_SUPABASE_ANON_KEY=your-actual-anon-key
CLOUD_SUPABASE_SERVICE_KEY=your-actual-service-role-key
```

### 4. Run Migration

```bash
# Make sure local database is running
npx supabase start

# Run the migration
npm run migrate:cloud
```

The script will:
- Export your local schema and data
- Connect to your cloud database
- Apply the schema and import data
- Verify the migration
- Create production environment files

### 5. Test Cloud Connection

```bash
# Test the cloud database connection
npm run test:cloud
```

This will verify:
- ✅ Supabase API connectivity
- ✅ Direct database connection
- ✅ Schema integrity
- ✅ Data migration success

### 6. Update Your Application

For **production deployment**:
```bash
# Use the generated production config
cp .env.cloud.production .env.production
```

For **local testing with cloud**:
```bash
# Test locally with cloud database
cp .env.cloud.production .env.local
```

## Environment Configuration

### Local Development (Default)
```bash
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=local-anon-key
```

### Cloud Production
```bash
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-cloud-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-cloud-service-key
```

## Remote Agent Configuration

Once migrated, remote agents can connect using:

```javascript
// Remote agent configuration
const supabase = createClient(
  'https://your-project.supabase.co',
  'your-anon-key'
);

// Or for server-side operations
const supabaseAdmin = createClient(
  'https://your-project.supabase.co',
  'your-service-role-key'
);
```

## Security Considerations

### Database Security
- ✅ **Row Level Security (RLS)**: Already configured in your schema
- ✅ **API Keys**: Use anon key for client, service role for server
- ✅ **SSL/TLS**: All connections encrypted
- ✅ **IP Restrictions**: Configure in Supabase dashboard if needed

### Environment Variables
- ✅ **Never commit** `.env.cloud` or `.env.production` files
- ✅ **Use different keys** for development and production
- ✅ **Rotate keys regularly** in production

## Troubleshooting

### Migration Fails
```bash
# Check local database is running
npx supabase status

# Verify cloud credentials
npm run test:cloud

# Check migration logs
tail -f supabase/cloud-migration/migration.log
```

### Connection Issues
```bash
# Test individual components
npm run test:cloud

# Check firewall/network
ping db.your-project-ref.supabase.co

# Verify credentials in Supabase dashboard
```

### Schema Mismatches
```bash
# Compare table counts
psql local-db -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';"
psql cloud-db -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';"

# Re-run migration if needed
npm run migrate:cloud
```

## Performance Optimization

### Connection Pooling
```javascript
// Use connection pooling for high-traffic applications
const supabase = createClient(url, key, {
  db: {
    schema: 'public',
  },
  auth: {
    autoRefreshToken: true,
    persistSession: true
  }
});
```

### Caching
```javascript
// Implement caching for frequently accessed data
const { data, error } = await supabase
  .from('affiliate_companies')
  .select('*')
  .eq('status', 'active')
  .cache(300); // 5 minutes
```

## Backup Strategy

### Automated Backups
- Supabase provides daily automated backups
- Configure backup retention in project settings
- Consider additional backup strategies for critical data

### Manual Backups
```bash
# Create manual backup
pg_dump "postgresql://postgres:<EMAIL>:5432/postgres" > backup.sql

# Restore from backup
psql "postgresql://postgres:<EMAIL>:5432/postgres" < backup.sql
```

## Monitoring

### Supabase Dashboard
- Monitor database performance
- Track API usage
- Set up alerts for errors

### Application Monitoring
```javascript
// Add error tracking
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(url, key, {
  global: {
    headers: {
      'x-application-name': 'wwms-production'
    }
  }
});
```

## Next Steps

1. **Deploy to Production**: Use cloud database for production deployment
2. **Configure Monitoring**: Set up alerts and monitoring
3. **Optimize Performance**: Implement caching and connection pooling
4. **Security Review**: Regular security audits and key rotation
5. **Backup Testing**: Regularly test backup and restore procedures

## Support

- **Supabase Docs**: [supabase.com/docs](https://supabase.com/docs)
- **Community**: [supabase.com/discord](https://supabase.com/discord)
- **WWMS Issues**: Create an issue in the project repository
