# Granular Permissions Architecture

## Overview

This document outlines the granular permissions system that allows Super Admins to configure per-Client-Tenant access controls and UI/UX customization as an override layer on top of base subscription tiers.

## Core Principles

1. **Foundation-First Development**: Complete foundational systems before advanced features
2. **Account-Level Permission Management**: Handle permissions at the user account level (/super-admin/users) rather than organization level
3. **Skeleton Architecture**: Establish the complete structure before implementing workflows
4. **Override Layer**: Granular permissions act as refinements on top of base subscription tiers
5. **Role-Based Access Control (RBAC)**: Enforce strict role validation using the `SUPER_ADMIN` role for all administrative functions

## Role Validation Pattern

### SUPER_ADMIN Role
- The `SUPER_ADMIN` role is the highest privilege role in the system
- Required for all administrative functions in the `/api/super-admin/` namespace
- Validated using the `toUserRoles` helper function for consistent role normalization

### Role Validation in API Routes
```typescript
// 1. Get session and validate authentication
const session = await getSession(request);
if (!session) {
  return NextResponse.json({ 
    error: 'Unauthorized',
    details: 'No valid session found' 
  }, { status: 401 });
}

// 2. Normalize and validate roles
const currentUserRoles = toUserRoles(Array.isArray(session.role) ? session.role : [session.role]);
const isSuperAdmin = currentUserRoles.includes('SUPER_ADMIN');

if (!isSuperAdmin) {
  return NextResponse.json({ 
    error: 'Forbidden',
    message: 'Insufficient permissions. SUPER_ADMIN role required.',
    requiredRole: 'SUPER_ADMIN',
    userRoles: currentUserRoles
  }, { status: 403 });
}
```

### Error Handling
- **401 Unauthorized**: Returned when no valid session is found
- **403 Forbidden**: Returned when user lacks required permissions
- Include detailed error messages for debugging and logging

## Database Schema

### 1. Tenant Feature Permissions
```sql
CREATE TABLE tenant_feature_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  feature_key VARCHAR(100) NOT NULL, -- 'quotes.view', 'events.create', 'analytics.access'
  is_enabled BOOLEAN DEFAULT true,
  custom_config JSONB DEFAULT '{}', -- Feature-specific configuration
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(tenant_id, feature_key)
);
```

### 2. Tenant UI Customizations
```sql
CREATE TABLE tenant_ui_customizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  ui_element VARCHAR(100) NOT NULL, -- 'sidebar.affiliates', 'page.analytics', 'section.financial_data'
  visibility TEXT CHECK (visibility IN ('visible', 'hidden', 'restricted')) DEFAULT 'visible',
  custom_properties JSONB DEFAULT '{}', -- Element-specific customization
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(tenant_id, ui_element)
);
```

### 3. User Permission Overrides
```sql
CREATE TABLE user_permission_overrides (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  permission_type TEXT NOT NULL, -- 'feature', 'ui', 'data_access'
  permission_key VARCHAR(100) NOT NULL, -- Specific permission identifier
  permission_value JSONB NOT NULL, -- Permission configuration
  granted_by UUID REFERENCES auth.users(id), -- Super admin who granted this
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, tenant_id, organization_id, permission_type, permission_key)
);
```

### 4. Permission Templates
```sql
CREATE TABLE permission_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  template_type TEXT CHECK (template_type IN ('subscription_tier', 'role_based', 'custom')) NOT NULL,
  permissions JSONB NOT NULL, -- Complete permission configuration
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Permission Management UI Architecture

### 1. Super Admin User Management (/super-admin/users)
- **Account-Level Permission Assignment**: Assign/remove permissions from user accounts
- **Organization Context**: Manage permissions within specific organization contexts
- **Tenant Context**: Handle TNC and White Label tenant permissions
- **Permission Templates**: Apply predefined permission sets
- **Granular Controls**: Individual feature and UI element permissions

### 2. Permission Assignment Interface
```typescript
interface PermissionAssignmentUI {
  userAccount: {
    basicInfo: UserProfile;
    currentPermissions: UserPermission[];
    availableTemplates: PermissionTemplate[];
  };
  
  organizationContext: {
    selectedOrg: Organization;
    orgSpecificPermissions: Permission[];
    inheritedPermissions: Permission[];
  };
  
  tenantContext: {
    selectedTenant: Tenant;
    tenantSpecificPermissions: Permission[];
    networkPermissions: Permission[];
  };
  
  granularControls: {
    featurePermissions: FeaturePermission[];
    uiCustomizations: UICustomization[];
    dataAccessControls: DataAccessControl[];
  };
}
```

## Implementation Phases

### Phase 1: Foundation (Week 1)
- [ ] Create permission tables and relationships
- [ ] Implement basic permission checking middleware
- [ ] Create permission template system
- [ ] Set up RLS policies for permission tables

### Phase 2: User Management UI (Week 2)
- [ ] Enhance /super-admin/users with permission management
- [ ] Create permission assignment interface
- [ ] Implement permission template application
- [ ] Add organization and tenant context switching

### Phase 3: Feature Integration (Week 3)
- [ ] Integrate permission checking into existing features
- [ ] Implement UI element visibility controls
- [ ] Create feature flag system based on permissions
- [ ] Add permission inheritance logic

### Phase 4: Advanced Controls (Week 4)
- [ ] Implement bespoke plan creation
- [ ] Add managed service workflow configurations
- [ ] Create permission audit trail
- [ ] Implement permission conflict resolution

## Use Cases

### 1. Fully Managed Client
**Scenario**: Client only wants to see final service costs, not detailed affiliate financial data.

**Implementation**:
```json
{
  "ui_customizations": {
    "section.affiliate_financial_data": "hidden",
    "page.affiliate_management": "restricted",
    "sidebar.affiliate_rates": "hidden"
  },
  "feature_permissions": {
    "affiliates.view_rates": false,
    "affiliates.negotiate": false,
    "quotes.view_breakdown": false
  }
}
```

### 2. Self-Service Client with Limited Analytics
**Scenario**: Client manages their own affiliates but doesn't need advanced analytics.

**Implementation**:
```json
{
  "feature_permissions": {
    "affiliates.manage": true,
    "analytics.advanced": false,
    "reports.financial": false
  },
  "ui_customizations": {
    "page.advanced_analytics": "hidden",
    "sidebar.financial_reports": "hidden"
  }
}
```

### 3. TNC with Custom Branding
**Scenario**: LIMO123 wants their own branding but access to TransFlow network.

**Implementation**:
```json
{
  "tenant_permissions": {
    "network.transflow_access": true,
    "branding.custom": true,
    "customers.direct_registration": true
  },
  "ui_customizations": {
    "branding.logo": "custom",
    "branding.colors": "custom",
    "page.network_upsell": "visible"
  }
}
```

## Technical Implementation

### 1. Permission Checking Middleware
```typescript
export async function checkPermission(
  userId: string,
  tenantId: string,
  organizationId: string,
  permissionKey: string
): Promise<boolean> {
  // 1. Check user-specific overrides
  // 2. Check organization-level permissions
  // 3. Check tenant-level permissions
  // 4. Fall back to subscription tier defaults
  // 5. Apply inheritance rules
}
```

### 2. UI Component Permission Wrapper
```typescript
export function PermissionGate({
  permission,
  children,
  fallback = null
}: PermissionGateProps) {
  const hasPermission = usePermission(permission);
  return hasPermission ? children : fallback;
}
```

### 3. Feature Flag Integration
```typescript
export function useFeatureFlag(featureKey: string) {
  const { user, tenant, organization } = useContext();
  return checkPermission(user.id, tenant.id, organization.id, featureKey);
}
```

## Migration Strategy

### 1. Backward Compatibility
- Existing functionality continues to work
- Permissions default to "enabled" for existing users
- Gradual migration of features to permission-based system

### 2. Default Permission Sets
- Create default templates for each subscription tier
- Apply templates to existing users based on current roles
- Maintain existing role-based access as fallback

### 3. Testing Strategy
- Test permission inheritance logic
- Verify UI element visibility controls
- Validate feature flag integration
- Test permission conflict resolution

This architecture provides the foundation for the complete granular permissions system while maintaining flexibility for future enhancements and ensuring backward compatibility with existing functionality.
