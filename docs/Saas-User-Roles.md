# WWMS SaaS User Roles

> **System Entities Overview**
>
> | Entity/Page              | Description                                                                                  | DB Table(s)                |
> |-------------------------|---------------------------------------------------------------------------------------------|----------------------------|
> | **Tenants**             | Isolated workspaces for each client/affiliate/network. All business data is scoped by tenant_id. | tenants, tenant_users      |
> | **Users**               | Authenticated individuals. Linked to tenants via tenant_users (can belong to multiple tenants). | auth.users, profiles       |
> | **Orgs/Companies**      | Business entities (e.g., affiliate companies, client companies). May be linked to tenants or used for grouping. | companies, user_companies, organizations |

---

## User Roles Update (2025-07)

- **Tenants** are the top-level isolation boundary. All business data, users, and orgs are scoped by tenant_id.
- **Users** are authenticated individuals, linked to tenants via tenant_users, and can belong to multiple tenants.
- **Orgs/Companies** are business entities, linked to tenants or used for grouping users/data within a tenant.
- **White-labeling** is a feature/flag (not a separate account type), enabling custom branding, domains, and email templates per tenant if allowed by plan.
- **Granular permissions** are managed via templates and per-user overrides, supporting flexible access control and feature gating.
- **Super Admin** has global access, can manage tenants, users, and permissions, and can impersonate tenant admins for onboarding and support.

### Key Flows
- Super Admin creates a tenant (Client, Affiliate, TNC, White Label, etc.).
- Super Admin or Tenant Admin invites/creates users and assigns them to the tenant.
- Users may be further associated with organizations/companies for business logic, reporting, or permissions.
- Data access and UI context are always scoped by the current tenant.
- White-labeling is enabled per-tenant via a feature flag and branding configuration.

### White-Labeling
- White-label is a **feature/flag** on the tenant, not a separate tenant type.
- Enables custom branding, domains, and email templates per tenant.
- UI and API logic should always check the white-label flag and branding config.

### Permissions
- Four permission templates: Fully Managed Client, Self-Service Client, TNC Full Access, Medical Transport Specialist.
- Templates can be applied per user, with per-user overrides and audit trails.
- Permissions cover feature access, UI customizations, and access controls.

### Onboarding
- After tenant creation, Super Admin is prompted to invite/create the first admin user via a modal dialog.
- User creation/invitation API must be robust and support both new and existing users.
- All management pages use a shared drawer/modal pattern, context-aware based on tenant type and enabled features.

### Implementation Status
- See the Implementation Status section for completed, partial, and planned features.
- All architectural decisions and flows are now reflected in the codebase and documentation.

### Linear Issue References
- GUG-47: Test: White Label Branding, Isolation, and User Experience
- GUG-32: White-Label Engine
- GUG-61: UI: Super Admin Tenant Management
- GUG-63: Implement granular permissions skeleton architecture
- GUG-64: Granular Permissions
- GUG-95: Standardize API Endpoints for Super Admin

---

## Relationships and Flows

- **Tenants**: Top-level isolation boundary. All business data, users, and orgs are scoped by tenant_id.
- **Users**: Authenticated individuals. Linked to tenants via tenant_users. Can belong to multiple tenants.
- **Orgs/Companies**: Business entities. May be linked to tenants or used for grouping users/data within a tenant.

### Typical Flows
- Super Admin creates a tenant (Client, Affiliate, TNC, White Label, etc.).
- Super Admin or Tenant Admin invites/creates users and assigns them to the tenant.
- Users may be further associated with organizations/companies for business logic, reporting, or permissions.
- Data access and UI context are always scoped by the current tenant.

---

## Entity Relationships & Flows

- **Tenants** are the top-level isolation boundary. Each tenant can have multiple users and organizations/companies.
- **Users** are created via Supabase Auth and linked to tenants through the tenant_users table. Users can belong to multiple tenants and have roles per tenant.
- **Orgs/Companies** represent business entities and may be associated with tenants for reporting, grouping, or legacy purposes. For affiliates, the affiliate_user_companies table manages the many-to-many relationship between users and companies.

**Typical Flows:**
- Super Admin creates a tenant (Client, Affiliate, TNC, White Label, etc.).
- Super Admin or Tenant Admin invites/creates users and assigns them to the tenant.
- Users may be further associated with organizations/companies for business logic, reporting, or permissions.

---

## User Roles Summary

The WWMS SaaS platform implements a multi-tenant architecture with the following core user roles:

### Platform-Level Roles

1. **System Admin (`<EMAIL>`):** (Also referred to as Super Admin)
   - **Role:** `SUPER_ADMIN` (previously `ADMIN`)
   - **Tenancy:** Operates *above* individual Client/Affiliate Accounts with global "God's view."
   - **Primary Functions:** 
       - Platform governance, tenant account management (creation, configuration, suspension), subscription management, and global affiliate network oversight.
       - Manages user permissions and role assignments across all tenants
       - Does **not** act as a direct intermediary in routine quote transactions for self-service Client Tenants.
   - **Key Capabilities for Service Delivery & Customization:**
       - Utilizes **impersonation** to act on behalf of Client Tenants for the delivery of Hybrid (Collaborative, Managed Core) and Fully Managed service tiers.
       - Configures **granular access and feature visibility** for individual Client Tenants, tailoring the platform experience beyond standard role permissions or subscription tiers to meet specific service agreements.
   - **Authentication:** 
       - Supabase authenticated user with `SUPER_ADMIN` role
       - Role validation is performed using the `toUserRoles` helper function which normalizes role data from the session
   - **Role Validation Pattern:**
     ```typescript
     // Example of role validation in API routes
     const currentUserRoles = toUserRoles(session.role);
     if (!currentUserRoles.includes('SUPER_ADMIN')) {
       return NextResponse.json({ 
         error: 'Forbidden',
         message: 'Insufficient permissions. SUPER_ADMIN role required.',
         requiredRole: 'SUPER_ADMIN',
         userRoles: currentUserRoles
       }, { status: 403 });
     }
     ```

### Client Tenant Roles

2. **Client Account (Tenant Space / Paid Subscription):**
   - **Client Admin (Event Manager):** The main authenticated user managing a Client Account (e.g., `<EMAIL>`).
     - **Role:** `CLIENT`
     - **Portal:** `/event-manager/dashboard`
     - **Permissions:** Full access to create events, manage quotes, assign coordinators
     - Controls the Client Account's data and manages Coordinator and Passenger users
     - Can configure quote workflows including rate request vs. fixed offer decisions based on:
       - Affiliate-defined radius parameters from central points
       - Affiliate-defined date blocks for special events
       - Availability of configured rates for active vehicles
   - **Coordinator Users:** Authenticated users created by the Client Admin with limited privileges within their Client Account (e.g., `<EMAIL>`).
     - **Role:** `CLIENT_COORDINATOR`
     - **Portal:** `/event-manager/dashboard` (same portal, filtered data)
     - **Permissions:** Limited to assigned events/trips only, cannot create new events
     - **Assignment:** Assigned by CLIENT to specific events/trips for specialized coordination
   - **Passenger Users:** Authenticated users who can view their booking status and trip details.

### Affiliate Tenant Roles

3. **Affiliate Account (Tenant Space / Paid Subscription):**
   - **Affiliate Admin/Owner:** The main authenticated user managing an Affiliate Account (e.g., `<EMAIL>`). Controls the Affiliate Account\'s data and manages Dispatcher and Driver users. An Affiliate Admin can be associated with multiple Affiliate Companies.
     - Configures service areas with:
       - Radius parameters from central points (e.g., downtown areas) to determine when rate requests are appropriate
       - Date blocks for special events to enable special pricing
       - Rate cards for active vehicles that match quote requirements
     - Can submit counter-offers with explanatory notes in response to quote requests
   - **Dispatcher Users:** Authenticated users created by the Affiliate Admin with operational privileges within their Affiliate Account.
   - **Driver Users:** Authenticated users who can view trips assigned to them.

## Tenant User Relationships & Multi-Company Affiliates

- All sub-account users (Coordinators, Passengers, Dispatchers, Drivers) are Supabase authenticated users.
- Their `auth.uid()` is associated with the `tenant_id` of their parent Client or Affiliate Account.
- **For Affiliates**: The relationship between a user and an affiliate company, including their role (OWNER, DISPATCHER, DRIVER) and status (INVITED, ACTIVE, DISABLED), is managed in the `affiliate_user_companies` table. This allows:
    - A single user (e.g., an affiliate manager) to be linked to multiple `affiliate_companies` (e.g., branches in different cities).
    - Each `affiliate_company` to have multiple users with distinct roles.
- Upon login, if an affiliate user is linked to multiple active companies, they will be prompted to select a company context to operate under for that session.
- The `saas_tenants.tenant_users` table links users to Client tenants.

## Affiliate Onboarding Flows

Two primary flows exist for onboarding affiliates:

1.  **Classic Admin Invite**:
    *   A Super Admin or a Client Admin (with appropriate permissions) invites an existing or new user to become an Affiliate Admin/Owner for a new or existing Affiliate Company.
    *   An entry is created in `affiliate_user_companies` with the user\'s ID, the affiliate company ID, role (`OWNER`), and status (`INVITED`).
    *   The invited user receives an email, clicks a link to accept, registers (if new), and their status in `affiliate_user_companies` becomes `ACTIVE`.

2.  **Quote-Driven "Just-in-Time" Onboarding**:
    *   A Super Admin or Client Admin sends a quote request directly to an affiliate\'s email, even if the affiliate is not yet registered on the platform.
    *   The email contains a deep link to the specific offer (e.g., `/affiliate/offers/:offerId`).
    *   If the user is not registered:
        *   They are prompted for a one-click registration (email is pre-filled).
        *   Upon successful registration, a new `affiliate_companies` record can be auto-created (or linked to an existing one if identifiable).
        *   A corresponding entry in `affiliate_user_companies` is created, linking the user to this company with `role = OWNER` and `status = ACTIVE`.
    *   The user is then taken directly to the offer page.
    *   Post-interaction with the first offer, they are guided to complete their company profile and full onboarding.

## Row-Level Security Implementation

The core RLS policy pattern for tenant isolation:

```sql
CREATE POLICY tenant_isolation_policy ON table_name
  FOR ALL
  TO authenticated
  USING (
    -- 1. System Admin gets full access
    (auth.jwt() -> 'raw_app_meta_data' ->> 'role' = 'ADMIN')
    OR
    -- 2. Users access data from their tenant
    (tenant_id = saas_tenants.get_current_tenant_id())
    OR
    -- 3. All users can access global data
    (tenant_id IS NULL)
  );
```

For more details on roles, permissions, and workflows, please refer to the [WWMS SaaS Master Documentation](./WWMS-SaaS-Master-Documentation.md).
