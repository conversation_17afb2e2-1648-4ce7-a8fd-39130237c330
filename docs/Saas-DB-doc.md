# WWMS SaaS Database Documentation

> **System Entities Overview**
>
> | Entity/Page              | Description                                                                                  | DB Table(s)                |
> |-------------------------|---------------------------------------------------------------------------------------------|----------------------------|
> | **Tenants**             | Isolated workspaces for each client/affiliate/network. All business data is scoped by tenant_id. | tenants, tenant_users      |
> | **Users**               | Authenticated individuals. Linked to tenants via tenant_users (can belong to multiple tenants). | auth.users, profiles       |
> | **Orgs/Companies**      | Business entities (e.g., affiliate companies, client companies). May be linked to tenants or used for grouping. | companies, user_companies, organizations |

---

## Architectural Update (2025-07)

- **Tenants** are the top-level isolation boundary. All business data, users, and orgs are scoped by tenant_id.
- **Users** are authenticated individuals, linked to tenants via tenant_users, and can belong to multiple tenants.
- **Orgs/Companies** are business entities, linked to tenants or used for grouping users/data within a tenant.
- **White-labeling** is a feature/flag (not a separate account type), enabling custom branding, domains, and email templates per tenant if allowed by plan.
- **Granular permissions** are managed via templates and per-user overrides, supporting flexible access control and feature gating.
- **Super Admin** has global access, can manage tenants, users, and permissions, and can impersonate tenant admins for onboarding and support.

### Key Flows
- Super Admin creates a tenant (Client, Affiliate, TNC, White Label, etc.).
- Super Admin or Tenant Admin invites/creates users and assigns them to the tenant.
- Users may be further associated with organizations/companies for business logic, reporting, or permissions.
- Data access and UI context are always scoped by the current tenant.
- White-labeling is enabled per-tenant via a feature flag and branding configuration.

### White-Labeling
- White-label is a **feature/flag** on the tenant, not a separate tenant type.
- Enables custom branding, domains, and email templates per tenant.
- UI and API logic should always check the white-label flag and branding config.

### Permissions
- Four permission templates: Fully Managed Client, Self-Service Client, TNC Full Access, Medical Transport Specialist.
- Templates can be applied per user, with per-user overrides and audit trails.
- Permissions cover feature access, UI customizations, and access controls.

### Onboarding
- After tenant creation, Super Admin is prompted to invite/create the first admin user via a modal dialog.
- User creation/invitation API must be robust and support both new and existing users.
- All management pages use a shared drawer/modal pattern, context-aware based on tenant type and enabled features.

### Implementation Status
- See the Implementation Status section for completed, partial, and planned features.
- All architectural decisions and flows are now reflected in the codebase and documentation.

### Linear Issue References
- GUG-47: Test: White Label Branding, Isolation, and User Experience
- GUG-32: White-Label Engine
- GUG-61: UI: Super Admin Tenant Management
- GUG-63: Implement granular permissions skeleton architecture
- GUG-64: Granular Permissions
- GUG-95: Standardize API Endpoints for Super Admin

---

## Relationships and Flows

- **Tenants**: Top-level isolation boundary. All business data, users, and orgs are scoped by tenant_id.
- **Users**: Authenticated individuals. Linked to tenants via tenant_users. Can belong to multiple tenants.
- **Orgs/Companies**: Business entities. May be linked to tenants or used for grouping users/data within a tenant.

### Typical Flows
- Super Admin creates a tenant (Client, Affiliate, TNC, White Label, etc.).
- Super Admin or Tenant Admin invites/creates users and assigns them to the tenant.
- Users may be further associated with organizations/companies for business logic, reporting, or permissions.
- Data access and UI context are always scoped by the current tenant.

---

## 1. Database Overview
The database serves a multi-tenant SaaS transportation management system focusing on quotes, trips, events, and affiliate management, with robust security through row-level security (RLS) policies and role-based access.

## 2. Multi-Tenant Structure

### 2.1 Core Tenant Tables
- **saas_tenants.tenants**
  - `id`: Primary tenant identifier
  - `name`: Tenant organization name
  - `domain`: Custom domain (for white-labeling)
  - `status`: Active, suspended, inactive
  - `settings`: JSONB for tenant-specific configurations

- **saas_tenants.tenant_users**
  - `tenant_id`: Reference to tenant
  - `user_id`: Reference to auth.users
  - `role`: Role within this specific tenant

### 2.2. Tenant Isolation
- Tables with business data include `tenant_id` column
- Row Level Security (RLS) policies enforce tenant isolation
- Helper functions like `saas_tenants.get_current_tenant_id()` determine current context
- System admins can access all tenant data through special functions

## 3. User Roles and Authentication

### 3.1 Role System
The system uses an array-based role system in the profiles table:
- **Primary Roles**: SUPER_ADMIN (formerly ADMIN), CLIENT (formerly CUSTOMER), AFFILIATE, CLIENT_COORDINATOR (formerly EVENT_MANAGER), AFFILIATE_DISPATCH, PASSENGER, DRIVER
- **Portal Mapping**:
  - `CLIENT` → `/event-manager` (full access to organizational events/quotes)
  - `CLIENT_COORDINATOR` → `/event-manager` (limited to assigned events only)
  - `SUPER_ADMIN` → `/super-admin` (platform administration)
  - `AFFILIATE` → `/affiliate` (transportation provider portal)
- **Data Access**: CLIENT_COORDINATOR has same portal as CLIENT but with filtered data access via RLS policies
- **Storage**: Stored in both a legacy role column (single role) and a newer roles array column (multiple roles possible)
- **Synchronization**: Maintained via triggers that keep role = roles[1]

### 3.2 Authentication Flow
- When a user is created in auth.users, the on_auth_user_created trigger automatically creates a corresponding record in profiles
- JWT tokens contain role information in app_metadata.role
- Helper functions like has_role() check permissions based on the roles array in profiles

## 4. Core Business Tables

### 4.1 User Management
- **auth.users** (Supabase Auth)
  - Standard Supabase Auth table with JWT claims in raw_app_meta_data and raw_user_meta_data.
- **profiles**
  - Extended user information and roles
  - id (PK, References auth.users)
  - roles (TEXT[], Valid values: ADMIN, CLIENT, AFFILIATE, CLIENT_COORDINATOR, AFFILIATE_DISPATCHER, PASSENGER, DRIVER)
  - role (TEXT, Kept for backward compatibility, synced with roles[1])
  - RLS: Users can view/update their own profile, service role can manage all profiles

### 4.2 Business Entity Tables
All core business tables now include a tenant_id column and appropriate RLS policies:

- **quotes**: Core business entity for transportation quotes
- **intermediate_stops**: Multiple stops for quotes with coordinates
- **quote_timeline** and **quote_status_history**: Track changes to quotes
- **quote_offers** and **rate_proposals**: Handle price proposals from affiliates
- **affiliate_companies**: Transportation providers
- **vehicles**: Vehicle fleet management
- **drivers**: Driver information
- **rate_cards**: Pricing structures for affiliate services
- **trips**: Actual trips created from quotes
- **events**: Events that may require multiple trips

## 5. Row Level Security (RLS)

### 5.1 Tenant Isolation Policy Pattern
```sql
CREATE POLICY tenant_isolation_policy ON table_name
  FOR ALL
  TO authenticated
  USING (
    -- 1. Super Admin gets full access
    (auth.jwt() -> 'raw_app_meta_data' ->> 'role' = 'ADMIN')
    OR
    -- 2. Users access data from their tenant
    (tenant_id = saas_tenants.get_current_tenant_id())
    OR
    -- 3. All users can access global data
    (tenant_id IS NULL)
  );
```

### 5.2 Role-Based Access
Multiple approaches working together:
- JWT claim verification: auth.jwt()->'app_metadata'->>'role'
- Profile role check: 'ROLE' = ANY(profiles.roles)
- Helper functions: has_role(), has_admin_access(), etc.

### 5.3 Ownership-Based Access
Many tables restrict access based on relationship to the user:
- Client can access their own quotes
- Affiliate can access quotes assigned to them
- Owner can access their company data

## 6. Key Functions and Triggers

### 6.1 Tenant Context Functions
- **saas_tenants.get_current_tenant_id()**: Returns the tenant ID for the current user
- **saas_tenants.is_system_admin()**: Checks if the current user is a system admin
- **saas_tenants.user_belongs_to_tenant(tenant_id)**: Checks if user belongs to specified tenant

### 6.2 User Management
- **handle_new_user()**: Creates profiles when users are created
- **sync_role_from_roles()**: Keeps role column synchronized with first element of roles array

### 6.3 Triggers
- **on_auth_user_created**: Automatically creates profiles
- **quotes_status_history_trigger**: Logs quote status changes
- **record_quote_status_change_trigger**: Records timeline entries for quotes

### 6.4 Helper Functions
- **has_role(text)**: Checks if current user has a specific role
- **create_quote_with_stops(jsonb, jsonb)**: Atomically creates quote with intermediate stops
- **update_quote_with_stops(uuid, jsonb, jsonb)**: Updates quote with intermediate stops